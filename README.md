# Python-tools

## Development instructions for scrapers

1. Usually adding scraper is narrowed down to adding particular method into the correspondent IExchangeHttpApi or IExchangeStreamingApi implementation.

If exchange has HTTP endpoints following methods could be added:

|         | Trades      | Books | Liquidations | Open Interests | Funding rates |
| ------- | ----------- | ----- | ------------ | -------------- | ------------- |
| Spot    | last_trades() | book()  | last_liquidations() | last_open_interest() | last_funding_rates() |
| Futures | last_trades() | book()  | | futures_open_interest() | |
| Option  | | | | option_open_interest() | |

For Streaming endpoints:

|         | Trades         | Books           | Liquidations | Quotes |
| ------- | -------------- |-----------------| ------------ | ------ |
| Spot    | trades()         | books()         | liquidations() | quotes() |
| Futures | futures_trades() | futures_books() |     |        |
| Option  | option_trades()  | option_books()  |              |        |

2. To run scraper locally for test use apps from application folder. Example command:
```
python -m src.octopus.applications.http_book_scraper Bybit --machine svyat-HP-ZBook-Power-G7-Mobile-Workstation --poll-interval 300 --postgres-out localhost:5432:postgres:postgres:postgres+localhost:5432:postgres:postgres:postgres --rate-limit-multiplier 0.05 --spot --book-depth 30000 --instruments dydx-usdt
```
Command params for particular scraper are described in correspondent yaml file from `src/octopus/applications/deploy/` folder.

## Release instructions

April 2022: At present we are using docker in legacy Hetzner and
Kubernetes (k8s) in Copper/HEL1 staging and Silver/NY5 (nearly) production.
As some point in the future we will abandon the docker infrastructure
and just run k8s.  Until that time, follow both sets of instructions below
(ignoring any redundant steps):

March 2024: We have now added an AWS k8s cluster "FH1" - follow [Fh1 deployment instructions](#K8s Fh1 Deployment)

October 2024: We added another production data center "CP1" in JFK1

December 2024: We decommissioned Silver/NY5, also we removed AWS k8s cluster "FH1".
Now we have Copper/HEL1 as staging and two production envs: legacy Hetzner and CP1.

May 2025: We decommissioned Copper/HEL1 and added CDEV1 (Dallas).
Now we have CDEV1 as staging and two production envs: legacy Hetzner and CP1.

### Docker Release instructions for scrapers:
* Release your changes to staging and make sure they run there without errors for at least 15min
  1. Add scrapers to `inventory/staging.py`
  1. Configure deployments with `applications/deployment_configs_gen.py --operations-root {absolute path to your local operations repo}`.
  The application will generate correspondent deployments in `octopus` repo and prometheus targets in `operations` repo.
  Commit and push these changes (octopus and operations).
  1. Create MR for `operations` repo, get approve from owner (currently Oleksandr)
  and merge Prometheus targets.
  1. Using a master branch build on the octopus CI/CD: run `deploy:resources` -> `ops-prometheus-production` to apply changes in `operations` repo.
  This job is available for any pipeline into master branch under `deploy:resources` pipeline step.
  1. Use CI/CD `deploy:resources` -> `dashboards-staging` to update grafana dashboards.
  1. Use `deploy-staging-1` pipeline step with corresponding job to release your feed handler.
  1. Monitor Grafana dashboards for 15 minutes to spot errors

* Release your changes to production
  1. Add scrapers to `inventory/production.py` and rerun `deployment_configs_gen` (steps 1-4 above).
  1. Add Release Notes to your GitLab ticket.  [Template](https://gitlab.com/coinmetrics/wiki/-/wikis/Feed-Handlers/Release-Information)
  1. Get approval from component owner (currently Oleksandr) or CTO or VP Engineering.
  1. Write a short summary of the changes you're planning to deploy in #sp-prod-deployment-announcements. Format: Feed handlers (ticket): Description.  Link the `ticket` to your gitlab ticket.
  1. Merge your changes into master
  1. Deploy production grafana dashboards.
  1. Using `deploy:production-1` pipeline, deploy changes to one of production servers and monitor the changes for between 15min and 1h
  1. Validate the data are written into the database
  1. if all good, deploy to the other production server and monitor the changes for 15min-1h
  1. if no issues, deployment can be treated as successful.  Mark slack entry above as (done).

# k8s Release instructions for feed handlers
* Release your changes to staging and make sure they run there without errors for at least 15min
  1. Add feed handlers to `inventory/cdev1.py`
  1. Configure deployments with `applications/deployment_k8s_gen.py`.  Commit and push these changes.
  1. Use CI/CD `deploy:resources` -> `dashboards-cdev1` to update grafana dashboards.
  1. If k8s pod names changed, delete old pod(s) (ask for help if this is not clear).
  1. Use `deploy-k8s:cdev1:feed-handlers-1` pipeline step with corresponding job(s) to release your feed handler(s).
  1. Monitor Grafana dashboards for 15 minutes to spot errors
  1. Make sure all pods are running with `kubectl -n fh get pods | grep -v " 1/1 " | grep -v "^NAME"`
  1. Validate the data are written to the database
  1. Use `deploy-k8s:cdev1:feed-handlers-2` pipeline step with corresponding job(s) to release your feed handler(s).

* Release your changes to production
  1. Add feed handlers to `inventory/ny5.py` and rerun `deployment_k8s_gen`.
  1. Add Release Notes to your GitLab ticket.  [Template](https://gitlab.com/coinmetrics/wiki/-/wikis/Feed-Handlers/Release-Information)
  1. Get approval from component owner (currently Oleksandr) or CTO or VP Engineering.
  1. Write a short summary of the changes you're planning to deploy in #sp-prod-deployment-announcements. Format: Feed handlers (ticket): Description.  Link the `ticket` to your gitlab ticket.
  1. Merge your changes into master
  1. Deploy production grafana dashboards `deploy:resources` -> `dashboards-ny5`.
  1. If k8s pod names changed, delete old pod(s) (ask for help if this is not clear).
  1. Using `deploy-k8s:ny5:feed-handlers-1` pipeline, deploy changes one production server and monitor the changes for between 15min and 1h
  1. Make sure all pods are running with `kubectl -n fh get pods | grep -v " 1/1 " | grep -v "^NAME"`
  1. Validate the data are written into the database
  1. if all good, use `deploy-k8s:ny5:feed-handlers-2` to deploy to the other production server and monitor the changes for 15min-1h
  1. if no issues, deployment can be treated as successful.  Mark slack entry above as (done).

# k8s Custom Deployments
The k8s environment supports customized deployments via the `custom-deployment` targets in the GitLab CI/CD pipeline.
  1. Navigate to the project CI/CD pipeline, and click on the specific deployment target (not the play button)
  1. Configure a `CUSTOM` variable where the value is an egrep-style regex for what you want to deploy<br>
     Note that the `DRYRUN` variable is automatically set to `1` for validation purposes
  1. Click `Trigger this manual action` and verify the actual deployments that will be started in the log
  1. If the log looks correct, click the chevron in the upper-right-hand corner (by the restart logo) and select `Update CI/CD variables`
  1. To force the deployment, override the `DRYRUN` variable with value of `0` and restart the job using `Run job again`

Some useful regex patterns for doing custom deployments:
- Deploy one feed handler and the shipper: `bybit-trad-spot-stre|shipper-spot-trade`
- Deploy all Bybit spot books: `bybit-.*-book-spot`
- Deploy everything: `.*`

There is also a short ["walk-through" video](https://www.loom.com/share/a231ba1a7ada474f9283240b623776a6) available, if that is your preferred learning style.

Notes:
- Always perform a DRYRUN test before trying to run an actual deployment. The simple act of doing a merge commit can change the target
  deployment set in an unexpected way; this is an important guardrail, please use it.
- Use the regular deployment buttons when adding or removing feed handlers, since that often requires shipper updates.
- Custom deployments can be run multiple times, for example, starting with a single feed handler, and then expanding the regex to include
  additional feed handlers in subsequent runs. If the same feed handler is matched across multiple deployment runs, k8s will detect
  that the deployment fingerprint has not changed and will not restart the feed handler a second time (which is quite useful).
- The logs for each deployment run can be found on the GitLab CI/CD Jobs page, in the lower right-hand corner (scroll to the bottom).


### Disclaimer
An emergency bugfix is allowed to bypass the process, and get the approval “in arrears”

Note: all IPs of all our servers can be found here: https://gitlab.com/coinmetrics/ops/operations/-/blob/master/inventories/production.yml

### New Metrics deployment (Docker environment only)
In case you need to add new metrics, you need to do additional steps:
- in case your service is entirely new:
  - add new target to Prometheus: file prometheus.yml in `operations` repo
  - create merge request, pass the review and merge
- update prometheus config in `octopus` by running ops-prometheus-production manual step in any pipeline for master


## Conventions

* Top-level package `src` contains packages for shared general-purpose libraries and projects.
* Project's executable scripts should be put into `applications` folder of the project.
* Applications are launched as follows: `python -m src.PROJECT_NAME.applications.APP_NAME <arguments>`, for example: `python -m src.octopus.applications.streaming_trade_scraper Coinbase`. See details [here](#running-applications-locally).

## Projects

### API client

* [feed_client](src/api_client/applications/feed_client.py) implements basic WebSocket client for trades, quotes and order book endpoints of CM market data feed.

### Octopus

Applications for market data collection.

* [trade_history_scraper](src/octopus/applications/trade_history_scraper.py) downloads historical trades from exchanges and continuously fetches new trades afterwards.
* [http_trade_scraper](src/octopus/applications/http_trade_scraper.py) continuously fetches latest trades in near real time via HTTP.
* [streaming_trade_scraper](src/octopus/applications/streaming_trade_scraper.py) continuously fetches latest trades in real time via WebSocket, FIX or other streaming protocol.
* [http_book_scraper](src/octopus/applications/http_book_scraper.py) continuously fetches order book snapshots via HTTP.
* [streaming_book_scraper](src/octopus/applications/streaming_book_scraper.py) continuously fetches order book updates in real time via WebSocket, FIX or other streaming protocol.
* [http_open_interest_scraper](src/octopus/applications/http_open_interest_scraper.py) continuously fetches open interest data points via HTTP.
* [futures_metadata_scraper](src/octopus/applications/futures_metadata_scraper.py) downloads futures contract specifications from exchanges.
* [shipper](src/octopus/applications/shipper.py) continuously moves trades, books and other market data from local emergency storage to primary database.
* [proxy_check](src/octopus/applications/proxy_check.py) monitors health of forward proxy servers.
* [proxy_groups_gen](src/octopus/applications/proxy_groups_gen.py) prints configs for proxy groups for given proxies list and group weights.
* [proxy_groups_utilization](src/octopus/applications/proxy_groups_utilization.py) prints proxy groups capacity allocation rate.

### Resources

* [validate_resources](src/resources/applications/validate_resources.py) validates Coin Metrics data files.

### Site

* [site_servant](src/site/applications/site_servant.py) script running on coinmetrics.io website that is responsible for downloading CSVs and splitting them into chunks digestible by charts.
* [datavis_servant](src/site/applications/datavis_servant.py) prototype of data visualizer back end service.

### Utils

* [proxy_list_transform](src/octopus/applications/proxy_list_transform.py) handy script to transform vendor list of proxies to our format.
* [proxy_list_split_by_geolocation](src/octopus/applications/proxy_list_split_by_geolocation.py) script to split vendor list of proxies by its actual geolocation.

## Database setup

### Octopus
Run Postgres instance for local development:
```shell
$ docker run -d --name octopus -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=postgres -p 6432:5432 postgres:13
```
All following starts might be done with the command:
```shell
$ docker restart octopus
```

## Kafka

### Gotchas
- Don't use `python-kafka` lib. Use `confluent-kafka` instead. We got almost 50% less CPU utilization after switching to `confluent-kafka`.

### Local Kafka setup
To test kafka locally you can go to local_kafka folder and do docker-compose up there to bring kafka up.

```shell
cd local_kafka
docker-compose up -d
```

after that you can pass the kafka url and ip to the appropriate scraper parameter like:

```shell
--kafka-out-proto 127.0.0.1:9091
```

to check that the values are written correctly you can either check kafka UI on `127.0.0.1:9000` address or connect to `127.0.0.1:9091` with a consumer and read messages from appropriate topic (`trades` for trade data)

## Proxy groups capacity management

`proxy_groups_utilization.py` application can be used for proxy groups capacity management.

To detect overused proxy groups set `--threshold` argument (default value is `0.5` which is 100% of proxy capacity since we have 2 instances):

    `proxy_groups_utilization.py --threshold 0.5 --data-types trade book open_interest`

## Proxies extension

The process to extend the proxies should be:
1. Transform vendor proxies list to our format - the 'src/octopus/applications/proxy_list_transform.py' script might be used
1.1. [WebShare] vendor is known for providing proxies with wrong geolocation -
for it we can use the 'src/octopus/applications/proxy_list_split_by_geolocation.py' script.
It will split proxies by their actual geolocation
2. Update proxy groups - the 'src/octopus/applications/proxy_groups_gen.py' might be used to split proxies in proportion

There are three ways to update proxy groups:
1. [minimal setup] Add desired groups to HANDCRAFTED_GROUPS inside `proxies/inventory.py`

        ```
        HANDCRAFTED_GROUPS: Dict[str, List[str]] = {
            "group_tag": [
                "http:seconduser:Vah5eaheeshetiaphioph3:************:5555",
                "http:seconduser:Vah5eaheeshetiaphioph3:************:5555",
            ],
            ...
        }
        ```

2. [minimal setup + enums for verbosity] Handcraft proxy groups manually
    - add enum with group names to `proxies/inventory.py`

        ```
        class TestProxyEu(StrEnum):
            BOOK_TICK_BY_TICK = "test-proxy-eu-book-tick-by-tick"
            TRADE_REALTIME = "test-proxy-eu-trade-realtime"
        ```

    - add proxy groups to HANDCRAFTED_GROUPS inside `proxies/inventory.py`

        ```
        HANDCRAFTED_GROUPS: Dict[str, List[str]] = {
            TestProxyEu.BOOK_TICK_BY_TICK: _production_hetzner_proxy([(1, 25)]),
            TestProxyEu.TRADE_REALTIME: _production_hetzner_proxy([(25, 30)]),
            ...
        }
        ```

    - [not mandatory] to keep track of all groups we have we should add a group to PROXY_GROUPS_CONFIGS
        ```
        # add to `proxies/inventory.py`
        class TestProxy(StrEnum):
            EU = "test-proxy-eu"
        ```
        ```
        # add to `proxy_groups_gen.py`
        PROXY_GROUPS_CONFIGS = [
            ...
            ProxyGroupConfig(PROXIES_INVENTORY[TestProxy.EU], hand_crafted=True)
        ]
        ```
3. [sophisticated setup] split proxies in proportion
    -  defined WEIGHTS in `proxy_groups_gen.py`:

        ```
        WEIGHTS: List[ProxyPoolWeight] = [
            ProxyPoolWeight("option-ticker", 6),
            ProxyPoolWeight("metadata-realtime", 3),
            ProxyPoolWeight("trade-history", 81),
            ProxyPoolWeight("trade-realtime", 30),
            ProxyPoolWeight("book-realtime", 9),
            ProxyPoolWeight("book-1h-realtime", 50),
            ProxyPoolWeight("book-history", 0),
            ProxyPoolWeight("open-interest-realtime", 10),
            ProxyPoolWeight("open-interest-history", 0),
            ProxyPoolWeight("liquidations-realtime", 26),
            ProxyPoolWeight("liquidations-history", 26),
            ProxyPoolWeight("funding-rate-realtime", 5),
            ProxyPoolWeight("funding-rate-history", 1),
            ProxyPoolWeight("quote-realtime", 50),
        ]
        ```

    - add enums with group names to `proxies/inventory.py`

        ```
        class TestProxy(StrEnum):
            EU = "test-proxy-eu"
        ```

    - add new proxies to PROXIES_INVENTORY in `proxies/inventory.py`- not mandatory, to keep track of all groups we have

        ```
        PROXIES_INVENTORY: Dict[str, List[str]] = {
            TestProxy.EU: _production_hetzner_proxy([(1, 30)])
            ...
        }
        ```

    - add new row to proxy configs

        ```
        PROXY_GROUPS_CONFIGS = [
            ProxyGroupConfig(INVENTORY[TestProxy.EU], WEIGHTS, enum_class_name="TestProxyEu", name_prefix="test-proxy-eu"),
            ...
        ]
        ```

    - run the `proxy_groups_gen.py` script
        - it will generate enum `TestProxyEu` with proxy group names in `proxies/enums.py`
        - and add new proxy groups split in `proxies/groups.py`

4. [sophisticated setup + manual] use script to split proxies in proportion, but adjust proxies manually
    - all the steps from p.3 except the last (run the `proxy_groups_gen.py` script)
    - run the `proxy_groups_gen.py` script with `--check` option - the output will show the diff

        ```
        removed 1 from HetznerUs.BOOK_TICK_BY_TICK [30 -> 29]
        added 4 to HetznerUs.FUTURES_TICKER [0 -> 4]
        removed 2 from HetznerUs.TRADE_HISTORY [61 -> 59]
        removed 1 from HetznerUs.BOOK_1H_REALTIME [37 -> 36]
        added 7 to WebShareEu.FUTURES_TICKER [0 -> 7]
        removed 2 from WebShareEu.TRADE_HISTORY [92 -> 90]
        removed 1 from WebShareEu.TRADE_REALTIME [34 -> 33]
        removed 1 from WebShareEu.BOOK_1H_REALTIME [57 -> 56]
        removed 1 from WebShareEu.LIQUIDATIONS_REALTIME [30 -> 29]
        removed 1 from WebShareEu.LIQUIDATIONS_HISTORY [30 -> 29]
        removed 1 from WebShareEu.QUOTE_REALTIME [57 -> 56]
        added 3 to WebShareUs.FUTURES_TICKER [0 -> 3]
        removed 1 from WebShareUs.TRADE_HISTORY [37 -> 36]
        removed 1 from WebShareUs.TRADE_REALTIME [14 -> 13]
        removed 1 from WebShareUs.BOOK_1H_REALTIME [23 -> 22]
        added 11 to SmartproxyUs.FUTURES_TICKER [0 -> 11]
        removed 4 from SmartproxyUs.TRADE_HISTORY [143 -> 139]
        removed 1 from SmartproxyUs.TRADE_REALTIME [53 -> 52]
        removed 2 from SmartproxyUs.BOOK_1H_REALTIME [88 -> 86]
        removed 1 from SmartproxyUs.LIQUIDATIONS_REALTIME [21 -> 20]
        removed 1 from SmartproxyUs.LIQUIDATIONS_HISTORY [46 -> 45]
        removed 2 from SmartproxyUs.QUOTE_REALTIME [88 -> 86]
        added 3 to BinanceNY5WebShare.FUTURES_TICKER [0 -> 3]
        removed 1 from BinanceNY5WebShare.TRADE_HISTORY [46 -> 45]
        removed 1 from BinanceNY5WebShare.LIQUIDATIONS_REALTIME [15 -> 14]
        removed 1 from BinanceNY5WebShare.QUOTE_REALTIME [28 -> 27]
        ```

    - review the groups and try to minimize the impacted groups while keeping the defined proportion

        ```
        added 4 to HetznerUs.FUTURES_TICKER [0 -> 4]

        removed 3 from HetznerUs.TRADE_HISTORY [61 -> 58]
        removed 1 from HetznerUs.BOOK_1H_REALTIME [37 -> 36]

        added 7 to WebShareEu.FUTURES_TICKER [0 -> 7]

        removed 3 from WebShareEu.TRADE_HISTORY [92 -> 89]
        removed 2 from WebShareEu.BOOK_1H_REALTIME [57 -> 55]
        removed 2 from WebShareEu.QUOTE_REALTIME [57 -> 55]

        added 3 to WebShareUs.FUTURES_TICKER [0 -> 3]

        removed 1 from WebShareUs.TRADE_HISTORY [37 -> 36]
        removed 2 from WebShareUs.BOOK_1H_REALTIME [23 -> 21]

        added 11 to SmartproxyUs.FUTURES_TICKER [0 -> 11]

        removed 5 from SmartproxyUs.TRADE_HISTORY [143 -> 138]
        removed 3 from SmartproxyUs.BOOK_1H_REALTIME [88 -> 85]
        removed 3 from SmartproxyUs.QUOTE_REALTIME [88 -> 85]

        added 3 to BinanceNY5WebShare.FUTURES_TICKER [0 -> 3]

        removed 2 from BinanceNY5WebShare.TRADE_HISTORY [46 -> 44]
        removed 1 from BinanceNY5WebShare.QUOTE_REALTIME [28 -> 27]
        ```

        After such minimization we will need to deploy only the following FHs:
        ```
        TRADE_HISTORY
        BOOK_1H_REALTIME
        QUOTE_REALTIME
        ```

    - manually pull proxies directly from the chosen groups following the minimized changes list above ^

### WebShare proxies update
When WebShare proxies are updated, the `WebShare` group should be split by geolocation, 
because this vendor is not reliable in term of what location the provided proxies will be from.
The `proxy_list_split_by_geolocation.py` script can be used for this purpose.


## Historical Patch
The daily totals application stores daily trades count per exchange in Minio file storage.
The historical patch application reads the daily cache and imports/exports rows between envs as required.

The 'historical_patch_admin.py` client can be used locally for the following:

1. Retrieve daily counts
2. Compare daily counts between envs
3. Delete cached daily counts

### Usage
Export Minio credentials (request from Infra team):
```
export CM_MINIO_ENDPOINT=minio.cnmtrcs.io:9002
export CM_MINIO_REGION=eu-hel1-hetz
export CM_PATCHDB_MINIO_BUCKET=market-data-patch
export CM_PATCHDB_MINIO_ACCESS_KEY=KEY
export CM_PATCHDB_MINIO_SECRET_KEY=PASSWORD
```

#### Retrieve daily counts
Retrieve cached daily counts for Coinbase
```
python -m src.octopus.applications.storage.historical_patch_admin counts --exchanges Coinbase --host-env hel1
DAY            HEL1
----------  -------
2014-01-01        0
...
2023-08-19  1208863
2023-08-20   975061
2023-08-21  1357495
2023-08-22  1554149
2023-08-23  1464904
```

#### Compare daily counts
Compare Coinbase daily counts between CDEV1 and CP1:
```
python -m src.octopus.applications.storage.historical_patch_admin compare --exchanges Coinbase --host-env cdev1 --other-env cp1
DAY           CDEV1       CP1       DIFF
----------  -------  --------  ---------
2014-01-01        0         0          0
...
2023-08-14  1208195   1208935       -740
2023-08-15  1537723   1538433       -710
2023-08-16  1862792   1863451       -659
2023-08-17  3021725   3022871      -1146
2023-08-18  2292130   2293051       -921
2023-08-19  1208863   1209108       -245
2023-08-20   975061    975398       -337
2023-08-21  1357495   1358216       -721
2023-08-22  1554149   1554651       -502
2023-08-23  1464904   1465262       -358
```

#### Delete cached daily totals
Cached totals can be deleted with the following command - if the daily-totals app is running it will commence repopulating the file:
```
python -m src.octopus.applications.storage.historical_patch_admin delete-exports --exchanges Coinbase --host-env hel1 --begin 2020-01-01 --end 2020-01-30

```

## Disabling feed handlers
The [disable-fh](./disable-fh) script allows for manual scaling down feed handlers of feed handlers (using `kubectl scale deployment DEPLOYMENT --replicas 0`).  It allows for groups of feed handlers, such as all instance 1 spot books, to be scaled down with a single command.

It is assumed that the required access to K8s clusters is configured.

```
usage: ./disable-fh [-Vhpe] -c CONTEXT -i INSTANCE -m MARKET -t TYPE

Scale down feed handler deployments in kubernetes environment.


REQUIRED OPTIONS:
    -c CONTEXT K8s context, e.g. 'copper', 'hel1', 'silver'
    -i INSTANCE feed handler stack instance {1|2}
    -t TYPE feed handler type {book|trade|meta|quote|ticker|open}
    -m MARKET feed handler market {spot|futures|options}
OTHER OPTIONS:
    -p         Preview mode (deployments will not be scaled)
    -e         Filter for specific exchange
    -h         Show this help message
    -V         Show version
Examples:
    # Disable all stack 1 spot trade feed handlers on copper
    ./disable-fh -c copper -t trade -m spot -i 1
    # Disable stack 2 binance futures books on copper
    ./disable-fh -c copper -t book -m futures -i 2 -e binance
    # Preview disable stack 2 open interest  futures on copper
    ./disable-fh -c copper -t open -m futures -p

```


## Running applications locally

Most applications support `--help` and `-h` CLI flags.

### Migrating db data
Use the `patch` application to copy missing trades and book data from one database environment to another.

Currently, patching trades and orderbooks between 2 dates is supported using `python -m src.octopus.applications.storage.patch`.

#### Example
The following examples employ port forwarding for remote db access as described
[here](https://gitlab.com/victor198/cm-tools/-/wikis/Production-Postgres-Access).

Preview missing trades from Hel1 to local db instance:
```
$ python -m src.octopus.applications.storage.patch \
  --type trades \
  --exchange MEXC \
  --source-database localhost:${DB_PORT_HEL1}:pg-trades-spot-stg-hel1-p:local:PASSWORD \
  --target-database localhost:5432:postgres:postgres:postgres \
  --begin "2022-10-31 00:00:00"  \
  --end "2022-10-31 00:02:00" \
  --spot
```

Copy missing trades from Hel1 to local db instance (note addition of `--commit`):
```
$ python -m src.octopus.applications.storage.patch \
  --type trades \
  --exchange MEXC \
  --source-database localhost:${DB_PORT_HEL1}:pg-trades-spot-stg-hel1-p:local:PASSWORD \
  --target-database localhost:5432:postgres:postgres:postgres \
  --begin "2022-10-31 00:00:00"  \
  --end "2022-10-31 00:02:00" \
  --spot \
  --commit
```

Copy missing Binance spot books from prod read replica to NY5 write db:
```
$ python -m src.octopus.applications.patch \
  --type books binance \
  --source-database localhost:${DB_PORT_HETZ}:postgres-books-replica4:public:PASSWORD \
  --target-database localhost:${DB_PORT_NY5}:pg-books-prd-ny5-p:postgres:PASSWORD \
  --begin "2022-10-31 00:00:00" \
  --end "2022-10-31 00:02:00" \
  --spot \
  --commit
```

Trade and order book data is big and we need to be mindful of the impact of copying large numbers of rows on database lag. The
`patch` application offers a number of options for controlling the write speed:
```
--batch-row-size batch_row_size
                      Number of rows to copy in each batch
--copy-wait-time copy_wait_time
                      Number seconds to wait between each copy request
--max-writes-per-second max_writes_per_second
                      Maximum number of rows to copy per second
```
The defaults for these parameters aim to achieve no greater than 525 w/s, however these params should be adjusted if
write speed increases.

###  Ubuntu 24.04

#### Docker

1. Install Docker: `sudo apt install docker`
2. Go to repository's folder: `cd $PATH_TO_REPO`
3. Build an image: `sudo docker build . -t python-tools`
4. Run desired application: `sudo docker run -it --init --network=host python-tools python -m src.octopus.applications.streaming_trade_scraper Coinbase`

#### Virtualenv

1. Install uv: `curl -LsSf https://astral.sh/uv/install.sh | sh`
2. Install python 3.13: `uv python install 3.13`
3. Create venv `uv venv .venv --python=python3.13`
4. Install packages with dev packages: `uv pip install -r pyproject.toml --all-extras`
5. Update project dependencies: `uv add <package_name>`
6. Init submodules: `git submodule init`
7. Download submodules: `git submodule update --recursive`
8. Run desired application: `python -m src.octopus.applications.streaming_trade_scraper Coinbase`

## Miscellaneous information

### Pre-commit hooks

Please run `pre-commit install` command from the root of the repository to set up the git hooks.

### Type and style checking

Type and style checking can be accomplished via running:
```shell
uv run ruff check --fix src test
```

Format code with:
```shell
uv run ruff format --diff src test
```

### Automatic tests

Run `python -m pytest` to launch a test suite.

PostgreSQL-related tests are skipped by default, Docker is required to run them locally: `sudo ENABLE_DB_TESTS=true python -m pytest`.

### Generating protobuf

#### Windows
1. Download [protobuf compiler](https://github.com/protocolbuffers/protobuf/releases).
2. Download [protobuf-mypy plugin](https://github.com/dropbox/mypy-protobuf/).
3. Use the following command to generate the protobufs:
```shell
<path_to_downloaded_compiler>\bin\protoc --plugin=protoc-gen-mypy=<path_to_downloaded_mypy_plugin>\python\protoc_gen_mypy.bat --proto_path=resources\market_data  --python_out=src\octopus --mypy_out=src\octopus market_data_feed.proto

mv src\octopus\market_data_feed_pb2.py src\octopus\feed_protocol_pb2.py
mv src\octopus\market_data_feed_pb2.pyi src\octopus\feed_protocol_pb2.pyi
```
#### Mac
1. brew install protobuf
2. pip3.8 install mypy-protobuf
3. Run from directory containing octopus and resources repos:
```shell
protoc --proto_path=resources/market_data --python_out=src/octopus --mypy_out=src/octopus market_data_feed.proto

mv src/octopus/market_data_feed_pb2.py src/octopus/feed_protocol_pb2.py
mv src/octopus/market_data_feed_pb2.pyi src/octopus/feed_protocol_pb2.pyi
```

#### Linux
1. sudo apt update
2. sudo apt install protobuf-compiler
3. Run from directory containing octopus and resources repos:
```shell
protoc --proto_path=resources/market_data --python_out=src/octopus --mypy_out=src/octopus market_data_feed.proto

mv src/octopus/market_data_feed_pb2.py src/octopus/feed_protocol_pb2.py
mv src/octopus/market_data_feed_pb2.pyi src/octopus/feed_protocol_pb2.pyi
```

## Troubleshooting

### Mac
1. In case of the error `eventfd/_eventfd.c:2:10: fatal error: 'sys/eventfd.h' file not found`
   try to install `eventfd==0.1` instead of the version pinned in requirements.txt

### Mac M1
1. In case of issues with installing `psycopg2` package use `psycopg2-binary==2.9.2` instead.


## Updating resources
To update files from resource repository:
1. Create new branch for resources repo. e.g. octopus-847-fix-rate-limit-errors-for-kucoin
2. Update files locally
3. Commit them into remote resources
4. Create merge request to master and got it approved
5. Merge the changes
6. Include `resource` directory (with the last commit hash inside) into the octopus commit

To make sure the last commit hash is in changes:
 ```
 cd resources
 git pull origin master
 ```
