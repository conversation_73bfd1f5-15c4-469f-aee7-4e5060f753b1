stages:
  - build
  - test
  - deploy:resources
  - deploy:staging
  - deploy:production-1
  - deploy:production-2
  - deploy-k8s:cdev1:feed-handlers-1
  - deploy-k8s:cdev1:feed-handlers-2
  - deploy-k8s:cp1:feed-handlers-1
  - deploy-k8s:cp1:feed-handlers-2
  - deploy-k8s:cp2:feed-handlers-1
  - deploy-k8s:cp2:feed-handlers-2
  - maintenance

variables:
  GIT_SUBMODULE_STRATEGY: recursive

build:
  image: docker:27.0.3
  stage: build
  services:
    - docker:27.0.3-dind
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
    - apk add git
    - git submodule foreach --recursive git clean -ffdx
  script:
    - docker pull $CI_REGISTRY_IMAGE:master || true
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG || true
    - docker build --cache-from $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG --cache-from $CI_REGISTRY_IMAGE:master -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
  tags:
    - linux
    - docker
    - coinmetrics-build-runner

test:
  image: docker:27.0.3
  stage: test
  services:
    - docker:27.0.3-dind
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA || true
    - docker run --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m ruff check --fix src test
    - docker run --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m ruff format --diff src test
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock -e ENABLE_DB_TESTS=true $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m pytest test
  tags:
    - linux
    - docker
    - coinmetrics-build-runner

ops-prometheus-production:
  stage: deploy:resources
  image: alpine:latest
  dependencies: []
  script:
  - apk add --no-cache curl
  - >-
    curl -X POST --fail
    -F "token=$DEPLOY_TOKEN"
    -F "ref=master"
    -F "variables[OPS_DEPLOY_PLAYBOOK]=prometheus_config"
    https://gitlab.com/api/v4/projects/$OPS_PROJECT_ID/trigger/pipeline
  environment:
    name: production
  tags:
  - linux
  - docker
  only:
  - master
  when: manual

dashboards-production:
  image: docker:20.10.3
  stage: deploy:resources
  services:
    - docker:20.10.3-dind
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA || true
    - docker run --env GRAFANA_API_KEY="$GRAFANA_API_KEY" --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m src.octopus.applications.dashboards_updater production
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - docker
    - coinmetrics-build-runner

dashboards-cp1:
  image: docker:20.10.3
  stage: deploy:resources
  services:
    - docker:20.10.3-dind
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA || true
    - docker run --env GRAFANA_API_KEY_K8S_CP1="$GRAFANA_API_KEY_K8S_CP1" --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m src.octopus.applications.dashboards_updater cp1
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - docker
    - coinmetrics-build-runner


dashboards-cdev1:
  image: docker:20.10.3
  stage: deploy:resources
  services:
    - docker:20.10.3-dind
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA || true
    - docker run --env GRAFANA_API_KEY_K8S_CDEV1="$GRAFANA_API_KEY_K8S_CDEV1" --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m src.octopus.applications.dashboards_updater cdev1
  environment:
    name: staging
  when: manual
  tags:
    - linux
    - docker
    - coinmetrics-build-runner

dashboards-mgmt1:
  image: docker:20.10.3
  stage: deploy:resources
  services:
    - docker:20.10.3-dind
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA || true
    - docker run --env GRAFANA_API_KEY_K8S_MGMT1="$GRAFANA_API_KEY_K8S_MGMT1" --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA python -m src.octopus.applications.dashboards_updater mgmt1
  environment:
    name: staging
  when: manual
  tags:
    - linux
    - docker
    - coinmetrics-build-runner


liqu-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-data-loaders-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-loader-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-loader-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "1"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-hist-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-hist-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-hist-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-opti-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-opti-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-opti-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-opti-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-data-loaders-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-loader-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-loader-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "1"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

tick-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG futu-tick-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG futu-tick-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_ticker_f_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

tick-real-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG opti-tick-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG opti-tick-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_ticker_o_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-real-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-opti-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-opti-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group1-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group1-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group1-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group2-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group2-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group2-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books2-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group2.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group3-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group3-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group3-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books3-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group3.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group4-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group4-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group4-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books4-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group4.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-opti-group1-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-opti-group1-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-opti-group1-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_option_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

metr-dail-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG metr-dail-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG metr-dail-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metrics_daily.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

liqu-hist-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-hist-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-hist-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

liqu-data-loaders-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-loader-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-loader-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "1"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-data-loaders-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-loader-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-loader-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "1"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

prox-chec-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG prox-chec-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG prox-chec-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA service=proxy-check" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_proxy_check.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

quot-real-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_quote_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

quot-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_quote_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-opti-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-opti-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-opti-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-opti-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-opti-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group1-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group1-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group1-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group2-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group2-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group2-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books2-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group2.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group3-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group3-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group3-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books3-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group3.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group4-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group4-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group4-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books4-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group4.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-hist-futu-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-hist-futu-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-hist-futu-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

kafka-eater-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG kafka-eater-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG kafka-eater-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-1 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/kafka_eater/applications/deploy/production_kafka_eater.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

liqu-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

liqu-data-loaders-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-loader-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-loader-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "2"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-hist-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-hist-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-hist-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-data-loaders-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-loader-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-loader-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "2"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-opti-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-opti-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-data-loaders-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-loader-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-loader-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "2"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-opti-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-opti-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

meta-real-spot-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-spot-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG meta-real-spot-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metadata_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-data-loaders-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-loader-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-loader-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_loader.ansible.yml
  variables:
    FEED_HANDLER_INSTANCE: "2"
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

tick-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG futu-tick-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG futu-tick-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_ticker_f_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

tick-real-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG opti-tick-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG opti-tick-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_ticker_o_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

open-inte-real-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-opti-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG open-inte-real-opti-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_open_interest_realtime_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group1-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group1-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group1-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group2-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group2-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group2-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books2-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group2.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group3-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group3-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group3-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books3-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group3.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-futu-group4-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group4-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-futu-group4-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books4-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_futures_group4.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-opti-group1-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-opti-group1-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-opti-group1-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_option_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

metr-dail-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG metr-dail-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG metr-dail-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_metrics_daily.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

liqu-hist-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-hist-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG liqu-hist-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_liquidation_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-spot-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-spot-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-spot-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

prox-chec-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG prox-chec-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG prox-chec-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA service=proxy-check" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_proxy_check.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

quot-real-spot-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-spot-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-spot-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_quote_realtime_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

quot-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG quot-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_quote_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-opti-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-opti-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-hour-spot-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-spot-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-hour-spot-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=feed-handlers-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_hourly_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-real-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-real-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_realtime_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-spot-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-spot-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-spot-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

trad-hist-opti-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-opti-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG trad-hist-opti-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_trade_history_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group1-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group1-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group1-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group1.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group2-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group2-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group2-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books2-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group2.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group3-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group3-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group3-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books3-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group3.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

book-real-spot-group4-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group4-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG book-real-spot-group4-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=fh-books4-prod-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_book_realtime_spot_group4.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

fund-rate-hist-futu-2-production:
  stage: deploy:production-2
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-hist-futu-2-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG fund-rate-hist-futu-2-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-2 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_funding_rate_history_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

datavis-backend-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG datavis-backend-prod" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG datavis-backend-prod" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=datavis-backend-prod-0 tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/site/applications/deploy/production_datavis_servant.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-trade-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-trade-spot tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_trade_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-trade-futures-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-futures-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-futures-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-trade-futures tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_trade_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-trade-option-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-option-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-trade-option-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-trade-option tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_trade_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-book-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-book-spot tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_book_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-book-futures-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-futures-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-futures-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-book-futures tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_book_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


daily-totals-book-option-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-option-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG daily-totals-book-option-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=daily-totals-book-option tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_daily_totals_book_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-book-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-book-spot tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_book_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-book-futures-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-futures-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-futures-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-book-futures tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_book_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-book-option-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-option-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-book-option-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-book-option tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_book_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-option-ticker-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-option-ticker-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-option-ticker-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-option-ticker tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_option_ticker.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-trade-spot-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-spot-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-spot-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-trade-spot tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_trade_spot.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-trade-futures-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-futures-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-futures-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-trade-futures tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_trade_futures.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


historical-patch-trade-option-1-production:
  stage: deploy:production-1
  before_script:
    - REQUEST_TYPE=start_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-option-1-production" deploy_request.sh
  after_script:
    - REQUEST_TYPE=finish_deploy REQUEST_MESSAGE="$CI_COMMIT_REF_SLUG historical-patch-trade-option-1-production" deploy_request.sh
  script:
    - echo $OPS_RUNNER_PASSWORD | sudo -Su deployer ansible-playbook --user deploy-scrapers --extra-vars="hosts=scrapers-production-1 service=historical-patch-trade-option tag=$CI_COMMIT_SHA" --inventory=inventory.ansible.yml src/octopus/applications/deploy/production_historical_patch_trade_option.ansible.yml
  environment:
    name: production
  when: manual
  only:
    - master
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner


delete-old-pipelines:
  stage: maintenance
  image: python:3
  rules:
    - if: $OLD_PIPELINE_WEEKS != null
      when: always
  script:
    - python3 -m pip install python-gitlab pyyaml
    - ./ci/delete-old-pipelines.py --token=$OLD_PIPELINE_TOKEN --weeks=$OLD_PIPELINE_WEEKS $CI_PROJECT_ID
  tags:
    - linux
    - coinmetrics-ops-scrapers-runner

# Separate deployment configurations for each K8S deployment
include:
  - local: '/src/octopus/applications/deploy_k8s/cdev1/.gitlab-ci.yml'
  - local: '/src/octopus/applications/deploy_k8s/cp1/.gitlab-ci.yml'
  - local: '/src/octopus/applications/deploy_k8s/cp2/.gitlab-ci.yml'
  - local: '/src/kafka_eater/applications/deploy_k8s/.gitlab-ci.yml'
