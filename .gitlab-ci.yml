stages:
  - build
  - deploy

build:
  stage: build
  image: python:3.7-slim
  script:
    - pip3 install jsonschema requests
    - python3 test.py
    - python3 ams_data_generator.py
  artifacts:
    paths:
      - ams-data
    expire_in: 1 day
  tags:
    - kube-cp1-small
    - linux

staging:
  stage: deploy
  image: alpine:latest
  script:
    - apk add --no-cache git
    - "git clone https://$AMS_DATA_USER:$<EMAIL>/coinmetrics/data-delivery/infrastructure-services/ams-data.git ams-data-old"
    - cd ams-data-old
    - git checkout staging
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "$AMS_DATA_USER"
    - cp -rf ../ams-data/* .
    - git add . --all
    - git commit -m "$CI_COMMIT_MESSAGE" || true
    - git push
  environment:
    name: staging
  tags:
    - kube-cp1-small
    - linux
  when: manual

production:
  stage: deploy
  image: alpine:latest
  script:
    - apk add --no-cache git
    - "git clone https://$AMS_DATA_USER:$<EMAIL>/coinmetrics/data-delivery/infrastructure-services/ams-data.git ams-data-old"
    - cd ams-data-old
    - git checkout master
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "$AMS_DATA_USER"
    - cp -rf ../ams-data/* .
    - git add . --all
    - git commit -m "$CI_COMMIT_MESSAGE" || true
    - git push
  environment:
    name: production
  tags:
    - kube-cp1-small
    - linux
  only:
    - master

market-factory/resources.stg-hetz:
  stage: deploy
  image: registry.gitlab.com/coinmetrics/libs/shared-files:latest
  tags:
    - linux
    - kube-cp1-small
  when: manual
  script:
    - shared-files publish
      --s3-endpoint=https://minio.cnmtrcs.io:9002
      --s3-region=eu-hel1-hetz
      --file-group-id=market-factory/stg-hetz/resources
      --metadata-key=git-hash
      --metadata-value=$CI_COMMIT_SHA
      currency.json exchange.json market_whitelist.json
  environment: market-factory-resources-stg-hetz
  resource_group: market-factory/resources.stg-hetz

market-factory/resources.stg-k8s:
  stage: deploy
  image: registry.gitlab.com/coinmetrics/libs/shared-files:latest
  tags:
    - kube-cp1-small
    - linux
  when: manual
  script:
    - shared-files publish
      --s3-endpoint=https://minio.cnmtrcs.io:9002
      --s3-region=eu-hel1-hetz
      --file-group-id=market-factory/stg-k8s/resources
      --metadata-key=git-hash
      --metadata-value=$CI_COMMIT_SHA
      currency.json exchange.json market_whitelist.json
  environment: market-factory-resources-stg-k8s
  resource_group: market-factory/resources.stg-k8s

market-factory/resources.prd-hetz:
  stage: deploy
  image: registry.gitlab.com/coinmetrics/libs/shared-files:latest
  tags:
    - kube-cp1-small
    - linux
  when: manual
  script:
    - shared-files publish
      --s3-endpoint=https://minio.cnmtrcs.io:9002
      --s3-region=eu-hel1-hetz
      --file-group-id=market-factory/prd-hetz/resources
      --metadata-key=git-hash
      --metadata-value=$CI_COMMIT_SHA
      currency.json exchange.json market_whitelist.json
  environment: market-factory-resources-prd-hetz
  resource_group: market-factory/resources.prd-hetz

market-factory/resources.prd-cp1:
  stage: deploy
  image: registry.gitlab.com/coinmetrics/libs/shared-files:latest
  tags:
    - kube-cp1-small
    - linux
  when: manual
  script:
    - shared-files publish
      --s3-endpoint=https://b4-jfk1.cnmtrcs.io
      --s3-region=us-east-1
      --file-group-id=resources-cp1/market-factory/resources
      --metadata-key=git-hash
      --metadata-value=$CI_COMMIT_SHA
      currency.json exchange.json market_whitelist.json
  environment: market-factory-resources-prd-cp1
  resource_group: market-factory/resources.prd-cp1

market-factory/resources.prd-cp2:
  stage: deploy
  image: registry.gitlab.com/coinmetrics/libs/shared-files:latest
  tags:
    - kube-any-small
    - linux
  when: manual
  script:
    - shared-files publish
      --s3-endpoint=https://b3-par1.cnmtrcs.io
      --s3-region=us-east-1
      --file-group-id=resources-cp2/market-factory/resources
      --metadata-key=git-hash
      --metadata-value=$CI_COMMIT_SHA
      currency.json exchange.json market_whitelist.json
  environment: market-factory-resources-prd-cp2
  resource_group: market-factory/resources.prd-cp2
