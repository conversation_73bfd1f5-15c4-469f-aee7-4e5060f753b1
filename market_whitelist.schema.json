{"type": "object", "properties": {"pair": {"type": "string", "pattern": "^[a-z0-9_.\\-]+$"}, "pair_start": {"type": "string", "pattern": "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}, "pair_end": {"type": "string", "pattern": "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}, "ranges": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "string", "pattern": "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}, "markets": {"type": "array", "items": {"type": "string"}}}, "required": ["start", "markets"]}}}, "required": ["pair", "pair_start", "ranges"]}