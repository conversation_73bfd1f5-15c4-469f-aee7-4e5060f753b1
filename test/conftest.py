from typing import Iterable

import pytest

from src.octopus.qa_utils import get_postgres_exch_storage, get_postgres_storage
from src.octopus.storage.postgres.book_old import (
    PostgresOldFuturesBookStorage,
    PostgresOldOptionBookStorage,
    PostgresOldSpotBookStorage,
)
from src.octopus.storage.postgres.funding_rate import PostgresFundingRateStorage
from src.octopus.storage.postgres.futures_ticker import PostgresFuturesTickerStorage
from src.octopus.storage.postgres.liquidations import PostgresLiquidationStorage
from src.octopus.storage.postgres.open_interest import PostgresFuturesOpenInterestStorage
from src.octopus.storage.postgres.option_metadata import PostgresOptionMetadataStorage
from src.octopus.storage.postgres.option_ticker import PostgresOptionTickerStorage
from src.octopus.storage.postgres.trade import PostgresFuturesTradeStorage, PostgresOptionTradeStorage, PostgresSpotTradeStorage
from src.resources.exchange import glib_exchange
from src.utils.postgres import postgres_connect


@pytest.fixture(scope="session")
def postgres_old_spot_book_storage_session() -> Iterable[PostgresOldSpotBookStorage]:
    with get_postgres_storage(PostgresOldSpotBookStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_old_futures_book_storage_session() -> Iterable[PostgresOldFuturesBookStorage]:
    with get_postgres_storage(PostgresOldFuturesBookStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_old_option_book_storage_session() -> Iterable[PostgresOldOptionBookStorage]:
    with get_postgres_storage(PostgresOldOptionBookStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_option_ticker_storage_session() -> Iterable[PostgresOptionTickerStorage]:
    with get_postgres_storage(PostgresOptionTickerStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_liquidation_storage_session() -> Iterable[PostgresLiquidationStorage]:
    with get_postgres_storage(PostgresLiquidationStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_funding_rate_storage_session() -> Iterable[PostgresFundingRateStorage]:
    with get_postgres_storage(PostgresFundingRateStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_futures_open_interest_storage_session() -> Iterable[PostgresFuturesOpenInterestStorage]:
    with get_postgres_storage(PostgresFuturesOpenInterestStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_option_metadata_storage_session() -> Iterable[PostgresOptionMetadataStorage]:
    with get_postgres_storage(PostgresOptionMetadataStorage) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_futures_ticker_storage_session() -> Iterable[PostgresFuturesTickerStorage]:
    with get_postgres_exch_storage(PostgresFuturesTickerStorage, glib_exchange().exchange_id_by_name("MEXC")) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_option_ticker_storage_session() -> Iterable[PostgresOptionTickerStorage]:
    with get_postgres_storage(PostgresOptionTickerStorage) as storage:
        yield storage


@pytest.fixture(scope="function")
def postgres_old_spot_book_storage(
    postgres_old_spot_book_storage_session: PostgresOldSpotBookStorage,
) -> Iterable[PostgresOldSpotBookStorage]:
    yield postgres_old_spot_book_storage_session
    # Cleanup
    with postgres_connect(postgres_old_spot_book_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_old_spot_book_storage_session._book_table))


@pytest.fixture(scope="function")
def postgres_old_option_book_storage(
    postgres_old_option_book_storage_session: PostgresOldOptionBookStorage,
) -> Iterable[PostgresOldOptionBookStorage]:
    yield postgres_old_option_book_storage_session
    # Cleanup
    with postgres_connect(postgres_old_option_book_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_old_option_book_storage_session._book_table))


@pytest.fixture(scope="function")
def postgres_old_futures_book_storage(
    postgres_old_futures_book_storage_session: PostgresOldFuturesBookStorage,
) -> Iterable[PostgresOldFuturesBookStorage]:
    yield postgres_old_futures_book_storage_session
    # Cleanup
    with postgres_connect(postgres_old_futures_book_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_old_futures_book_storage_session._book_table))


@pytest.fixture(scope="function")
def postgres_option_ticker_storage(
    postgres_option_ticker_storage_session: PostgresOptionTickerStorage,
) -> Iterable[PostgresOptionTickerStorage]:
    yield postgres_option_ticker_storage_session
    # Cleanup
    with postgres_connect(postgres_option_ticker_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_option_ticker_storage_session._full_table))


@pytest.fixture(scope="function")
def postgres_option_metadata_storage(
    postgres_option_metadata_storage_session: PostgresOptionMetadataStorage,
) -> Iterable[PostgresOldSpotBookStorage]:
    yield postgres_option_metadata_storage_session
    # Cleanup
    with postgres_connect(postgres_option_metadata_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_option_metadata_storage_session._full_table))


@pytest.fixture(scope="session")
def postgres_mexc_spot_trade_storage_session() -> Iterable[PostgresSpotTradeStorage]:
    with get_postgres_exch_storage(PostgresSpotTradeStorage, glib_exchange().exchange_id_by_name("MEXC")) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_option_trade_storage_session() -> Iterable[PostgresOptionTradeStorage]:
    with get_postgres_exch_storage(PostgresOptionTradeStorage, glib_exchange().exchange_id_by_name("MEXC")) as storage:
        yield storage


@pytest.fixture(scope="session")
def postgres_mexc_futures_trade_storage_session() -> Iterable[PostgresFuturesTradeStorage]:
    with get_postgres_exch_storage(PostgresFuturesTradeStorage, glib_exchange().exchange_id_by_name("MEXC")) as storage:
        yield storage


@pytest.fixture(scope="function")
def postgres_mexc_spot_trade_storage(
    postgres_mexc_spot_trade_storage_session: PostgresSpotTradeStorage,
) -> Iterable[PostgresSpotTradeStorage]:
    yield postgres_mexc_spot_trade_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_spot_trade_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_spot_trade_storage_session._table_name))


@pytest.fixture(scope="function")
def postgres_mexc_futures_trade_storage(
    postgres_mexc_futures_trade_storage_session: PostgresFuturesTradeStorage,
) -> Iterable[PostgresFuturesTradeStorage]:
    yield postgres_mexc_futures_trade_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_futures_trade_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_futures_trade_storage_session._table_name))


@pytest.fixture(scope="function")
def postgres_mexc_option_trade_storage(
    postgres_mexc_option_trade_storage_session: PostgresFuturesTradeStorage,
) -> Iterable[PostgresFuturesTradeStorage]:
    yield postgres_mexc_option_trade_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_option_trade_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_option_trade_storage_session._table_name))


@pytest.fixture(scope="function")
def postgres_mexc_liquidation_storage(
    postgres_mexc_liquidation_storage_session: PostgresLiquidationStorage,
) -> Iterable[PostgresLiquidationStorage]:
    yield postgres_mexc_liquidation_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_liquidation_storage_session._connection_params) as conn:
        with conn.cursor() as cursor:
            cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_liquidation_storage_session._table_name))
        conn.commit()


@pytest.fixture(scope="function")
def postgres_mexc_futures_open_interest_storage(
    postgres_mexc_futures_open_interest_storage_session: PostgresFuturesOpenInterestStorage,
) -> Iterable[PostgresFuturesOpenInterestStorage]:
    yield postgres_mexc_futures_open_interest_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_futures_open_interest_storage_session._connection_params) as conn:
        with conn.cursor() as cursor:
            cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_futures_open_interest_storage_session._table_name))
            conn.commit()


@pytest.fixture(scope="function")
def postgres_mexc_funding_rate_storage(
    postgres_mexc_funding_rate_storage_session: PostgresFundingRateStorage,
) -> Iterable[PostgresFundingRateStorage]:
    yield postgres_mexc_funding_rate_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_funding_rate_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_funding_rate_storage_session._table_name))
        conn.commit()


@pytest.fixture(scope="function")
def postgres_mexc_futures_ticker_storage(
    postgres_mexc_futures_ticker_storage_session: PostgresFuturesTickerStorage,
) -> Iterable[PostgresFuturesTickerStorage]:
    yield postgres_mexc_futures_ticker_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_futures_ticker_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_futures_ticker_storage_session._table_name))
        conn.commit()


@pytest.fixture(scope="function")
def postgres_mexc_option_ticker_storage(
    postgres_mexc_option_ticker_storage_session: PostgresOptionTickerStorage,
) -> Iterable[PostgresOptionTickerStorage]:
    yield postgres_mexc_option_ticker_storage_session
    # Cleanup
    with postgres_connect(postgres_mexc_option_ticker_storage_session._connection_params) as conn, conn.cursor() as cursor:
        cursor.execute("TRUNCATE TABLE {};".format(postgres_mexc_option_ticker_storage_session._table_name))
        conn.commit()
