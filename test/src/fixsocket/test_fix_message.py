from datetime import UTC, datetime
from decimal import Decimal

from src.fixsocket.data import FIX_SEPARATOR, FixMessage
from src.fixsocket.fix_message import Fix<PERSON>essageBuilder, FixMessageParser, _fix_checksum, fix_wire_decode, fix_wire_encode
from src.fixsocket.fix_translator import FixTranslator
from src.octopus.data import TradeData
from src.utils.timeutil import dt_to_us


def test_fmt_fix_msg() -> None:
    noisy_debug_flag = False

    def printable_to_fix_msg(printable: str) -> bytes:
        return bytes(printable.replace("|", FIX_SEPARATOR), "utf-8")

    def noisy_debug(debug_message: str) -> None:
        if noisy_debug_flag:
            print(debug_message)

    fix_msg1 = FixMessageBuilder._fmt_fix_msg(35, "A")
    expected = f"35=A{FIX_SEPARATOR}".encode()
    noisy_debug(f"fix_fmt_msg: {fix_msg1}")
    assert fix_msg1.encode() == expected

    fmb = FixMessageBuilder()
    fmb.add(8, "FIX.4.4")
    fmb.add(9, 101)
    fmb.add(35, "A")
    msg = fmb.get()
    noisy_debug(f"Build message {msg!r}")
    expected = printable_to_fix_msg("8=FIX.4.4|9=101|35=A|")
    assert msg == expected

    fmb2 = FixMessageBuilder()
    fmb2.add(98, 0)
    fmb2.add(108, 30)
    fmb2.add(141, "Y")
    fmb.add_msgs(fmb2.get_msgs())
    msg2 = fmb.get()
    noisy_debug(f"Add messages {msg2!r}")
    expected = printable_to_fix_msg("8=FIX.4.4|9=101|35=A|98=0|108=30|141=Y|")
    assert msg2 == expected

    preamble = FixMessageBuilder()
    preamble.add(49, "SenderCompID")
    preamble.add(56, "TargetCompID")
    preamble.add(34, 1)
    """ Can't do this because it's not testable:
    preamble.add(52, datetime.now(UTC).strftime('%Y%m%d-%H:%M:%S.%f')[:-3])
    """
    now = datetime(2021, 2, 3, 21, 8, 20, 493315, tzinfo=UTC)
    preamble.add(52, now.strftime("%Y%m%d-%H:%M:%S.%f")[:-3])
    expected = printable_to_fix_msg("49=SenderCompID|56=TargetCompID|34=1|52=20210203-21:08:20.493|")
    noisy_debug(f"preamble => [{preamble}]")
    assert preamble.get() == expected

    body = FixMessageBuilder()
    body.add(98, 0)
    body.add(108, 30)
    body.add(141, "Y")
    body.add(553, "Username")
    body.add(554, "Password")

    m1 = FixMessageBuilder()
    m1.add(35, "A")
    m1.add_msgs(preamble.get_msgs())
    m1.add_msgs(body.get_msgs())
    noisy_debug(f"m1 => [{m1}]")
    expected_str = (
        "35=A|49=SenderCompID|56=TargetCompID|34=1|52=20210203-21:08:20.493|98=0|108=30|141=Y|553=Username|554=Password|"
    )
    expected = printable_to_fix_msg(expected_str)
    fix_message_str = m1.get()
    assert fix_message_str == expected

    expected_str = "8=FIX.4.4|9=111|35=A|49=SenderCompID|56=TargetCompID|34=1|52=20210203-21:08:20.493|98=0|108=30|141=Y|553=Username|554=Password|10=151|"
    expected = printable_to_fix_msg(expected_str)
    fix_string = fix_wire_encode(fix_message_str)
    assert fix_string == expected


def test_msg_parser() -> None:
    trade_msg1 = bytes(
        "8=FIX.4.4|9=161|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4110|52=20210127-18:45:23.828|22=8|48=5004|262=CM-MD_All|268=1|269=2|270=29654.59|271=0.01|272=20210127|273=18:45:23.775|10=096|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    trade_msg2 = bytes(
        "8=FIX.4.4|9=153|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4251|52=20210127-19:39:34.192|22=8|48=6666|262=CM-MD_All|268=1|269=2|270=2.1|271=1|272=20210127|273=19:39:34.101|10=200|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    trade_msg3 = bytes(
        "8=FIX.4.4|9=152|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4252|52=20210127-19:39:34.192|22=8|48=6666|262=CM-MD_All|268=1|269=2|270=2|271=-1|272=20210127|273=19:39:34.122|10=153|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    expected_messages_raw = [
        "35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4110|52=20210127-18:45:23.828|22=8|48=5004|262=CM-MD_All|268=1|269=2|270=29654.59|271=0.01|272=20210127|273=18:45:23.775|",
        "35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4251|52=20210127-19:39:34.192|22=8|48=6666|262=CM-MD_All|268=1|269=2|270=2.1|271=1|272=20210127|273=19:39:34.101|",
        "35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4252|52=20210127-19:39:34.192|22=8|48=6666|262=CM-MD_All|268=1|269=2|270=2|271=-1|272=20210127|273=19:39:34.122|",
    ]
    trade_msgs = [trade_msg1, trade_msg2, trade_msg3]

    def parse_expected_message(message: str) -> FixMessage:
        # return [(int(key), value) for chunk in message.split('|') for key, value in chunk.split('=')]
        fix_msg = []
        for chunk in message.split("|"):
            if chunk == "":  # handle trailing '|'
                break
            key, value = chunk.split("=")
            fix_msg.append((int(key), value))
        return fix_msg

    for fix_wire_message, expected_message_str in zip(trade_msgs, expected_messages_raw, strict=False):
        fix_msg_bytes = fix_wire_decode(fix_wire_message)
        expected_msg_bytes = bytes(expected_message_str.replace("|", FIX_SEPARATOR), "utf-8")
        assert fix_msg_bytes == expected_msg_bytes

        fix_message = FixMessageParser.parse_fix_messages(fix_msg_bytes)
        expected_fix_msg = parse_expected_message(expected_message_str)
        assert fix_message == expected_fix_msg


def test_checksums() -> None:
    # 8=FIX.4.4|9=75|35=A|34=1092|49=TESTBUY1|52=20180920-18:24:59.643|56=TESTSELL1|98=0|108=60|10=178|
    t = "8=FIX.4.4|9=75|35=A|34=1092|49=TESTBUY1|52=20180920-18:24:59.643|56=TESTSELL1|98=0|108=60|"
    expected_sum = "178"
    _test_checksum(t, expected_sum)

    # Example from https://en.wikipedia.org/wiki/Financial_Information_eXchange#Trailer:_Checksum
    # 8=FIX.4.2|9=65|35=A|49=SERVER|56=CLIENT|34=177|52=20090107-18:15:16|98=0|108=30|10=062|
    t = "8=FIX.4.2|9=65|35=A|49=SERVER|56=CLIENT|34=177|52=20090107-18:15:16|98=0|108=30|"
    expected_sum = "062"
    _test_checksum(t, expected_sum)

    # Bad Checksum from LMAXMarketData
    # 8=FIX4.4|9=127|35=A|49=coinmetricsdigUAT1|56=LMXBLM|34=1|52=20210111-22:10:25|98=0|108=30|141=Y|553=coinmetricsdigUAT1|554=coinmetricsdigUAT1|10=161|
    t = "8=FIX4.4|9=127|35=A|49=coinmetricsdigUAT1|56=LMXBLM|34=1|52=20210111-22:10:25|98=0|108=30|141=Y|553=coinmetricsdigUAT1|554=coinmetricsdigUAT1|"
    expected_sum = "165"
    _test_checksum(t, expected_sum)

    # [8=FIX4.4|9=127|35=A|49=coinmetricsdigUAT1|56=LMXBLM|34=1|52=20210111-22:48:12|98=0|108=30|141=Y|553=coinmetricsdigUAT1|554=coinmetricsdigUAT1|10=172|
    t = "8=FIX4.4|9=127|35=A|49=coinmetricsdigUAT1|56=LMXBLM|34=1|52=20210111-22:48:12|98=0|108=30|141=Y|553=coinmetricsdigUAT1|554=coinmetricsdigUAT1|"
    expected_sum = "172"
    _test_checksum(t, expected_sum)


def _test_checksum(printable_wire_message: str, expected_sum: str) -> None:
    fix_wire_bytes = bytes(printable_wire_message.replace("|", "\x01"), "utf-8")
    checksum = _fix_checksum(fix_wire_bytes)
    bytes_expected_sum = bytes(f"10={expected_sum}{FIX_SEPARATOR}", "utf-8")
    assert checksum == bytes_expected_sum


def test_trade_parser() -> None:
    def parse_timestamp(timestamp: str) -> datetime:
        return datetime.strptime(timestamp, "%Y%m%d %H:%M:%S.%f")

    trade_msg1 = bytes(
        "8=FIX.4.4|9=163|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4110|52=20210127-18:45:23.828|22=8|48=5004|262=CM-MD_trade|268=1|269=2|270=29654.59|271=0.01|272=20210127|273=18:45:23.775|10=089|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    trade_msg2 = bytes(
        "8=FIX.4.4|9=155|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4251|52=20210127-19:39:34.192|22=8|48=5004|262=CM-MD_trade|268=1|269=2|270=2.1|271=1|272=20210127|273=19:39:34.101|10=178|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    trade_msg3 = bytes(
        "8=FIX.4.4|9=154|35=W|49=LMXBLM|56=coinmetricsdigUAT1|34=4252|52=20210127-19:39:34.192|22=8|48=5004|262=CM-MD_trade|268=1|269=2|270=2|271=-1|272=20210127|273=19:39:34.122|10=131|".replace(
            "|", FIX_SEPARATOR
        ),
        "utf-8",
    )
    trade_msgs = [trade_msg1, trade_msg2, trade_msg3]

    dt1 = parse_timestamp("20210127 18:45:23.775000")
    dt2 = parse_timestamp("20210127 19:39:34.101000")
    dt3 = parse_timestamp("20210127 19:39:34.122000")
    expected_trades = [
        TradeData(dt_to_us(dt1), Decimal("0.01"), Decimal("29654.59"), True, dt1),
        TradeData(dt_to_us(dt2), Decimal("1"), Decimal("2.1"), True, dt2),
        TradeData(dt_to_us(dt3), Decimal("1"), Decimal("2"), False, dt3),
    ]

    fmt = FixTranslator("coinmetricsdigUAT1", "LMXBLM")
    for fix_wire_message, expected_trade in zip(trade_msgs, expected_trades, strict=False):
        fix_string_message = fix_wire_decode(fix_wire_message)
        fix_message = fmt.decode(fix_string_message)
        assert fix_message["symbol"] == "5004"

        trade = fix_message["trades"][0]
        # logic copied from lmax.py _extract_trades()
        size = Decimal(trade["size"])
        amount = abs(size)
        is_buy = size > 0
        price = Decimal(trade["price"])
        timestamp = datetime.strptime(f"{trade['date']} {trade['time']}", "%Y%m%d %H:%M:%S.%f")
        assert expected_trade.amount == amount
        assert expected_trade.price == price
        assert expected_trade.is_buy == is_buy
        assert expected_trade.time == timestamp
