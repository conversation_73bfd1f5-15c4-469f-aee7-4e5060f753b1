# # -*- coding: utf-8 -*-
#
# """
# Code is based on:
#
# https://github.com/websocket-client/websocket-client/releases/tag/v0.57.0
#
# websocket - WebSocket client library for Python
#
# Copyright (C) 2010 <PERSON><PERSON><PERSON>(liris)
#
#     This library is free software; you can redistribute it and/or
#     modify it under the terms of the GNU Lesser General Public
#     License as published by the Free Software Foundation; either
#     version 2.1 of the License, or (at your option) any later version.
#
#     This library is distributed in the hope that it will be useful,
#     but WITHOUT ANY WARRANTY; without even the implied warranty of
#     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
#     Lesser General Public License for more details.
#
#     You should have received a copy of the GNU Lesser General Public
#     License along with this library; if not, write to the Free Software
#     Foundation, Inc., 51 Franklin Street, Fifth Floor,
#     Boston, MA  02110-1335  USA
#
# """
#
# import os
# import os.path
# import socket
# from base64 import decodebytes as base64decode
# from typing import List, Optional, Union
#
# import pytest
#
# from src.utils.diagnostics import Diagnostics
# from src.utils.socket import PollSockets
# from src.utils.stream import ClosedByPeer, ConnectionEvent, MessageReceived
# from src.websocket.connection import WebSocketConnection
# from src.websocket.data import WebSocketMessage
# from src.websocket.exceptions import WebSocketConnectionClosed, WebSocketInvalidHttpHeader, WebSocketProtocolViolation
# from src.websocket.utils import _create_sec_websocket_key, _read_headers, _validate_headers
#
# _DIAGNOSTICS = Diagnostics()
#
#
# def create_mask_key(_: int) -> bytes:
#     return b"abcd"
#
#
# def select_mock(read: bool = False, write: bool = False) -> PollSockets:
#     return lambda r, w, e, t: (r if read else [], w if write else [], [])  # type: ignore
#
#
# def communicate_and_return_last_event(connection: WebSocketConnection) -> Optional[ConnectionEvent[WebSocketMessage]]:
#     event: Optional[ConnectionEvent[WebSocketMessage]] = None
#     for event in connection.communicate(1.0):
#         pass
#     return event
#
#
# class SockMock(socket.socket):
#     def __init__(self) -> None:
#         self.data: List[Union[bytes, Exception]] = []
#         self.sent: List[bytes] = []
#
#     def add_packet(self, data: Union[bytes, Exception]) -> None:
#         self.data.append(data)
#
#     def gettimeout(self) -> Optional[float]:
#         return None
#
#     def settimeout(self, timeout: Optional[float] = None) -> None:
#         pass
#
#     def shutdown(self, shutdown_type: int) -> None:
#         pass
#
#     def recv(self, bufsize: int, flags: int = 0) -> bytes:
#         if self.data:
#             e = self.data.pop(0)
#             if isinstance(e, Exception):
#                 raise e
#             if len(e) > bufsize:
#                 self.data.insert(0, e[bufsize:])
#             return e[:bufsize]
#         else:
#             return bytes()
#
#     def send(self, data: bytes, flags: int = 0) -> int:  # type: ignore
#         self.sent.append(data)
#         return len(data)
#
#     def close(self) -> None:
#         pass
#
#
# class HeaderSockMock(SockMock):
#     def __init__(self, fname: str):
#         SockMock.__init__(self)
#         path = os.path.join(os.path.dirname(__file__), fname)
#         with open(path, "rb") as f:
#             self.add_packet(f.read())
#
#
# def test_ws_key() -> None:
#     key = _create_sec_websocket_key()
#     assert key != "24"
#     assert "¥n" not in key
#
#
# def test_ws_utils() -> None:
#     key = "c6b8hTg4EeGb2gQMztV1/g=="
#     required_header = {
#         "upgrade": "websocket",
#         "connection": "upgrade",
#         "sec-websocket-accept": "Kxep+hNu9n51529fGidYu7a3wO0=",
#     }
#     assert _validate_headers(required_header, key, []) == (True, None)
#
#     header = required_header.copy()
#     header["upgrade"] = "http"
#     assert _validate_headers(header, key, []) == (False, None)
#     del header["upgrade"]
#     assert _validate_headers(header, key, []) == (False, None)
#
#     header = required_header.copy()
#     header["connection"] = "something"
#     assert _validate_headers(header, key, []) == (False, None)
#     del header["connection"]
#     assert _validate_headers(header, key, []) == (False, None)
#
#     header = required_header.copy()
#     header["sec-websocket-accept"] = "something"
#     assert _validate_headers(header, key, []) == (False, None)
#     del header["sec-websocket-accept"]
#     assert _validate_headers(header, key, []) == (False, None)
#
#     header = required_header.copy()
#     header["sec-websocket-protocol"] = "sub1"
#     assert _validate_headers(header, key, ["sub1", "sub2"]) == (True, "sub1")
#     assert _validate_headers(header, key, ["sub2", "sub3"]) == (False, None)
#
#     header = required_header.copy()
#     header["sec-websocket-protocol"] = "sUb1"
#     assert _validate_headers(header, key, ["Sub1", "suB2"]) == (True, "sub1")
#
#
# def test_read_headers() -> None:
#     status, header, _ = _read_headers(HeaderSockMock("data/header01.txt"))
#     assert status == 101
#     assert header["connection"] == "Upgrade"
#
#     with pytest.raises(WebSocketInvalidHttpHeader):
#         _read_headers(HeaderSockMock("data/header02.txt"))
#
#
# def test_send() -> None:
#     # TODO: add longer frame data
#     sock = SockMock()
#     sock.add_packet(BlockingIOError())
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, get_mask_key=create_mask_key, poll_sockets=select_mock(write=True))
#
#     connection.send("Hello")
#     communicate_and_return_last_event(connection)
#     assert sock.sent[0] == b"\x81\x85abcd)\x07\x0f\x08\x0e"
#
#     connection.send("こんにちは")
#     communicate_and_return_last_event(connection)
#     assert sock.sent[1] == b"\x81\x8fabcd\x82\xe3\xf0\x87\xe3\xf1\x80\xe5\xca\x81\xe2\xc5\x82\xe3\xcc"
#
#
# def test_recv() -> None:
#     sock = SockMock()
#     sock.add_packet(b"\x81\x8fabcd\x82\xe3\xf0\x87\xe3\xf1\x80\xe5\xca\x81\xe2\xc5\x82\xe3\xcc")
#     sock.add_packet(BlockingIOError())
#     sock.add_packet(b"\x81\x85abcd)\x07\x0f\x08\x0e")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "こんにちは"
#
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "Hello"
#
#
# def test_recv_with_simple_fragmentation() -> None:
#     sock = SockMock()
#     # OPCODE=TEXT, FIN=0, MSG='Brevity is '
#     sock.add_packet(b"\x01\x8babcd#\x10\x06\x12\x08\x16\x1aD\x08\x11C")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=1, MSG='the soul of wit'
#     sock.add_packet(b"\x80\x8fabcd\x15\n\x06D\x12\r\x16\x08A\r\x05D\x16\x0b\x17")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#
#     event = communicate_and_return_last_event(connection)
#     assert event is None
#
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "Brevity is the soul of wit"
#
#     with pytest.raises(WebSocketConnectionClosed):
#         communicate_and_return_last_event(connection)
#
#
# def test_recv_with_simple_fragmentation_2() -> None:
#     sock = SockMock()
#     # OPCODE=TEXT, FIN=0, MSG='Brevity is '
#     sock.add_packet(b"\x01\x8babcd#\x10\x06\x12\x08\x16\x1aD\x08\x11C")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=0, MSG=' '
#     sock.add_packet(b"\x00\x8babcd#\x10\x06\x12\x08\x16\x1aD\x08\x11C")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=1, MSG='the soul of wit'
#     sock.add_packet(b"\x80\x8fabcd\x15\n\x06D\x12\r\x16\x08A\r\x05D\x16\x0b\x17")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#
#     assert communicate_and_return_last_event(connection) is None
#     assert communicate_and_return_last_event(connection) is None
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "Brevity is Brevity is the soul of wit"
#
#     # OPCODE=CONT, FIN=0, MSG='Brevity is '
#     sock.add_packet(b"\x80\x8babcd#\x10\x06\x12\x08\x16\x1aD\x08\x11C")
#     sock.add_packet(BlockingIOError())
#
#     with pytest.raises(WebSocketProtocolViolation):
#         communicate_and_return_last_event(connection)
#
#     with pytest.raises(WebSocketConnectionClosed):
#         communicate_and_return_last_event(connection)
#
#
# def test_close() -> None:
#     sock = SockMock()
#     connection = WebSocketConnection(sock)
#     connection.close(gracefully=False)
#     assert not connection._connected
#
#     sock = SockMock()
#     sock.add_packet(b"\x88\x80\x17\x98p\x84")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, ClosedByPeer)
#     connection.close(gracefully=False)
#     assert not connection._connected
#
#
# def test_recv_cont_fragmentation() -> None:
#     sock = SockMock()
#     # OPCODE=CONT, FIN=1, MSG='the soul of wit'
#     sock.add_packet(b"\x80\x8fabcd\x15\n\x06D\x12\r\x16\x08A\r\x05D\x16\x0b\x17")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#
#     with pytest.raises(WebSocketProtocolViolation):
#         communicate_and_return_last_event(connection)
#
#
# def test_recv_with_prolonged_fragmentation() -> None:
#     sock = SockMock()
#     # OPCODE=TEXT, FIN=0, MSG='Once more unto the breach, '
#     sock.add_packet(b"\x01\x9babcd.\x0c\x00\x01A\x0f\x0c\x16\x04B\x16\n\x15\rC\x10\t\x07C\x06\x13\x07\x02\x07\tNC")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=0, MSG='dear friends, '
#     sock.add_packet(b"\x00\x8eabcd\x05\x07\x02\x16A\x04\x11\r\x04\x0c\x07\x17MB")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=1, MSG='once more'
#     sock.add_packet(b"\x80\x89abcd\x0e\x0c\x00\x01A\x0f\x0c\x16\x04")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, poll_sockets=select_mock(read=True))
#
#     assert communicate_and_return_last_event(connection) is None
#     assert communicate_and_return_last_event(connection) is None
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "Once more unto the breach, dear friends, once more"
#
#     with pytest.raises(WebSocketConnectionClosed):
#         communicate_and_return_last_event(connection)
#
#
# def test_recv_with_fragmentation_and_control_frame() -> None:
#     sock = SockMock()
#     # OPCODE=TEXT, FIN=0, MSG='Too much '
#     sock.add_packet(b"\x01\x89abcd5\r\x0cD\x0c\x17\x00\x0cA")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=PING, FIN=1, MSG='Please PONG this'
#     sock.add_packet(b"\x89\x90abcd1\x0e\x06\x05\x12\x07C4.,$D\x15\n\n\x17")
#     sock.add_packet(BlockingIOError())
#     # OPCODE=CONT, FIN=1, MSG='of a good thing'
#     sock.add_packet(b"\x80\x8fabcd\x0e\x04C\x05A\x05\x0c\x0b\x05B\x17\x0c\x08\x0c\x04")
#     sock.add_packet(BlockingIOError())
#     connection = WebSocketConnection(sock, get_mask_key=create_mask_key, poll_sockets=select_mock(read=True, write=True))
#
#     assert communicate_and_return_last_event(connection) is None
#     assert communicate_and_return_last_event(connection) is None
#     event = communicate_and_return_last_event(connection)
#     assert isinstance(event, MessageReceived) and event.data == "Too much of a good thing"
#
#     with pytest.raises(WebSocketConnectionClosed):
#         communicate_and_return_last_event(connection)
#
#     assert sock.sent[0] == b"\x8a\x90abcd1\x0e\x06\x05\x12\x07C4.,$D\x15\n\n\x17"
#
#
# def test_nonce() -> None:
#     """WebSocket key should be a random 16-byte nonce."""
#     key = _create_sec_websocket_key()
#     nonce = base64decode(key.encode("utf-8"))
#     assert 16 == len(nonce)
