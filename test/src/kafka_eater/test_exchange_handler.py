from typing import Optional
from unittest.mock import Mock

import pytest

from src.kafka_eater.exchange_handler import KAFKA_1, KAFKA_2, <PERSON><PERSON><PERSON><PERSON>
from src.kafka_eater.kafka_eater_types import KE_V_T, CleanedData
from src.kafka_eater.message_processor import MessageProcessor

RAW_KEY_VALUE_1 = {1: {"some_payload": 1}, 2: {"some_payload": 2}, 3: {"some_payload": 3}}


RAW_KEY_VALUE_2 = {
    1: {"some_payload": 1},
    2: {"some_payload": 2},
    3: {"some_payload": 3},
    5: {"some_payload": 5},
    6: {"some_payload": 6},
}

RAW_KEY_VALUE_3 = {
    2: {"some_payload": 2},
    4: {"some_payload": 4},
}

RAW_KEY_VALUE_4 = {
    4: {"some_payload": 4},
    6: {"some_payload": 6},
    7: {"some_payload": 7},
    9: {"some_payload": 9},
}

RAW_KEY_VALUE_5 = {
    9: {"some_payload": 9},
    10: {"some_payload": 10},
    13: {"some_payload": 13},
}

RAW_KEY_VALUE_6 = {
    8: {"some_payload": 8},
    9: {"some_payload": 9},
    11: {"some_payload": 11},
}

RAW_KEY_VALUE_7 = {
    10: {"some_payload": 10},
    11: {"some_payload": 11},
    13: {"some_payload": 13},
}


@pytest.mark.parametrize(
    "data, previous_common_max_key, expected_common_max_key, expected_processed_data",
    [
        (
            {"BTCUSDT": {KAFKA_1: CleanedData(data=RAW_KEY_VALUE_1, min=1, max=3)}},
            None,
            3,
            {"BTCUSDT": [value for value in RAW_KEY_VALUE_1.values()]},
        ),
        (
            {"BTCUSDT": {KAFKA_2: CleanedData(data=RAW_KEY_VALUE_1, min=1, max=3)}},
            None,
            3,
            {"BTCUSDT": [value for value in RAW_KEY_VALUE_1.values()]},
        ),
        (
            {"BTCUSDT": {KAFKA_1: CleanedData(data=RAW_KEY_VALUE_1, min=1, max=3)}},
            1,
            3,
            {"BTCUSDT": [value for value in RAW_KEY_VALUE_1.values()][1:]},
        ),
        (
            {"BTCUSDT": {KAFKA_2: CleanedData(data=RAW_KEY_VALUE_1, min=1, max=3)}},
            2,
            3,
            {"BTCUSDT": [value for value in RAW_KEY_VALUE_1.values()][2:]},
        ),
    ],
)
def test_exchange_handler_one_of_intervals_is_empty(
    data: KE_V_T[Optional[int]],
    previous_common_max_key: Optional[int],
    expected_common_max_key: Optional[int],
    expected_processed_data: KE_V_T[Optional[int]],
) -> None:
    diagnostics = Mock()
    symbol = "BTCUSDT"
    message_processor = MessageProcessor()
    exchange_handler = ExchangeHandler(message_processor.exchange_id_key_translator_dict[11], "HitBTC", diagnostics)
    exchange_handler.common_max_key_by_instrument[symbol] = previous_common_max_key
    result = exchange_handler.process(data)
    assert exchange_handler.common_max_key_by_instrument[symbol] == expected_common_max_key
    assert result == expected_processed_data


@pytest.mark.parametrize(
    "data, previous_common_max_key, expected_common_max_key, expected_left_data, expected_processed_data",
    [
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_3, min=2, max=4),
                }
            },
            None,
            4,
            {KAFKA_1: {5: {"some_payload": 5}, 6: {"some_payload": 6}}},
            {"BTCUSDT": [{"some_payload": 2}, {"some_payload": 3}, {"some_payload": 4}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_4, min=4, max=9),
                }
            },
            None,
            6,
            {KAFKA_2: {7: {"some_payload": 7}, 9: {"some_payload": 9}}},
            {"BTCUSDT": [{"some_payload": 4}, {"some_payload": 5}, {"some_payload": 6}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_4, min=4, max=9),
                }
            },
            2,
            6,
            {KAFKA_2: {7: {"some_payload": 7}, 9: {"some_payload": 9}}},
            {"BTCUSDT": [{"some_payload": 3}, {"some_payload": 4}, {"some_payload": 5}, {"some_payload": 6}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_3, min=2, max=4),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_4, min=4, max=9),
                }
            },
            None,
            4,
            {KAFKA_2: {6: {"some_payload": 6}, 7: {"some_payload": 7}, 9: {"some_payload": 9}}},
            {"BTCUSDT": [{"some_payload": 4}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_3, min=2, max=4),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_4, min=4, max=9),
                }
            },
            1,
            4,
            {KAFKA_2: {6: {"some_payload": 6}, 7: {"some_payload": 7}, 9: {"some_payload": 9}}},
            {"BTCUSDT": [{"some_payload": 2}, {"some_payload": 4}]},
        ),
    ],
)
def test_exchange_handler_intersecting_intervals(
    data: KE_V_T[Optional[int]],
    previous_common_max_key: Optional[int],
    expected_common_max_key: Optional[int],
    expected_left_data: KE_V_T[Optional[int]],
    expected_processed_data: KE_V_T[Optional[int]],
) -> None:
    diagnostics = Mock()
    symbol = "BTCUSDT"
    message_processor = MessageProcessor()
    exchange_handler = ExchangeHandler(message_processor.exchange_id_key_translator_dict[11], "HitBTC", diagnostics)
    exchange_handler.common_max_key_by_instrument[symbol] = previous_common_max_key
    result = exchange_handler.process(data)
    assert exchange_handler.common_max_key_by_instrument[symbol] == expected_common_max_key
    assert exchange_handler.left_data[symbol] == expected_left_data
    assert result == expected_processed_data


@pytest.mark.parametrize(
    "data, previous_common_max_key, expected_common_max_key, expected_left_data, expected_processed_data",
    (
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_5, min=9, max=13),
                }
            },
            None,
            13,
            None,
            {"BTCUSDT": [{"some_payload": 9}, {"some_payload": 10}, {"some_payload": 13}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_5, min=9, max=13),
                }
            },
            9,
            13,
            None,
            {"BTCUSDT": [{"some_payload": 10}, {"some_payload": 13}]},
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_5, min=9, max=13),
                }
            },
            3,
            13,
            None,
            {
                "BTCUSDT": [
                    {"some_payload": 5},
                    {"some_payload": 6},
                    {"some_payload": 9},
                    {"some_payload": 10},
                    {"some_payload": 13},
                ]
            },
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_5, min=9, max=13),
                }
            },
            1,
            6,
            None,
            {
                "BTCUSDT": [
                    {"some_payload": 2},
                    {"some_payload": 3},
                    {"some_payload": 5},
                    {"some_payload": 6},
                ]
            },
        ),
        (
            {
                "BTCUSDT": {
                    KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
                    KAFKA_2: CleanedData(data=RAW_KEY_VALUE_5, min=9, max=13),
                }
            },
            -1,
            6,
            None,
            {
                "BTCUSDT": [
                    {"some_payload": 1},
                    {"some_payload": 2},
                    {"some_payload": 3},
                    {"some_payload": 5},
                    {"some_payload": 6},
                ]
            },
        ),
    ),
)
def test_exchange_handler_not_intersecting_intervals(
    data: KE_V_T[Optional[int]],
    previous_common_max_key: Optional[int],
    expected_common_max_key: Optional[int],
    expected_left_data: KE_V_T[Optional[int]],
    expected_processed_data: KE_V_T[Optional[int]],
) -> None:
    diagnostics = Mock()
    symbol = "BTCUSDT"
    message_processor = MessageProcessor()
    exchange_handler = ExchangeHandler(message_processor.exchange_id_key_translator_dict[11], "HitBTC", diagnostics)
    exchange_handler.common_max_key_by_instrument[symbol] = previous_common_max_key
    result = exchange_handler.process(data)
    assert exchange_handler.common_max_key_by_instrument[symbol] == expected_common_max_key
    if expected_left_data:
        assert exchange_handler.left_data[symbol] == expected_left_data
    else:
        assert exchange_handler.left_data == {}
    assert result == expected_processed_data


def test_exchange_handler_data_saturation() -> None:
    diagnostics = Mock()
    symbol = "BTCUSDT"
    message_processor = MessageProcessor()
    exchange_handler = ExchangeHandler(message_processor.exchange_id_key_translator_dict[11], "HitBTC", diagnostics)
    exchange_handler.process({
        "BTCUSDT": {
            KAFKA_1: CleanedData(data=RAW_KEY_VALUE_2, min=1, max=6),
            KAFKA_2: CleanedData(data=RAW_KEY_VALUE_4, min=4, max=9),
        }
    })
    result = exchange_handler.process({
        "BTCUSDT": {
            KAFKA_1: CleanedData(data=RAW_KEY_VALUE_6, min=7, max=11),
            KAFKA_2: CleanedData(data=RAW_KEY_VALUE_7, min=10, max=13),
        }
    })
    assert exchange_handler.common_max_key_by_instrument[symbol] == 11
    assert exchange_handler.left_data[symbol] == {KAFKA_2: {13: {"some_payload": 13}}}
    assert result == {
        "BTCUSDT": [
            {"some_payload": 7},
            {"some_payload": 8},
            {"some_payload": 9},
            {"some_payload": 10},
            {"some_payload": 11},
        ]
    }
