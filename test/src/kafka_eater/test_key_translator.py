from pytest import raises

from src.kafka_eater.key_translator import PrimaryKeyTranslatorExchangeSequenceIdMixin, PrimaryKeyTranslatorWithIntPrimary<PERSON><PERSON>


def test_primary_key_translator_exchange_sequence_id_mixin_get_key() -> None:
    key_translator = PrimaryKeyTranslatorExchangeSequenceIdMixin()
    payload1 = {"id": 2, "exchange_sequence_id": 234, "some_other_key": "abracadabra"}
    assert key_translator.get_key(payload1) == 234

    payload2 = {"id": 2, "some_other_key": "abracadabra"}
    with raises(KeyError):
        key_translator.get_key(payload2)


def test_primary_key_translator_with_int_primary_key_cmp() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.cmp(1, 3) == -1
    assert key_translator.cmp(22, 22) == 0
    assert key_translator.cmp(6, 2) == 1
    with raises(TypeError):
        key_translator.cmp("6", 2)  # type: ignore


def test_primary_key_translator_with_int_primary_key_get_min_key() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.get_min_key([22, 4, 56]) == 4


def test_primary_key_translator_with_int_primary_key_get_max_key() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.get_max_key([22, 4, 56, 12]) == 56


def test_primary_key_translator_with_int_primary_key_sort_keys_asc() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.sort_keys_asc([22, 4, 56, 12]) == [4, 12, 22, 56]


def test_primary_key_translator_with_int_primary_key_get_keys_bigger_than_threshold() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.get_keys_bigger_than_threshold([3, 522, 6, 28], 28) == [522]
    assert key_translator.get_keys_bigger_than_threshold([3, 522, 6, 28], 28, equal=True) == [522, 28]


def test_primary_key_translator_with_int_primary_key_get_keys_less_than_threshold() -> None:
    key_translator = PrimaryKeyTranslatorWithIntPrimaryKey()
    assert key_translator.get_keys_less_than_threshold([3, 522, 6, 28], 28) == [3, 6]
    assert key_translator.get_keys_less_than_threshold([3, 522, 6, 28], 28, equal=True) == [3, 6, 28]
