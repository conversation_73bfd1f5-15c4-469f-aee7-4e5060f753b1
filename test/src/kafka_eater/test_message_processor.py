import json
from unittest.mock import MagicMock

from confluent_kafka import TopicPartition

from src.kafka_eater.kafka_eater_types import CleanedData
from src.kafka_eater.message_processor import MessageProcessor

quote_1 = {
    "exchange_id": 10,
    "symbol": "btcusdt",
    "base_id": 0,
    "quote_id": 100,
    "market_type": "spot",
    "exchange_time": 1646227719444000,
    "exchange_sequence_id": 149842556330,
    "feed_handler_sequence_id": 1527,
    "bid_amount": "0.0002",
    "bid_price": "43710.53",
    "ask_amount": "0.04759",
    "ask_price": "43710.55",
}

quote_2 = {
    "exchange_id": 10,
    "symbol": "btcusdt",
    "base_id": 0,
    "quote_id": 100,
    "market_type": "spot",
    "exchange_time": 1646227719444001,
    "exchange_sequence_id": 149842556331,
    "feed_handler_sequence_id": 1528,
    "bid_amount": "0.0003",
    "bid_price": "43710.6",
    "ask_amount": "0.04759",
    "ask_price": "43710.55",
}

quote_3 = {
    "exchange_id": 10,
    "symbol": "btcusdt",
    "base_id": 0,
    "quote_id": 100,
    "market_type": "spot",
    "exchange_time": 1646227719444002,
    "exchange_sequence_id": 149842556332,
    "feed_handler_sequence_id": 1529,
    "bid_amount": "0.0003",
    "bid_price": "43710.6",
    "ask_amount": "0.047",
    "ask_price": "43710.6",
}

quote_4 = {
    "exchange_id": 10,
    "symbol": "ethusdt",
    "base_id": 6,
    "quote_id": 100,
    "market_type": "spot",
    "exchange_time": 1646227719444005,
    "exchange_sequence_id": 123,
    "feed_handler_sequence_id": 3,
    "bid_amount": "0.0001",
    "bid_price": "43710.6",
    "ask_amount": "0.047",
    "ask_price": "43710.6",
}

quote_5 = {
    "exchange_id": 10,
    "symbol": "ethusdt",
    "base_id": 6,
    "quote_id": 100,
    "market_type": "spot",
    "exchange_time": 1646227719444034,
    "exchange_sequence_id": 125,
    "feed_handler_sequence_id": 4,
    "bid_amount": "0.0002",
    "bid_price": "43710.6",
    "ask_amount": "0.047",
    "ask_price": "43710.6",
}


def create_mock_message(value: dict) -> MagicMock:
    message = MagicMock()
    message.value.return_value = json.dumps(value).encode("utf8")
    return message


def test_base_check() -> None:
    message_processor = MessageProcessor()
    data = {
        TopicPartition(topic="quotes_10", partition=0): [
            create_mock_message(quote_1),
            create_mock_message(quote_2),
            create_mock_message(quote_3),
            create_mock_message(quote_4),
            create_mock_message(quote_5),
        ],
    }
    processed_data = message_processor.process(data, [10], 1)
    expected_result = {
        10: {
            "btcusdt": {
                "kafka_1": CleanedData(
                    data={
                        quote_1["exchange_sequence_id"]: quote_1,
                        quote_2["exchange_sequence_id"]: quote_2,
                        quote_3["exchange_sequence_id"]: quote_3,
                    },
                    min=quote_1["exchange_sequence_id"],
                    max=quote_3["exchange_sequence_id"],
                )
            },
            "ethusdt": {
                "kafka_1": CleanedData(
                    data={
                        quote_4["exchange_sequence_id"]: quote_4,
                        quote_5["exchange_sequence_id"]: quote_5,
                    },
                    min=quote_4["exchange_sequence_id"],
                    max=quote_5["exchange_sequence_id"],
                )
            },
        }
    }
    assert processed_data == expected_result
