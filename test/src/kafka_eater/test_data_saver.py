from unittest.mock import Mo<PERSON>

import pytest

from src.kafka_eater.data_saver import DataSaver
from src.utils.diagnostics import Diagnostics


@pytest.mark.parametrize("symbol,expected_result", [("BTCUSD", "BTCUSD"), ("BTC/USD", "BTCUSD")])
def test_sanitize_symbol(symbol: str, expected_result: str) -> None:
    data_saver = DataSaver("any_path", <PERSON><PERSON>(Diagnostics))
    assert expected_result == data_saver._sanitize_symbol(symbol)
