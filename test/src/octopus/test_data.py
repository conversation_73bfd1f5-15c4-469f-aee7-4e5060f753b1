from datetime import UTC, datetime
from decimal import Decimal

import pytest

from src.octopus.data import BookData, BookType, CollectionStat, Instrument, Market, MarketType, PriceLevel, TradeData


def test_order_book_data_unknown_counts_preserving_fields() -> None:
    bids = [PriceLevel(price=Decimal("{}.0".format(price)), amount=Decimal("0.1"), count=1) for price in range(10, 16)]
    asks = [PriceLevel(price=Decimal("{}.0".format(price)), amount=Decimal("0.1"), count=1) for price in range(17, 25)]
    order_book = BookData(BookType.FULL, None, None, bids, asks)
    unknown_counts = order_book.unknown_counts()
    assert order_book.book_type == unknown_counts.book_type


def test_tradedata() -> None:
    TradeData(6060842, Decimal("2.0"), Decimal("11603.21"), True, datetime(2021, 3, 14, 1, 59, 26, 535, tzinfo=UTC))
    with pytest.raises(ValueError):
        TradeData(6060842, Decimal("-2.0"), Decimal("11603.21"), False, datetime(2021, 3, 14, 1, 59, 26, 535, tzinfo=UTC))


def test_collection_stat() -> None:
    market = Market(exchange_id=1, instrument=Instrument(0, 3, MarketType.SPOT, "BTCUSD"))
    assert (
        CollectionStat(
            market=market,
            database_hour=datetime(2024, 1, 1, 0, tzinfo=UTC),
            max_database_time=datetime(2024, 1, 1, 0, 59, tzinfo=UTC),
        ).market.instrument.symbol
        == "BTCUSD"
    )
    with pytest.raises(ValueError):
        CollectionStat(
            market=market,
            database_hour=datetime(2024, 1, 1, 0, 1, tzinfo=UTC),
            max_database_time=datetime(2024, 1, 1, 0, 59, tzinfo=UTC),
        )
    with pytest.raises(ValueError):
        CollectionStat(
            market=market,
            database_hour=datetime(2024, 1, 1, 0, 1, tzinfo=UTC),
            max_database_time=datetime(2024, 1, 1, 0, 0, 59, tzinfo=UTC),
        )
    with pytest.raises(ValueError):
        CollectionStat(
            market=market,
            database_hour=datetime(2024, 1, 1, 0, 1, tzinfo=UTC),
            max_database_time=datetime(2024, 1, 1, 0, 0, 0, 1, tzinfo=UTC),
        )
