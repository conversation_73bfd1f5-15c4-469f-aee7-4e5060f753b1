from datetime import UTC, datetime
from decimal import Decimal
from typing import List
from unittest.mock import Mock

import pytest
from sortedcontainers import SortedDict  # type: ignore

from src.octopus.data import Book, BookData, BookType, Instrument, Market, PriceLevel
from src.octopus.run import BookScraper, Transform
from src.octopus.utils import SortDirection
from src.utils.diagnostics import Diagnostics
from src.utils.pyroutine import IPyRoutineEnv, ProcessBatch, chain_transform_batch
from test.src.octopus.standard_tests import standard_book_delta_transform_test

BTC_USD = Instrument.spot("BTCUSD", "btc", "usd")
BIG_BOOK = Book(
    market=Market(exchange_id=1, instrument=BTC_USD),
    depth_limit=100,
    collect_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    deduplication_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    scraper_session_id=1,
    scraper_sequence_id=1,
    data=BookData(
        exchange_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
        exchange_sequence_id=1,
        book_type=BookType.FULL,
        bids=[PriceLevel(price=Decimal(str(price)), amount=Decimal("1"), count=1) for price in range(2999, 2000, -1)],
        asks=[PriceLevel(price=Decimal(str(price)), amount=Decimal("1"), count=1) for price in range(3001, 4000, 1)],
    ),
)
ONLY_BIDS_BOOK = Book(
    market=Market(exchange_id=1, instrument=BTC_USD),
    depth_limit=100,
    collect_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    deduplication_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    scraper_session_id=1,
    scraper_sequence_id=1,
    data=BookData(
        exchange_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
        exchange_sequence_id=1,
        book_type=BookType.FULL,
        bids=[PriceLevel(price=Decimal(str(price)), amount=Decimal("1"), count=1) for price in range(2999, 2000, -1)],
        asks=[],
    ),
)
ONLY_ASKS_BOOK = Book(
    market=Market(exchange_id=1, instrument=BTC_USD),
    depth_limit=100,
    collect_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    deduplication_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
    scraper_session_id=1,
    scraper_sequence_id=1,
    data=BookData(
        exchange_time=datetime(year=2021, month=11, day=1, tzinfo=UTC),
        exchange_sequence_id=1,
        book_type=BookType.FULL,
        bids=[],
        asks=[PriceLevel(price=Decimal(str(price)), amount=Decimal("1"), count=1) for price in range(3001, 4000, 1)],
    ),
)


@pytest.mark.parametrize(
    "book,depth,is_websocket,expected_bids_length,expected_asks_length",
    (
        # 10% price depth applied
        (BIG_BOOK, 100, False, 300, 300),
        # 10% price depth should not be applied to websockets
        (BIG_BOOK, 100, True, 100, 100),
        # 10% price depth should not be applied to depth other than 100
        (BIG_BOOK, 99, False, 99, 99),
        (BIG_BOOK, 99, True, 99, 99),
        # one-sided books
        (ONLY_BIDS_BOOK, 100, False, 300, 0),
        (ONLY_ASKS_BOOK, 100, False, 0, 301),
    ),
)
def test_apply_desired_depth(
    book: Book, depth: int, is_websocket: bool, expected_bids_length: int, expected_asks_length: int
) -> None:
    sliced_book = BookScraper.apply_desired_depth(book, depth, is_websocket)
    assert len(sliced_book.data.bids) == expected_bids_length
    assert len(sliced_book.data.asks) == expected_asks_length


@pytest.mark.parametrize(
    "prices,target,sort_direction,expected_index",
    (
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [1, 2, 3, 4, 5]],
            3,
            SortDirection.ASCENDING,
            3,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [5, 4, 3, 2, 1]],
            3,
            SortDirection.DESCENDING,
            3,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [1, 2, 3, 4, 5]],
            10,
            SortDirection.ASCENDING,
            5,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [5, 4, 3, 2, 1]],
            -2,
            SortDirection.DESCENDING,
            5,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [1, 2, 3, 4, 5]],
            0,
            SortDirection.ASCENDING,
            0,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [5, 4, 3, 2, 1]],
            10,
            SortDirection.DESCENDING,
            0,
        ),
        # wrong sort direction
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [5, 4, 3, 2, 1]],
            6,
            SortDirection.ASCENDING,
            5,
        ),
        (
            [PriceLevel(price=Decimal(str(price)), amount=Decimal("1")) for price in [1, 2, 3, 4, 5]],
            3,
            SortDirection.DESCENDING,
            5,
        ),
    ),
)
def test_next_index(prices: List[PriceLevel], target: Decimal, sort_direction: SortDirection, expected_index: int) -> None:
    assert BookScraper.next_index(prices, target, sort_direction) == expected_index


def test_chain_batch_transform() -> None:
    """Ensures all the transforms are ran sequentially."""
    blocking_send_mock = Mock()
    channel_mock = Mock()

    def add_number_to_batch_transform(number: int) -> Transform[int]:
        def add_to_batch(batch: List[int], diagnostics: Diagnostics) -> List[int]:
            batch.append(number)
            return batch

        return add_to_batch

    def assert_batch_equals_process() -> ProcessBatch[int]:
        def proc(batch: List[int], env: IPyRoutineEnv) -> None:
            env.blocking_send(batch, channel_mock)

        return proc

    chain_transform_batch(
        transforms=[add_number_to_batch_transform(1), add_number_to_batch_transform(2), add_number_to_batch_transform(3)],
        process=assert_batch_equals_process(),
    )([], Mock(blocking_send=blocking_send_mock))
    blocking_send_mock.assert_called_with([1, 2, 3], channel_mock)


@pytest.mark.parametrize(
    "full_book_state,input_updates,depth,sign",
    (
        (
            SortedDict({
                Decimal("0.001029"): PriceLevel(price=Decimal("0.001029"), amount=Decimal("406100")),
                Decimal("0.001030"): PriceLevel(price=Decimal("0.001030"), amount=Decimal("493100")),
                Decimal("0.001031"): PriceLevel(price=Decimal("0.001031"), amount=Decimal("1349700")),
                Decimal("0.001032"): PriceLevel(price=Decimal("0.001032"), amount=Decimal("11662500")),
                Decimal("0.001033"): PriceLevel(price=Decimal("0.001033"), amount=Decimal("6192700")),
                Decimal("0.001034"): PriceLevel(price=Decimal("0.001034"), amount=Decimal("876200")),
                Decimal("0.001035"): PriceLevel(price=Decimal("0.001035"), amount=Decimal("1291900")),
                Decimal("0.001036"): PriceLevel(price=Decimal("0.001036"), amount=Decimal("10228600")),
                Decimal("0.001037"): PriceLevel(price=Decimal("0.001037"), amount=Decimal("3233600")),
                Decimal("0.001038"): PriceLevel(price=Decimal("0.001038"), amount=Decimal("2643300")),
                Decimal("0.001039"): PriceLevel(price=Decimal("0.001039"), amount=Decimal("9650100")),
                Decimal("0.001040"): PriceLevel(price=Decimal("0.001040"), amount=Decimal("23380500")),
                Decimal("0.001041"): PriceLevel(price=Decimal("0.001041"), amount=Decimal("743600")),
                Decimal("0.001042"): PriceLevel(price=Decimal("0.001042"), amount=Decimal("6092900")),
                Decimal("0.001043"): PriceLevel(price=Decimal("0.001043"), amount=Decimal("2518200")),
                Decimal("0.001044"): PriceLevel(price=Decimal("0.001044"), amount=Decimal("867100")),
                Decimal("0.001045"): PriceLevel(price=Decimal("0.001045"), amount=Decimal("11666700")),
                Decimal("0.001046"): PriceLevel(price=Decimal("0.001046"), amount=Decimal("903500")),
                Decimal("0.001047"): PriceLevel(price=Decimal("0.001047"), amount=Decimal("1367800")),
                Decimal("0.001048"): PriceLevel(price=Decimal("0.001048"), amount=Decimal("4528900")),
                Decimal("0.001049"): PriceLevel(price=Decimal("0.001049"), amount=Decimal("1779200")),
                Decimal("0.001050"): PriceLevel(price=Decimal("0.001050"), amount=Decimal("1181900")),
                Decimal("0.001051"): PriceLevel(price=Decimal("0.001051"), amount=Decimal("1237900")),
                Decimal("0.001052"): PriceLevel(price=Decimal("0.001052"), amount=Decimal("1253500")),
                Decimal("0.001053"): PriceLevel(price=Decimal("0.001053"), amount=Decimal("512300")),
                Decimal("0.001054"): PriceLevel(price=Decimal("0.001054"), amount=Decimal("1029800")),
                Decimal("0.001055"): PriceLevel(price=Decimal("0.001055"), amount=Decimal("2153100")),
                Decimal("0.001056"): PriceLevel(price=Decimal("0.001056"), amount=Decimal("2094900")),
                Decimal("0.001057"): PriceLevel(price=Decimal("0.001057"), amount=Decimal("2830800")),
                Decimal("0.001058"): PriceLevel(price=Decimal("0.001058"), amount=Decimal("1923200")),
                Decimal("0.001059"): PriceLevel(price=Decimal("0.001059"), amount=Decimal("3117500")),
                Decimal("0.001060"): PriceLevel(price=Decimal("0.001060"), amount=Decimal("18594300")),
                Decimal("0.001061"): PriceLevel(price=Decimal("0.001061"), amount=Decimal("542500")),
                Decimal("0.001062"): PriceLevel(price=Decimal("0.001062"), amount=Decimal("2348200")),
                Decimal("0.001063"): PriceLevel(price=Decimal("0.001063"), amount=Decimal("846000")),
                Decimal("0.001064"): PriceLevel(price=Decimal("0.001064"), amount=Decimal("596900")),
                Decimal("0.001065"): PriceLevel(price=Decimal("0.001065"), amount=Decimal("1904900")),
                Decimal("0.001066"): PriceLevel(price=Decimal("0.001066"), amount=Decimal("844300")),
                Decimal("0.001067"): PriceLevel(price=Decimal("0.001067"), amount=Decimal("562800")),
                Decimal("0.001068"): PriceLevel(price=Decimal("0.001068"), amount=Decimal("2326100")),
                Decimal("0.001069"): PriceLevel(price=Decimal("0.001069"), amount=Decimal("480200")),
                Decimal("0.001070"): PriceLevel(price=Decimal("0.001070"), amount=Decimal("1703400")),
                Decimal("0.001071"): PriceLevel(price=Decimal("0.001071"), amount=Decimal("326400")),
                Decimal("0.001072"): PriceLevel(price=Decimal("0.001072"), amount=Decimal("1005400")),
                Decimal("0.001073"): PriceLevel(price=Decimal("0.001073"), amount=Decimal("915500")),
                Decimal("0.001074"): PriceLevel(price=Decimal("0.001074"), amount=Decimal("1567600")),
                Decimal("0.001075"): PriceLevel(price=Decimal("0.001075"), amount=Decimal("1482400")),
                Decimal("0.001076"): PriceLevel(price=Decimal("0.001076"), amount=Decimal("497400")),
                Decimal("0.001077"): PriceLevel(price=Decimal("0.001077"), amount=Decimal("483700")),
                Decimal("0.001078"): PriceLevel(price=Decimal("0.001078"), amount=Decimal("969700")),
                Decimal("0.001079"): PriceLevel(price=Decimal("0.001079"), amount=Decimal("777300")),
                Decimal("0.001080"): PriceLevel(price=Decimal("0.001080"), amount=Decimal("2799000")),
                Decimal("0.001081"): PriceLevel(price=Decimal("0.001081"), amount=Decimal("622700")),
                Decimal("0.001082"): PriceLevel(price=Decimal("0.001082"), amount=Decimal("2223400")),
                Decimal("0.001083"): PriceLevel(price=Decimal("0.001083"), amount=Decimal("8702200")),
                Decimal("0.001084"): PriceLevel(price=Decimal("0.001084"), amount=Decimal("504800")),
                Decimal("0.001085"): PriceLevel(price=Decimal("0.001085"), amount=Decimal("441400")),
                Decimal("0.001086"): PriceLevel(price=Decimal("0.001086"), amount=Decimal("2121300")),
                Decimal("0.001087"): PriceLevel(price=Decimal("0.001087"), amount=Decimal("652900")),
                Decimal("0.001088"): PriceLevel(price=Decimal("0.001088"), amount=Decimal("578000")),
                Decimal("0.001089"): PriceLevel(price=Decimal("0.001089"), amount=Decimal("1169300")),
                Decimal("0.001090"): PriceLevel(price=Decimal("0.001090"), amount=Decimal("2424400")),
                Decimal("0.001091"): PriceLevel(price=Decimal("0.001091"), amount=Decimal("812000")),
                Decimal("0.001092"): PriceLevel(price=Decimal("0.001092"), amount=Decimal("1001400")),
                Decimal("0.001093"): PriceLevel(price=Decimal("0.001093"), amount=Decimal("1557400")),
                Decimal("0.001094"): PriceLevel(price=Decimal("0.001094"), amount=Decimal("1019000")),
                Decimal("0.001095"): PriceLevel(price=Decimal("0.001095"), amount=Decimal("566700")),
                Decimal("0.001096"): PriceLevel(price=Decimal("0.001096"), amount=Decimal("619600")),
                Decimal("0.001097"): PriceLevel(price=Decimal("0.001097"), amount=Decimal("1379400")),
                Decimal("0.001098"): PriceLevel(price=Decimal("0.001098"), amount=Decimal("697500")),
                Decimal("0.001099"): PriceLevel(price=Decimal("0.001099"), amount=Decimal("2099500")),
                Decimal("0.001100"): PriceLevel(price=Decimal("0.001100"), amount=Decimal("5037000")),
                Decimal("0.001101"): PriceLevel(price=Decimal("0.001101"), amount=Decimal("725200")),
                Decimal("0.001102"): PriceLevel(price=Decimal("0.001102"), amount=Decimal("599700")),
                Decimal("0.001103"): PriceLevel(price=Decimal("0.001103"), amount=Decimal("417000")),
                Decimal("0.001104"): PriceLevel(price=Decimal("0.001104"), amount=Decimal("757200")),
                Decimal("0.001105"): PriceLevel(price=Decimal("0.001105"), amount=Decimal("3704900")),
                Decimal("0.001106"): PriceLevel(price=Decimal("0.001106"), amount=Decimal("8345100")),
                Decimal("0.001107"): PriceLevel(price=Decimal("0.001107"), amount=Decimal("985300")),
                Decimal("0.001108"): PriceLevel(price=Decimal("0.001108"), amount=Decimal("919700")),
                Decimal("0.001109"): PriceLevel(price=Decimal("0.001109"), amount=Decimal("312000")),
                Decimal("0.001110"): PriceLevel(price=Decimal("0.001110"), amount=Decimal("1517500")),
                Decimal("0.001111"): PriceLevel(price=Decimal("0.001111"), amount=Decimal("1675400")),
                Decimal("0.001112"): PriceLevel(price=Decimal("0.001112"), amount=Decimal("414800")),
                Decimal("0.001113"): PriceLevel(price=Decimal("0.001113"), amount=Decimal("1449300")),
                Decimal("0.001114"): PriceLevel(price=Decimal("0.001114"), amount=Decimal("488600")),
                Decimal("0.001115"): PriceLevel(price=Decimal("0.001115"), amount=Decimal("1391200")),
                Decimal("0.001116"): PriceLevel(price=Decimal("0.001116"), amount=Decimal("957200")),
                Decimal("0.001117"): PriceLevel(price=Decimal("0.001117"), amount=Decimal("1624700")),
                Decimal("0.001118"): PriceLevel(price=Decimal("0.001118"), amount=Decimal("836700")),
                Decimal("0.001119"): PriceLevel(price=Decimal("0.001119"), amount=Decimal("421200")),
                Decimal("0.001120"): PriceLevel(price=Decimal("0.001120"), amount=Decimal("1857400")),
                Decimal("0.001121"): PriceLevel(price=Decimal("0.001121"), amount=Decimal("699700")),
                Decimal("0.001122"): PriceLevel(price=Decimal("0.001122"), amount=Decimal("741200")),
                Decimal("0.001123"): PriceLevel(price=Decimal("0.001123"), amount=Decimal("1266500")),
                Decimal("0.001124"): PriceLevel(price=Decimal("0.001124"), amount=Decimal("14776200")),
                Decimal("0.001125"): PriceLevel(price=Decimal("0.001125"), amount=Decimal("9049000")),
                Decimal("0.001126"): PriceLevel(price=Decimal("0.001126"), amount=Decimal("16776600")),
                Decimal("0.001127"): PriceLevel(price=Decimal("0.001127"), amount=Decimal("423500")),
                Decimal("0.001128"): PriceLevel(price=Decimal("0.001128"), amount=Decimal("23555800")),
                Decimal("0.001129"): PriceLevel(price=Decimal("0.001129"), amount=Decimal("493800")),
                Decimal("0.001130"): PriceLevel(price=Decimal("0.001130"), amount=Decimal("24906300")),
                Decimal("0.001131"): PriceLevel(price=Decimal("0.001131"), amount=Decimal("856700")),
                Decimal("0.001132"): PriceLevel(price=Decimal("0.001132"), amount=Decimal("21803400")),
                Decimal("0.001133"): PriceLevel(price=Decimal("0.001133"), amount=Decimal("690700")),
                Decimal("0.001134"): PriceLevel(price=Decimal("0.001134"), amount=Decimal("32100200")),
                Decimal("0.001135"): PriceLevel(price=Decimal("0.001135"), amount=Decimal("994300")),
                Decimal("0.001136"): PriceLevel(price=Decimal("0.001136"), amount=Decimal("15300900")),
                Decimal("0.001137"): PriceLevel(price=Decimal("0.001137"), amount=Decimal("218600")),
                Decimal("0.001138"): PriceLevel(price=Decimal("0.001138"), amount=Decimal("11116300")),
                Decimal("0.001139"): PriceLevel(price=Decimal("0.001139"), amount=Decimal("930100")),
                Decimal("0.001140"): PriceLevel(price=Decimal("0.001140"), amount=Decimal("25613900")),
                Decimal("0.001141"): PriceLevel(price=Decimal("0.001141"), amount=Decimal("4734000")),
                Decimal("0.001142"): PriceLevel(price=Decimal("0.001142"), amount=Decimal("16911600")),
                Decimal("0.001143"): PriceLevel(price=Decimal("0.001143"), amount=Decimal("604200")),
                Decimal("0.001144"): PriceLevel(price=Decimal("0.001144"), amount=Decimal("14737900")),
                Decimal("0.001145"): PriceLevel(price=Decimal("0.001145"), amount=Decimal("4687500")),
                Decimal("0.001146"): PriceLevel(price=Decimal("0.001146"), amount=Decimal("26419900")),
                Decimal("0.001147"): PriceLevel(price=Decimal("0.001147"), amount=Decimal("1136200")),
                Decimal("0.001148"): PriceLevel(price=Decimal("0.001148"), amount=Decimal("12354300")),
                Decimal("0.001149"): PriceLevel(price=Decimal("0.001149"), amount=Decimal("621600")),
                Decimal("0.001150"): PriceLevel(price=Decimal("0.001150"), amount=Decimal("28680300")),
                Decimal("0.001151"): PriceLevel(price=Decimal("0.001151"), amount=Decimal("974100")),
                Decimal("0.001152"): PriceLevel(price=Decimal("0.001152"), amount=Decimal("21439500")),
                Decimal("0.001153"): PriceLevel(price=Decimal("0.001153"), amount=Decimal("421100")),
                Decimal("0.001154"): PriceLevel(price=Decimal("0.001154"), amount=Decimal("22873200")),
                Decimal("0.001155"): PriceLevel(price=Decimal("0.001155"), amount=Decimal("4600200")),
                Decimal("0.001156"): PriceLevel(price=Decimal("0.001156"), amount=Decimal("23036200")),
                Decimal("0.001157"): PriceLevel(price=Decimal("0.001157"), amount=Decimal("462700")),
                Decimal("0.001158"): PriceLevel(price=Decimal("0.001158"), amount=Decimal("16388000")),
                Decimal("0.001159"): PriceLevel(price=Decimal("0.001159"), amount=Decimal("898600")),
                Decimal("0.001160"): PriceLevel(price=Decimal("0.001160"), amount=Decimal("26243700")),
                Decimal("0.001161"): PriceLevel(price=Decimal("0.001161"), amount=Decimal("1245200")),
                Decimal("0.001162"): PriceLevel(price=Decimal("0.001162"), amount=Decimal("29639200")),
                Decimal("0.001163"): PriceLevel(price=Decimal("0.001163"), amount=Decimal("510300")),
                Decimal("0.001164"): PriceLevel(price=Decimal("0.001164"), amount=Decimal("1380700")),
                Decimal("0.001165"): PriceLevel(price=Decimal("0.001165"), amount=Decimal("5645100")),
                Decimal("0.001166"): PriceLevel(price=Decimal("0.001166"), amount=Decimal("401300")),
                Decimal("0.001167"): PriceLevel(price=Decimal("0.001167"), amount=Decimal("1282700")),
                Decimal("0.001168"): PriceLevel(price=Decimal("0.001168"), amount=Decimal("1523900")),
                Decimal("0.001169"): PriceLevel(price=Decimal("0.001169"), amount=Decimal("474100")),
                Decimal("0.001170"): PriceLevel(price=Decimal("0.001170"), amount=Decimal("4171200")),
                Decimal("0.001171"): PriceLevel(price=Decimal("0.001171"), amount=Decimal("693700")),
                Decimal("0.001172"): PriceLevel(price=Decimal("0.001172"), amount=Decimal("533900")),
                Decimal("0.001173"): PriceLevel(price=Decimal("0.001173"), amount=Decimal("2033300")),
                Decimal("0.001174"): PriceLevel(price=Decimal("0.001174"), amount=Decimal("8112800")),
                Decimal("0.001175"): PriceLevel(price=Decimal("0.001175"), amount=Decimal("5948700")),
                Decimal("0.001176"): PriceLevel(price=Decimal("0.001176"), amount=Decimal("724500")),
                Decimal("0.001177"): PriceLevel(price=Decimal("0.001177"), amount=Decimal("373200")),
                Decimal("0.001178"): PriceLevel(price=Decimal("0.001178"), amount=Decimal("1023300")),
                Decimal("0.001179"): PriceLevel(price=Decimal("0.001179"), amount=Decimal("1870900")),
            }),
            [
                PriceLevel(price=Decimal("0.001029"), amount=Decimal("0")),
                PriceLevel(price=Decimal("0.001030"), amount=Decimal("0")),
                PriceLevel(price=Decimal("0.001031"), amount=Decimal("193000")),
                PriceLevel(price=Decimal("0.001032"), amount=Decimal("5530300")),
                PriceLevel(price=Decimal("0.001033"), amount=Decimal("9479400")),
                PriceLevel(price=Decimal("0.001124"), amount=Decimal("26421000")),
                PriceLevel(price=Decimal("0.001126"), amount=Decimal("16260700")),
                PriceLevel(price=Decimal("0.001130"), amount=Decimal("24505200")),
                PriceLevel(price=Decimal("0.001132"), amount=Decimal("22526700")),
                PriceLevel(price=Decimal("0.001134"), amount=Decimal("23141500")),
                PriceLevel(price=Decimal("0.001140"), amount=Decimal("19152000")),
                PriceLevel(price=Decimal("0.001146"), amount=Decimal("19001900")),
                PriceLevel(price=Decimal("0.001148"), amount=Decimal("19715200")),
                PriceLevel(price=Decimal("0.001152"), amount=Decimal("13511300")),
                PriceLevel(price=Decimal("0.001158"), amount=Decimal("28066200")),
                PriceLevel(price=Decimal("0.001160"), amount=Decimal("17898300")),
                PriceLevel(price=Decimal("0.001162"), amount=Decimal("16809600")),
                PriceLevel(price=Decimal("0.001229"), amount=Decimal("332400")),
                PriceLevel(price=Decimal("0.001230"), amount=Decimal("12267300")),
            ],
            100,
            1,
        ),
    ),
)
def test_compute_book_delta(
    full_book_state: "SortedDict[Decimal, PriceLevel]", input_updates: List[PriceLevel], depth: int, sign: int
) -> None:
    new_updates, new_book_state = BookScraper._compute_limited_delta_side(full_book_state, input_updates, depth, sign)
    standard_book_delta_transform_test(
        full_book_state=full_book_state,
        input_updates=input_updates,
        depth=depth,
        sign=sign,
        new_updates=new_updates,
        new_book_state=new_book_state,
    )
