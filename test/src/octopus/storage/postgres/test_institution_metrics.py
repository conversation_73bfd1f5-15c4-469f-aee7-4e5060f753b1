from datetime import datetime, timezone
from decimal import Decimal

from src.octopus.data import InstitutionMetrics
from src.octopus.qa_utils import get_postgres_storage
from src.octopus.storage.postgres.institution_metrics import PostgresInstitutionMetricsStorage


def _parse_date(intdate: int) -> datetime:
    yyyy: int = intdate // 10000
    mm: int = intdate // 100 % 100
    dd: int = intdate % 100
    return datetime(yyyy, mm, dd, tzinfo=timezone.utc)


def test_fund_nav_storage() -> None:
    institution_id = 1
    im_0 = InstitutionMetrics(
        institution_id,
        _parse_date(20210504),
        {
            "gbtc_net_asset_value": Decimal("52.37604231"),
            "gbtc_shares_outstanding": Decimal("692370100"),
            "gbtc_total_assets": Decimal("36263605651.7789"),
        },
    )
    im_1 = InstitutionMetrics(
        institution_id,
        _parse_date(20210505),
        {
            "gbtc_net_asset_value": Decimal("52.62632183"),
            "gbtc_shares_outstanding": Decimal("692370100"),
            "gbtc_total_assets": Decimal("36436891708.0693"),
        },
    )
    im_1_conflict = InstitutionMetrics(
        institution_id,
        _parse_date(20210505),
        {
            "gbtc_net_asset_value": Decimal("52.6060842"),
            "gbtc_shares_outstanding": Decimal("692370100"),
            "gbtc_total_assets": Decimal("36422879778.1624"),
        },
    )
    im_2 = InstitutionMetrics(
        institution_id,
        _parse_date(20210506),
        {
            "gbtc_net_asset_value": Decimal("53.76436467"),
            "gbtc_shares_outstanding": Decimal("692370100"),
            "gbtc_total_assets": Decimal("37224838543.0044"),
        },
    )

    with get_postgres_storage(PostgresInstitutionMetricsStorage) as storage:
        storage.save_institution_metrics(im_0)
        assert storage.get_institution_metrics(5) == [im_0]
        storage.delete_institution_metrics(im_0)
        assert storage.get_institution_metrics(5) == []

        storage.save_institution_metrics(im_0)
        storage.save_institution_metrics(im_1)
        assert storage.get_institution_metrics(5) == [im_0, im_1]

        storage.save_institution_metrics(im_1_conflict)
        storage.save_institution_metrics(im_2)
        assert storage.get_institution_metrics(5) == [im_0, im_1, im_2]

        assert storage.get_max_date(institution_id) == _parse_date(20210506)
        assert storage.get_max_fund_date(institution_id, "gbtc") == _parse_date(20210506)

        storage.delete_institution_metrics(im_1)
        storage.delete_institution_metrics(im_2)
        assert storage.get_institution_metrics(5) == [im_0]
