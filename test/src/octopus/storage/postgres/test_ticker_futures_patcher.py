import csv
import random
from datetime import datetime
from io import String<PERSON>
from typing import Set, Union
from unittest import mock
from unittest.mock import Mock

import psycopg2  # type: ignore
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time  # type: ignore
from psycopg2 import sql

from src.octopus.storage.postgres.futures_ticker import PostgresFuturesTickerStorage
from src.octopus.storage.postgres.patch.db_patcher import DBPatcher, TableColumn, TableColumns
from src.octopus.storage.postgres.patch.ticker_futures_patcher import TickerFuturesPatcher
from src.utils.postgres import PgConnectionParams, postgres_connect
from test.src.octopus.storage.postgres.util import _mock_postgres_connect, assert_csv_rows_match_ticker_futures, list_to_buffer

EXCHANGE_ID = 49
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(name="ticker_symbol", data_type="text", ordinal_position=1),
        TableColumn(name="ticker_deduplication_time", data_type="timestamp with time zone", ordinal_position=2),
        TableColumn(name="ticker_time", data_type="timestamp with time zone", ordinal_position=3),
        TableColumn(name="ticker_price_mark", data_type="numeric", ordinal_position=4),
        TableColumn(name="ticker_price_index", data_type="numeric", ordinal_position=5),
        TableColumn(name="ticker_estimated_settlement_price", data_type="numeric", ordinal_position=6),
        TableColumn(name="ticker_estimated_funding_rate", data_type="numeric", ordinal_position=7),
        TableColumn(name="ticker_estimated_funding_rate_time", data_type="timestamp with time zone", ordinal_position=8),
        TableColumn(name="ticker_database_time", data_type="timestamp with time zone", ordinal_position=9),
    ]
)
_default_ticker_futures_patcher = TickerFuturesPatcher(
    exchange_id=EXCHANGE_ID,
    diagnostics=Mock(),
    source_schema_name="public",
    source_connection_params=PgConnectionParams("localhost:9432:postgres:postgres:postgres"),
    target_connection_params=PgConnectionParams("localhost:8432:postgres:postgres:postgres"),
    target_schema_name="public",
    source_table_columns=TABLE_COLS,
    target_table_columns=TABLE_COLS,
)

_csv_list = [
    "ticker_symbol,ticker_deduplication_time,ticker_time,ticker_price_mark,ticker_price_index,ticker_estimated_settlement_price,ticker_estimated_funding_rate,ticker_estimated_funding_rate_time,ticker_database_time",  # noqa: E501
    "BTCUSDU24,2023-11-17 14:00:00+00,2023-11-17 14:00:00.629+00,68176.99,66708.56,0.00,,,2023-11-17 14:00:04.066+00",
    "ETHUSDU24,2023-11-17 14:01:00+00,2023-11-17 14:01:00.629+00,3582.37,3506.09,0.00,,,2023-11-17 14:01:06.071+00",
    "SOLUSD,2023-11-17 14:02:00+00,2023-11-17 14:02:00.629+00,169.17,169.24,,0.0001,2023-11-17 14:02:00+00,"
    "2023-11-17 14:02:07.067+00",
    "ETHUSD,2023-11-17 14:03:00+00,2023-11-17 14:03:00.629+00,3504.95,3506.06,,0.0001,2023-11-17 14:03:00+00,"
    "2023-11-17 04:03:08.067+00",
    "BTCUSDZ24,2023-11-17 14:04:00+00,2023-11-17 14:04:00.629+00,70353.55,66708.56,0.00,,,2023-11-17 14:04:09.067+00",
]

_csv_futures_ticker_times: Set[str] = set()


def _csv_futures_ticker_time() -> str:
    global _csv_futures_ticker_times
    futures_ticker_time = f"2023-04-01 00:00:{random.choice(range(0, 60)):02d}+00"
    while futures_ticker_time in _csv_futures_ticker_times:
        futures_ticker_time = f"2023-04-01 00:{random.choice(range(0, 60)):02d}:{random.choice(range(0, 60)):02d}+00"
    _csv_futures_ticker_times.add(futures_ticker_time)
    return futures_ticker_time


def _csv_ticker_futures() -> str:
    ticker_symbol = random.choice(["btc-usdt", "eth-usdt", "eth-btc"])
    ticker_time = "2023-04-01 00:00:00 +0000"
    ticker_deduplication_time = _csv_futures_ticker_time()
    ticker_price_mark = random.uniform(0.1, 10000)
    ticker_price_index = random.uniform(0.1, 10000)
    ticker_estimated_settlement_price = random.uniform(0.1, 10000)
    ticker_estimated_funding_rate = random.uniform(0.1, 10000)
    ticker_estimated_funding_rate_time = "2023-04-01 00:00:00 +0000"
    ticker_database_time = f"2023-04-01 00:00:0{random.choice(range(0, 10))} +0000"
    return (
        f"{ticker_symbol},"
        f"{ticker_deduplication_time},"
        f"{ticker_time},"
        f"{ticker_price_mark},"
        f"{ticker_price_index},"
        f"{ticker_estimated_settlement_price},"
        f"{ticker_estimated_funding_rate},"
        f"{ticker_estimated_funding_rate_time}, "
        f"{ticker_database_time}"
    )


@freeze_time("2022-02-01")  # type: ignore
def test_batch_datetimes() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=2, minutes=36)
    hours = [h for h in _default_ticker_futures_patcher._batch_datetimes(begin=begin, end=end)]
    assert hours == [
        (
            datetime.fromisoformat("2022-02-01T00:00:00"),
            datetime.fromisoformat("2022-02-01T01:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T01:00:00"),
            datetime.fromisoformat("2022-02-01T02:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T02:00:00"),
            datetime.fromisoformat("2022-02-01T02:36:00"),
        ),
    ]


def test_copy_missing_rows_to_buffer() -> None:
    row_list = [_csv_list[0]] + (_csv_list[1:] * 1000)
    new_row_list = [
        "BTCUSD,2023-07-11 00:00:00+00,2023-07-11 00:00:00.629+00,169.17,169.24,,0.0001,2023-07-11 00:00:00+00,"
        "2023-07-11 00:00:07.067+00",
        "ADAUSD,2023-07-11 00:00:00+00,2023-07-11 00:00:00.629+00,3504.95,3506.06,,0.0001,2023-07-11 00:00:00+00,"
        "2023-07-11 00:00:08.067+00",
    ]
    target_rows = list_to_buffer(row_list)
    source_list = row_list + new_row_list
    source_rows = list_to_buffer(source_list)
    missing_rows = DBPatcher.copy_missing_rows_to_buffer(
        target_rows=target_rows,
        source_rows=source_rows,
        unique_key_from_row=_default_ticker_futures_patcher.unique_key_from_row,
        source_columns=TABLE_COLS,
        target_columns=TABLE_COLS,
    )
    assert [line.strip() for line in missing_rows.readlines()][1:] == new_row_list


@freeze_time("2022-11-14T22:17:18.689735")  # type: ignore
def test_get_batch_end() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=11)
    batch_end_dates = [d.isoformat() for d in _default_ticker_futures_patcher._get_batch_end(begin=begin, end=end)]
    assert batch_end_dates == [
        "2022-11-14T23:00:00",
        "2022-11-15T00:00:00",
        "2022-11-15T01:00:00",
        "2022-11-15T02:00:00",
        "2022-11-15T03:00:00",
        "2022-11-15T04:00:00",
        "2022-11-15T05:00:00",
        "2022-11-15T06:00:00",
        "2022-11-15T07:00:00",
        "2022-11-15T08:00:00",
        "2022-11-15T09:00:00",
        "2022-11-15T09:17:18.689735",
    ]


def test_copy_rows_between_to_buffer(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    rows = list_to_buffer(
        _csv_list
        + [
            "SOLBTCUSD,2023-11-17 00:04:00 +0000,2023-11-17 00:04:00.629  +000000,169.17,169.24,,0.0001,"
            "2023-11-17 00:04:00 +0000,2023-11-17 00:00:07.067  +000000",
        ]
    )
    rows.seek(0)
    ticker_futures_patcher = TickerFuturesPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
        target_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
        target_schema_name="public",
    )
    with postgres_connect(postgres_mexc_futures_ticker_storage._connection_params) as conn:
        DBPatcher.copy_to_target(
            tgt_conn=conn,
            rows=rows,
            query=DBPatcher.target_query(table_name=ticker_futures_patcher._target_table_name, columns=TABLE_COLS),
            columns=TABLE_COLS,
        )
        begin = datetime(2023, 11, 17, 14, 0)
        end = datetime(2023, 11, 17, 15, 0)
        buffer = ticker_futures_patcher.copy_rows_between_to_buffer(
            conn=conn,
            table_name=ticker_futures_patcher._target_table_name,
            datetime_column=ticker_futures_patcher._source_datetime_column,
            begin=begin,
            end=end,
            schema_name=ticker_futures_patcher._target_schema_name,
            columns=TABLE_COLS,
        )
        assert [line.strip() for line in buffer.readlines()] == _csv_list


def test_batch_copy_to_target(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    rows = list_to_buffer(_csv_list)
    ticker_futures_patcher = TickerFuturesPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
        target_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
        target_schema_name="public",
        batch_row_size=1,
        max_writes_per_second=1000000,
        copy_wait_time=0,
    )
    with postgres_connect(postgres_mexc_futures_ticker_storage._connection_params) as conn:
        row_count, batch_count = DBPatcher.batch_copy_to_target(
            tgt_conn=conn,
            rows=rows,
            table_name=ticker_futures_patcher._target_table_name,
            batch_row_size=ticker_futures_patcher._batch_row_size,
            copy_wait_time=ticker_futures_patcher._copy_wait_time,
            max_writes_per_second=ticker_futures_patcher._max_writes_per_second,
            diagnostics=Mock(),
            columns=TABLE_COLS,
        )
        assert row_count == 5
        assert batch_count == 5


copy_to_buffer = DBPatcher.copy_to_buffer


def _mock_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        return list_to_buffer(_csv_list)
    else:
        return copy_to_buffer(conn, query)


def _mock_batch_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        row_list = [_csv_list[0]] + [_csv_ticker_futures() for i in range(100)]
        return list_to_buffer(row_list)
    else:
        return copy_to_buffer(conn, query)


def test_patch_between(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2023, 11, 17, 14)
    end = datetime(2023, 11, 17, 15)
    with mock.patch("src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_copy_to_buffer):
        ticker_futures_patcher = TickerFuturesPatcher(
            exchange_id=EXCHANGE_ID,
            diagnostics=Mock(),
            source_schema_name="public",
            source_connection_params=Mock(),
            target_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
            target_schema_name="public",
            batch_row_size=1,
            max_writes_per_second=1000000,
            copy_wait_time=0,
            source_table_columns=TABLE_COLS,
            target_table_columns=TABLE_COLS,
        )
        mock_src_conn = Mock()
        with postgres_connect(postgres_mexc_futures_ticker_storage._connection_params) as conn:
            ticker_futures_patcher.patch_between(
                src_conn=mock_src_conn,
                tgt_conn=conn,
                begin=begin,
                end=end,
                commit=True,
            )
            ticker_futures = postgres_mexc_futures_ticker_storage.get_futures_ticker(10000)
            csv_rows = list(csv.reader(_csv_list))
            assert_csv_rows_match_ticker_futures(row_list=csv_rows, ticker_futures=ticker_futures)


def test_batch_patch_between(postgres_mexc_futures_ticker_storage: PostgresFuturesTickerStorage) -> None:
    begin = datetime(2023, 4, 1, 0, 0)
    end = datetime(2023, 4, 1, 0, 10)
    with mock.patch(
        "src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_batch_copy_to_buffer
    ):
        with mock.patch(
            "src.octopus.storage.postgres.patch.db_patcher.postgres_connect",
            side_effect=_mock_postgres_connect,
        ):
            ticker_futures_patcher = TickerFuturesPatcher(
                exchange_id=EXCHANGE_ID,
                diagnostics=Mock(),
                source_schema_name="public",
                source_connection_params=Mock(),
                target_connection_params=postgres_mexc_futures_ticker_storage._connection_params,
                target_schema_name="public",
                batch_row_size=1,
                max_writes_per_second=1000000,
                copy_wait_time=0,
                source_table_columns=TABLE_COLS,
                target_table_columns=TABLE_COLS,
            )
            ticker_futures_patcher.batch_patch_between(begin=begin, end=end, commit=True)
            batch_count = len([i for i in ticker_futures_patcher._batch_datetimes(begin=begin, end=end)])
            # _mock_batch_copy_to_buffer returns 100 random csv ticker_futures per batch
            expected_row_count = batch_count * 100
            ticker_futures = postgres_mexc_futures_ticker_storage.get_futures_ticker(100000)
            assert len(ticker_futures) == expected_row_count
