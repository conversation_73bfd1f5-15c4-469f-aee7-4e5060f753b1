# import csv
# import random
# from datetime import datetime
# from io import StringIO
# from typing import Union
# from unittest import mock
# from unittest.mock import Mock
#
# import psycopg2
# from dateutil.relativedelta import relativedelta
# from freezegun import freeze_time  # type: ignore
# from psycopg2 import sql
#
# from src.octopus.data import MarketType
# from src.octopus.storage.postgres.book_old import PostgresOldSpotBookStorage
# from src.octopus.storage.postgres.patch.book_patcher import BookPatcher
# from src.octopus.storage.postgres.patch.db_patcher import DBPatcher, TableColumn, TableColumns
# from src.utils.postgres import PgConnectionParams, postgres_connect
# from test.src.octopus.storage.postgres.util import _mock_postgres_connect, assert_csv_rows_match_books, list_to_buffer
#
# EXCHANGE_ID = 4
# TABLE_COLS = TableColumns(
#     columns=[
#         TableColumn(name="book_exchange_id", ordinal_position=1, data_type="smallint"),
#         TableColumn(name="book_base_id", ordinal_position=2, data_type="smallint"),
#         TableColumn(name="book_quote_id", ordinal_position=3, data_type="smallint"),
#         TableColumn(name="book_depth_limit", ordinal_position=4, data_type="integer"),
#         TableColumn(name="book_time", ordinal_position=5, data_type="timestamp with time zone"),
#         TableColumn(name="book_database_time", ordinal_position=6, data_type="timestamp with time zone"),
#         TableColumn(name="book_exchange_sequence_id", ordinal_position=7, data_type="numeric"),
#         TableColumn(name="book_exchange_time", ordinal_position=8, data_type="timestamp with time zone"),
#         TableColumn(name="book_bids", ordinal_position=9, data_type="ARRAY"),
#         TableColumn(name="book_asks", ordinal_position=10, data_type="ARRAY"),
#     ]
# )
#
# _default_book_patcher = BookPatcher(
#     exchange_id=EXCHANGE_ID,
#     market_type=MarketType.SPOT,
#     diagnostics=Mock(),
#     source_schema_name="public",
#     source_connection_params=PgConnectionParams("localhost:9432:postgres:postgres:postgres"),
#     target_connection_params=PgConnectionParams("localhost:8432:postgres:postgres:postgres"),
#     target_schema_name="public",
# )
#
# _csv_list = [
#     "book_exchange_id,book_base_id,book_quote_id,book_depth_limit,book_time,book_database_time,book_exchange_sequence_id,book_exchange_time,book_bids,book_asks",  # noqa: E501
#     '4,28,100,100,2023-04-01 00:00:00+00,2023-04-01 00:00:00.581903+00,1586802450,2023-04-01 00:00:00.014+00,"{""(2863.00000000,0.26850000,)"",""(3879.00000000,0.26860000,)"",""(3829.00000000,0.26870000,)"",""(5272.00000000,0.26880000,)"",""(10950.00000000,0.26890000,)"",""(5022.00000000,0.26900000,)"",""(18885.00000000,0.26910000,)"",""(7535.00000000,0.26920000,)"",""(605.00000000,0.26930000,)"",""(8402.00000000,0.26940000,)""}","{""(3789.00000000,0.26840000,)"",""(5427.00000000,0.26830000,)"",""(6253.00000000,0.26820000,)"",""(9586.00000000,0.26810000,)"",""(13556.00000000,0.26800000,)"",""(8667.00000000,0.26790000,)"",""(24366.00000000,0.26780000,)"",""(40555.00000000,0.26770000,)"",""(576.00000000,0.26760000,)"",""(18178.00000000,0.26750000,)""}"',  # noqa: E501
#     '4,2117,100,100,2023-04-01 00:00:00+00,2023-04-01 00:00:00.581903+00,1177495768,2023-04-01 00:00:00.215+00,"{""(4872.00000000,0.04105000,)"",""(11430.00000000,0.04106000,)"",""(12185.00000000,0.04107000,)"",""(12184.00000000,0.04108000,)"",""(12186.00000000,0.04109000,)"",""(14161.00000000,0.04110000,)"",""(1471.00000000,0.04112000,)"",""(6133.00000000,0.04114000,)"",""(64774.00000000,0.04115000,)"",""(17146.00000000,0.04116000,)""}","{""(33.00000000,0.04103000,)"",""(7942.00000000,0.04102000,)"",""(41445.00000000,0.04101000,)"",""(293759.00000000,0.04100000,)"",""(7781.00000000,0.04099000,)"",""(41729.00000000,0.04098000,)"",""(63065.00000000,0.04097000,)"",""(10179.00000000,0.04096000,)"",""(68528.00000000,0.04095000,)"",""(82703.00000000,0.04094000,)""}"',  # noqa: E501
#     '4,1384,100,100,2023-04-01 00:00:00+00,2023-04-01 00:00:00.626873+00,3719338034,2023-04-01 00:00:00.215+00,"{""(0.02444000,8797.00000000,)"",""(0.02016000,8798.00000000,)"",""(0.00878000,8799.00000000,)"",""(0.02272000,8800.00000000,)"",""(0.00878000,8801.00000000,)"",""(0.01041000,8802.00000000,)"",""(0.03557000,8803.00000000,)"",""(0.50223000,8804.00000000,)"",""(0.55250000,8805.00000000,)"",""(0.54266000,8806.00000000,)""}","{""(0.02433000,8794.00000000,)"",""(0.01293000,8793.00000000,)"",""(0.04819000,8792.00000000,)"",""(0.12523000,8791.00000000,)"",""(0.03558000,8790.00000000,)"",""(0.22218000,8789.00000000,)"",""(0.01494000,8788.00000000,)"",""(0.00944000,8787.00000000,)"",""(0.00944000,8786.00000000,)"",""(0.00944000,8785.00000000,)""}"',  # noqa: E501
#     '4,2115,100,100,2023-04-01 00:00:00+00,2023-04-01 00:00:00.626873+00,4760461926,2023-04-01 00:00:00.114+00,"{""(1597.00000000,0.62710000,)"",""(1774.00000000,0.62720000,)"",""(4798.00000000,0.62730000,)"",""(7866.00000000,0.62740000,)"",""(2345.00000000,0.62750000,)"",""(343.00000000,0.62760000,)"",""(1408.00000000,0.62770000,)"",""(14932.00000000,0.62780000,)"",""(12814.00000000,0.62790000,)"",""(3524.00000000,0.62800000,)""}","{""(6171.00000000,0.62700000,)"",""(7509.00000000,0.62690000,)"",""(5781.00000000,0.62680000,)"",""(4418.00000000,0.62670000,)"",""(4050.00000000,0.62660000,)"",""(3127.00000000,0.62650000,)"",""(18438.00000000,0.62640000,)"",""(12319.00000000,0.62630000,)"",""(13658.00000000,0.62620000,)"",""(25647.00000000,0.62610000,)""}"',  # noqa: E501
#     '4,1785,100,30000,2023-04-01 00:00:00+00,2023-04-01 00:00:01.367055+00,132193759,,"{""(803.00000000,0.23910000,)"",""(1017.00000000,0.23920000,)"",""(1423.00000000,0.23930000,)"",""(1253.00000000,0.23940000,)"",""(3073.00000000,0.23960000,)"",""(2182.00000000,0.23970000,)"",""(573.00000000,0.23980000,)"",""(2500.00000000,0.23990000,)"",""(2450.00000000,0.24000000,)"",""(4262.00000000,0.24040000,)""}","{""(419.00000000,0.23860000,)"",""(838.00000000,0.23850000,)"",""(300.00000000,0.23840000,)"",""(419.00000000,0.23830000,)"",""(2171.00000000,0.23820000,)"",""(9522.00000000,0.23810000,)"",""(816.00000000,0.23800000,)"",""(3669.00000000,0.23790000,)"",""(2928.00000000,0.23780000,)"",""(1924.00000000,0.23770000,)""}"',  # noqa: E501
# ]
#
#
# def _rand_csv_price_level() -> str:
#     price_level = (
#         (random.randint(0, 60000) / 100),
#         (random.randint(100, 1000) / 100),
#     )
#     return f'""{price_level}""'.replace(" ", "").replace(")", ",)")
#
#
# def _rand_csv_price_levels() -> str:
#     return '"{' + ",".join([_rand_csv_price_level() for i in range(0, 10)]) + '}"'
#
#
# def _csv_book() -> str:
#     book_base_id = random.choice(range(0, 10000))
#     book_quote_id = random.choice(range(0, 10000))
#     book_time = "2023-04-01 00:00:00+00"
#     book_database_time = f"2023-04-01 00:00:0{random.choice(range(0, 10))}+00"
#     book_exchange_time = f"2023-04-01 00:00:0{random.choice(range(0, 10))}+00"
#     book_depth_limit = random.choice([100, 30000])
#     if book_depth_limit == 30000:
#         book_exchange_sequence_id = 0
#     else:
#         book_exchange_sequence_id = random.choice(range(1, 100000))
#     return (
#         f"{EXCHANGE_ID},"
#         f"{book_base_id},"
#         f"{book_quote_id},"
#         f"{book_depth_limit},"
#         f"{book_time},"
#         f"{book_database_time},"
#         f"{book_exchange_sequence_id},"
#         f"{book_exchange_time}," + _rand_csv_price_levels() + "," + _rand_csv_price_levels()
#     )
#
#
# @freeze_time("2022-02-01")
# def test_batch_datetimes() -> None:
#     begin = datetime.utcnow()
#     end = begin + relativedelta(hours=1, minutes=36)
#     batch = [h for h in _default_book_patcher._batch_datetimes(begin=begin, end=end)]
#     assert batch == [
#         (
#             datetime.fromisoformat("2022-02-01T00:00:00"),
#             datetime.fromisoformat("2022-02-01T00:10:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T00:10:00"),
#             datetime.fromisoformat("2022-02-01T00:20:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T00:20:00"),
#             datetime.fromisoformat("2022-02-01T00:30:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T00:30:00"),
#             datetime.fromisoformat("2022-02-01T00:40:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T00:40:00"),
#             datetime.fromisoformat("2022-02-01T00:50:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T00:50:00"),
#             datetime.fromisoformat("2022-02-01T01:00:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T01:00:00"),
#             datetime.fromisoformat("2022-02-01T01:10:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T01:10:00"),
#             datetime.fromisoformat("2022-02-01T01:20:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T01:20:00"),
#             datetime.fromisoformat("2022-02-01T01:30:00"),
#         ),
#         (
#             datetime.fromisoformat("2022-02-01T01:30:00"),
#             datetime.fromisoformat("2022-02-01T01:36:00"),
#         ),
#     ]
#
#
# def test_copy_missing_rows_to_buffer() -> None:
#     row_list = [_csv_list[0]] + (_csv_list[1:] * 1000)
#     new_row_list = [
#         '4,1907,2000,30000,2023-04-01 00:00:00+00,2023-04-01 00:08:21.209104+00,13560157,,"{""(0.19900000,125451.00,)"",""(0.80000000,126209.00,)"",""(6.50000000,126692.00,)"",""(0.59100000,126999.00,)"",""(2.96600000,128000.00,)"",""(48.16200000,128999.00,)"",""(1.34200000,129000.00,)"",""(58.25600000,129241.00,)"",""(0.15600000,129365.00,)"",""(1.00000000,129900.00,)""}","{""(3.79600000,124125.00,)"",""(5.20000000,124000.00,)"",""(2.21400000,123600.00,)"",""(2.42800000,123525.00,)"",""(15.23800000,123524.00,)"",""(7.40100000,123000.00,)"",""(20.40700000,122501.00,)"",""(8.00000000,122500.00,)"",""(41.81700000,122001.00,)"",""(10.00000000,122000.00,)""}"',  # noqa: E501
#         '4,1,2000,30000,2023-04-01 00:00:00+00,2023-04-01 00:08:21.209104+00,13560157,,"{""(0.19900000,125451.00,)"",""(0.80000000,126209.00,)"",""(6.50000000,126692.00,)"",""(0.59100000,126999.00,)"",""(2.96600000,128000.00,)"",""(48.16200000,128999.00,)"",""(1.34200000,129000.00,)"",""(58.25600000,129241.00,)"",""(0.15600000,129365.00,)"",""(1.00000000,129900.00,)""}","{""(3.79600000,124125.00,)"",""(5.20000000,124000.00,)"",""(2.21400000,123600.00,)"",""(2.42800000,123525.00,)"",""(15.23800000,123524.00,)"",""(7.40100000,123000.00,)"",""(20.40700000,122501.00,)"",""(8.00000000,122500.00,)"",""(41.81700000,122001.00,)"",""(10.00000000,122000.00,)""}"',  # noqa: E501
#     ]
#     target_rows = list_to_buffer(row_list)
#     source_list = row_list + new_row_list
#     source_rows = list_to_buffer(source_list)
#     missing_rows = DBPatcher.copy_missing_rows_to_buffer(
#         target_rows=target_rows,
#         source_rows=source_rows,
#         unique_key_from_row=DBPatcher.unique_book_spot_key_from_row,
#         source_columns=TABLE_COLS,
#         target_columns=TABLE_COLS,
#     )
#     assert [line.strip() for line in missing_rows.readlines()][1:] == new_row_list
#
#
# def test_get_batch_end() -> None:
#     begin = datetime(year=2023, month=4, day=1, hour=0, minute=5, second=23)
#     end = begin + relativedelta(hours=1, minutes=15)
#     batch_end_dates = [d.isoformat() for d in _default_book_patcher._get_batch_end(begin=begin, end=end)]
#     assert batch_end_dates == [
#         "2023-04-01T00:15:23",
#         "2023-04-01T00:25:23",
#         "2023-04-01T00:35:23",
#         "2023-04-01T00:45:23",
#         "2023-04-01T00:55:23",
#         "2023-04-01T01:05:23",
#         "2023-04-01T01:15:23",
#         "2023-04-01T01:20:23",
#     ]
#
#
# def test_copy_rows_between_to_buffer(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
#     rows = list_to_buffer(
#         _csv_list
#         + [
#             '4,1907,2000,30000,2023-04-01 02:00:00+00,2023-04-01 02:08:21.209104+00,13560157,,"{""(0.19900000,125451.00,)"",""(0.80000000,126209.00,)"",""(6.50000000,126692.00,)"",""(0.59100000,126999.00,)"",""(2.96600000,128000.00,)"",""(48.16200000,128999.00,)"",""(1.34200000,129000.00,)"",""(58.25600000,129241.00,)"",""(0.15600000,129365.00,)"",""(1.00000000,129900.00,)""}","{""(3.79600000,124125.00,)"",""(5.20000000,124000.00,)"",""(2.21400000,123600.00,)"",""(2.42800000,123525.00,)"",""(15.23800000,123524.00,)"",""(7.40100000,123000.00,)"",""(20.40700000,122501.00,)"",""(8.00000000,122500.00,)"",""(41.81700000,122001.00,)"",""(10.00000000,122000.00,)""}"',  # noqa: E501
#         ]
#     )
#     rows.seek(0)
#     book_patcher = BookPatcher(
#         exchange_id=EXCHANGE_ID,
#         market_type=MarketType.SPOT,
#         diagnostics=Mock(),
#         source_schema_name="public",
#         source_connection_params=postgres_old_spot_book_storage._connection_params,
#         target_connection_params=postgres_old_spot_book_storage._connection_params,
#         target_schema_name="public",
#         source_table_columns=TABLE_COLS,
#         target_table_columns=TABLE_COLS,
#     )
#     with postgres_connect(postgres_old_spot_book_storage._connection_params) as conn:
#         DBPatcher.copy_to_target(
#             tgt_conn=conn,
#             rows=rows,
#             query=DBPatcher.target_query(table_name=book_patcher._target_table_name, columns=TABLE_COLS),
#             columns=TABLE_COLS,
#         )
#         begin = datetime(2023, 4, 1, 0, 0)
#         end = datetime(2023, 4, 1, 1, 0)
#         buffer = book_patcher.copy_rows_between_to_buffer(
#             conn=conn,
#             table_name=book_patcher._target_table_name,
#             datetime_column=book_patcher._source_datetime_column,
#             begin=begin,
#             end=end,
#             schema_name=book_patcher._source_schema_name,
#             columns=book_patcher.source_table_columns,  # target columns because we are copying to the target
#         )
#         assert [line.strip() for line in buffer.readlines()] == _csv_list
#
#
# def test_batch_copy_to_target(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
#     rows = list_to_buffer(_csv_list)
#     book_patcher = BookPatcher(
#         exchange_id=EXCHANGE_ID,
#         market_type=MarketType.SPOT,
#         diagnostics=Mock(),
#         source_schema_name="public",
#         source_connection_params=postgres_old_spot_book_storage._connection_params,
#         target_connection_params=postgres_old_spot_book_storage._connection_params,
#         target_schema_name="public",
#         batch_row_size=1,
#         max_writes_per_second=1000000,
#         copy_wait_time=0,
#     )
#     with postgres_connect(postgres_old_spot_book_storage._connection_params) as conn:
#         row_count, batch_count = DBPatcher.batch_copy_to_target(
#             tgt_conn=conn,
#             rows=rows,
#             table_name=book_patcher._target_table_name,
#             batch_row_size=book_patcher._batch_row_size,
#             copy_wait_time=book_patcher._copy_wait_time,
#             max_writes_per_second=book_patcher._max_writes_per_second,
#             diagnostics=Mock(),
#             columns=TABLE_COLS,
#         )
#         assert row_count == 5
#         assert batch_count == 5
#
#
# copy_to_buffer = DBPatcher.copy_to_buffer
#
#
# def _mock_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
#     if isinstance(conn, Mock):
#         return list_to_buffer(_csv_list)
#     else:
#         return copy_to_buffer(conn, query)
#
#
# def _mock_batch_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
#     if isinstance(conn, Mock):
#         row_list = [_csv_list[0]] + [_csv_book() for i in range(100)]
#         return list_to_buffer(row_list)
#     else:
#         return copy_to_buffer(conn, query)
#
#
# def test_patch_between(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
#     begin = datetime(2023, 4, 1, 0)
#     end = datetime(2023, 4, 1, 1)
#     with mock.patch("src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_copy_to_buffer):
#         book_patcher = BookPatcher(
#             exchange_id=EXCHANGE_ID,
#             market_type=MarketType.SPOT,
#             diagnostics=Mock(),
#             source_schema_name="public",
#             source_connection_params=Mock(),
#             target_connection_params=postgres_old_spot_book_storage._connection_params,
#             target_schema_name="public",
#             batch_row_size=1,
#             max_writes_per_second=1000000,
#             copy_wait_time=0,
#             target_table_columns=TABLE_COLS,
#             source_table_columns=TABLE_COLS,
#         )
#         mock_src_conn = Mock()
#         with postgres_connect(postgres_old_spot_book_storage._connection_params) as conn:
#             book_patcher.patch_between(
#                 src_conn=mock_src_conn,
#                 tgt_conn=conn,
#                 begin=begin,
#                 end=end,
#                 commit=True,
#             )
#             books = postgres_old_spot_book_storage.get_books(10000)
#             csv_rows = list(csv.reader(_csv_list))
#             assert_csv_rows_match_books(row_list=csv_rows, books=books)
#
#
# def test_batch_patch_between(postgres_old_spot_book_storage: PostgresOldSpotBookStorage) -> None:
#     begin = datetime(2023, 4, 1, 0, 0)
#     end = datetime(2023, 4, 1, 0, 10)
#     with mock.patch(
#         "src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_batch_copy_to_buffer
#     ):
#         with mock.patch(
#             "src.octopus.storage.postgres.patch.db_patcher.postgres_connect",
#             side_effect=_mock_postgres_connect,
#         ):
#             book_patcher = BookPatcher(
#                 exchange_id=EXCHANGE_ID,
#                 market_type=MarketType.SPOT,
#                 diagnostics=Mock(),
#                 source_schema_name="public",
#                 source_connection_params=Mock(),
#                 target_connection_params=postgres_old_spot_book_storage._connection_params,
#                 target_schema_name="public",
#                 batch_row_size=1,
#                 max_writes_per_second=1000000,
#                 copy_wait_time=0,
#                 target_table_columns=TABLE_COLS,
#                 source_table_columns=TABLE_COLS,
#             )
#             book_patcher.batch_patch_between(begin=begin, end=end, commit=True)
#             batch_count = len([i for i in book_patcher._batch_datetimes(begin=begin, end=end)])
#             # _mock_batch_copy_to_buffer returns 100 random csv trades per batch
#             expected_row_count = batch_count * 100
#             books = postgres_old_spot_book_storage.get_books(100000)
#             assert len(books) == expected_row_count
