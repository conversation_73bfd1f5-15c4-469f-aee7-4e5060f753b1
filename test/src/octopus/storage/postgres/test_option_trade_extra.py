from datetime import datetime
from decimal import Decimal

from src.octopus.data import Instrument, Market, Trade, TradeData
from src.octopus.qa_utils import get_postgres_storage
from src.octopus.storage.postgres.option_trade_extra import PostgresOptionTradeExtraStorage


def test_option_trade_extra_storage() -> None:
    MKT_0 = Market(37, Instrument.option("BTC-7JAN22-38000-P"))
    MKT_1 = Market(37, Instrument.option("BTC-30SEP22-25000-P"))
    MKT_2 = Market(37, Instrument.option("ETH-28JAN22-3000-P"))

    OT_0 = Trade(
        MKT_0,
        TradeData(
            trade_id=196150559,
            amount=Decimal(0),
            price=Decimal(0),
            is_buy=None,
            time=datetime(2000, 1, 1),
            implied_vol=Decimal("0.8467"),
            mark_price=Decimal("0.00546041"),
            index_price=Decimal("48783.7"),
        ),
    )
    # TODO: sort out how/why this is a conflict - made up different data for now
    OT_0_CONFLICT = Trade(
        MKT_0,
        TradeData(
            trade_id=196150559,
            amount=Decimal(0),
            price=Decimal(0),
            is_buy=None,
            time=datetime(2000, 1, 1),
            implied_vol=Decimal("0.8467"),
            mark_price=Decimal("0.00646041"),
            index_price=Decimal("48783.7"),
        ),
    )
    OT_1 = Trade(
        MKT_1,
        TradeData(
            trade_id=196150553,
            amount=Decimal(0),
            price=Decimal(0),
            is_buy=None,
            time=datetime(2000, 1, 1),
            implied_vol=Decimal("0.901"),
            mark_price=Decimal("0.05079807"),
            index_price=Decimal("48777.33"),
        ),
    )
    OT_2 = Trade(
        MKT_2,
        TradeData(
            trade_id=10896839,
            amount=Decimal(0),
            price=Decimal(0),
            is_buy=None,
            time=datetime(2000, 1, 1),
            implied_vol=Decimal("0.9967"),
            mark_price=Decimal("0.027087"),
            index_price=Decimal("3987.77"),
        ),
    )

    with get_postgres_storage(PostgresOptionTradeExtraStorage) as storage:
        storage.save_option_trade_extra([OT_0, OT_1])
        assert storage.get_option_trade_extra(5) == [OT_1, OT_0]

        storage.save_option_trade_extra([OT_0_CONFLICT, OT_2])
        assert storage.get_option_trade_extra(5) == [OT_2, OT_1, OT_0]

        storage.delete_option_trade_extra([OT_1])
        assert storage.get_option_trade_extra(5) == [OT_2, OT_0]
