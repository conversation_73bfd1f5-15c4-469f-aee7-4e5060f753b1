import csv
import random
from datetime import datetime
from io import String<PERSON>
from typing import Set, Union
from unittest import mock
from unittest.mock import Mock

import psycopg2  # type: ignore
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time  # type: ignore
from psycopg2 import sql

from src.octopus.storage.postgres.option_ticker import PostgresOptionTickerStorage
from src.octopus.storage.postgres.patch.db_patcher import DBPatcher, TableColumn, TableColumns
from src.octopus.storage.postgres.patch.ticker_option_patcher import TickerOptionPatcher
from src.utils.postgres import PgConnectionParams, postgres_connect
from test.src.octopus.storage.postgres.util import _mock_postgres_connect, assert_csv_rows_match_ticker_options, list_to_buffer

EXCHANGE_ID = 49
_default_ticker_option_patcher = TickerOptionPatcher(
    exchange_id=EXCHANGE_ID,
    diagnostics=Mock(),
    source_schema_name="public",
    source_connection_params=PgConnectionParams("localhost:9432:postgres:postgres:postgres"),
    target_connection_params=PgConnectionParams("localhost:8432:postgres:postgres:postgres"),
    target_schema_name="public",
)
TABLE_COLS = TableColumns(
    columns=[
        TableColumn(
            name="ticker_exchange_id",
            data_type="smallint",
            ordinal_position=1,
        ),
        TableColumn(
            name="ticker_symbol",
            data_type="text",
            ordinal_position=2,
        ),
        TableColumn(
            name="ticker_time",
            data_type="timestamp with time zone",
            ordinal_position=3,
        ),
        TableColumn(
            name="ticker_exchange_time",
            data_type="timestamp with time zone",
            ordinal_position=4,
        ),
        TableColumn(
            name="ticker_price_last",
            data_type="numeric",
            ordinal_position=5,
        ),
        TableColumn(
            name="ticker_price_bid",
            data_type="numeric",
            ordinal_position=6,
        ),
        TableColumn(
            name="ticker_price_ask",
            data_type="numeric",
            ordinal_position=7,
        ),
        TableColumn(
            name="ticker_price_mark",
            data_type="numeric",
            ordinal_position=8,
        ),
        TableColumn(
            name="ticker_price_index",
            data_type="numeric",
            ordinal_position=9,
        ),
        TableColumn(
            name="ticker_amount_bid",
            data_type="numeric",
            ordinal_position=10,
        ),
        TableColumn(
            name="ticker_amount_ask",
            data_type="numeric",
            ordinal_position=11,
        ),
        TableColumn(
            name="ticker_index_name",
            data_type="text",
            ordinal_position=12,
        ),
        TableColumn(
            name="ticker_implied_vol_trade",
            data_type="numeric",
            ordinal_position=13,
        ),
        TableColumn(
            name="ticker_implied_vol_bid",
            data_type="numeric",
            ordinal_position=14,
        ),
        TableColumn(
            name="ticker_implied_vol_ask",
            data_type="numeric",
            ordinal_position=15,
        ),
        TableColumn(
            name="ticker_implied_vol_mark",
            data_type="numeric",
            ordinal_position=16,
        ),
        TableColumn(
            name="ticker_greek_delta",
            data_type="numeric",
            ordinal_position=17,
        ),
        TableColumn(
            name="ticker_greek_gamma",
            data_type="numeric",
            ordinal_position=18,
        ),
        TableColumn(
            name="ticker_greek_theta",
            data_type="numeric",
            ordinal_position=19,
        ),
        TableColumn(
            name="ticker_greek_vega",
            data_type="numeric",
            ordinal_position=20,
        ),
        TableColumn(
            name="ticker_greek_rho",
            data_type="numeric",
            ordinal_position=21,
        ),
        TableColumn(
            name="ticker_database_time",
            data_type="timestamp with time zone",
            ordinal_position=22,
        ),
        TableColumn(
            name="ticker_estimated_settlement_price",
            data_type="numeric",
            ordinal_position=23,
        ),
    ]
)

_csv_list = [
    "ticker_exchange_id,ticker_symbol,ticker_time,ticker_exchange_time,ticker_price_last,ticker_price_bid,ticker_price_ask,ticker_price_mark,ticker_price_index,ticker_amount_bid,ticker_amount_ask,ticker_index_name,ticker_implied_vol_trade,ticker_implied_vol_bid,ticker_implied_vol_ask,ticker_implied_vol_mark,ticker_greek_delta,ticker_greek_gamma,ticker_greek_theta,ticker_greek_vega,ticker_greek_rho,ticker_database_time,ticker_estimated_settlement_price",  # noqa: E501
    f"{EXCHANGE_ID},MATIC_USDC-2AUG24-0d62-P,2023-11-17 14:00:00+00,2023-11-17 00:00:33.173+00,,,,0.1116,0.5088,,,"
    f"SYN.MATIC_USDC-2AUG24,,0.0,0.0,0.7657,-0.97531,1.15836,-0.00024,0.00004,-0.0001,2023-11-17 00:00:38.019+00,0.5088",
    f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d64-P,2023-11-17 14:00:00+00,2023-11-17 00:00:33.113+00,,,,0.1325,0.5088,,,"
    f"SYN.MATIC_USDC-9AUG24,,0.0,0.0,0.7083,-0.95051,1.50449,-0.00027,0.0001,-0.00022,2023-11-17 00:00:40.014+00,0.5088",
    f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d7-P,2023-11-17 14:00:00+00,2023-11-17 00:00:31.693+00,,,,0.1919,0.5088,,,"
    f"SYN.MATIC_USDC-9AUG24,,0.0,0.0,0.8476,-0.97216,0.78561,-0.0002,0.00006,-0.00024,2023-11-17 00:00:44.483+00,0.5088",
    f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d52-C,2023-11-17 14:00:00+00,2023-11-17 00:00:31.693+00,0.016,0.017,0.0175,"
    f"0.0173,0.5088,20000.0,25000.0,SYN.MATIC_USDC-9AUG24,,0.6116,0.6258,0.6206,0.44329,7.09636,-0.00097,0.00035,"
    f"0.00006,2023-11-17 00:00:52.483+00,0.5088",
    f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d46-C,2023-11-17 14:00:00+00,2023-11-17 00:00:25.105+00,,,,0.0551,0.5089,,,"
    f"SYN.MATIC_USDC-9AUG24,,0.9538,1.0493,0.6268,0.81947,4.36789,-0.00061,0.00025,0.00013,"
    f"2023-11-17 00:00:55.485+00,0.5089",
]


_csv_option_ticker_times: Set[str] = set()


def _csv_option_ticker_time() -> str:
    global _csv_option_ticker_times
    option_ticker_time = f"2023-04-01 00:00:{random.choice(range(0, 60)):02d}+00"
    while option_ticker_time in _csv_option_ticker_times:
        option_ticker_time = f"2023-04-01 00:{random.choice(range(0, 60)):02d}:{random.choice(range(0, 60)):02d}+00"
    _csv_option_ticker_times.add(option_ticker_time)
    return option_ticker_time


def _csv_ticker_option() -> str:
    ticker_symbol = random.choice(["BNB-240729-520-C", "BNB-240729-520-P", "BNB-240729-540-C", "BNB-240729-540-P"])
    ticker_time = _csv_option_ticker_time()
    ticker_exchange_time = "2023-04-01 00:00:00.629 +000000"
    ticker_price_last = random.uniform(0.1, 10000)
    ticker_price_bid = random.uniform(0.1, 10000)
    ticker_price_ask = random.uniform(0.1, 10000)
    ticker_price_mark = random.uniform(0.1, 10000)
    ticker_price_index = random.uniform(0.1, 10000)
    ticker_amount_bid = random.uniform(0.1, 10000)
    ticker_amount_ask = random.uniform(0.1, 10000)
    ticker_index_name = "SYN.BNB-240729"
    ticker_implied_vol_trade = random.uniform(0.1, 10000)
    ticker_implied_vol_bid = random.uniform(0.1, 10000)
    ticker_implied_vol_ask = random.uniform(0.1, 10000)
    ticker_implied_vol_mark = random.uniform(0.1, 10000)
    ticker_greek_delta = random.uniform(0.1, 10000)
    ticker_greek_gamma = random.uniform(0.1, 10000)
    ticker_greak_theta = random.uniform(0.1, 10000)
    ticker_greek_vega = random.uniform(0.1, 10000)
    ticker_greek_rho = random.uniform(0.1, 10000)
    ticker_estimated_settlement_price = random.uniform(0.1, 10000)
    ticker_database_time = f"2023-04-01 00:00:0{random.choice(range(0, 10))} +0000"
    return (
        f"{EXCHANGE_ID}, "
        f"{ticker_symbol}, "
        f"{ticker_time}, "
        f"{ticker_exchange_time}, "
        f"{ticker_price_last}, "
        f"{ticker_price_bid}, "
        f"{ticker_price_ask}, "
        f"{ticker_price_mark}, "
        f"{ticker_price_index}, "
        f"{ticker_amount_bid}, "
        f"{ticker_amount_ask}, "
        f"{ticker_index_name}, "
        f"{ticker_implied_vol_trade}, "
        f"{ticker_implied_vol_bid}, "
        f"{ticker_implied_vol_ask}, "
        f"{ticker_implied_vol_mark}, "
        f"{ticker_greek_delta}, "
        f"{ticker_greek_gamma}, "
        f"{ticker_greak_theta}, "
        f"{ticker_greek_vega}, "
        f"{ticker_greek_rho}, "
        f"{ticker_database_time}, "
        f"{ticker_estimated_settlement_price}"
    )


@freeze_time("2022-02-01")  # type: ignore
def test_batch_datetimes() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=2, minutes=36)
    hours = [h for h in _default_ticker_option_patcher._batch_datetimes(begin=begin, end=end)]
    assert hours == [
        (
            datetime.fromisoformat("2022-02-01T00:00:00"),
            datetime.fromisoformat("2022-02-01T01:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T01:00:00"),
            datetime.fromisoformat("2022-02-01T02:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T02:00:00"),
            datetime.fromisoformat("2022-02-01T02:36:00"),
        ),
    ]


def test_copy_missing_rows_to_buffer() -> None:
    row_list = [_csv_list[0]] + (_csv_list[1:] * 1000)
    new_row_list = [
        f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d46-D,2023-11-17 00:00:00+00,2023-11-17 00:00:25.105+00,,,,0.0551,0.5089,,,"
        f"SYN.MATIC_USDC-9AUG24,,0.9538,1.0493,0.6268,0.81947,4.36789,-0.00061,0.00025,0.00013,"
        f"2023-11-17 00:00:31.485+00,0.5089",
        f"{EXCHANGE_ID},MATIC_USDC-9AUG24-0d46-E,2023-11-17 00:00:00+00,2023-11-17 00:00:25.105+00,,,,0.0551,0.5089,,,"
        f"SYN.MATIC_USDC-9AUG24,,0.9538,1.0493,0.6268,0.81947,4.36789,-0.00061,0.00025,0.00013,"
        f"2023-11-17 00:00:31.485+00,0.5089",
    ]
    target_rows = list_to_buffer(row_list)
    source_list = row_list + new_row_list
    source_rows = list_to_buffer(source_list)
    missing_rows = DBPatcher.copy_missing_rows_to_buffer(
        target_rows=target_rows,
        source_rows=source_rows,
        unique_key_from_row=_default_ticker_option_patcher.unique_key_from_row,
        source_columns=TABLE_COLS,
        target_columns=TABLE_COLS,
    )
    assert [line.strip() for line in missing_rows.readlines()][1:] == new_row_list


@freeze_time("2022-11-14T22:17:18.689735")  # type: ignore
def test_get_batch_end() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=11)
    batch_end_dates = [d.isoformat() for d in _default_ticker_option_patcher._get_batch_end(begin=begin, end=end)]
    assert batch_end_dates == [
        "2022-11-14T23:00:00",
        "2022-11-15T00:00:00",
        "2022-11-15T01:00:00",
        "2022-11-15T02:00:00",
        "2022-11-15T03:00:00",
        "2022-11-15T04:00:00",
        "2022-11-15T05:00:00",
        "2022-11-15T06:00:00",
        "2022-11-15T07:00:00",
        "2022-11-15T08:00:00",
        "2022-11-15T09:00:00",
        "2022-11-15T09:17:18.689735",
    ]


def test_copy_rows_between_to_buffer(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    rows = list_to_buffer(
        _csv_list
        + [
            "37,MATIC_USDC-9AUG24-0d46-G,2023-11-17 14:01:00+00,2023-11-17 00:00:25.105+00,,,,0.0551,0.5089,,,"
            "SYN.MATIC_USDC-9AUG24,,0.9538,1.0493,0.6268,0.81947,4.36789,-0.00061,0.00025,0.00013,"
            "2023-11-17 00:00:31.485+00,0.5089",
        ]
    )
    rows.seek(0)
    ticker_option_patcher = TickerOptionPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_option_ticker_storage._connection_params,
        target_connection_params=postgres_mexc_option_ticker_storage._connection_params,
        target_schema_name="public",
    )
    with postgres_connect(postgres_mexc_option_ticker_storage._connection_params) as conn:
        DBPatcher.copy_to_target(
            tgt_conn=conn,
            rows=rows,
            query=DBPatcher.target_query(table_name=ticker_option_patcher._target_table_name, columns=TABLE_COLS),
            columns=TABLE_COLS,
        )
        begin = datetime(2023, 11, 17, 14, 0)
        end = datetime(2023, 11, 17, 15, 0)
        buffer = ticker_option_patcher.copy_rows_between_to_buffer(
            conn=conn,
            table_name=ticker_option_patcher._target_table_name,
            datetime_column=ticker_option_patcher._source_datetime_column,
            begin=begin,
            end=end,
            schema_name=ticker_option_patcher._target_schema_name,
            columns=TABLE_COLS,
        )
        assert [line.strip() for line in buffer.readlines()] == _csv_list


def test_batch_copy_to_target(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    rows = list_to_buffer(_csv_list)
    ticker_option_patcher = TickerOptionPatcher(
        exchange_id=EXCHANGE_ID,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_option_ticker_storage._connection_params,
        target_connection_params=postgres_mexc_option_ticker_storage._connection_params,
        target_schema_name="public",
        batch_row_size=1,
        max_writes_per_second=1000000,
        copy_wait_time=0,
    )
    with postgres_connect(postgres_mexc_option_ticker_storage._connection_params) as conn:
        row_count, batch_count = DBPatcher.batch_copy_to_target(
            tgt_conn=conn,
            rows=rows,
            table_name=ticker_option_patcher._target_table_name,
            batch_row_size=ticker_option_patcher._batch_row_size,
            copy_wait_time=ticker_option_patcher._copy_wait_time,
            max_writes_per_second=ticker_option_patcher._max_writes_per_second,
            diagnostics=Mock(),
            columns=TABLE_COLS,
        )
        assert row_count == 5
        assert batch_count == 5


copy_to_buffer = DBPatcher.copy_to_buffer


def _mock_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        return list_to_buffer(_csv_list)
    else:
        return copy_to_buffer(conn, query)


def _mock_batch_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        row_list = [_csv_list[0]] + [_csv_ticker_option() for i in range(100)]
        return list_to_buffer(row_list)
    else:
        return copy_to_buffer(conn, query)


def test_patch_between(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2023, 11, 17, 14)
    end = datetime(2023, 11, 17, 15)
    with mock.patch("src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_copy_to_buffer):
        ticker_option_patcher = TickerOptionPatcher(
            exchange_id=EXCHANGE_ID,
            diagnostics=Mock(),
            source_schema_name="public",
            source_connection_params=Mock(),
            target_connection_params=postgres_mexc_option_ticker_storage._connection_params,
            target_schema_name="public",
            batch_row_size=1,
            max_writes_per_second=1000000,
            copy_wait_time=0,
            source_table_columns=TABLE_COLS,
            target_table_columns=TABLE_COLS,
        )
        mock_src_conn = Mock()
        with postgres_connect(postgres_mexc_option_ticker_storage._connection_params) as conn:
            ticker_option_patcher.patch_between(
                src_conn=mock_src_conn,
                tgt_conn=conn,
                begin=begin,
                end=end,
                commit=True,
            )
            ticker_options = sorted(
                postgres_mexc_option_ticker_storage.get_option_ticker(10000),
                key=lambda x: (x.data.time, x.market.instrument.symbol, x.data.exchange_time),
            )
            csv_rows = sorted(list(csv.reader(_csv_list[1:])), key=lambda x: (x[1], x[2], x[3]))
            assert_csv_rows_match_ticker_options(row_list=csv_rows, ticker_options=ticker_options)


def test_batch_patch_between(postgres_mexc_option_ticker_storage: PostgresOptionTickerStorage) -> None:
    begin = datetime(2023, 4, 1, 0, 0)
    end = datetime(2023, 4, 1, 0, 10)
    with mock.patch(
        "src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_batch_copy_to_buffer
    ):
        with mock.patch(
            "src.octopus.storage.postgres.patch.db_patcher.postgres_connect",
            side_effect=_mock_postgres_connect,
        ):
            ticker_option_patcher = TickerOptionPatcher(
                exchange_id=EXCHANGE_ID,
                diagnostics=Mock(),
                source_schema_name="public",
                source_connection_params=Mock(),
                target_connection_params=postgres_mexc_option_ticker_storage._connection_params,
                target_schema_name="public",
                batch_row_size=1,
                max_writes_per_second=1000000,
                copy_wait_time=0,
                source_table_columns=TABLE_COLS,
                target_table_columns=TABLE_COLS,
            )
            ticker_option_patcher.batch_patch_between(begin=begin, end=end, commit=True)
            batch_count = len([i for i in ticker_option_patcher._batch_datetimes(begin=begin, end=end)])
            # _mock_batch_copy_to_buffer returns 100 random csv ticker_option per batch
            expected_row_count = batch_count * 100
            ticker_options = postgres_mexc_option_ticker_storage.get_option_ticker(100000)
            assert len(ticker_options) == expected_row_count
