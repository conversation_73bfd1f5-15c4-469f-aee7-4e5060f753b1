from datetime import UTC, datetime, timedelta
from decimal import Decimal

from src.octopus.data import FundingRate, FundingRateData, Instrument, Market
from src.octopus.qa_utils import get_postgres_storage
from src.octopus.storage.postgres.funding_rate import PostgresFundingRateStorage


def test_funding_rate_storage() -> None:
    MARKET_0 = Market(0, Instrument.futures("BTC-USD"))
    MARKET_1 = Market(1, Instrument.futures("btcusd"))
    MARKET_2 = Market(3, Instrument.futures("BTC-PERP"))
    MARKET_3 = Market(4, Instrument.futures("ETH-SWAP"))

    FR_0 = FundingRate(
        MARKET_0,
        FundingRateData(Decimal("1.2341232"), timedelta(hours=8), timedelta(hours=8), datetime.now(UTC) - timedelta(days=3)),
    )

    FM_0_CONFLICT = FundingRate(
        MARKET_0, FundingRateData(Decimal("1.222"), timedelta(hours=8), timedelta(hours=1), FR_0.data.time)
    )

    FR_1 = FundingRate(
        MARKET_1,
        FundingRateData(Decimal("-0.2341232"), timedelta(hours=8), timedelta(hours=8), datetime.now(UTC) - timedelta(days=5)),
    )

    FR_2 = FundingRate(
        MARKET_2,
        FundingRateData(Decimal("0.124"), timedelta(hours=8), timedelta(milliseconds=1), datetime.now(UTC) - timedelta(days=6)),
    )

    FR_3 = FundingRate(
        MARKET_3,
        FundingRateData(Decimal("0.35341232"), timedelta(hours=4), timedelta(hours=4), datetime.now(UTC) - timedelta(days=7)),
    )

    with get_postgres_storage(PostgresFundingRateStorage) as storage:
        storage.save_funding_rates([FR_0, FR_1, FR_2])
        assert storage.get_funding_rates(5) == [FR_0, FR_1, FR_2]

        storage.save_funding_rates([FM_0_CONFLICT, FR_3])
        assert storage.get_funding_rates(5) == [FR_0, FR_1, FR_2, FR_3]

        storage.delete_funding_rates([FR_1, FR_2])
        assert storage.get_funding_rates(5) == [FR_0, FR_3]
