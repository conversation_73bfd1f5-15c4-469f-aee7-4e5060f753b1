import random
import string
from datetime import datetime
from decimal import Decimal
from io import StringIO
from typing import Union
from unittest import mock
from unittest.mock import Mock

import psycopg2
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time  # type: ignore

from src.octopus.data import MarketType
from src.octopus.storage.postgres.patch.db_patcher import DBPatcher, TableColumn, TableColumns
from src.octopus.storage.postgres.patch.trade_patcher import TradePatcher
from src.octopus.storage.postgres.trade import PostgresSpotTradeStorage
from src.utils.postgres import PgConnectionParams, postgres_connect
from test.src.octopus.storage.postgres.util import assert_csv_rows_match_trades, list_to_buffer

EXCHANGE_ID = 49
LIST_OF_TRADES = [
    "trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time",
    "16671744000134387012612659125048,TRXUSDT,252.00,0.063089,t,2022-10-31 00:00:00.013+00,2022-10-31 00:00:00.280248+00",
    "16671744000359964125117418592037,ETHUSDT,0.21070,1590.54,f,2022-10-31 00:00:00.035+00,2022-10-31 00:00:00.280248+00",
    "16671744000533658680600713750403,ETHUSDT,0.40000,1590.54,f,2022-10-31 00:00:00.053+00,2022-10-31 00:00:00.280248+00",
    "16671744000538561409172148649390,POLOUSDT,95931.09,0.000291,f,2022-10-31 00:00:00.053+00,2022-10-31 00:00:00.280248+00",
    "166717440006417657423591846364435,ETHUSDT,0.30872,1590.51,t,2022-10-31 00:00:00.064+00,2022-10-31 00:00:00.280248+00",
    "16671744000687935452939241554666,ETHUSDT,0.23000,1590.49,f,2022-10-31 00:00:00.068+00,2022-10-31 00:00:00.280248+00",
    "1667174400087140668679653505372,ETHUSDT,0.15630,1590.49,t,2022-10-31 00:00:00.087+00,2022-10-31 00:00:00.280248+00",
    "166717440011012264812942589863632,ETHUSDT,0.05278,1590.47,t,2022-10-31 00:00:00.11+00,2022-10-31 00:00:00.280248+00",
    "16671744001128322473687054104490,BTC4LUSDT,52.11,0.135,f,2022-10-31 00:00:00.112+00,2022-10-31 00:00:00.280248+00",
    "16671744001276700632400381480567,ETHUSDT,0.45949,1590.47,f,2022-10-31 00:00:00.127+00,2022-10-31 00:00:00.280248+00",
]

TABLE_COLS = TableColumns(
    columns=[
        TableColumn(name="trade_id", ordinal_position=1, data_type="text"),
        TableColumn(name="trade_symbol", ordinal_position=2, data_type="text"),
        TableColumn(name="trade_amount", ordinal_position=3, data_type="text"),
        TableColumn(name="trade_price", ordinal_position=4, data_type="text"),
        TableColumn(name="trade_buy", ordinal_position=5, data_type="text"),
        TableColumn(name="trade_time", ordinal_position=6, data_type="text"),
        TableColumn(name="trade_database_time", ordinal_position=7, data_type="text"),
    ]
)
_default_trade_patcher = TradePatcher(
    exchange_id=49,
    market_type=MarketType.SPOT,
    diagnostics=Mock(),
    source_schema_name="public",
    source_connection_params=PgConnectionParams("localhost:9432:postgres:postgres:postgres"),
    target_connection_params=PgConnectionParams("localhost:8432:postgres:postgres:postgres"),
    target_schema_name="public",
    source_table_columns=TABLE_COLS,
    target_table_columns=TABLE_COLS,
)


def _csv_trade() -> str:
    instruments = [
        "ETHUSD",
        "ETHUSDT",
        "POLOUSDT",
        "BTCUSDT",
        "BTCUSD",
        "TRXUSDT",
    ]
    trade_id = Decimal("".join(random.choices(string.digits, k=32)))
    instrument = random.choice(instruments)
    return f"{trade_id},{instrument},252.00,0.063089,t,2022-10-31 00:00:00.013+00,2022-10-31 00:00:00.280248+00"


def test_copy_to_target(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    rows = list_to_buffer(LIST_OF_TRADES)
    trade_patcher = TradePatcher(
        exchange_id=EXCHANGE_ID,
        market_type=MarketType.SPOT,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_schema_name="public",
    )
    with postgres_connect(postgres_mexc_spot_trade_storage._connection_params) as conn:
        DBPatcher.copy_to_target(
            tgt_conn=conn,
            rows=rows,
            query=DBPatcher.target_query(trade_patcher._target_table_name, columns=TABLE_COLS),
            columns=TABLE_COLS,
        )
        trades = postgres_mexc_spot_trade_storage.get_trades(10000)
        assert_csv_rows_match_trades(row_list=LIST_OF_TRADES, trades=trades)


def test_copy_to_buffer(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    rows = list_to_buffer(LIST_OF_TRADES)
    trade_patcher = TradePatcher(
        exchange_id=EXCHANGE_ID,
        market_type=MarketType.SPOT,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_schema_name="public",
    )
    with postgres_connect(postgres_mexc_spot_trade_storage._connection_params) as conn:
        DBPatcher.copy_to_target(
            tgt_conn=conn,
            rows=rows,
            query=DBPatcher.target_query(table_name=trade_patcher._target_table_name, columns=TABLE_COLS),
            columns=TABLE_COLS,
        )
        query = psycopg2.sql.SQL("COPY (SELECT * FROM {}) TO STDOUT WITH CSV HEADER;").format(
            psycopg2.sql.Identifier(trade_patcher._target_table_name),
        )
        buffer = DBPatcher.copy_to_buffer(conn=conn, query=query)
        assert [i for i in buffer.read().split("\n") if i] == LIST_OF_TRADES


@freeze_time("2022-02-01")
def test_batch_datetimes() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=2, minutes=36)
    hours = [h for h in _default_trade_patcher._batch_datetimes(begin=begin, end=end)]
    assert hours == [
        (
            datetime.fromisoformat("2022-02-01T00:00:00"),
            datetime.fromisoformat("2022-02-01T01:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T01:00:00"),
            datetime.fromisoformat("2022-02-01T02:00:00"),
        ),
        (
            datetime.fromisoformat("2022-02-01T02:00:00"),
            datetime.fromisoformat("2022-02-01T02:36:00"),
        ),
    ]


def test_writes_per_second() -> None:
    wps = DBPatcher.writes_per_second(duration=10, copied_row_count=500)
    assert wps == 50


def test_copy_missing_rows_to_buffer() -> None:
    row_list = [LIST_OF_TRADES[0]] + (LIST_OF_TRADES[1:] * 1000)
    new_row_list = [
        "166717440011283224736870541012345,BTC4LUSDT,52.11,0.135,f,2022-10-31 00:00:00.112+00,2022-10-31 00:00:00.280248+00",
        "166717440012767006324003814803790,ETHUSDT,0.45949,1590.47,f,2022-10-31 00:00:00.127+00,2022-10-31 00:00:00.280248+00",
    ]
    target_rows = list_to_buffer(row_list)
    source_list = row_list + new_row_list
    source_rows = list_to_buffer(source_list)
    missing_rows = DBPatcher.copy_missing_rows_to_buffer(
        target_rows=target_rows,
        source_rows=source_rows,
        unique_key_from_row=DBPatcher.unique_trade_key_from_row,
        source_columns=TABLE_COLS,
        target_columns=TABLE_COLS,
    )
    assert [line.strip() for line in missing_rows.readlines()][1:] == new_row_list


@freeze_time("2022-11-14T22:17:18.689735")
def test_get_batch_end() -> None:
    begin = datetime.utcnow()
    end = begin + relativedelta(hours=11)
    batch_end_dates = [d.isoformat() for d in _default_trade_patcher._get_batch_end(begin=begin, end=end)]
    assert batch_end_dates == [
        "2022-11-14T23:00:00",
        "2022-11-15T00:00:00",
        "2022-11-15T01:00:00",
        "2022-11-15T02:00:00",
        "2022-11-15T03:00:00",
        "2022-11-15T04:00:00",
        "2022-11-15T05:00:00",
        "2022-11-15T06:00:00",
        "2022-11-15T07:00:00",
        "2022-11-15T08:00:00",
        "2022-11-15T09:00:00",
        "2022-11-15T09:17:18.689735",
    ]


def test_batch_rows() -> None:
    row_list = [LIST_OF_TRADES[0]] + (LIST_OF_TRADES[1:] * 1000)
    rows = list_to_buffer(row_list)
    batch_row_size = 500
    batch_results = list(DBPatcher.batch_rows(rows=rows, batch_row_size=batch_row_size))
    assert len(batch_results) == 20
    for batch in batch_results:
        lines = batch.read().splitlines()
        assert lines[0] == LIST_OF_TRADES[0]
        assert len(lines) == batch_row_size + 1


def test_copy_rows_between_to_buffer(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    rows = list_to_buffer(
        LIST_OF_TRADES
        + ["16671744001276700632400381480999,ETHUSDT,0.45949,1590.47,f,2022-11-02 00:00:00.127+00,2022-11-02 00:00:00.280248+00"]
    )
    rows.seek(0)
    trade_patcher = TradePatcher(
        exchange_id=EXCHANGE_ID,
        market_type=MarketType.SPOT,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_schema_name="public",
        source_table_columns=TABLE_COLS,
        target_table_columns=TABLE_COLS,
    )
    with postgres_connect(postgres_mexc_spot_trade_storage._connection_params) as conn:
        DBPatcher.copy_to_target(
            tgt_conn=conn,
            rows=rows,
            query=DBPatcher.target_query(table_name=trade_patcher._target_table_name, columns=TABLE_COLS),
            columns=TABLE_COLS,
        )
        begin = datetime(2022, 10, 31)
        end = datetime(2022, 11, 1)
        buffer = trade_patcher.copy_rows_between_to_buffer(
            conn=conn,
            table_name=trade_patcher._target_table_name,
            datetime_column="trade_time",
            begin=begin,
            end=end,
            schema_name="public",
            columns=TABLE_COLS,
        )
        assert [line.strip() for line in buffer.readlines()] == LIST_OF_TRADES


def test_batch_copy_to_target(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    rows = list_to_buffer(LIST_OF_TRADES)
    trade_patcher = TradePatcher(
        exchange_id=EXCHANGE_ID,
        market_type=MarketType.SPOT,
        diagnostics=Mock(),
        source_schema_name="public",
        source_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
        target_schema_name="public",
        batch_row_size=1,
        max_writes_per_second=1000000,
        copy_wait_time=0,
    )
    with postgres_connect(postgres_mexc_spot_trade_storage._connection_params) as conn:
        row_count, batch_count = DBPatcher.batch_copy_to_target(
            tgt_conn=conn,
            rows=rows,
            batch_row_size=trade_patcher._batch_row_size,
            copy_wait_time=trade_patcher._copy_wait_time,
            max_writes_per_second=trade_patcher._max_writes_per_second,
            table_name=trade_patcher._target_table_name,
            diagnostics=Mock(),
            columns=TABLE_COLS,
        )
        assert row_count == 10
        assert batch_count == 10


def test_calc_copy_wait_time() -> None:
    assert DBPatcher.calc_copy_wait_time(duration=10, copied_row_count=250, copy_wait_time=1, max_writes_per_second=500) == 0.95


copy_to_buffer = DBPatcher.copy_to_buffer


def _mock_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: psycopg2.sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        return list_to_buffer(LIST_OF_TRADES)
    else:
        return copy_to_buffer(conn, query)


def _mock_batch_copy_to_buffer(conn: Union[Mock, psycopg2.extensions.connection], query: psycopg2.sql.SQL) -> StringIO:
    if isinstance(conn, Mock):
        row_list = [LIST_OF_TRADES[0]] + [_csv_trade() for i in range(100)]
        return list_to_buffer(row_list)
    else:
        return copy_to_buffer(conn, query)


def test_patch_between(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    begin = datetime(2022, 10, 31)
    end = datetime(2022, 11, 1)
    mock_src_conn = Mock()
    with mock.patch("src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_copy_to_buffer):
        trade_patcher = TradePatcher(
            exchange_id=EXCHANGE_ID,
            market_type=MarketType.SPOT,
            diagnostics=Mock(),
            source_schema_name="public",
            source_connection_params=mock_src_conn,
            target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
            target_schema_name="public",
            batch_row_size=1,
            max_writes_per_second=1000000,
            copy_wait_time=0,
            source_table_columns=TABLE_COLS,
            target_table_columns=TABLE_COLS,
        )
        with postgres_connect(postgres_mexc_spot_trade_storage._connection_params) as conn:
            trade_patcher.patch_between(
                src_conn=mock_src_conn,
                tgt_conn=conn,
                begin=begin,
                end=end,
                commit=True,
            )
            trades = postgres_mexc_spot_trade_storage.get_trades(10000)
            assert_csv_rows_match_trades(row_list=LIST_OF_TRADES, trades=trades)


def test_batch_patch_between(postgres_mexc_spot_trade_storage: PostgresSpotTradeStorage) -> None:
    begin = datetime(2022, 10, 31)
    end = datetime(2022, 11, 1)
    with mock.patch(
        "src.octopus.storage.postgres.patch.db_patcher.DBPatcher.copy_to_buffer", side_effect=_mock_batch_copy_to_buffer
    ):
        with mock.patch(
            "src.octopus.storage.postgres.patch.db_patcher.postgres_connect",
            side_effect=_mock_postgres_connect,
        ):
            trade_patcher = TradePatcher(
                exchange_id=EXCHANGE_ID,
                market_type=MarketType.SPOT,
                diagnostics=Mock(),
                source_schema_name="public",
                source_connection_params=Mock(),
                target_connection_params=postgres_mexc_spot_trade_storage._connection_params,
                target_schema_name="public",
                batch_row_size=1,
                max_writes_per_second=1000000,
                copy_wait_time=0,
                source_table_columns=TABLE_COLS,
                target_table_columns=TABLE_COLS,
            )
            trade_patcher.batch_patch_between(begin=begin, end=end, commit=True)
            batch_count = len([i for i in trade_patcher._batch_datetimes(begin=begin, end=end)])
            # _mock_batch_copy_to_buffer returns 100 random csv trades per batch
            expected_row_count = batch_count * 100
            trades = postgres_mexc_spot_trade_storage.get_trades(100000)
            assert len(trades) == expected_row_count


def _mock_postgres_connect(params: PgConnectionParams) -> Union[Mock, psycopg2.extensions.connection]:
    if isinstance(params, Mock):
        mock_connect = Mock()
        mock_connect.__enter__ = Mock(return_value=Mock())
        mock_connect.__exit__ = Mock(return_value=None)
        return mock_connect
    else:
        return postgres_connect(params)
