from typing import Dict, List, Set, Tuple

from src.octopus.market_control import compute_bucket_delta


def test_compute_bucket_delta() -> None:
    def run_test(updates: List[Tuple[Set[int], Set[int], Set[int]]], bucket_sizes: List[int]) -> None:
        for bucket_size in bucket_sizes:
            bucket_index = 0
            buckets: Dict[int, Set[int]] = {}

            for additions, deletions, expected in updates:
                delta = compute_bucket_delta(buckets, additions, deletions, bucket_size)

                for delete_bucket in delta.delete_buckets:
                    del buckets[delete_bucket]

                for add_bucket in delta.add_buckets:
                    bucket_index += 1
                    buckets[bucket_index] = set(add_bucket)

                items = set()

                for _, bucket_items in buckets.items():
                    assert 0 < len(bucket_items) <= bucket_size, (len(bucket_items), bucket_size)

                    for item in bucket_items:
                        assert item not in items, item
                        items.add(item)

                assert items == expected, (items, expected)

    run_test(
        [
            ({1, 2, 3, 4, 5}, set(), {1, 2, 3, 4, 5}),
            ({6, 7}, {2, 3}, {1, 4, 5, 6, 7}),
            ({2}, {1, 4, 5}, {2, 6, 7}),
            ({1, 3, 5}, {2, 6}, {1, 3, 5, 7}),
            (set(), {3}, {1, 5, 7}),
            ({2}, {5, 7}, {1, 2}),
        ],
        [i + 1 for i in range(5)],
    )
