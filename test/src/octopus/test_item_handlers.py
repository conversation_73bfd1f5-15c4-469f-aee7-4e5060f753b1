from copy import deepcopy
from datetime import datetime, timedelta, timezone
from decimal import Decimal

import pytest

from src.octopus.data import BookData, BookType, PriceLevel
from src.octopus.item_handlers import QuoteItemHandler

book_data_1 = BookData(
    exchange_time=datetime(year=2021, month=11, day=1, tzinfo=timezone(timedelta(hours=3))),
    exchange_sequence_id=1,
    book_type=BookType.FULL,
    bids=[
        PriceLevel(price=Decimal("3.3"), amount=Decimal("1.2"), count=3),
        PriceLevel(price=Decimal("3.2"), amount=Decimal("1.2"), count=1),
    ],
    asks=[PriceLevel(price=Decimal("4.1"), amount=Decimal("2.1"), count=2)],
)

book_data_2 = BookData(
    exchange_time=datetime(year=2021, month=11, day=1, tzinfo=timezone(timedelta(hours=7))),
    exchange_sequence_id=23,
    book_type=BookType.FULL,
    bids=[PriceLevel(price=Decimal("3.3"), amount=Decimal("1.2"), count=3)],
    asks=[
        PriceLevel(price=Decimal("4.1"), amount=Decimal("2.1"), count=2),
        PriceLevel(price=Decimal("4.2"), amount=Decimal("2.1"), count=4),
    ],
)

book_data_3 = BookData(
    exchange_time=datetime(year=2021, month=11, day=1, tzinfo=timezone(timedelta(hours=2))),
    exchange_sequence_id=2,
    book_type=BookType.FULL,
    bids=[PriceLevel(price=Decimal("4.3"), amount=Decimal("1.2"), count=3)],
    asks=[PriceLevel(price=Decimal("5.1"), amount=Decimal("2.1"), count=2)],
)

book_data_4 = BookData(
    exchange_time=datetime(year=2021, month=11, day=1, tzinfo=timezone(timedelta(hours=2))),
    exchange_sequence_id=2,
    book_type=BookType.FULL,
    bids=[],
    asks=[PriceLevel(price=Decimal("5.1"), amount=Decimal("2.1"), count=2)],
)

book_data_5 = BookData(
    exchange_time=datetime(year=2021, month=11, day=1, tzinfo=timezone(timedelta(hours=2))),
    exchange_sequence_id=2,
    book_type=BookType.FULL,
    bids=[],
    asks=[PriceLevel(price=Decimal("5.1"), amount=Decimal("2.0"), count=2)],
)

book_data_6 = deepcopy(book_data_5)


@pytest.mark.parametrize(
    "book1,book2,expected",
    [
        (book_data_1, book_data_2, True),  # book_data_1 != book_data_2, but their quotes are equal
        (book_data_1, book_data_3, False),  # book_data_1 and book_data_3 are different books
        (book_data_4, book_data_5, False),
        (book_data_5, book_data_6, True),
    ],
)
def test_are_quotes_equal(book1: BookData, book2: BookData, expected: bool) -> None:
    quote_item_handler = QuoteItemHandler()
    assert expected == quote_item_handler._are_quotes_equal(book1, book2)
