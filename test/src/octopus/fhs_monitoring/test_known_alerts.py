from datetime import datetime, timezone

import pytest

from src.octopus.inventory.types import DataType
from src.octopus.monitoring.alerts.known_alerts import is_suspended_due_to_schedule
from src.octopus.monitoring.alerts.models import Alert, AlertPriority
from src.octopus.monitoring.errors import MonitoringError
from src.octopus.monitoring.models import MonitoringLevel


def _alert(container_name) -> Alert:
    return Alert(
        data_type=DataType.TRADE.value,
        container_name=container_name,
        error_type=MonitoringError,
        message="",
        error_level=MonitoringLevel.PROMETHEUS_METRICS,
        priority=AlertPriority.HIGH,
    )


@pytest.mark.parametrize(
    "alert_time,alert,is_suspended",
    (
        (
            datetime(year=2023, month=6, day=13, hour=20, minute=59, tzinfo=timezone.utc),
            _alert("feed-handler-binance-trad-futu-hist-1"),
            False,
        ),
        (
            datetime(year=2023, month=6, day=17, hour=20, minute=59, tzinfo=timezone.utc),
            _alert("feed-handler-binance-trad-futu-hist-1"),
            False,
        ),
        (
            datetime(year=2023, month=6, day=13, hour=20, minute=59, tzinfo=timezone.utc),
            _alert("feed-handler-deribit-trad-opti-stre-1"),
            False,
        ),
        (
            datetime(year=2023, month=6, day=13, hour=20, minute=59, tzinfo=timezone.utc),
            _alert("feed-handler-cme-trad-futu-stre-1"),
            False,
        ),
        (
            datetime(year=2023, month=6, day=16, hour=20, minute=59, tzinfo=timezone.utc),
            _alert("feed-handler-cme-trad-futu-stre-1"),
            False,
        ),
        (
            datetime(year=2023, month=6, day=16, hour=21, minute=0, tzinfo=timezone.utc),
            _alert("feed-handler-cme-trad-futu-stre-1"),
            True,
        ),
        (
            datetime(year=2023, month=6, day=17, hour=12, minute=0, tzinfo=timezone.utc),
            _alert("feed-handler-cme-trad-futu-stre-1"),
            True,
        ),
        (
            datetime(year=2023, month=6, day=18, hour=22, minute=0, tzinfo=timezone.utc),
            _alert("feed-handler-cme-trad-futu-stre-1"),
            False,
        ),
    ),
)
def test_schedule(alert: Alert, alert_time: datetime, is_suspended: bool) -> None:
    assert is_suspended_due_to_schedule(alert, alert_time) == is_suspended
