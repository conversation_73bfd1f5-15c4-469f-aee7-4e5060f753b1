import json
from datetime import UTC, datetime
from decimal import Decimal

from freezegun import freeze_time  # type: ignore

from src.octopus.data import Book, BookData, BookType, Instrument, Market, PriceLevel, Trade, TradeData
from src.octopus.streaming_protocols.kafka_message import json_message_from_quote, json_message_from_trade
from src.utils.timeutil import dt_to_us


def test_json_message_from_trade() -> None:
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    result_message = json_message_from_trade(
        Trade(Market(4, Instrument.futures("BTCUSDT")), TradeData(0, Decimal("0.1"), Decimal("0.11"), True, reference_time))
    )

    result_data = json.loads(result_message)
    assert result_data["symbol"] == "BTCUSDT"
    assert result_data["exchange_id"] == 4
    assert result_data["exchange_time"] == dt_to_us(reference_time)
    assert result_data["market_type"] == "futures"
    assert result_data["amount"] == "0.1"
    assert result_data["price"] == "0.11"
    assert result_data["trade_id"] == "0"
    assert result_data["direction"] == "buy"
    assert "implied_vol" not in result_data
    assert "mark_price" not in result_data
    assert "index_price" not in result_data


@freeze_time("2022-01-01")
def test_json_message_from_quote() -> None:
    exchange_id = 9
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    instrument = Instrument.spot("BTCUSDK", "btc", "usdk")
    ask_price = Decimal("57018.8")
    ask_amount = Decimal("0.00136043")
    ask_count = None
    bid_price = Decimal("57010.6")
    bid_amount = Decimal("0.00103643")
    bid_count = 2
    result_message = json_message_from_quote(
        Book(
            market=Market(exchange_id=exchange_id, instrument=instrument),
            depth_limit=1,
            scraper_sequence_id=0,
            scraper_session_id=1638349845274093,
            collect_time=reference_time,
            deduplication_time=reference_time,
            data=BookData(
                asks=[PriceLevel(price=ask_price, amount=ask_amount, count=ask_count)],
                bids=[PriceLevel(price=bid_price, amount=bid_amount, count=bid_count)],
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=reference_time,
            ),
        )
    )

    result_data = json.loads(result_message)
    assert result_data["symbol"] == instrument.symbol
    assert result_data["exchange_id"] == exchange_id
    assert result_data["exchange_time"] == dt_to_us(reference_time)
    assert result_data["market_type"] == instrument.market_type.name.lower()
    assert result_data["base_id"] == instrument.base
    assert result_data["quote_id"] == instrument.quote
    assert result_data["ask_price"] == str(ask_price)
    assert result_data["ask_amount"] == str(ask_amount)
    assert result_data["bid_price"] == str(bid_price)
    assert result_data["bid_amount"] == str(bid_amount)
    assert result_data["bid_count"] == str(bid_count)
    assert result_data["feed_handler_session_id"] == 1638349845274093
    assert result_data["feed_handler_receive_time"] == 1640995200000000
    assert "exchange_sequence_id" not in result_data
    assert "ask_count" not in result_data


def test_json_message_from_quote_empty_asks_should_work() -> None:
    exchange_id = 9
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    instrument = Instrument.spot("BTCUSDK", "btc", "usdk")
    bid_price = Decimal("57010.6")
    bid_amount = Decimal("0.00103643")
    bid_count = 2
    result_message = json_message_from_quote(
        Book(
            market=Market(exchange_id=exchange_id, instrument=instrument),
            depth_limit=1,
            scraper_sequence_id=0,
            scraper_session_id=1638349845274093,
            collect_time=reference_time,
            deduplication_time=reference_time,
            data=BookData(
                asks=[],
                bids=[PriceLevel(price=bid_price, amount=bid_amount, count=bid_count)],
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=reference_time,
            ),
        )
    )
    result_data = json.loads(result_message)
    assert "exchange_sequence_id" not in result_data
    assert "ask_price" not in result_data
    assert "ask_amount" not in result_data
    assert "ask_count" not in result_data


def test_json_message_from_quote_empty_bids_should_work() -> None:
    exchange_id = 9
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    instrument = Instrument.spot("BTCUSDK", "btc", "usdk")
    ask_price = Decimal("57018.8")
    ask_amount = Decimal("0.00136043")
    ask_count = None
    result_message = json_message_from_quote(
        Book(
            market=Market(exchange_id=exchange_id, instrument=instrument),
            depth_limit=1,
            scraper_sequence_id=0,
            scraper_session_id=1638349845274093,
            collect_time=reference_time,
            deduplication_time=reference_time,
            data=BookData(
                asks=[PriceLevel(price=ask_price, amount=ask_amount, count=ask_count)],
                bids=[],
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=reference_time,
            ),
        )
    )

    result_data = json.loads(result_message)
    assert "exchange_sequence_id" not in result_data
    assert "bid_price" not in result_data
    assert "bid_amount" not in result_data
    assert "bid_count" not in result_data
