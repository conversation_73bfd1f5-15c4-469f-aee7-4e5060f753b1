from datetime import UTC, datetime
from decimal import Decimal

from src.octopus.data import Book, BookData, BookType, Instrument, Market, PriceLevel, Trade, TradeData
from src.octopus.streaming_protocols.protobuf import (
    BOOK_MARKET_TYPE_FUTURES,
    MARKET_TYPE_FUTURES,
    TRADE_DIRECTION_BUY,
    message_from_book,
    message_from_trade,
)
from src.utils.timeutil import dt_to_us


def test_message_from_trade_futures() -> None:
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    result_message = message_from_trade(
        Trade(Market(4, Instrument.futures("BTCUSDT")), TradeData(0, Decimal("0.1"), Decimal("0.11"), True, reference_time))
    )
    assert result_message.symbol == "BTCUSDT"
    assert result_message.exchange_time == dt_to_us(reference_time)
    assert result_message.market_type == MARKET_TYPE_FUTURES
    assert result_message.amount == "0.1"
    assert result_message.price == "0.11"
    assert result_message.id == "0"
    assert result_message.base_id == -1
    assert result_message.quote_id == -1
    assert result_message.buy == TRADE_DIRECTION_BUY


def test_message_from_book_futures() -> None:
    reference_time = datetime(2021, 1, 3, 2, 2, 1, 234, tzinfo=UTC)
    result_message = message_from_book(
        Book(
            Market(4, Instrument.futures("BTCUSDT")),
            100,
            reference_time,
            reference_time,
            1,
            0,
            BookData(
                BookType.FULL,
                1,
                None,
                [
                    PriceLevel(price=Decimal("2.0"), amount=Decimal("1.1")),
                ],
                [
                    PriceLevel(price=Decimal("2.1"), amount=Decimal("0.31")),
                    PriceLevel(price=Decimal("2.2"), amount=Decimal("0.36")),
                ],
            ),
        ),
        False,
    )
    assert result_message.symbol == "BTCUSDT"
    assert result_message.scraper_receive_time == dt_to_us(reference_time)
    assert result_message.market_type == BOOK_MARKET_TYPE_FUTURES
