from typing import Dict, List, Optional

import pytest

from src.octopus.applications.proxies.groups import PROXY_GROUPS
from src.octopus.arguments import get_exclusive_proxies_for_machine, get_proxies_total
from src.utils.http import ProxyParams
from src.utils.proxies import get_segment_proxies

PROXIES_1 = ["1"]
PROXIES_3 = [str(i) for i in range(1, 4)]
PROXIES_7 = [str(i) for i in range(1, 8)]
PROXIES_8 = [str(i) for i in range(1, 9)]
PROXIES_99 = [str(i) for i in range(1, 100)]


@pytest.mark.parametrize(
    "proxies,segment_number,segments_count,res",
    (
        (PROXIES_1, 1, 3, PROXIES_1),
        (PROXIES_3, 1, 3, PROXIES_3[0:3]),
        (PROXIES_3, 2, 3, PROXIES_3[0:3]),
        (PROXIES_3, 3, 3, PROXIES_3[0:3]),
        (PROXIES_7, 1, 3, PROXIES_7[0:3]),
        (PROXIES_7, 2, 3, PROXIES_7[3:5]),
        (PROXIES_7, 3, 3, PROXIES_7[5:7]),
        (PROXIES_8, 1, 3, PROXIES_8[0:3]),
        (PROXIES_8, 2, 3, PROXIES_8[3:6]),
        (PROXIES_8, 3, 3, PROXIES_8[6:8]),
        (PROXIES_99, 1, 200, PROXIES_99[0:3]),
        (PROXIES_99, 200, 200, PROXIES_99[97:99]),
    ),
)
def test_proxies_segments(proxies: List[str], segment_number: int, segments_count: int, res: List[str]) -> None:
    assert get_segment_proxies(proxies, segment_number, segments_count) == res


@pytest.mark.parametrize(
    "proxies,all_proxy_groups,proxy_groups_keys,excluded,instance_number,expected_result",
    (
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
            ],
            {},
            [],
            [],
            1,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:user:pass:1.1.1.3:5555",
                ]
            ],
        ),
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.4:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.5:5555"),
            ],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
            },
            [["first"]],
            [],
            1,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.1:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:user:pass:1.1.1.3:5555",
                    "http:first:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.5:5555",
                ]
            ],
        ),
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.4:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.5:5555"),
            ],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first", "second"]],
            [],
            1,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.1:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:user:pass:1.1.1.3:5555",
                    "http:first:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.4:5555",
                    "http:second:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.5:5555",
                ]
            ],
        ),
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.4:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.5:5555"),
            ],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first", "second"]],
            ["http:first:pass:1.1.1.3:5555"],
            1,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.1:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:user:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.4:5555",
                    "http:second:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.5:5555",
                ]
            ],
        ),
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.4:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.5:5555"),
            ],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first", "second[2/2]"]],
            ["http:first:pass:1.1.1.3:5555"],
            1,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.5:5555",
                ]
            ],
        ),
        (
            [
                ProxyParams.from_string("http:user:pass:1.1.1.1:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.2:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.3:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.4:5555"),
                ProxyParams.from_string("http:user:pass:1.1.1.5:5555"),
            ],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first", "second[2/2]"]],
            ["http:first:pass:1.1.1.3:5555"],
            2,
            [
                (1, ProxyParams.from_string(proxy_str))
                for proxy_str in [
                    "http:user:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.1:5555",
                    "http:user:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:user:pass:1.1.1.3:5555",
                    "http:user:pass:1.1.1.4:5555",
                    "http:user:pass:1.1.1.5:5555",
                ]
            ],
        ),
        (
            [],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first"], ["second"]],
            [],
            2,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
        ),
        (
            [],
            {
                "first": [
                    "http:first:pass:1.1.1.1:5555",
                    "http:first:pass:1.1.1.2:5555",
                    "http:first:pass:1.1.1.3:5555",
                ],
                "second": [
                    "http:second:pass:1.1.1.1:5555",
                    "http:second:pass:1.1.1.2:5555",
                    "http:second:pass:1.1.1.3:5555",
                    "http:second:pass:1.1.1.4:5555",
                ],
            },
            [["first"], ["second"]],
            [],
            2,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
        ),
    ),
)
def test_get_proxies_total(
    proxies: List[ProxyParams],
    all_proxy_groups: Dict[str, List[str]],
    proxy_groups_keys: Optional[List[List[Optional[str]]]],
    excluded: List[str],
    instance_number: int,
    expected_result: List[ProxyParams],
) -> None:
    PROXY_GROUPS.update(all_proxy_groups)
    proxies = get_proxies_total(proxies, proxy_groups_keys, excluded, instance_number=instance_number)
    assert proxies == expected_result


@pytest.mark.parametrize(
    "proxies,instance_number,expected_result",
    (
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
            ],
            1,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
            ],
        ),
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
            ],
            2,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
            ],
        ),
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
            1,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
            ],
        ),
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
            2,
            [
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
        ),
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
            1,
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
            ],
        ),
        (
            [
                (1, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.1:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
            2,
            [
                (1, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (1, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (1, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.2:5555")),
                (2, ProxyParams.from_string("http:first:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.3:5555")),
                (2, ProxyParams.from_string("http:second:pass:1.1.1.4:5555")),
            ],
        ),
    ),
)
def test_exclusive_proxies(
    proxies: List[ProxyParams],
    instance_number: int,
    expected_result: List[ProxyParams],
):
    proxies = get_exclusive_proxies_for_machine(proxies, instance_number=instance_number)
    assert proxies == expected_result
