from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import List

import pytest

from src.octopus.data import Book, BookData, BookType, Instrument, Market, MarketType, PriceLevel, TradeData
from src.octopus.utils import (
    compress_price_levels,
    compute_delta_between_books,
    get_books_separated_by,
    trim_last_timestamp_entries_if_recent,
)
from src.utils.diagnostics import NullCounter


def test_full_to_l2() -> None:
    full = [
        PriceLevel(price=Decimal(2.5), amount=Decimal(1), count=1),
        PriceLevel(price=Decimal(2.5), amount=Decimal(2), count=1),
        PriceLevel(price=Decimal(3), amount=Decimal(3), count=1),
    ]
    assert compress_price_levels(full, 100) == [
        PriceLevel(price=Decimal(2.5), amount=Decimal(3), count=2),
        PriceLevel(price=Decimal(3), amount=Decimal(3), count=1),
    ]

    full = [
        PriceLevel(price=Decimal(3), amount=Decimal(3), count=1),
        PriceLevel(price=Decimal(2.5), amount=Decimal(1), count=1),
        PriceLevel(price=Decimal(2.5), amount=Decimal(2), count=2),
    ]
    assert compress_price_levels(full, 100) == [
        PriceLevel(price=Decimal(3), amount=Decimal(3), count=1),
        PriceLevel(price=Decimal(2.5), amount=Decimal(3), count=3),
    ]

    full = [
        PriceLevel(price=Decimal(3), amount=Decimal(3)),
        PriceLevel(price=Decimal(2.5), amount=Decimal(1)),
        PriceLevel(price=Decimal(2.5), amount=Decimal(2)),
        PriceLevel(price=Decimal(2.0), amount=Decimal(2)),
    ]
    assert compress_price_levels(full, 100) == [
        PriceLevel(price=Decimal(3), amount=Decimal(3)),
        PriceLevel(price=Decimal(2.5), amount=Decimal(3)),
        PriceLevel(price=Decimal(2.0), amount=Decimal(2)),
    ]


def test_get_books_separated_by() -> None:
    depth = 100
    market_a = Market(0, Instrument(0, 3, MarketType.SPOT))
    market_b = Market(0, Instrument(0, 6, MarketType.SPOT))

    full_data = BookData(BookType.FULL, None, None, [], [])
    delta_data = BookData(BookType.DELTA, None, None, [], [])

    timestamp_1 = datetime(2009, 1, 1, 0, 0, 10, tzinfo=UTC)
    timestamp_2 = datetime(2009, 1, 1, 0, 0, 1, tzinfo=UTC)
    timestamp_3 = datetime(2009, 1, 1, 0, 0, 8, tzinfo=UTC)
    timestamp_4 = datetime(2009, 1, 1, 0, 0, 5, tzinfo=UTC)
    timestamp_5 = datetime(2009, 1, 1, 0, 0, 6, tzinfo=UTC)
    books = [
        Book(market_a, depth, timestamp_1, timestamp_1, 0, 0, full_data),
        Book(market_b, depth, timestamp_2, timestamp_2, 0, 0, full_data),
        Book(market_a, depth, timestamp_3, timestamp_3, 0, 0, full_data),
        Book(market_a, depth, timestamp_4, timestamp_4, 0, 0, full_data),
        Book(market_b, depth, timestamp_5, timestamp_5, 0, 0, delta_data),
    ]

    result = get_books_separated_by(
        books, timedelta(seconds=5), {}, lambda book: book.data.book_type == BookType.FULL, NullCounter()
    )

    assert len(result) == 3
    assert result[0].collect_time == timestamp_4
    assert result[1].collect_time == timestamp_1
    assert result[2].collect_time == timestamp_2

    result = get_books_separated_by(
        books,
        timedelta(seconds=5),
        {market_a: datetime(2009, 1, 1, 0, 0, 4, tzinfo=UTC)},
        lambda book: True,
        NullCounter(),
    )

    assert len(result) == 4
    assert result[0].collect_time == timestamp_4
    assert result[1].collect_time == timestamp_1
    assert result[2].collect_time == timestamp_2
    assert result[3].collect_time == timestamp_5


def test_compute_delta_between_books() -> None:
    delta = compute_delta_between_books(
        BookData(
            BookType.FULL,
            None,
            None,
            [PriceLevel(price=Decimal(2), amount=Decimal(2)), PriceLevel(price=Decimal(1), amount=Decimal(1))],
            [PriceLevel(price=Decimal(10), amount=Decimal(10))],
        ),
        BookData(
            BookType.FULL,
            None,
            None,
            [PriceLevel(price=Decimal(3), amount=Decimal(3)), PriceLevel(price=Decimal(1), amount=Decimal(2))],
            [PriceLevel(price=Decimal(10), amount=Decimal(10))],
        ),
    )

    assert delta == BookData(
        BookType.DELTA,
        None,
        None,
        [
            PriceLevel(price=Decimal(3), amount=Decimal(3)),
            PriceLevel(price=Decimal(2), amount=Decimal(0)),
            PriceLevel(price=Decimal(1), amount=Decimal(2)),
        ],
        [],
    )

    delta = compute_delta_between_books(
        BookData(
            BookType.FULL,
            None,
            None,
            [PriceLevel(price=Decimal(10), amount=Decimal(10))],
            [PriceLevel(price=Decimal(1), amount=Decimal(1)), PriceLevel(price=Decimal(2), amount=Decimal(2))],
        ),
        BookData(
            BookType.FULL,
            None,
            None,
            [PriceLevel(price=Decimal(10), amount=Decimal(10))],
            [PriceLevel(price=Decimal(1), amount=Decimal(2)), PriceLevel(price=Decimal(3), amount=Decimal(3))],
        ),
    )

    assert delta == BookData(
        BookType.DELTA,
        None,
        None,
        [],
        [
            PriceLevel(price=Decimal(1), amount=Decimal(2)),
            PriceLevel(price=Decimal(2), amount=Decimal(0)),
            PriceLevel(price=Decimal(3), amount=Decimal(3)),
        ],
    )


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        ([timedelta(seconds=2), timedelta(seconds=1)], 1),
        ([timedelta(seconds=2), timedelta(seconds=1), timedelta(seconds=1)], 1),
        ([timedelta(minutes=32), timedelta(minutes=31)], 2),
    ],
)
def test_trim_last_timestamp_entries_if_recent(test_input: List[timedelta], expected_result: int) -> None:
    now = datetime.now(UTC)
    test_list = [TradeData(0, Decimal(0), Decimal(0), True, now - delta) for delta in test_input]
    assert len(trim_last_timestamp_entries_if_recent(test_list, lambda t: t.time)) == expected_result
