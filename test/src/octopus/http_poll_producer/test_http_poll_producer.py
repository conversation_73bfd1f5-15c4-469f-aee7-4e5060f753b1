import datetime
from datetime import UTC
from decimal import Decimal
from typing import List, Optional

from dateutil.relativedelta import relativedelta
from freezegun import freeze_time  # type: ignore

from src.octopus.data import Instrument, Market, Trade, TradeData
from src.octopus.http_producer import HttpPollProducer
from src.utils.timeutil import dt_to_aware

FROZEN_TIME = "2023-01-01 00:00:10"


def _get_trade_batch(start: datetime.datetime, tzinfo: Optional[datetime.tzinfo] = None) -> List[Trade]:
    instrument = Instrument.spot("BTC-USD", "btc", "usd")
    market = Market(exchange_id=1, instrument=instrument)
    batch = []
    for i in range(0, 3):
        trade_time = start + relativedelta(seconds=1)
        assert trade_time.tzinfo == tzinfo
        batch.append(
            Trade(
                market=market,
                data=TradeData(
                    trade_id=i,
                    is_buy=True,
                    amount=Decimal("1"),
                    price=Decimal("1"),
                    time=trade_time,
                ),
            )
        )
    return batch


@freeze_time(FROZEN_TIME)
def test_get_trade_lag_seconds() -> None:
    start = dt_to_aware(datetime.datetime.now(UTC)) - relativedelta(seconds=10)
    batch = _get_trade_batch(start=start, tzinfo=UTC)
    assert HttpPollProducer._get_trade_lag_seconds(batch) == 9.0


@freeze_time(FROZEN_TIME)
def test_get_trade_lag_seconds_when_first_entry_is_latest() -> None:
    start = dt_to_aware(datetime.datetime.now(UTC)) - relativedelta(seconds=10)
    batch = _get_trade_batch(start=start, tzinfo=UTC)
    batch.reverse()
    assert HttpPollProducer._get_trade_lag_seconds(batch) == 9.0


@freeze_time(FROZEN_TIME)
def test_get_trade_lag_seconds_when_trade_times_are_naive() -> None:
    start = datetime.datetime.now(UTC) - relativedelta(seconds=10)
    batch = _get_trade_batch(start=start, tzinfo=UTC)
    assert HttpPollProducer._get_trade_lag_seconds(batch) == 9.0
