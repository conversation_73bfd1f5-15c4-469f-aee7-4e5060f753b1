from src.octopus.generators.deployment.yaml_dumper import gen_yaml_content


def test_gen_yaml_content_from_dict() -> None:
    target_dict = {
        "version": "3.7",
        "services": {
            "service1": {"image": "some_image", "entrypoint": "line1\nline2\nline3", "networks": ["network1", "network2"]},
            "service2": {"image": "some_image", "entrypoint": "line1\nline2\nline3", "networks": ["network1", "network2"]},
        },
        "networks": {
            "network1": {
                "driver": "bridge",
            },
            "network2": {"driver": "overlay", "external": True},
        },
    }
    expected = """
version: 3.7

services:
  service1:
    image: some_image
    entrypoint: line1
      line2
      line3
    networks: [network1, network2]

  service2:
    image: some_image
    entrypoint: line1
      line2
      line3
    networks: [network1, network2]


networks:
  network1:
    driver: bridge
  network2:
    driver: overlay
    external: True
""".strip()

    assert gen_yaml_content(target_dict) == expected


def test_gen_yaml_content_from_list() -> None:
    target_dict = [
        {
            "hosts": {
                "tasks": [
                    {"task1": "task_name", "param1": {"sub_param_1": "val", "sub_param_2": "val"}},
                    {"task2": "task_name_2", "param1": {"sub_param_1": "val", "sub_param_2": "val"}},
                ]
            }
        }
    ]

    expected = """
- hosts:
    tasks:
    - task1: task_name
      param1:
        sub_param_1: val
        sub_param_2: val
    - task2: task_name_2
      param1:
        sub_param_1: val
        sub_param_2: val
""".strip()

    assert gen_yaml_content(target_dict) == expected
