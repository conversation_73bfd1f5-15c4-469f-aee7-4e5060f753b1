from decimal import Decimal
from http import HTTPStatus

from src.octopus.data import BookData, BookType, Instrument, PriceLevel, TradeData
from src.octopus.exchange.bitstamp import EXCHANGE_NAME
from src.octopus.qa_utils import (
    MessageReplayHttpClient,
    standard_book_stream_test,
    standard_last_trades_test,
    standard_quote_stream_test,
    standard_trade_stream_test,
)
from src.utils.http import HttpResponse
from src.utils.timeutil import dt_from_us, dt_from_us_aware

BTC_USD = Instrument.spot("btcusd", "btc", "usd")
BTC_EUR = Instrument.spot("btceur", "btc", "eur")


def test_last_trades() -> None:
    standard_last_trades_test(
        EXCHANGE_NAME,
        BTC_USD,
        [
            TradeData(105693177, Decimal("0.00530539"), Decimal("9721.19"), False, dt_from_us(1582290500000000)),
            TradeData(105693179, Decimal("0.01406307"), Decimal("9731.51"), True, dt_from_us(1582290500000000)),
        ],
    )


def test_trade_stream() -> None:
    messages = """
        {"event":"bts:subscription_succeeded","channel":"live_trades_btcusd","data":{}}
        {"event":"bts:subscription_succeeded","channel":"live_trades_btceur","data":{}}
        {"data": {"buy_order_id": 1201993825599488, "amount_str": "0.00530539", "timestamp": "1582290500", "microtimestamp": "1582290500319000", "id": 105693177, "amount": 0.00530539, "sell_order_id": 1201993833111552, "price_str": "9721.19", "type": 1, "price": 9721.19}, "event": "trade", "channel": "live_trades_btcusd"}
        {"data": {"buy_order_id": 1201993925541888, "amount_str": "0.02199811", "timestamp": "1582290522", "microtimestamp": "1582290522885000", "id": 105693193, "amount": 0.02199811, "sell_order_id": 1201993924624384, "price_str": "9023.90", "type": 0, "price": 9023.9}, "event": "trade", "channel": "live_trades_btceur"}

        {"event":"bts:request_reconnect","data":{}}

        {"event":"bts:subscription_succeeded","channel":"live_trades_btcusd","data":{}}
        {"event":"bts:subscription_succeeded","channel":"live_trades_btceur","data":{}}
        {"data": {"buy_order_id": 1201993835368448, "amount_str": "0.01406307", "timestamp": "1582290500", "microtimestamp": "1582290500870000", "id": 105693179, "amount": 0.01406307, "sell_order_id": 1201993827840000, "price_str": "9731.51", "type": 0, "price": 9731.51}, "event": "trade", "channel": "live_trades_btcusd"}
        {"data": {"buy_order_id": 1201993894969344, "amount_str": "0.01466500", "timestamp": "1582290516", "microtimestamp": "1582290516204000", "id": 105693189, "amount": 0.014665, "sell_order_id": 1201993898176512, "price_str": "9011.06", "type": 1, "price": 9011.06}, "event": "trade", "channel": "live_trades_btceur"}
    """  # noqa: E501

    expected_trades = [
        (BTC_USD, TradeData(105693177, Decimal("0.00530539"), Decimal("9721.19"), False, dt_from_us(1582290500319000))),
        (BTC_EUR, TradeData(105693193, Decimal("0.02199811"), Decimal("9023.90"), True, dt_from_us(1582290522885000))),
        (BTC_USD, TradeData(105693179, Decimal("0.01406307"), Decimal("9731.51"), True, dt_from_us(1582290500870000))),
        (BTC_EUR, TradeData(105693189, Decimal("0.01466500"), Decimal("9011.06"), False, dt_from_us(1582290516204000))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USD, BTC_EUR], messages, expected_trades, expected_connect_count=2)


def test_book_spot_stream() -> None:
    messages = """
        {"data": {"timestamp": "1661334767", "microtimestamp": "1661334767073758", "bids": [], "asks": [["21329", "2.78425859"], ["21331", "0.95311416"]]}, "channel": "diff_order_book_btcusd", "event": "data"}
        {"data": {"timestamp": "1661334769", "microtimestamp": "1661334769484950", "bids": [["21311", "0.00000000"], ["21310", "0.46925922"], ["21309", "0.00000000"], ["21307", "0.12300000"]], "asks": [["21320", "1.24082126"], ["21321", "0.84975550"], ["21322", "5.55730000"]]}, "channel": "diff_order_book_btcusd", "event": "data"}
    """  # noqa: E501
    snapshot_body = """{"timestamp": "1661334767", "microtimestamp": "1661334767073758", "bids": [["21311", "0.46923918"], ["21309", "0.15000000"], ["21308", "0.15000000"]], "asks": [["21321", "1.31877987"], ["21322", "5.79128855"], ["21323", "1.79168102"]]}"""  # noqa: E501
    http_client = MessageReplayHttpClient([HttpResponse(HTTPStatus.OK, snapshot_body)])

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1661334769484950),
        bids=[
            PriceLevel(price=Decimal("21310"), amount=Decimal("0.46925922")),
            PriceLevel(price=Decimal("21308"), amount=Decimal("0.15000000")),
            PriceLevel(price=Decimal("21307"), amount=Decimal("0.12300000")),
        ],
        asks=[
            PriceLevel(price=Decimal("21320"), amount=Decimal("1.24082126")),
            PriceLevel(price=Decimal("21321"), amount=Decimal("0.84975550")),
            PriceLevel(price=Decimal("21322"), amount=Decimal("5.55730000")),
            PriceLevel(price=Decimal("21323"), amount=Decimal("1.79168102")),
        ],
    )
    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USD,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
        http_client=http_client,
    )


def test_book_spot_stream_missing_asks() -> None:
    messages = """
        {"data": {"timestamp": "1661334767", "microtimestamp": "1661334767073758", "bids": [], "asks": [["21329", "2.78425859"], ["21331", "0.95311416"]]}, "channel": "diff_order_book_btcusd", "event": "data"}
        {"data": {"timestamp": "1661334769", "microtimestamp": "1661334769484950", "bids": [["21311", "0.00000000"], ["21310", "0.46925922"], ["21309", "0.00000000"], ["21307", "0.12300000"]], "asks": [["21320", "1.24082126"], ["21321", "0.84975550"], ["21322", "5.55730000"]]}, "channel": "diff_order_book_btcusd", "event": "data"}
    """  # noqa: E501
    snapshot_body = """{"timestamp": "1661334767", "microtimestamp": "1661334767073758", "bids": [["21311", "0.46923918"], ["21309", "0.15000000"], ["21308", "0.15000000"]]}"""  # noqa: E501
    http_client = MessageReplayHttpClient([HttpResponse(HTTPStatus.OK, snapshot_body)])

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1661334769484950),
        bids=[
            PriceLevel(price=Decimal("21310"), amount=Decimal("0.46925922")),
            PriceLevel(price=Decimal("21308"), amount=Decimal("0.15000000")),
            PriceLevel(price=Decimal("21307"), amount=Decimal("0.12300000")),
        ],
        asks=[
            PriceLevel(price=Decimal("21320"), amount=Decimal("1.24082126")),
            PriceLevel(price=Decimal("21321"), amount=Decimal("0.84975550")),
            PriceLevel(price=Decimal("21322"), amount=Decimal("5.55730000")),
        ],
    )
    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USD,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
        http_client=http_client,
    )


def test_quote_spot_standard_test_without_updates() -> None:
    msg = """
        {"data": {"timestamp": "1656072301", "microtimestamp": "1656072301797175", "bids": [["21108.76", "0.26000000"], ["21107.71", "0.27650835"], ["21107.70", "0.26000000"]], "asks": [["21126.93", "0.10400000"], ["21128.30", "0.73806234"], ["21128.31", "0.47287763"]]}, "channel": "order_book_btcusd", "event": "data"}
        {"data": {"timestamp": "1656072301", "microtimestamp": "1656072301902510", "bids": [["21108.77", "0.02000000"], ["21108.76", "0.36400000"], ["21107.71", "0.27650835"]], "asks": [["21126.93", "0.10400000"], ["21128.30", "0.73806234"], ["21130.29", "0.38500374"]]}, "channel": "order_book_btcusd", "event": "data"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1656072301902510),
        bids=[PriceLevel(price=Decimal("21108.77"), amount=Decimal("0.02000000"))],
        asks=[PriceLevel(price=Decimal("21126.93"), amount=Decimal("0.10400000"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)


def test_quote_zero_quote_must_be_saved() -> None:
    msg = """
        {"data": {"timestamp": "1656072301", "microtimestamp": "1656072301797175", "bids": [["21108.76", "0.26000000"], ["21107.71", "0.27650835"], ["21107.70", "0.26000000"]], "asks": [["21126.93", "0.10400000"], ["21128.30", "0.73806234"], ["21128.31", "0.47287763"]]}, "channel": "order_book_btcusd", "event": "data"}
        {"data": {"timestamp": "1656072301", "microtimestamp": "1656072301902510", "bids": [], "asks": []}, "channel": "order_book_btcusd", "event": "data"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1656072301902510),
        bids=[],
        asks=[],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)
