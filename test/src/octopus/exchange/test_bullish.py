from decimal import Decimal
from typing import Any, Dict, List
from unittest import mock

import pytest

from src.octopus.data import (
    BookData,
    BookDataTolerateCrossedBook,
    BookType,
    FuturesContractData,
    Instrument,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.bullish import EXCHANGE_NAME
from src.octopus.qa_utils import standard_book_stream_test, standard_book_test, standard_trade_stream_test
from src.utils.timeutil import dt_from_any_aware

BTC_USDC = Instrument.spot("BTCUSDC", "btc", "usdc", alt_symbol="BTCUSDC")
ETH_USDC = Instrument.spot("ETHUSDC", "eth", "usdc", alt_symbol="ETHUSDC")
BTC_USDC_PERP = Instrument.futures("BTC-USDC-PERP")


def test_trade_stream() -> None:
    messages = """
        {"type":"update","dataType":"V1TAAnonymousTrade","data":{"trades":[{"tradeId":"100032000001702899","symbol":"BTCUSDC","price":"28371.3950","quantity":"0.00088000","side":"BUY","isTaker":true,"createdAtTimestamp":"1680360732976","createdAtDatetime":"2023-04-01T14:52:12.976Z"},{"tradeId":"100032000001702902","symbol":"BTCUSDC","price":"28371.6398","quantity":"0.00821792","side":"SELL","isTaker":true,"createdAtTimestamp":"1680360733026","createdAtDatetime":"2023-04-01T14:52:13.026Z"},{"tradeId":"100032000001702906","symbol":"BTCUSDC","price":"28372.8159","quantity":"0.09030000","side":"BUY","isTaker":true,"createdAtTimestamp":"1680360733054","createdAtDatetime":"2023-04-01T14:52:13.054Z"}],"symbol":"BTCUSDC"}}
    """  # noqa: E501
    expected_trades = [
        (
            BTC_USDC,
            TradeData(
                trade_id=100032000001702899,
                amount=Decimal("0.00088000"),
                price=Decimal("28371.3950"),
                is_buy=True,
                time=dt_from_any_aware(1680360732976),
            ),
        ),
        (
            BTC_USDC,
            TradeData(
                trade_id=100032000001702902,
                amount=Decimal("0.00821792"),
                price=Decimal("28371.6398"),
                is_buy=False,
                time=dt_from_any_aware(1680360733026),
            ),
        ),
        (
            BTC_USDC,
            TradeData(
                trade_id=100032000001702906,
                amount=Decimal("0.09030000"),
                price=Decimal("28372.8159"),
                is_buy=True,
                time=dt_from_any_aware(1680360733054),
            ),
        ),
    ]
    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDC], messages, expected_trades)


def get_spot_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "marketId": "10015",
            "symbol": "BTCUSDC",
            "quoteAssetId": "5",
            "baseAssetId": "1",
            "quoteSymbol": "USDC",
            "baseSymbol": "BTC",
            "quotePrecision": "4",
            "basePrecision": "8",
            "pricePrecision": "4",
            "quantityPrecision": "8",
            "costPrecision": "4",
            "minQuantityLimit": "0.00029411",
            "maxQuantityLimit": "100.00000000",
            "maxPriceLimit": None,
            "minPriceLimit": "0.01",
            "maxCostLimit": None,
            "minCostLimit": None,
            "timeZone": "Etc/UTC",
            "tickSize": "0.1000",
            "liquidityTickSize": "100.0000",
            "liquidityPrecision": "4",
            "makerFee": "0",
            "takerFee": "1",
            "orderTypes": ["LMT", "MKT", "STOP_LIMIT"],
            "spotTradingEnabled": True,
            "marginTradingEnabled": False,
            "marketEnabled": True,
            "createOrderEnabled": True,
            "cancelOrderEnabled": True,
            "feeTiers": [
                {"feeTierId": "11", "staticSpreadFee": "0.00000000", "isDislocationEnabled": True},
                {"feeTierId": "1", "staticSpreadFee": "0.00000000", "isDislocationEnabled": False},
                {"feeTierId": "12", "staticSpreadFee": "0.00020000", "isDislocationEnabled": True},
                {"feeTierId": "16", "staticSpreadFee": "0.00300000", "isDislocationEnabled": True},
                {"feeTierId": "13", "staticSpreadFee": "0.00060000", "isDislocationEnabled": True},
                {"feeTierId": "3", "staticSpreadFee": "0.00060000", "isDislocationEnabled": False},
                {"feeTierId": "6", "staticSpreadFee": "0.00300000", "isDislocationEnabled": False},
                {"feeTierId": "2", "staticSpreadFee": "0.00020000", "isDislocationEnabled": False},
                {"feeTierId": "14", "staticSpreadFee": "0.00100000", "isDislocationEnabled": True},
                {"feeTierId": "15", "staticSpreadFee": "0.00150000", "isDislocationEnabled": True},
                {"feeTierId": "4", "staticSpreadFee": "0.00100000", "isDislocationEnabled": False},
                {"feeTierId": "5", "staticSpreadFee": "0.00150000", "isDislocationEnabled": False},
                {"feeTierId": "7", "staticSpreadFee": "0.00500000", "isDislocationEnabled": False},
                {"feeTierId": "8", "staticSpreadFee": "0.01000000", "isDislocationEnabled": False},
                {"feeTierId": "9", "staticSpreadFee": "0.01500000", "isDislocationEnabled": False},
                {"feeTierId": "10", "staticSpreadFee": "0.02000000", "isDislocationEnabled": False},
                {"feeTierId": "17", "staticSpreadFee": "0.00500000", "isDislocationEnabled": True},
                {"feeTierId": "18", "staticSpreadFee": "0.01000000", "isDislocationEnabled": True},
                {"feeTierId": "19", "staticSpreadFee": "0.01500000", "isDislocationEnabled": True},
                {"feeTierId": "20", "staticSpreadFee": "0.02000000", "isDislocationEnabled": True},
            ],
        },
        {
            "marketId": "10016",
            "symbol": "ETHUSDC",
            "quoteAssetId": "5",
            "baseAssetId": "4",
            "quoteSymbol": "USDC",
            "baseSymbol": "ETH",
            "quotePrecision": "4",
            "basePrecision": "8",
            "pricePrecision": "4",
            "quantityPrecision": "8",
            "costPrecision": "4",
            "minQuantityLimit": "0.00498753",
            "maxQuantityLimit": "250.00000000",
            "maxPriceLimit": "100",
            "minPriceLimit": None,
            "maxCostLimit": None,
            "minCostLimit": None,
            "timeZone": "Etc/UTC",
            "tickSize": "0.0100",
            "liquidityTickSize": "10.0000",
            "liquidityPrecision": "4",
            "makerFee": "0",
            "takerFee": "1",
            "orderTypes": ["LMT", "MKT", "STOP_LIMIT"],
            "spotTradingEnabled": True,
            "marginTradingEnabled": True,
            "marketEnabled": False,
            "createOrderEnabled": True,
            "cancelOrderEnabled": True,
            "feeTiers": [
                {"feeTierId": "18", "staticSpreadFee": "0.01000000", "isDislocationEnabled": True},
                {"feeTierId": "8", "staticSpreadFee": "0.01000000", "isDislocationEnabled": False},
                {"feeTierId": "9", "staticSpreadFee": "0.01500000", "isDislocationEnabled": False},
                {"feeTierId": "1", "staticSpreadFee": "0.00000000", "isDislocationEnabled": False},
                {"feeTierId": "11", "staticSpreadFee": "0.00000000", "isDislocationEnabled": True},
                {"feeTierId": "2", "staticSpreadFee": "0.00020000", "isDislocationEnabled": False},
                {"feeTierId": "3", "staticSpreadFee": "0.00060000", "isDislocationEnabled": False},
                {"feeTierId": "13", "staticSpreadFee": "0.00060000", "isDislocationEnabled": True},
                {"feeTierId": "14", "staticSpreadFee": "0.00100000", "isDislocationEnabled": True},
                {"feeTierId": "4", "staticSpreadFee": "0.00100000", "isDislocationEnabled": False},
                {"feeTierId": "16", "staticSpreadFee": "0.00300000", "isDislocationEnabled": True},
                {"feeTierId": "6", "staticSpreadFee": "0.00300000", "isDislocationEnabled": False},
                {"feeTierId": "19", "staticSpreadFee": "0.01500000", "isDislocationEnabled": True},
                {"feeTierId": "20", "staticSpreadFee": "0.02000000", "isDislocationEnabled": True},
                {"feeTierId": "15", "staticSpreadFee": "0.00150000", "isDislocationEnabled": True},
                {"feeTierId": "5", "staticSpreadFee": "0.00150000", "isDislocationEnabled": False},
                {"feeTierId": "12", "staticSpreadFee": "0.00020000", "isDislocationEnabled": True},
                {"feeTierId": "10", "staticSpreadFee": "0.02000000", "isDislocationEnabled": False},
                {"feeTierId": "17", "staticSpreadFee": "0.00500000", "isDislocationEnabled": True},
                {"feeTierId": "7", "staticSpreadFee": "0.00500000", "isDislocationEnabled": False},
            ],
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDC,
            SpotContractData(
                symbol="BTCUSDC",
                base_id=0,
                quote_id=912,
                base_name="btc",
                quote_name="usdc",
                native_base_name="BTC",
                native_quote_name="USDC",
                listing_date=dt_from_any_aware(SpotContractData.DEFAULT_LISTING_DATE),
                end_date=None,
                is_current=True,
                status="online",
                amount_increment=Decimal("10") ** (-int("8")),
                amount_size_min=Decimal("0.00029411"),
                amount_size_max=Decimal("100.00000000"),
                price_increment=Decimal("10") ** (-int("4")),
                price_size_min=Decimal("0.01"),
                price_size_max=None,
                taker_fee=Decimal("1") / 10_000,
                maker_fee=Decimal("0") / 10_000,
                margin_trading_enabled=False,
            ),
        ),
        (
            ETH_USDC,
            SpotContractData(
                symbol="ETHUSDC",
                base_id=6,
                quote_id=912,
                base_name="eth",
                quote_name="usdc",
                native_base_name="ETH",
                native_quote_name="USDC",
                listing_date=dt_from_any_aware(SpotContractData.DEFAULT_LISTING_DATE),
                end_date=None,
                is_current=True,
                status="offline",
                amount_increment=Decimal("10") ** (-int("8")),
                amount_size_min=Decimal("0.00498753"),
                amount_size_max=Decimal("250.00000000"),
                price_increment=Decimal("10") ** (-int("4")),
                price_size_min=None,
                price_size_max=Decimal("100"),
                taker_fee=Decimal("1") / 10_000,
                maker_fee=Decimal("0") / 10_000,
                margin_trading_enabled=True,
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.bullish.BullishHttpApi._get_spot_markets", get_spot_markets)
def test_spot_metadata_base(instrument: Instrument, expected_result: SpotContractData) -> None:
    diagnostics_mock = mock.Mock()
    http_client_mock = mock.Mock()
    http_api = exchange_http_api("Bullish", [], diagnostics_mock)
    for contract_data in http_api.spot_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def get_future_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "marketId": "20000",
            "symbol": "BTC-USDC-PERP",
            "quoteAssetId": "5",
            "baseAssetId": "1",
            "quoteSymbol": "USDC",
            "baseSymbol": "BTC",
            "quotePrecision": "4",
            "basePrecision": "8",
            "pricePrecision": "4",
            "quantityPrecision": "8",
            "costPrecision": "4",
            "minQuantityLimit": "0.00050000",
            "maxQuantityLimit": "200.00000000",
            "maxPriceLimit": "400",
            "minPriceLimit": "11111",
            "maxCostLimit": "1",
            "minCostLimit": "1",
            "timeZone": "Etc/UTC",
            "tickSize": "0.1000",
            "liquidityTickSize": "100.0000",
            "liquidityPrecision": "4",
            "makerFee": "0",
            "takerFee": "1",
            "orderTypes": ["LMT", "MKT", "STOP_LIMIT", "POST_ONLY"],
            "spotTradingEnabled": True,
            "marginTradingEnabled": True,
            "marketEnabled": True,
            "createOrderEnabled": True,
            "cancelOrderEnabled": True,
            "feeTiers": [
                {"feeTierId": "12", "staticSpreadFee": "0.00020000", "isDislocationEnabled": True},
                {"feeTierId": "2", "staticSpreadFee": "0.00020000", "isDislocationEnabled": False},
            ],
            "marketType": "PERPETUAL",
            "contractMultiplier": "1",
            "settlementAssetSymbol": "USDC",
            "underlyingQuoteSymbol": "USDC",
            "underlyingBaseSymbol": "BTC",
            "maxInitialLeverage": "7",
            "warningLeverage": "11",
            "liquidationLeverage": "15",
            "fullLiquidationLeverage": "25",
            "defaultedLeverage": "40",
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDC_PERP,
            FuturesContractData(
                symbol="BTC-USDC-PERP",
                underlying_base_id=0,
                underlying_quote_id=912,
                underlying_base_name="btc",
                underlying_quote_name="usdc",
                underlying_native_base_name="BTC",
                underlying_native_quote_name="USDC",
                listing_date=None,
                expiry_date=None,
                margin_asset_id=912,
                margin_asset_name="usdc",
                size_asset_id=0,
                size_asset_name="btc",
                contract_size=Decimal("1"),
                tick_size=Decimal("0.1000"),
                multiplier_size=Decimal("1"),
                amount_increment=Decimal(10 ** -Decimal("8")),
                amount_size_min=Decimal("0.00050000"),
                amount_size_max=Decimal("200.00000000"),
                price_increment=Decimal(10 ** -Decimal("4")),
                price_size_min=Decimal("11111"),
                price_size_max=Decimal("400"),
                order_size_min=Decimal("1"),
                maker_fee=Decimal(Decimal("0.0001") * Decimal("0")),
                taker_fee=Decimal(Decimal("0.0001") * Decimal("1")),
                margin_trading_enabled=True,
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.bullish.BullishHttpApi._get_future_markets", get_future_markets)
def test_futures_metadata_base(instrument: Instrument, expected_result: FuturesContractData) -> None:
    diagnostics_mock = mock.Mock()
    http_client_mock = mock.Mock()
    http_api = exchange_http_api("Bullish", [], diagnostics_mock)
    for contract_data in http_api.futures_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def test_book() -> None:
    standard_book_test(
        EXCHANGE_NAME,
        BTC_USDC,
        10,
        BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=123,
            exchange_time=dt_from_any_aware(1679249710847),
            bids=[
                PriceLevel(price=Decimal("2.0"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("1.9"), amount=Decimal("1.2")),
                PriceLevel(price=Decimal("1.8"), amount=Decimal("1.3")),
                PriceLevel(price=Decimal("1.7"), amount=Decimal("1.4")),
            ],
            asks=[
                PriceLevel(price=Decimal("2.1"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("2.2"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.3"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.4"), amount=Decimal("0.7")),
            ],
        ),
        BookDataTolerateCrossedBook(
            book_type=BookType.FULL,
            exchange_sequence_id=123,
            exchange_time=dt_from_any_aware(1679249710847),
            bids=[
                PriceLevel(price=Decimal("2.0"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("1.9"), amount=Decimal("1.2")),
                PriceLevel(price=Decimal("1.8"), amount=Decimal("1.3")),
                PriceLevel(price=Decimal("1.7"), amount=Decimal("1.4")),
            ],
            asks=[
                PriceLevel(price=Decimal("2.1"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("2.2"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.3"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.4"), amount=Decimal("0.7")),
            ],
        ),
    )


def test_book_stream() -> None:
    messages = """
    {"type": "snapshot", "dataType": "V1TALevel2", "data": {"symbol": "BTCUSDC", "bids": ["28297.8000", "0.00045097", "28297.7000", "0.00624365", "28297.6000", "0.00624368", "28297.5000", "0.00624372", "28297.4000", "0.00624375"], "asks": ["28297.9000", "0.00579265", "28298.0000", "0.00624358", "28298.1000", "0.00624355", "28298.2000", "0.00624352", "28298.3000", "0.00624348"], "sequenceNumberRange": [18302714, 18302714], "datetime": "2023-03-29T16:46:35.678Z", "timestamp": "1680108395678"}}
    {"type": "update", "dataType": "V1TALevel2", "data": {"symbol": "BTCUSDC", "bids": ["28297.8000", "0.0", "28297.7000", "0.006"], "asks": [], "sequenceNumberRange": [18302714, 18302721], "datetime": "2023-03-29T16:46:35.943Z", "timestamp": "1680108395943"}}
    {"type": "update", "dataType": "V1TALevel2", "data": {"symbol": "BTCUSDC", "bids": [], "asks": ["28297.9000", "0.003", "28298.0000", "0.0"], "sequenceNumberRange": [18302722, 18302728], "datetime": "2023-03-29T16:46:36.157Z", "timestamp": "1680108396157"}}
    """  # noqa: E501

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_any_aware(1680108396157),
        bids=[
            PriceLevel(price=Decimal("28297.7000"), amount=Decimal("0.006")),
            PriceLevel(price=Decimal("28297.6000"), amount=Decimal("0.00624368")),
            PriceLevel(price=Decimal("28297.5000"), amount=Decimal("0.00624372")),
            PriceLevel(price=Decimal("28297.4000"), amount=Decimal("0.00624375")),
        ],
        asks=[
            PriceLevel(price=Decimal("28297.9000"), amount=Decimal("0.003")),
            PriceLevel(price=Decimal("28298.1000"), amount=Decimal("0.00624355")),
            PriceLevel(price=Decimal("28298.2000"), amount=Decimal("0.00624352")),
            PriceLevel(price=Decimal("28298.3000"), amount=Decimal("0.00624348")),
        ],
    )
    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USDC,
        messages,
        4,
        expected_book,
    )
