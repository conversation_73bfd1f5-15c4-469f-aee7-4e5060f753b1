from datetime import UTC, datetime, timedelta
from decimal import Decimal

import pytest

from src.octopus.data import BookData, BookType, Instrument, PriceLevel, TradeData
from src.octopus.exchange.poloniex import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST, extract_error_message_hash
from src.octopus.qa_utils import standard_book_stream_test, standard_last_trades_test, standard_trade_stream_test
from src.octopus.vex.generate import GeneratedTradeTimeRange, monotonic_time_trade_sequence
from src.utils.timeutil import dt_from_ms, dt_from_ms_aware

# NOTE: We are still using the legacy-style symbology internally
BTC_USDT = Instrument.spot("USDT_BTC", "btc", "usdt")
ETH_BTC = Instrument.spot("BTC_ETH", "eth", "btc")


@pytest.mark.skip(reason="currently broken in vex due to symbology tampering")
def test_last_trades() -> None:
    count = MAX_TRADES_PER_REQUEST
    start_date = datetime(2022, 7, 3, tzinfo=UTC)

    standard_last_trades_test(
        EXCHANGE_NAME,
        BTC_USDT,
        monotonic_time_trade_sequence(
            start_date, [GeneratedTradeTimeRange(timedelta(), timedelta(milliseconds=count - 1), count)]
        ),
    )


def test_trade_stream() -> None:
    messages = """
    {"channel": "trades", "data": [{"symbol": "BTC_USDT", "amount": "23.28733064", "quantity": "0.001006", "takerSide": "sell", "createTime": 1659466009666, "price": "23148.44", "id": "60000115", "ts": 1659466009671}]}
    {"channel": "trades", "data": [{"symbol": "ETH_BTC", "amount": "0.00007121", "quantity": "0.001", "takerSide": "buy", "createTime": 1659466021395, "price": "0.07121", "id": "60000835", "ts": 1659466021403}]}
    {"channel": "trades", "data": [{"symbol": "ETH_BTC", "amount": "0.00021363", "quantity": "0.003", "takerSide": "sell", "createTime": 1659466050063, "price": "0.07121", "id": "60000836", "ts": 1659466050069}]}
    """  # noqa: E501

    expected_trades = [
        (BTC_USDT, TradeData(60000115, Decimal("0.001006"), Decimal("23148.44"), False, dt_from_ms(1659466009666))),
        (ETH_BTC, TradeData(60000835, Decimal("0.001"), Decimal("0.07121"), True, dt_from_ms(1659466021395))),
        (ETH_BTC, TradeData(60000836, Decimal("0.003"), Decimal("0.07121"), False, dt_from_ms(1659466050063))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT, ETH_BTC], messages, expected_trades)


@pytest.mark.parametrize(
    "log_message,meaningful_text",
    (
        (
            "HTTPSConnectionPool(host='poloniex.com', port=443): Max retries exceeded with url: /public?command=returnTicker (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7ff246680eb0>: Failed to establish a new connection: [Errno -3] Try again'))):",  # noqa: E501
            "HTTPSConnectionPool(host='poloniex.com', port=443): Max retries exceeded with url: /public?command=returnTicker (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection >: Failed to establish a new connection: [Errno -3] Try again'))):",  # noqa: E501
        ),
        (
            "403 Client Error: Forbidden for url: https://poloniex.com/public?command=returnTradeHistory&currencyPair=USDT_SUSHI&start=1656843438&end=1659435438",  # noqa: E501
            "403 Client Error: Forbidden for url: https://poloniex.com/public?command=returnTradeHistory&currencyPair=USDT_SUSHI&&",  # noqa: E501
        ),
    ),
)
def test_error_logs_deduplication(log_message: str, meaningful_text: str) -> None:
    calculated_hash = extract_error_message_hash(log_message)
    assert calculated_hash == hash(meaningful_text)


def test_book_stream() -> None:
    messages = """
    {"channel": "book_lv2", "data": [{"symbol": "BTC_USDT", "createTime": 1659468904666, "asks": [["23211.88", "0.422209"], ["23219.4", "0.15"], ["23321.4", "0.278343"]], "bids": [["22924.24", "0.002718"], ["22900", "0.017121"], ["22809.7", "0.1766"]], "lastId": 50871, "id": 50872, "ts": 1659468904942}], "action": "snapshot"}
    {"channel": "book_lv2", "data": [{"symbol": "BTC_USDT", "createTime": 1659468905365, "asks": [], "bids": [["22924.24", "0"], ["22338.36", "0.00007"]], "lastId": 50872, "id": 50873, "ts": 1659468905370}], "action": "update"}
    {"channel": "book_lv2", "data": [{"symbol": "BTC_USDT", "createTime": 1659468905470, "asks": [["22944.81", "0.000813"], ["23321.4", "0"]], "bids": [], "lastId": 50873, "id": 50874, "ts": 1659468905475}], "action": "update"}
    """  # noqa: E501

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_ms_aware(1659468905470),
        bids=[
            PriceLevel(price=Decimal("22900"), amount=Decimal("0.017121")),
            PriceLevel(price=Decimal("22809.7"), amount=Decimal("0.1766")),
            PriceLevel(price=Decimal("22338.36"), amount=Decimal("0.00007")),
        ],
        asks=[
            PriceLevel(price=Decimal("22944.81"), amount=Decimal("0.000813")),
            PriceLevel(price=Decimal("23211.88"), amount=Decimal("0.422209")),
            PriceLevel(price=Decimal("23219.4"), amount=Decimal("0.15")),
        ],
    )

    standard_book_stream_test(EXCHANGE_NAME, BTC_USDT, messages, 3, expected_book)
