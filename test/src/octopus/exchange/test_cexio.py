from datetime import datetime, timedelta
from decimal import Decimal
from http import HTTPStatus

from src.octopus.data import BookData, BookType, Instrument, PriceLevel, TradeData
from src.octopus.exchange.cexio import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.qa_utils import standard_book_stream_test, standard_trade_history_traversal_test, standard_trade_stream_test
from src.octopus.vex.generate import GeneratedTradeIdRange, monotonic_id_trade_sequence
from src.utils.http import HttpRequest, HttpResponse, IHttpClient
from src.utils.timeutil import align_dt_to_interval, dt_from_us, dt_from_us_aware

BTC_USD = Instrument.spot("BTC/USD", "btc", "usd")


class HttpClient(IHttpClient):
    def request(self, request: HttpRequest, timeout: float = 0.0, raw: bool = False) -> HttpResponse:
        if request.url == "https://cex.io/api/currency_profile":
            data = '{"ok":"ok","data":{"symbols":[{"code":"BTC","minimumCurrencyAmount":"0.00000001"}]}}'
            return HttpResponse(HTTPStatus.OK, data)
        else:
            raise Exception("unknown url: {}".format(request.url))


def test_trade_history_traversal() -> None:
    trade_time = align_dt_to_interval(datetime.utcnow(), timedelta(seconds=1))

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_id_trade_sequence(
            [
                GeneratedTradeIdRange(1, MAX_TRADES_PER_REQUEST),
                GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST // 2, MAX_TRADES_PER_REQUEST + 1),
                GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST * 2, MAX_TRADES_PER_REQUEST - 1),
            ],
            time_gen=lambda: trade_time,
        ),
    )


def test_trade_stream() -> None:
    messages = """
        {"e":"connected"}

        {"e":"md","data":{"id":666456768,"buy":[[7912.6,80000000],[7912.5,438266594]],"sell":[[7920.6,7692432],[7920.8,200000000]],"buy_total":140267357,"sell_total":29931682479, "pair":"BTC:USD"}}
        {"e":"md_groupped","data":{"buy":{"7910.0":518266594,"7890.0":323512274},"sell":{"7930.0":266472432,"7940.0":436621436}, "pair":"BTC:USD", "id":666456768}}
        {"e":"ohlcv24","data":["7983.1","7983.1","7648.3","7916.7",22953756900],"pair":"BTC:USD"}

        {"e":"history","data":["buy:1583832665229:251998:7916.7:10777315","sell:1583831907636:30000000:7911.5:10777307"]}
        {"e":"history-update","data":[["buy","1583832860963","1264143","7909.8","10777316"], ["sell","1583826736897","9309489","7898.5","10777240"]]}
    """  # noqa: E501

    expected_trades = [
        (BTC_USD, TradeData(10777315, Decimal("0.00251998"), Decimal("7916.7"), True, dt_from_us(1583832665229000))),
        (BTC_USD, TradeData(10777307, Decimal("0.3"), Decimal("7911.5"), False, dt_from_us(1583831907636000))),
        (BTC_USD, TradeData(10777316, Decimal("0.01264143"), Decimal("7909.8"), True, dt_from_us(1583832860963000))),
        (BTC_USD, TradeData(10777240, Decimal("0.09309489"), Decimal("7898.5"), False, dt_from_us(1583826736897000))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USD], messages, expected_trades, http_client=HttpClient())


def test_book_stream() -> None:
    """
    1. Reconnect if authorizarion request doesn't return 'ok'.
    1. Reconnect if subscription request doesn't return 'ok'.
    2. Reconnect if snapshot is crossed.
    3. Reconnect if non-incremental socket_sequence in encountered.
    4. Reconnect if crossed book is detected.
    """
    messages = """
        {"e":"connected"}
        {"e":"auth","data":{"ok":"fail"},"ok":"fail","timestamp":1583830681}

        {"e":"connected"}
        {"e":"auth","data":{"ok":"ok"},"ok":"ok","timestamp":1588230681}
        {"e":"order-book-subscribe","data":{"timestamp":1588230682,"bids":[],"asks":[],"pair":"BTC:USD","id":727321139,"sell_total":"305.68467676","buy_total":"2826299.30"},"oid":"cb776bc4-8ab1-11ea-9407-7c763586de13_order-book-subscribe", "ok":"fail"}

        {"e":"connected"}
        {"e":"auth","data":{"ok":"ok"},"ok":"ok","timestamp":1588230681}
        {"e":"order-book-subscribe","data":{"timestamp":1588230682,"bids":[[8999.9,1.0], [8990,0.5]],"asks":[[8995.5,0.2], [9000.2,0.3]],"pair":"BTC:USD","id":727321139,"sell_total":"305.68467676","buy_total":"2826299.30"},"oid":"cb776bc4-8ab1-11ea-9407-7c763586de13_order-book-subscribe", "ok":"ok"}

        {"e":"connected"}
        {"e":"auth","data":{"ok":"ok"},"ok":"ok","timestamp":1588230681}
        {"e":"order-book-subscribe","data":{"timestamp":1588230682,"bids":[[8999.9,1.0], [8990,0.5]],"asks":[[9005.5,0.2], [9006.2,0.3]],"pair":"BTC:USD","id":727321139,"sell_total":"305.68467676","buy_total":"2826299.30"},"oid":"cb776bc4-8ab1-11ea-9407-7c763586de13_order-book-subscribe", "ok":"ok"}
        {"e":"md_update","data":{"id":727321143,"pair":"BTC:USD","time":1588230681778,"bids":[[8999.1,0.00000000],[8990.3,0.23674763]],"asks":[]}}

        {"e":"connected"}
        {"e":"auth","data":{"ok":"ok"},"ok":"ok","timestamp":1588230681}
        {"e":"order-book-subscribe","data":{"timestamp":1588230682,"bids":[[8994.9,1.0], [8990,0.5]],"asks":[[8995.5,0.2], [9000.2,0.3]],"pair":"BTC:USD","id":727321139,"sell_total":"305.68467676","buy_total":"2826299.30"},"oid":"cb776bc4-8ab1-11ea-9407-7c763586de13_order-book-subscribe", "ok":"ok"}
        {"e":"md_update","data":{"id":727321140,"pair":"BTC:USD","time":1588230681778,"bids":[[8999.95,0.00000001],[8990.3,0.23674763]],"asks":[]}}

        {"e":"connected"}
        {"e":"auth","data":{"ok":"ok"},"ok":"ok","timestamp":1588230681}
        {"e":"order-book-subscribe","data":{"timestamp":1588230682,"bids":[[8994.9,1.0], [8990,0.5]],"asks":[[8995.5,0.2], [9000.2,0.3]],"pair":"BTC:USD","id":727321139,"sell_total":"305.68467676","buy_total":"2826299.30"},"oid":"cb776bc4-8ab1-11ea-9407-7c763586de13_order-book-subscribe", "ok":"ok"}
        {"e":"md_update","data":{"id":727321140,"pair":"BTC:USD","time":1588230681778,"bids":[[8994.9,0.00000001]],"asks":[[8995.5,0.1234]]}}
        {"e":"md_update","data":{"id":727321141,"pair":"BTC:USD","time":1588230681779,"bids":[[8995,0.00000001],[8993.6, 0.01]],"asks":[[8997.5,0.1234],[8998.6,0.1]]}}
        {"e":"md_update","data":{"id":727321142,"pair":"BTC:USD","time":1588230681780,"bids":[[8990,0.0000000]],"asks":[[9000.2,0]]}}
    """  # noqa: E501

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=727321142,
        exchange_time=dt_from_us_aware(1588230681780000),
        bids=[
            PriceLevel(price=Decimal("8995"), amount=Decimal("0.00000001")),
            PriceLevel(price=Decimal("8994.9"), amount=Decimal("0.00000001")),
            PriceLevel(price=Decimal("8993.6"), amount=Decimal("0.01")),
        ],
        asks=[
            PriceLevel(price=Decimal("8995.5"), amount=Decimal("0.1234")),
            PriceLevel(price=Decimal("8997.5"), amount=Decimal("0.1234")),
            PriceLevel(price=Decimal("8998.6"), amount=Decimal("0.1")),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME, BTC_USD, messages, 2, expected_book, expected_connect_count=6, http_client=HttpClient()
    )
