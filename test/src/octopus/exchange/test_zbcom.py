from decimal import Decimal

from src.octopus.data import Instrument, TradeData
from src.octopus.exchange.zbcom import EXCHANGE_NAME
from src.octopus.qa_utils import standard_trade_stream_test
from src.utils.timeutil import dt_from_us

BTC_USDT = Instrument.spot("btcusdt", "btc", "usdt")
ETH_BTC = Instrument.spot("ethbtc", "btc", "eth")


def test_trade_stream() -> None:
    messages = """
        {"data":[{"date":1582802978,"amount":"0.0642","price":"8800.54","trade_type":"bid","type":"buy","tid":784740993},{"date":1582802978,"amount":"0.0399","price":"8801.65","trade_type":"bid","type":"buy","tid":784740994}],"dataType":"trades","channel":"btcusdt_trades"}
        {"data":[{"date":1582802858,"amount":"0.388","price":"0.025725","trade_type":"ask","type":"sell","tid":250250275}],"dataType":"trades","channel":"ethbtc_trades"}
    """

    expected_trades = [
        (BTC_USDT, TradeData(784740993, Decimal("0.0642"), Decimal("8800.54"), True, dt_from_us(1582802978000000))),
        (BTC_USDT, TradeData(784740994, Decimal("0.0399"), Decimal("8801.65"), True, dt_from_us(1582802978000000))),
        (ETH_BTC, TradeData(250250275, Decimal("0.388"), Decimal("0.025725"), False, dt_from_us(1582802858000000))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT, ETH_BTC], messages, expected_trades)
