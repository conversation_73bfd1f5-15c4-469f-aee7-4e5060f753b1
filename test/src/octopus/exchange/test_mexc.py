from decimal import Decimal
from http import HTTPStatus
from typing import Any, Dict, List
from unittest.mock import Mock, patch

from src.octopus.data import (
    BookData,
    BookType,
    FuturesContractData,
    Instrument,
    MarketType,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.mexc import <PERSON>XCHANGE_NAME, MEXCHttpApi
from src.octopus.qa_utils import MessageReplayHttpClient, standard_book_stream_test, standard_trade_stream_test
from src.octopus.translation import ExchangeTickerTranslator
from src.resources.currency import glib_currency
from src.resources.exchange import glib_exchange
from src.utils.http import HttpResponse, IHttpClient
from src.utils.timeutil import dt_from_ms_aware

BTC_USDT = Instrument.spot("BTCUSDT", "btc", "usdt")
ETH_BTC = Instrument.spot("ETHBTC", "eth", "btc")
BTC_USDT_PERPETUAL = Instrument.futures("BTC_USDT")
FROZEN_TIME = "2022-10-17 10:21:34Z"


# def test_book_spot_stream() -> None:
#     messages = """
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866469, "bids": [{"p": "22000.71", "v": "1.100000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006901"}, "s": "BTCUSDT", "t": 1662976866469}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866469, "bids": [{"p": "22146.71", "v": "1.100000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006902"}, "s": "BTCUSDT", "t": 1662976866469}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866469, "bids": [{"p": "22146.70", "v": "0.000000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006903"}, "s": "BTCUSDT", "t": 1662976866469}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866469, "bids": [{"p": "22146.11", "v": "0.700000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006904"}, "s": "BTCUSDT", "t": 1662976866469}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866478, "asks": [{"p": "22147.03", "v": "0.150000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006907"}, "s": "BTCUSDT", "t": 1662976866478}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866478, "asks": [{"p": "22147.64", "v": "0.000000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006905"}, "s": "BTCUSDT", "t": 1662976866478}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866478, "asks": [{"p": "22148.00", "v": "0.620000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006906"}, "s": "BTCUSDT", "t": 1662976866478}
#         {"c": "<EMAIL>@BTCUSDT", "d":{"E": 1662976866478, "asks": [{"p": "22148.00", "v": "0.500000"}], "e": "<EMAIL>", "s": "BTCUSDT", "version": "3902006908"}, "s": "BTCUSDT", "t": 1662976866478}
#     """  # noqa: E501
#     snapshot_body = """{"lastUpdateId": 3902006901, "bids": [["22146.71", "0.143450"], ["22146.70", "0.143450"], ["22146.29", "1.145329"]], "asks": [["22147.02", "0.325450"], ["22147.03", "0.143450"], ["22147.64", "0.022500"]]}"""  # noqa: E501
#     http_client = MessageReplayHttpClient([HttpResponse(HTTPStatus.OK, snapshot_body)])
#
#     expected_book = BookData(
#         book_type=BookType.FULL,
#         exchange_sequence_id=3902006908,
#         exchange_time=dt_from_ms_aware(1662976866478),
#         bids=[
#             PriceLevel(price=Decimal("22146.71"), amount=Decimal("1.100000")),
#             PriceLevel(price=Decimal("22146.29"), amount=Decimal("1.145329")),
#             PriceLevel(price=Decimal("22146.11"), amount=Decimal("0.700000")),
#         ],
#         asks=[
#             PriceLevel(price=Decimal("22147.02"), amount=Decimal("0.325450")),
#             PriceLevel(price=Decimal("22147.03"), amount=Decimal("0.150000")),
#             PriceLevel(price=Decimal("22148.00"), amount=Decimal("0.500000")),
#         ],
#     )
#
#     standard_book_stream_test(
#         EXCHANGE_NAME,
#         BTC_USDT,
#         messages,
#         3,
#         expected_book,
#         expected_connect_count=1,
#         http_client=http_client,
#         mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
#     )


def test_book_futures_stream() -> None:
    messages = """
        {"symbol": "BTC_USDT", "data": {"asks": [], "bids": [["18754.3", 200000, 1]], "version": 6599238095}, "channel": "push.depth", "ts": 1663591645533}
        {"symbol": "BTC_USDT", "data": {"asks": [["18754.6", 111111, 2]], "bids": [["18754.4", 0, 0], ["18754.1", 3333, 3]], "version": 6599238096}, "channel": "push.depth", "ts": 1663591645535}
        {"symbol": "BTC_USDT", "data": {"asks": ["18754.8", 34343, 1], "bids": [], "version": 6599238097}, "channel": "push.depth", "ts": 1663591645538}
    """  # noqa: E501
    snapshot_body = """{"success": "true", "code": 0, "data": {"bids": [["18754.4", 771463, 2], ["18754.3", 323136, 1], ["18754.2", 344412, 1]], "asks": [["18754.5", 504196, 2], ["18754.6", 291492, 1], ["18754.7", 201348, 1]], "version": 6599238095, "timestamp": 1663591646289}}"""  # noqa: E501
    http_client = MessageReplayHttpClient([HttpResponse(HTTPStatus.OK, snapshot_body)])

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=6599238096,
        exchange_time=dt_from_ms_aware(1663591645535),
        bids=[
            PriceLevel(price=Decimal("18754.3"), amount=Decimal("323136"), count=1),
            PriceLevel(price=Decimal("18754.2"), amount=Decimal("344412"), count=1),
            PriceLevel(price=Decimal("18754.1"), amount=Decimal("3333"), count=3),
        ],
        asks=[
            PriceLevel(price=Decimal("18754.5"), amount=Decimal("504196"), count=2),
            PriceLevel(price=Decimal("18754.6"), amount=Decimal("111111"), count=2),
            PriceLevel(price=Decimal("18754.7"), amount=Decimal("201348"), count=1),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USDT_PERPETUAL,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
        http_client=http_client,
    )


# def test_spot_trades_stream() -> None:
#     messages = """
#         {"c":"<EMAIL>@BTCUSDT","d":{"E":1665506187786,"deals":[{"S":2,"p":"19114.24","t":1665506187770,"v":"0.264512"}],"e":"<EMAIL>","s":"BTCUSDT"},"s":"BTCUSDT","t":1665506187770}
#         {"c":"<EMAIL>@BTCUSDT","d":{"E":1235344187786,"deals":[{"S":2,"p":"20000.24","t":1665506188800,"v":"0.015417"}, {"S":1,"p":"20000.29","t":1665506188801,"v":"0.02222"}],"e":"<EMAIL>","s":"BTCUSDT"},"s":"BTCUSDT","t":1665506188800}
#         {"c":"<EMAIL>@ETHBTC","d":{"E":1665565349029,"deals":[{"S":2,"p":"0.067817","t":1665565349015,"v":"0.012"}],"e":"<EMAIL>","s":"ETHBTC"},"s":"ETHBTC","sd":"ETHBTC","t":1665565349029}
#         """  # noqa: E501
#     expected_trades = [
#         (
#             BTC_USDT,
#             TradeData(
#                 trade_id=1665506187770725720331671993019,
#                 amount=Decimal("0.264512"),
#                 price=Decimal("19114.24"),
#                 time=dt_from_ms_aware(1665506187770),
#                 is_buy=False,
#             ),
#         ),
#         (
#             BTC_USDT,
#             TradeData(
#                 trade_id=16655061888002816218482112747207,
#                 amount=Decimal("0.015417"),
#                 price=Decimal("20000.24"),
#                 time=dt_from_ms_aware(1665506188800),
#                 is_buy=False,
#             ),
#         ),
#         (
#             BTC_USDT,
#             TradeData(
#                 trade_id=166550618880111711251480508133801,
#                 amount=Decimal("0.02222"),
#                 price=Decimal("20000.29"),
#                 time=dt_from_ms_aware(1665506188801),
#                 is_buy=True,
#             ),
#         ),
#         (
#             ETH_BTC,
#             TradeData(
#                 trade_id=166556534901510699637214401268652,
#                 amount=Decimal("0.012"),
#                 price=Decimal("0.067817"),
#                 time=dt_from_ms_aware(1665565349015),
#                 is_buy=False,
#             ),
#         ),
#     ]
#
#     standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT, ETH_BTC], messages, expected_trades)


def test_futures_trades_stream() -> None:
    messages = """
        {"symbol":"BTC_USDT","data":[{"p":19260,"v":5985,"T":1,"O":3,"M":1,"t":1666281331799}],"channel":"push.deal","ts":1666281331799}
        {"symbol":"BTC_USDT","data":[{"p":19259.9,"v":4033,"T":2,"O":3,"M":1,"t":1666281333786}],"channel":"push.deal","ts":1666281333786}
        {"symbol":"BTC_USDT","data":[{"p":19259.9,"v":3400,"T":2,"O":3,"M":1,"t":1666281333807}],"channel":"push.deal","ts":1666281333807}
        {"symbol":"ETH_BTC","data":[{"p":19259.9,"v":3400,"T":2,"O":3,"M":1,"t":1666281333807}],"channel":"push.deal","ts":1666281333807}
        """  # noqa: E501
    expected_trades = [
        (
            BTC_USDT_PERPETUAL,
            TradeData(
                trade_id=16662813317999238503265483235693,
                amount=Decimal("5985"),
                price=Decimal("19260"),
                time=dt_from_ms_aware(1666281331799),
                is_buy=True,
            ),
        ),
        (
            BTC_USDT_PERPETUAL,
            TradeData(
                trade_id=166628133378613252338562744940228,
                amount=Decimal("4033"),
                price=Decimal("19259.9"),
                time=dt_from_ms_aware(1666281333786),
                is_buy=False,
            ),
        ),
        (
            BTC_USDT_PERPETUAL,
            TradeData(
                trade_id=166628133380716394562945847173155,
                amount=Decimal("3400"),
                price=Decimal("19259.9"),
                time=dt_from_ms_aware(1666281333807),
                is_buy=False,
            ),
        ),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT_PERPETUAL], messages, expected_trades, market_type=MarketType.FUTURES)


def _get_mexc_spot_markets(self: MEXCHttpApi, client: IHttpClient) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "TOMO3LUSDT",
            "status": "ENABLED",
            "baseAsset": "TOMO3L",
            "baseAssetPrecision": 2,
            "quoteAsset": "USDT",
            "quotePrecision": 3,
            "quoteAssetPrecision": 3,
            "baseCommissionPrecision": 2,
            "quoteCommissionPrecision": 3,
            "orderTypes": ["LIMIT", "LIMIT_MAKER"],
            "quoteOrderQtyMarketAllowed": False,
            "isSpotTradingAllowed": False,
            "isMarginTradingAllowed": False,
            "quoteAmountPrecision": "5",
            "baseSizePrecision": "0.0001",
            "permissions": ["SPOT", "MARGIN"],
            "filters": [],
            "maxQuoteAmount": "5000000",
            "makerCommission": "0.002",
            "takerCommission": "0.002",
        },
        {
            "symbol": "ALEPHUSDT",
            "status": "DISABLED",
            "baseAsset": "ALEPH",
            "baseAssetPrecision": 2,
            "quoteAsset": "USDT",
            "quotePrecision": 4,
            "quoteAssetPrecision": 4,
            "baseCommissionPrecision": 2,
            "quoteCommissionPrecision": 4,
            "orderTypes": ["LIMIT", "LIMIT_MAKER"],
            "quoteOrderQtyMarketAllowed": False,
            "isSpotTradingAllowed": True,
            "isMarginTradingAllowed": False,
            "quoteAmountPrecision": "5",
            "baseSizePrecision": "5",
            "permissions": ["SPOT", "MARGIN"],
            "filters": [],
            "maxQuoteAmount": "5000000",
            "makerCommission": "0.002",
            "takerCommission": "0.002",
        },
        {
            "symbol": "OGNUSDT",
            "status": "ENABLED",
            "baseAsset": "OGN",
            "baseAssetPrecision": 2,
            "quoteAsset": "USDT",
            "quotePrecision": 4,
            "quoteAssetPrecision": 4,
            "baseCommissionPrecision": 2,
            "quoteCommissionPrecision": 4,
            "orderTypes": ["LIMIT", "LIMIT_MAKER"],
            "quoteOrderQtyMarketAllowed": False,
            "isSpotTradingAllowed": True,
            "isMarginTradingAllowed": True,
            "quoteAmountPrecision": "5",
            "baseSizePrecision": "0.01",
            "permissions": ["MARGIN"],
            "filters": [],
            "maxQuoteAmount": "5000000",
            "makerCommission": "0.002",
            "takerCommission": "0.002",
        },
        {
            "symbol": "QNTUSDT",
            "status": "ENABLED",
            "baseAsset": "QNT",
            "baseAssetPrecision": 2,
            "quoteAsset": "USDT",
            "quotePrecision": 3,
            "quoteAssetPrecision": 3,
            "baseCommissionPrecision": 2,
            "quoteCommissionPrecision": 3,
            "orderTypes": ["LIMIT", "LIMIT_MAKER"],
            "quoteOrderQtyMarketAllowed": False,
            "isSpotTradingAllowed": True,
            "isMarginTradingAllowed": False,
            "quoteAmountPrecision": "5",
            "baseSizePrecision": "0.01",
            "permissions": ["SPOT", "MARGIN"],
            "filters": [],
            "maxQuoteAmount": "5000000",
            "makerCommission": "0.002",
            "takerCommission": "0.002",
        },
    ]


def _get_mexc_futures_markets(self: MEXCHttpApi, client: IHttpClient) -> List[Dict[str, Any]]:
    return [
        {
            "amountScale": 4,
            "apiAllowed": False,
            "appraisal": 0,
            "askLimitPriceRate": 0.1,
            "automaticDelivery": 0,
            "baseCoin": "BTC",
            "baseCoinName": "BTC",
            "bidLimitPriceRate": 0.1,
            "conceptPlate": ["mc-trade-zone-grey", "mc-trade-zone-pow"],
            "contractSize": 0.0001,
            "depthStepList": [0.1, 1, 10, 100],
            "displayName": "BTC_USDT永续",
            "displayNameEn": "BTC_USDT PERPETUAL",
            "futureType": 1,
            "indexOrigin": ["BINANCE", "GATEIO", "HUOBI", "OKEX", "MEXC"],
            "initialMarginRate": 0.005,
            "isHidden": False,
            "isHot": False,
            "isNew": False,
            "maintenanceMarginRate": 0.004,
            "makerFeeRate": 0.0002,
            "marketOrderMaxLevel": 30,
            "marketOrderPriceLimitRate1": 0.1,
            "marketOrderPriceLimitRate2": 0.005,
            "maxLeverage": 200,
            "maxNumOrders": [200, 50],
            "maxVol": 5000000,
            "minLeverage": 1,
            "minVol": 1,
            "positionOpenType": 3,
            "priceCoefficientVariation": 0.05,
            "priceScale": 1,
            "priceUnit": 0.1,
            "quoteCoin": "USDT",
            "quoteCoinName": "USDT",
            "riskBaseVol": 525000,
            "riskIncrImr": 0.004,
            "riskIncrMmr": 0.004,
            "riskIncrVol": 525000,
            "riskLevelLimit": 5,
            "riskLimitType": "BY_VOLUME",
            "settleCoin": "USDT",
            "showAppraisalCountdown": 0,
            "state": 0,
            "symbol": "BTC_USDT",
            "takerFeeRate": 0.0006,
            "triggerProtect": 0.05,
            "volScale": 0,
            "volUnit": 1,
        },
        {
            "amountScale": 4,
            "apiAllowed": False,
            "appraisal": 0,
            "askLimitPriceRate": 0.1,
            "automaticDelivery": 0,
            "baseCoin": "ETH",
            "baseCoinName": "ETH",
            "bidLimitPriceRate": 0.1,
            "conceptPlate": ["mc-trade-zone-grey"],
            "contractSize": 0.01,
            "depthStepList": [0.01, 0.1, 1, 10],
            "displayName": "ETH_USDT永续",
            "displayNameEn": "ETH_USDT PERPETUAL",
            "futureType": 1,
            "indexOrigin": ["BINANCE", "GATEIO", "HUOBI", "OKEX", "MEXC"],
            "initialMarginRate": 0.005,
            "isHidden": False,
            "isHot": False,
            "isNew": False,
            "maintenanceMarginRate": 0.004,
            "makerFeeRate": 0.0002,
            "marketOrderMaxLevel": 30,
            "marketOrderPriceLimitRate1": 0.1,
            "marketOrderPriceLimitRate2": 0.005,
            "maxLeverage": 200,
            "maxNumOrders": [200, 50],
            "maxVol": 800000,
            "minLeverage": 1,
            "minVol": 1,
            "positionOpenType": 3,
            "priceCoefficientVariation": 0.05,
            "priceScale": 2,
            "priceUnit": 0.01,
            "quoteCoin": "USDT",
            "quoteCoinName": "USDT",
            "riskBaseVol": 62000,
            "riskIncrImr": 0.004,
            "riskIncrMmr": 0.004,
            "riskIncrVol": 62000,
            "riskLevelLimit": 5,
            "riskLimitType": "BY_VOLUME",
            "settleCoin": "USDT",
            "showAppraisalCountdown": 0,
            "state": 0,
            "symbol": "ETH_USDT",
            "takerFeeRate": 0.0006,
            "triggerProtect": 0.05,
            "volScale": 0,
            "volUnit": 1,
        },
        {
            "amountScale": 4,
            "apiAllowed": False,
            "appraisal": 0,
            "askLimitPriceRate": 0.3,
            "automaticDelivery": 0,
            "baseCoin": "APT",
            "baseCoinName": "APT",
            "bidLimitPriceRate": 0.3,
            "conceptPlate": [],
            "contractSize": 0.1,
            "depthStepList": [0.001, 0.01, 0.1, 1],
            "displayName": "APT_USDT永续",
            "displayNameEn": "APT_USDT PERPETUAL",
            "futureType": 1,
            "indexOrigin": ["BINANCE", "GATEIO", "HUOBI", "MEXC"],
            "initialMarginRate": 0.04,
            "isHidden": False,
            "isHot": True,
            "isNew": False,
            "maintenanceMarginRate": 0.02,
            "makerFeeRate": 0.0002,
            "marketOrderMaxLevel": 15,
            "marketOrderPriceLimitRate1": 0.2,
            "marketOrderPriceLimitRate2": 0.005,
            "maxLeverage": 25,
            "maxNumOrders": [200, 50],
            "maxVol": 500000,
            "minLeverage": 1,
            "minVol": 1,
            "positionOpenType": 3,
            "priceCoefficientVariation": 0.1,
            "priceScale": 3,
            "priceUnit": 0.001,
            "quoteCoin": "USDT",
            "quoteCoinName": "USDT",
            "riskBaseVol": 200000,
            "riskIncrImr": 0.01,
            "riskIncrMmr": 0.01,
            "riskIncrVol": 200000,
            "riskLevelLimit": 1,
            "riskLimitType": "BY_VOLUME",
            "settleCoin": "USDT",
            "showAppraisalCountdown": 0,
            "state": 0,
            "symbol": "APT_USDT",
            "takerFeeRate": 0.0006,
            "triggerProtect": 0.1,
            "volScale": 0,
            "volUnit": 1,
        },
    ]


@patch("src.octopus.exchange.mexc.MEXCHttpApi._get_spot_markets", _get_mexc_spot_markets)
def test_spot_metadata() -> None:
    # TODO: Implement VEX-based standardised tests
    translator = ExchangeTickerTranslator(EXCHANGE_NAME, glib_exchange(), glib_currency())
    mexc_http_api = MEXCHttpApi(exchange_name="MEXC", ticker_translator=translator, diagnostics=Mock())
    http_client_mock = Mock()
    spot_metadata = mexc_http_api.spot_markets_metadata(client=http_client_mock)
    expected_metadata = [
        SpotContractData(
            symbol="TOMO3LUSDT",
            base_id=translator.to_cm_id("tomo3l"),
            quote_id=translator.to_cm_id("usdt"),
            base_name=translator.translate("tomo3l"),
            quote_name=translator.translate("usdt"),
            native_base_name="TOMO3L",
            native_quote_name="USDT",
            listing_date=SpotContractData.DEFAULT_LISTING_DATE,
            end_date=None,
            is_current=True,
            status="online",
            amount_increment=Decimal("0.0001"),
            amount_size_min=None,
            amount_size_max=None,
            price_increment=Decimal("0.001"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.002"),
            maker_fee=Decimal("0.002"),
            margin_trading_enabled=False,
        ),
        SpotContractData(
            symbol="ALEPHUSDT",
            base_id=translator.to_cm_id("aleph"),
            quote_id=translator.to_cm_id("usdt"),
            base_name=translator.translate("aleph"),
            quote_name=translator.translate("usdt"),
            native_base_name="ALEPH",
            native_quote_name="USDT",
            listing_date=SpotContractData.DEFAULT_LISTING_DATE,
            end_date=None,
            is_current=True,
            status="offline",
            amount_increment=Decimal("5"),
            amount_size_min=None,
            amount_size_max=None,
            price_increment=Decimal("0.0001"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.002"),
            maker_fee=Decimal("0.002"),
            margin_trading_enabled=False,
        ),
        SpotContractData(
            symbol="QNTUSDT",
            base_id=translator.to_cm_id("qnt"),
            quote_id=translator.to_cm_id("usdt"),
            base_name=translator.translate("qnt"),
            quote_name=translator.translate("usdt"),
            native_base_name="QNT",
            native_quote_name="USDT",
            listing_date=SpotContractData.DEFAULT_LISTING_DATE,
            end_date=None,
            is_current=True,
            status="online",
            amount_increment=Decimal("0.01"),
            amount_size_min=None,
            amount_size_max=None,
            price_increment=Decimal("0.001"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.002"),
            maker_fee=Decimal("0.002"),
            margin_trading_enabled=False,
        ),
    ]
    assert spot_metadata == expected_metadata


@patch("src.octopus.exchange.mexc.MEXCHttpApi._get_futures_markets", _get_mexc_futures_markets)
def test_futures_metadata() -> None:
    # TODO: Implement VEX-based standardised tests
    translator = ExchangeTickerTranslator(EXCHANGE_NAME, glib_exchange(), glib_currency())
    mexc_http_api = MEXCHttpApi(exchange_name="MEXC", ticker_translator=translator, diagnostics=Mock())
    http_client_mock = Mock()
    futures_metadata = mexc_http_api.futures_markets_metadata(client=http_client_mock)
    expected_metadata = [
        FuturesContractData(
            symbol="BTC_USDT",
            underlying_base_id=translator.to_cm_id("btc"),
            underlying_quote_id=translator.to_cm_id("usdt"),
            underlying_base_name="btc",
            underlying_quote_name="usdt",
            size_asset_name="btc",
            size_asset_id=translator.to_cm_id("btc"),
            margin_asset_name="usdt",
            margin_asset_id=translator.to_cm_id("usdt"),
            underlying_native_base_name="BTC",
            underlying_native_quote_name="USDT",
            listing_date=None,
            expiry_date=None,
            contract_size=Decimal("0.0001"),
            tick_size=Decimal("0.1"),
            status="online",
            amount_increment=Decimal("1"),
            amount_size_min=Decimal("1"),
            amount_size_max=Decimal("5000000"),
            price_increment=Decimal("0.1"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.0006"),
            maker_fee=Decimal("0.0002"),
            margin_trading_enabled=None,
        ),
        FuturesContractData(
            symbol="ETH_USDT",
            underlying_base_id=translator.to_cm_id("eth"),
            underlying_quote_id=translator.to_cm_id("usdt"),
            underlying_base_name="eth",
            underlying_quote_name="usdt",
            size_asset_name="eth",
            size_asset_id=translator.to_cm_id("eth"),
            margin_asset_name="usdt",
            margin_asset_id=translator.to_cm_id("usdt"),
            underlying_native_base_name="ETH",
            underlying_native_quote_name="USDT",
            listing_date=None,
            expiry_date=None,
            contract_size=Decimal("0.01"),
            tick_size=Decimal("0.01"),
            status="online",
            amount_increment=Decimal("1"),
            amount_size_min=Decimal("1"),
            amount_size_max=Decimal("800000"),
            price_increment=Decimal("0.01"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.0006"),
            maker_fee=Decimal("0.0002"),
            margin_trading_enabled=None,
        ),
        FuturesContractData(
            symbol="APT_USDT",
            underlying_base_id=translator.to_cm_id("apt"),
            underlying_quote_id=translator.to_cm_id("usdt"),
            underlying_base_name="apt",
            underlying_quote_name="usdt",
            size_asset_name="apt",
            size_asset_id=translator.to_cm_id("apt"),
            margin_asset_name="usdt",
            margin_asset_id=translator.to_cm_id("usdt"),
            underlying_native_base_name="APT",
            underlying_native_quote_name="USDT",
            listing_date=None,
            expiry_date=None,
            contract_size=Decimal("0.1"),
            tick_size=Decimal("0.001"),
            status="online",
            amount_increment=Decimal("1"),
            amount_size_min=Decimal("1"),
            amount_size_max=Decimal("500000"),
            price_increment=Decimal("0.001"),
            price_size_min=None,
            price_size_max=None,
            order_size_min=None,
            taker_fee=Decimal("0.0006"),
            maker_fee=Decimal("0.0002"),
            margin_trading_enabled=None,
        ),
    ]
    assert futures_metadata == expected_metadata
