# Based on test_bitstamp.py
from decimal import Decimal
from typing import Any, Dict, List, Tuple, TypedDict
from unittest import mock
from unittest.mock import Mock

import pytest

from src.octopus.data import (
    BookData,
    BookType,
    FuturesContractData,
    Instrument,
    MarketType,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.cryptocom import EXCHANGE_NAME
from src.octopus.qa_utils import standard_book_stream_test, standard_last_trades_test, standard_trade_stream_test
from src.utils.timeutil import dt_from_any, dt_from_any_aware, dt_from_ms

BTC_USDT = Instrument.spot("BTC_USDT", "btc", "usdt")
ETH_USDT = Instrument.spot("ETH_USDT", "eth", "usdt")
BTCUSD_PERP = Instrument.futures("BTCUSD-PERP")
ETHUSD_PERP = Instrument.futures("ETHUSD-PERP")
BTCUSD_230929 = Instrument.futures("BTCUSD-230929")


"""
From: https://api.crypto.com/v2/public/get-trades?instrument_name=BTC_USDT
{"code":0,"method":"public/get-trades","result":{"instrument_name":"BTC_USDT",
"data":[
{"dataTime":1650386744390,"d":2434372175627908352,"s":"SELL","p":41271.25,"q":0.013783,"t":1650386744389,"i":"BTC_USDT"},
{"dataTime":1650386744256,"d":2434372171080054144,"s":"SELL","p":41271.23,"q":0.000121,"t":1650386744254,"i":"BTC_USDT"},
{"dataTime":1650386744197,"d":2434372169170930208,"s":"BUY","p":41271.24,"q":0.000018,"t":1650386744197,"i":"BTC_USDT"},
{"dataTime":1650386744196,"d":2434372169142715712,"s":"BUY","p":41271.24,"q":0.000244,"t":1650386744196,"i":"BTC_USDT"},
...
"""


class AnyStringWith(str):
    def __eq__(self, other: Any) -> bool:
        return self in other


class MyTest(TypedDict):
    input: Dict[str, Any]
    expected: List[TradeData]


tests = {
    "good_response": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id              q: amount      p: price       s: is_buy   t: timestamp      i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    {
                        "d": 2434372171080054144,
                        "q": "0.000121",
                        "p": "41271.23",
                        "s": "sell",
                        "t": 1650386744254,
                        "i": "BTC_USDT",
                    },
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[
            TradeData(2434372169170930208, Decimal("0.000018"), Decimal("41271.24"), True, dt_from_ms(1650386744197)),
            TradeData(2434372171080054144, Decimal("0.000121"), Decimal("41271.23"), False, dt_from_ms(1650386744254)),
            TradeData(2434372175627908352, Decimal("0.013783"), Decimal("41271.25"), False, dt_from_ms(1650386744389)),
        ],
    ),
    "missing_field": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id             q: amount      p: price       s: is_buy   t: timestamp        i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254},
                    {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "i": "BTC_USDT"},
                    {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "t": 1650386744254, "i": "BTC_USDT"},
                    {"d": 2434372171080054144, "q": "0.000121", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {"d": 2434372171080054144, "p": "41271.23", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {"q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {"d": 2434372171080054144},
                    {"q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[
            TradeData(2434372169170930208, Decimal("0.000018"), Decimal("41271.24"), True, dt_from_ms(1650386744197)),
            # TradeData(2434372171080054144, Decimal('0.000121'), Decimal('41271.23'), False, dt_from_ms(1650386744254)),
            TradeData(2434372175627908352, Decimal("0.013783"), Decimal("41271.25"), False, dt_from_ms(1650386744389)),
        ],
    ),
    "no_data": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
            },
        },
        expected=[],
    ),
    "no_result": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
        },
        expected=[],
    ),
    "zero_amount_q": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id                q: amount        p: price         s: is_buy   t: timestamp        i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    # {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254,
                    # "i": "BTC_USDT"},
                    {"d": 2434372171080054144, "q": "0", "p": "41271.23", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[],
    ),
    "zero_amount_d": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id                q: amount        p: price         s: is_buy   t: timestamp        i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    # {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254,
                    # "i": "BTC_USDT"},
                    {"d": 0, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254, "i": "BTC_USDT"},
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[],
    ),
    "zero_amount_t": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id                q: amount        p: price         s: is_buy   t: timestamp        i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    # {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254,
                    # "i": "BTC_USDT"},
                    {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 0, "i": "BTC_USDT"},
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[],
    ),
    "bad_symbol": MyTest(
        input={
            "code": 0,
            "method": "public/get-trades",
            "result": {
                "instrument_name": "BTC_USDT",
                "data": [
                    # d:trade_id                q: amount        p: price         s: is_buy   t: timestamp        i: instrument
                    {
                        "d": 2434372169170930208,
                        "q": "0.000018",
                        "p": "41271.24",
                        "s": "buy",
                        "t": 1650386744197,
                        "i": "BTC_USDT",
                    },
                    # {"d": 2434372171080054144, "q": "0.000121", "p": "41271.23", "s": "sell", "t": 1650386744254,
                    # "i": "BTC_USDT"},
                    {
                        "d": 2434372171080054144,
                        "q": "0.000121",
                        "p": "41271.23",
                        "s": "sell",
                        "t": 1650386744254,
                        "i": "BTC_USDTZZZ",
                    },
                    {
                        "d": 2434372175627908352,
                        "q": "0.013783",
                        "p": "41271.25",
                        "s": "sell",
                        "t": 1650386744389,
                        "i": "BTC_USDT",
                    },
                ],
            },
        },
        expected=[],
    ),
}


def test_last_trades() -> None:
    standard_last_trades_test(EXCHANGE_NAME, BTC_USDT, tests["good_response"]["expected"])


def get_trades_good_response(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["good_response"]["input"]


def get_trades_missing_field(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["missing_field"]["input"]


def get_trades_no_data(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["no_data"]["input"]


def get_trades_no_result(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["no_result"]["input"]


def get_trades_zero_amount_q(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["zero_amount_q"]["input"]


def get_trades_zero_amount_d(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["zero_amount_d"]["input"]


def get_trades_zero_amount_t(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["zero_amount_t"]["input"]


def get_trades_bad_symbol(var1: Any, var2: Any, var3: Any) -> Tuple[str, str, Dict[str, Any]]:
    return "url", "RESPONSE", tests["bad_symbol"]["input"]


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["good_response"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_good_response)
def test_last_trades_good_response(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result
    diagnostics_mock.info.assert_not_called()


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["missing_field"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_missing_field)
def test_last_trades_missing_field(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result
    diagnostics_mock.info.assert_called_with(AnyStringWith("Discarding incomplete trade"))


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["no_data"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_no_data)
def test_last_trades_no_data(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result
    diagnostics_mock.info.assert_called_with(AnyStringWith("Discarding mal-formed response"))


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["no_result"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_no_result)
def test_last_trades_no_result(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    diagnostics_mock.info.assert_called_with(AnyStringWith("Discarding mal-formed response"))


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["zero_amount_q"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_zero_amount_q)
def test_last_trades_zero_amount_q(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    assert "Discarding response" in str(diagnostics_mock.info.mock_calls[0]) and "url:" in str(
        diagnostics_mock.info.mock_calls[1]
    )
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["zero_amount_d"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_zero_amount_d)
def test_last_trades_zero_amount_d(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    assert "Discarding response" in str(diagnostics_mock.info.mock_calls[0]) and "url:" in str(
        diagnostics_mock.info.mock_calls[1]
    )
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["zero_amount_t"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_zero_amount_t)
def test_last_trades_zero_amount_t(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    assert "Discarding response" in str(diagnostics_mock.info.mock_calls[0]) and "url:" in str(
        diagnostics_mock.info.mock_calls[1]
    )
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result


@pytest.mark.parametrize("test_input, expected_result", [(None, tests["bad_symbol"]["expected"])])
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_trades", get_trades_bad_symbol)
def test_last_trades_bad_symbol(test_input: str, expected_result: List[TradeData]) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    http_api.last_trades(http_client_mock, BTC_USDT)
    assert "Discarding response" in str(diagnostics_mock.info.mock_calls[0]) and "url:" in str(
        diagnostics_mock.info.mock_calls[1]
    )
    assert http_api.last_trades(http_client_mock, BTC_USDT) == expected_result


def test_trade_stream() -> None:
    messages = """
    {"id":0,"code":0,"method":"subscribe"}
    {"id":1655228594246,"method":"public/heartbeat"}
    {"id":1655228623259,"method":"public/heartbeat","message":"server did not receive any client heartbeat, going to disconnect soon"}
    {"code":0,"method":"subscribe","result":{"instrument_name":"BTC_USDT","subscription":"trade.BTC_USDT","channel":"trade", "data":[{"dataTime":1655822565699,"d":2616768072112695593,"s":"BUY","p":"21645.12","q":"0.005392","t":1655822565698,"i":"BTC_USDT"}]}}
    {"code":0,"method":"subscribe","result":{"instrument_name":"ETH_USDT","subscription":"trade.ETH_USDT","channel":"trade","data":[{"dataTime":1655822565977,"d":2616768081435185921,"s":"BUY","p":"1189.24","q":"2.16839","t":1655822565976,"i":"ETH_USDT"},{"dataTime":1655822565977,"d":2616768081430481793,"s":"BUY","p":"1189.24","q":"2.03598","t":1655822565976,"i":"ETH_USDT"}]}}
    {"code":0,"method":"subscribe","result":{"instrument_name":"BTC_USDT","subscription":"trade.BTC_USDT","channel":"trade","data":[{"dataTime":1655917560731,"d":2619955576439923136,"s":"BUY","p":20253.37,"q":0.000065,"t":1655917560730,"i":"BTC_USDT"},{"dataTime":1655917560634,"d":2619955573182493312,"s":"SELL","p":20252.83,"q":0.503262,"t":1655917560633,"i":"BTC_USDT"}]}}
    """  # noqa: E501

    expected_trades = [
        (BTC_USDT, TradeData(2616768072112695593, Decimal("0.005392"), Decimal("21645.12"), True, dt_from_ms(1655822565698))),
        (ETH_USDT, TradeData(2616768081435185921, Decimal("2.16839"), Decimal("1189.24"), True, dt_from_ms(1655822565976))),
        (ETH_USDT, TradeData(2616768081430481793, Decimal("2.03598"), Decimal("1189.24"), True, dt_from_ms(1655822565976))),
        (BTC_USDT, TradeData(2619955576439923136, Decimal("0.000065"), Decimal("20253.37"), True, dt_from_ms(1655917560730))),
        (BTC_USDT, TradeData(2619955573182493312, Decimal("0.503262"), Decimal("20252.83"), False, dt_from_ms(1655917560633))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT, ETH_USDT], messages, expected_trades, expected_connect_count=1)


def test_trade_futures_stream() -> None:
    messages = """
    {"id": -9223372036854775808, "code": 0, "method": "subscribe", "result": {"channel": "trade", "subscription": "trade.BTCUSD-PERP", "instrument_name": "BTCUSD-PERP", "data": [{"d": "4611686018427576530", "t": 1682155552312, "p": "1.2188", "q": "1", "s": "SELL", "i": "BTCUSD-PERP"}, {"d": "4611686018427576529", "t": 1682155518703, "p": "1.2181", "q": "1", "s": "BUY", "i": "BTCUSD-PERP"}]}}
    {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "trade", "subscription": "trade.ETHUSD-PERP", "instrument_name": "ETHUSD-PERP", "data": [{"d": "4611686018474745026", "t": 1682155789649, "p": "1850.50", "q": "0.0125", "s": "SELL", "i": "ETHUSD-PERP"}]}}
    """  # noqa: E501

    expected_trades = [
        (
            BTCUSD_PERP,
            TradeData(4611686018427576530, Decimal("1"), Decimal("1.2188"), False, dt_from_ms(1682155552312)),
        ),
        (
            BTCUSD_PERP,
            TradeData(4611686018427576529, Decimal("1"), Decimal("1.2181"), True, dt_from_ms(1682155518703)),
        ),
        (ETHUSD_PERP, TradeData(4611686018474745026, Decimal("0.0125"), Decimal("1850.50"), False, dt_from_ms(1682155789649))),
    ]
    standard_trade_stream_test(
        EXCHANGE_NAME, [BTCUSD_PERP, ETHUSD_PERP], messages, expected_trades, market_type=MarketType.FUTURES
    )


def get_spot_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "BTC_USDT",
            "inst_type": "CCY_PAIR",
            "display_name": "BTC/USDT",
            "base_ccy": "BTC",
            "quote_ccy": "USDT",
            "quote_decimals": 2,
            "quantity_decimals": 5,
            "price_tick_size": "0.01",
            "qty_tick_size": "0.00001",
            "max_leverage": "50",
            "tradable": True,
            "expiry_timestamp_ms": 0,
            "beta_product": False,
            "margin_buy_enabled": True,
            "margin_sell_enabled": True,
        },
        {
            "symbol": "ETH_USDT",
            "inst_type": "CCY_PAIR",
            "display_name": "ETH/USDT",
            "base_ccy": "ETH",
            "quote_ccy": "USDT",
            "quote_decimals": 2,
            "quantity_decimals": 4,
            "price_tick_size": "0.01",
            "qty_tick_size": "0.0001",
            "max_leverage": "50",
            "tradable": True,
            "expiry_timestamp_ms": 0,
            "beta_product": False,
            "margin_buy_enabled": True,
            "margin_sell_enabled": False,
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDT,
            SpotContractData(
                symbol="BTC_USDT",
                base_id=0,
                quote_id=100,
                base_name="btc",
                quote_name="usdt",
                native_base_name="BTC",
                native_quote_name="USDT",
                listing_date=dt_from_any_aware(SpotContractData.DEFAULT_LISTING_DATE),
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.00001"),
                price_increment=Decimal("0.01"),
                margin_trading_enabled=True,
            ),
        ),
        (
            ETH_USDT,
            SpotContractData(
                symbol="ETH_USDT",
                base_id=6,
                quote_id=100,
                base_name="eth",
                quote_name="usdt",
                native_base_name="ETH",
                native_quote_name="USDT",
                listing_date=dt_from_any_aware(SpotContractData.DEFAULT_LISTING_DATE),
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.0001"),
                price_increment=Decimal("0.01"),
                margin_trading_enabled=False,
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_spot_markets", get_spot_markets)
def test_spot_metadata_base(instrument: Instrument, expected_result: SpotContractData) -> None:
    diagnostics_mock = mock.Mock()
    http_client_mock = mock.Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    for contract_data in http_api.spot_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def get_futures_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "BTCUSD-PERP",
            "inst_type": "PERPETUAL_SWAP",
            "display_name": "BTCUSD Perpetual",
            "base_ccy": "BTC",
            "quote_ccy": "USD",
            "quote_decimals": 1,
            "quantity_decimals": 4,
            "price_tick_size": "0.1",
            "qty_tick_size": "0.0001",
            "max_leverage": "100",
            "tradable": True,
            "expiry_timestamp_ms": 0,
            "beta_product": False,
            "underlying_symbol": "BTCUSD-INDEX",
            "contract_size": "1",
            "margin_buy_enabled": False,
            "margin_sell_enabled": False,
        },
        {
            "symbol": "BTCUSD-230929",
            "inst_type": "FUTURE",
            "display_name": "BTCUSD Futures 20230929",
            "base_ccy": "BTC",
            "quote_ccy": "USD",
            "quote_decimals": 1,
            "quantity_decimals": 4,
            "price_tick_size": "0.1",
            "qty_tick_size": "0.0001",
            "max_leverage": "100",
            "tradable": False,
            "expiry_timestamp_ms": 1695974400000,
            "beta_product": False,
            "underlying_symbol": "BTCUSD-INDEX",
            "contract_size": "1",
            "margin_buy_enabled": False,
            "margin_sell_enabled": False,
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTCUSD_PERP,
            FuturesContractData(
                symbol="BTCUSD-PERP",
                underlying_base_id=0,
                underlying_quote_id=3,
                size_asset_id=0,
                margin_asset_id=3,
                underlying_base_name="btc",
                underlying_quote_name="usd",
                size_asset_name="btc",
                margin_asset_name="usd",
                underlying_native_base_name="BTC",
                underlying_native_quote_name="USD",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                expiry_date=None,
                contract_size=Decimal("1"),
                tick_size=Decimal("0.0001"),
                status="online",
                amount_increment=Decimal("0.0001"),
                price_increment=Decimal("0.1"),
            ),
        ),
        (
            BTCUSD_230929,
            FuturesContractData(
                symbol="BTCUSD-230929",
                underlying_base_id=0,
                underlying_quote_id=3,
                size_asset_id=0,
                margin_asset_id=3,
                underlying_base_name="btc",
                underlying_quote_name="usd",
                size_asset_name="btc",
                margin_asset_name="usd",
                underlying_native_base_name="BTC",
                underlying_native_quote_name="USD",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                expiry_date=dt_from_any(1695974400000),
                contract_size=Decimal("1"),
                tick_size=Decimal("0.0001"),
                status="offline",
                amount_increment=Decimal("0.0001"),
                price_increment=Decimal("0.1"),
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.cryptocom.CryptoComHttpApi._get_futures_markets", get_futures_markets)
def test_futures_metadata_base(instrument: Instrument, expected_result: FuturesContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Crypto.com", [], diagnostics_mock)
    for contract_data in http_api.futures_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def test_book_spot_stream() -> None:
    messages = """
        {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "book", "subscription": "book.BTC_USDT", "instrument_name": "BTC_USDT", "depth": 150, "data": [{"asks": [["29819.86", "0.50312", "1"], ["29820.55", "0.09521", "1"], ["29822.24", "0.15000", "1"]], "bids": [["29819.85", "0.00005", "1"], ["29816.59", "0.00005", "1"], ["29815.65", "0.00313", "1"]], "t": 1682525745556, "tt": 1682525745531, "u": 36407363479808, "cs": -1832798423}]}}
        {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "book", "subscription": "book.BTC_USDT", "instrument_name": "BTC_USDT", "depth": 150, "data": [{"asks": [["29819.86", "0.50312", "1"], ["29820.55", "0.09521", "1"], ["29822.37", "0.15000", "1"]], "bids": [["29819.85", "0.00005", "1"], ["29816.59", "0.00005", "1"], ["29815.65", "0.00313", "1"]], "t": 1682525745874, "tt": 1682525745868, "u": 36407364120736, "cs": -1368431692}]}}
        {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "book", "subscription": "book.BTC_USDT", "instrument_name": "BTC_USDT", "depth": 150, "data": [{"asks": [["29819.86", "0.50310", "1"], ["29820.54", "0.01947", "2"], ["29820.55", "0.15000", "1"]], "bids": [["29819.85", "0.00005", "3"], ["29816.59", "0.00005", "1"], ["29815.65", "0.00313", "1"]], "t": 1682525746161, "tt": 1682525746147, "u": 36407364868384, "cs": 1009714971}]}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=36407364868384,
        exchange_time=dt_from_any_aware(1682525746147),
        bids=[
            PriceLevel(price=Decimal("29819.85"), amount=Decimal("0.00005"), count=3),
            PriceLevel(price=Decimal("29816.59"), amount=Decimal("0.00005"), count=1),
            PriceLevel(price=Decimal("29815.65"), amount=Decimal("0.00313"), count=1),
        ],
        asks=[
            PriceLevel(price=Decimal("29819.86"), amount=Decimal("0.50310"), count=1),
            PriceLevel(price=Decimal("29820.54"), amount=Decimal("0.01947"), count=2),
            PriceLevel(price=Decimal("29820.55"), amount=Decimal("0.15000"), count=1),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USDT,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
    )


def test_book_future_stream() -> None:
    messages = """
        {"id": -9223372036854775808, "code": 0, "method": "subscribe", "result": {"channel": "book", "subscription": "book.BTCUSD-PERP.50", "instrument_name": "BTCUSD-PERP", "depth": 50, "data": [{"asks": [["29375.0", "0.0110", "1"], ["29377.7", "0.0200", "1"], ["29378.0", "0.0002", "1"]], "bids": [["29374.9", "0.1671", "5"], ["29374.7", "0.0300", "1"], ["29374.5", "0.0402", "3"]], "t": 1682779599600, "tt": 1682779599600, "u": 37019966315456, "cs": -810562985}]}}
        {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "book.update", "subscription": "book.BTCUSD-PERP.50", "instrument_name": "BTCUSD-PERP", "depth": 50, "data": [{"update": {"asks": [["29375.0", "0.0", "0"], ["29377.7", "0.0900", "3"], ["29378.0", "0.0850", "2"], ["29379.1", "0.0250", "2"]], "bids": []}, "t": 1682779599612, "tt": 1682779599600, "u": 37019966334752, "pu": 37019966315456, "cs": -1027642870}]}}
        {"id": -1, "code": 0, "method": "subscribe", "result": {"channel": "book.update", "subscription": "book.BTCUSD-PERP.50", "instrument_name": "BTCUSD-PERP", "depth": 50, "data": [{"update": {"asks": [["29379.1", "0.0250", "1"]], "bids": [["29374.9", "0.2671", "2"]]}, "t": 1682779599612, "tt": 1682779599600, "u": 37019966334752, "pu": 37019966315456, "cs": -1027642870}]}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_any_aware(1682779599600),
        bids=[
            PriceLevel(price=Decimal("29374.9"), amount=Decimal("0.2671"), count=2),
            PriceLevel(price=Decimal("29374.7"), amount=Decimal("0.0300"), count=1),
            PriceLevel(price=Decimal("29374.5"), amount=Decimal("0.0402"), count=3),
        ],
        asks=[
            PriceLevel(price=Decimal("29377.7"), amount=Decimal("0.0900"), count=3),
            PriceLevel(price=Decimal("29378.0"), amount=Decimal("0.0850"), count=2),
            PriceLevel(price=Decimal("29379.1"), amount=Decimal("0.0250"), count=1),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        BTCUSD_PERP,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
    )
