from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List
from unittest import mock

import pytest
from freezegun import freeze_time

from src.octopus.data import (
    BookData,
    BookType,
    FundingRateData,
    Instrument,
    LiquidationData,
    Market,
    MarketType,
    OptionTicker,
    OptionTickerData,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.deribit import EXCHANGE_BIRTHDAY, EXCHANGE_NAME, MAX_TRADES_PER_REQUEST, TIMESTAMP_RESOLUTION
from src.octopus.qa_utils import (
    standard_book_stream_test,
    standard_book_test,
    standard_funding_rate_history_traversal_test,
    standard_last_funding_rates_test,
    standard_last_liquidations_test,
    standard_liquidation_history_traversal_test,
    standard_quote_stream_test,
    standard_ticker_stream_test,
    standard_trade_history_traversal_test,
    standard_trade_stream_test,
)
from src.octopus.vex.generate import (
    GeneratedLiquidationTimeRange,
    GeneratedTradeTimeRange,
    monotonic_time_liquidation_sequence,
    monotonic_time_trade_sequence,
)
from src.utils.timeutil import dt_from_any_aware, dt_from_ms, dt_from_ms_aware, dt_from_us, truncate_to_minute

BTC_USDC = Instrument.spot("BTC_USDC", "btc", "usdc")
ETH_BTC = Instrument.spot("ETH_BTC", "eth", "btc")
BTC_USD_PERPETUAL = Instrument.futures("BTC-PERPETUAL", metadata={"contract_size": Decimal("10")})
ETH_USD_PERPETUAL = Instrument.futures("ETH-PERPETUAL", metadata={"contract_size": Decimal("1")})
ETH_USD_27MAR20 = Instrument.futures("ETH-27MAR20", metadata={"contract_size": Decimal("1")})
UNI_USDC_PERPETUAL = Instrument.futures("UNI_USDC-PERPETUAL")

ETH_14MAR21_1800_C = Instrument.option("ETH-14MAR21-1800-C", metadata={"contract_size": Decimal("1")})
BTC_27FEB25_86000_C = Instrument.option("BTC-27FEB25-86000-C")
BNB_USDC_27FEB25_530_C = Instrument.option("BNB_USDC-27FEB25-530-C")


def test_trade_history_traversal() -> None:
    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(), MAX_TRADES_PER_REQUEST - 1),
            ],
            align_to=TIMESTAMP_RESOLUTION,
            with_mark_and_index_price=True,
        ),
    )

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(), timedelta(), MAX_TRADES_PER_REQUEST * 2 - 2),
            ],
            align_to=TIMESTAMP_RESOLUTION,
            with_mark_and_index_price=True,
        ),
    )

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(), timedelta(), MAX_TRADES_PER_REQUEST * 2 - 1),
            ],
            align_to=TIMESTAMP_RESOLUTION,
            with_mark_and_index_price=True,
        ),
        trades_should_be_missed=True,
    )


def test_spot_trade_stream() -> None:
    messages = """
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "trades.BTC_USDC.100ms", "data": [{"trade_seq": 167761, "trade_id": "BTC_USDC-596099", "timestamp": 1698584751971, "tick_direction": 1, "price": "34440.0", "mark_price": "34447.4013", "instrument_name": "BTC_USDC", "index_price": "34447.4013", "direction": "sell", "amount": "0.0001", "liquidation": "M"}]}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "trades.ETH_BTC.100ms", "data": [{"trade_seq": 264545, "trade_id": "ETH_BTC-596100", "timestamp": 1698584753333, "tick_direction": 1, "price": "0.0522", "mark_price": "0.052209", "instrument_name": "ETH_BTC", "index_price": "0.052209", "direction": "buy", "amount": "0.008", "liquidation": "T"}]}}
    """  # noqa: E501
    expected_trades = [
        (
            BTC_USDC,
            TradeData(
                trade_id=596099,
                amount=Decimal("0.0001"),
                price=Decimal("34440.0"),
                is_buy=False,
                time=dt_from_ms(1698584751971),
                mark_price=Decimal("34447.4013"),
                index_price=Decimal("34447.4013"),
                liquidation="M",
            ),
        ),
        (
            ETH_BTC,
            TradeData(
                trade_id=596100,
                amount=Decimal("0.008"),
                price=Decimal("0.0522"),
                is_buy=True,
                time=dt_from_ms(1698584753333),
                mark_price=Decimal("0.052209"),
                index_price=Decimal("0.052209"),
                liquidation="T",
            ),
        ),
    ]
    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDC, ETH_BTC], messages, expected_trades, market_type=MarketType.SPOT)


def test_futures_trade_stream() -> None:
    messages = """
        {"jsonrpc":"2.0","id":1,"error":{},"usIn":1583143035546815,"usOut":1583143035546847,"usDiff":32,"testnet":false}

        {"jsonrpc":"2.0","id":2,"result":["trades.ETH-PERPETUAL.100ms"],"usIn":1583064354354595,"usOut":1583064354354614,"usDiff":19,"testnet":false}
        {"jsonrpc":"2.0","id":0,"result":["trades.ETH-27MAR20.100ms"],"usIn":1583064354219069,"usOut":1583064354219121,"usDiff":52,"testnet":false}

        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-PERPETUAL.100ms","data":[{"trade_seq":10457692,"trade_id":"ETH-13271966","timestamp":1583064356786,"tick_direction":3,"price":227.5,"instrument_name":"ETH-PERPETUAL","index_price":227.31,"mark_price":227.31,"direction":"sell","amount":2319.0},{"trade_seq":10457693,"trade_id":"ETH-13271967","timestamp":1583064356786,"tick_direction":3,"price":227.3,"instrument_name":"ETH-PERPETUAL","index_price":227.31,"mark_price":227.31,"liquidation":"MT","direction":"buy","amount":5.0}]}}

        {"jsonrpc":"2.0","id":1,"result":["trades.BTC-PERPETUAL.100ms"],"usIn":1583064354354496,"usOut":1583064354354540,"usDiff":44,"testnet":false}

        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.BTC-PERPETUAL.100ms","data":[{"trade_seq":39073400,"trade_id":"63950191","timestamp":1583064362142,"tick_direction":0,"price":8750.0,"instrument_name":"BTC-PERPETUAL","index_price":8741.11,"mark_price":8741.11,"direction":"buy","amount":30.0},{"trade_seq":39073401,"trade_id":"63950192","timestamp":1583064362142,"tick_direction":1,"price":8750.0,"instrument_name":"BTC-PERPETUAL","index_price":8741.11,"mark_price":8741.11,"direction":"sell","amount":100.0}]}}

        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-27MAR20.100ms","data":[{"trade_seq":761893,"trade_id":"ETH-13272015","timestamp":1583064375072,"tick_direction":2,"price":230.2,"instrument_name":"ETH-27MAR20","index_price":227.42,"mark_price":227.42,"direction":"sell","amount":243.0}]}}
    """

    expected_trades = [
        (
            ETH_USD_PERPETUAL,
            TradeData(
                trade_id=13271966,
                amount=Decimal("2319.0"),
                price=Decimal("227.5"),
                is_buy=False,
                time=dt_from_us(1583064356786000),
                index_price=Decimal("227.31"),
                mark_price=Decimal("227.31"),
            ),
        ),
        (
            ETH_USD_PERPETUAL,
            TradeData(
                trade_id=13271967,
                amount=Decimal("5.0"),
                price=Decimal("227.3"),
                is_buy=True,
                time=dt_from_us(1583064356786000),
                index_price=Decimal("227.31"),
                mark_price=Decimal("227.31"),
                liquidation="MT",
            ),
        ),
        (
            BTC_USD_PERPETUAL,
            TradeData(
                trade_id=63950191,
                amount=Decimal("3.0"),
                price=Decimal("8750.0"),
                is_buy=True,
                time=dt_from_us(1583064362142000),
                index_price=Decimal("8741.11"),
                mark_price=Decimal("8741.11"),
            ),
        ),
        (
            BTC_USD_PERPETUAL,
            TradeData(
                trade_id=63950192,
                amount=Decimal("10.0"),
                price=Decimal("8750.0"),
                is_buy=False,
                time=dt_from_us(1583064362142000),
                index_price=Decimal("8741.11"),
                mark_price=Decimal("8741.11"),
            ),
        ),
        (
            ETH_USD_27MAR20,
            TradeData(
                trade_id=13272015,
                amount=Decimal("243.0"),
                price=Decimal("230.2"),
                is_buy=False,
                time=dt_from_us(1583064375072000),
                index_price=Decimal("227.42"),
                mark_price=Decimal("227.42"),
            ),
        ),
    ]

    standard_trade_stream_test(
        EXCHANGE_NAME,
        [BTC_USD_PERPETUAL, ETH_USD_PERPETUAL, ETH_USD_27MAR20],
        messages,
        expected_trades,
        market_type=MarketType.FUTURES,
        expected_connect_count=2,
    )


def test_option_trade_stream() -> None:
    messages = """
        {"jsonrpc":"2.0","id":1,"error":{},"usIn":1583143035546815,"usOut":1583143035546847,"usDiff":32,"testnet":false}

        {"jsonrpc":"2.0","id":9,"result":["trades.ETH-14MAR21-1800-C.100ms"],"usIn":1615646978121771,"usOut":1615646978121837,"usDiff":66,"testnet":false}

        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-14MAR21-1800-C.100ms","data":[{"trade_seq":17,"trade_id":"ETH-51404041","timestamp":1615622830604,"tick_direction":1,"price":0.016,"mark_price":0.017032,"iv":82.73,"instrument_name":"ETH-14MAR21-1800-C","index_price":1793.82,"direction":"buy","amount":1.0, "liquidation": "M"}]}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-14MAR21-1800-C.100ms","data":[{"trade_seq":18,"trade_id":"ETH-51404333","timestamp":1615623206490,"tick_direction":2,"price":0.015,"mark_price":0.015788,"iv":81.59,"instrument_name":"ETH-14MAR21-1800-C","index_price":1791.27,"direction":"buy","amount":4.0, "liquidation": "T"}]}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-14MAR21-1800-C.100ms","data":[{"trade_seq":19,"trade_id":"ETH-51404999","timestamp":1615623986797,"tick_direction":2,"price":0.012,"mark_price":0.013392,"iv":74.49,"instrument_name":"ETH-14MAR21-1800-C","index_price":1784.56,"direction":"sell","amount":2.0},{"trade_seq":20,"trade_id":"ETH-51405000","timestamp":1615623986797,"tick_direction":3,"price":0.012,"mark_price":0.013392,"iv":74.49,"instrument_name":"ETH-14MAR21-1800-C","index_price":1784.56,"direction":"sell","amount":3.0}]}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"trades.ETH-14MAR21-1800-C.100ms","data":[{"trade_seq":21,"trade_id":"ETH-51406995","timestamp":1615626213094,"tick_direction":3,"price":0.012,"mark_price":0.013566,"iv":74.4,"instrument_name":"ETH-14MAR21-1800-C","index_price":1785.55,"direction":"sell","amount":10.0},{"trade_seq":22,"trade_id":"ETH-51406996","timestamp":1615626213094,"tick_direction":3,"price":0.012,"mark_price":0.013566,"iv":74.4,"instrument_name":"ETH-14MAR21-1800-C","index_price":1785.55,"direction":"sell","amount":329.0},{"trade_seq":23,"trade_id":"ETH-51406997","timestamp":1615626213094,"tick_direction":3,"price":0.012,"mark_price":0.013566,"iv":74.4,"instrument_name":"ETH-14MAR21-1800-C","index_price":1785.55,"direction":"sell","amount":13.0}]}}
    """  # noqa: E501

    expected_trades = [
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51404041,
                Decimal("1"),
                Decimal("0.016"),
                True,
                dt_from_us(1615622830604 * 1000),
                Decimal("82.73") / Decimal(100),
                Decimal("0.017032"),
                Decimal("1793.82"),
                liquidation="M",
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51404333,
                Decimal("4"),
                Decimal("0.015"),
                True,
                dt_from_us(1615623206490 * 1000),
                Decimal("81.59") / Decimal(100),
                Decimal("0.015788"),
                Decimal("1791.27"),
                liquidation="T",
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51404999,
                Decimal("2"),
                Decimal("0.012"),
                False,
                dt_from_us(1615623986797 * 1000),
                Decimal("74.49") / Decimal(100),
                Decimal("0.013392"),
                Decimal("1784.56"),
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51405000,
                Decimal("3"),
                Decimal("0.012"),
                False,
                dt_from_us(1615623986797 * 1000),
                Decimal("74.49") / Decimal(100),
                Decimal("0.013392"),
                Decimal("1784.56"),
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51406995,
                Decimal("10"),
                Decimal("0.012"),
                False,
                dt_from_us(1615626213094 * 1000),
                Decimal("74.4") / Decimal(100),
                Decimal("0.013566"),
                Decimal("1785.55"),
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51406996,
                Decimal("329"),
                Decimal("0.012"),
                False,
                dt_from_us(1615626213094 * 1000),
                Decimal("74.4") / Decimal(100),
                Decimal("0.013566"),
                Decimal("1785.55"),
            ),
        ),
        (
            ETH_14MAR21_1800_C,
            TradeData(
                51406997,
                Decimal("13"),
                Decimal("0.012"),
                False,
                dt_from_us(1615626213094 * 1000),
                Decimal("74.4") / Decimal(100),
                Decimal("0.013566"),
                Decimal("1785.55"),
            ),
        ),
    ]

    options = [ETH_14MAR21_1800_C]  # TODO: additional options
    standard_trade_stream_test(
        EXCHANGE_NAME, options, messages, expected_trades, market_type=MarketType.OPTION, expected_connect_count=2
    )


def test_liquidation_history_traversal() -> None:
    second = timedelta(seconds=1)

    standard_liquidation_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        monotonic_time_liquidation_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedLiquidationTimeRange(timedelta(days=4), timedelta(days=1) - second, 2),
                GeneratedLiquidationTimeRange(second, timedelta(days=4) - second, 2),
                GeneratedLiquidationTimeRange(timedelta(days=4) + second, timedelta(days=1), 2),
            ],
        ),
    )


def test_last_liquidations() -> None:
    standard_last_liquidations_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        [
            LiquidationData(
                63950191, Decimal("0.00581300"), Decimal("10983.51000000"), True, False, dt_from_us(1564964484115000)
            ),
            LiquidationData(
                63950192, Decimal("8.66100000"), Decimal("10980.02030000"), False, False, dt_from_us(1564964532973000)
            ),
        ],
    )


def test_funding_rates_history_traversal() -> None:
    standard_funding_rate_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        [
            FundingRateData(
                Decimal(f"0.0{i}"), timedelta(hours=8), timedelta(milliseconds=1), EXCHANGE_BIRTHDAY + timedelta(hours=8 * i)
            )
            for i in range(1, min(int((datetime.now(UTC) - EXCHANGE_BIRTHDAY).total_seconds()) // 3600, 2000))
        ],
    )


def test_last_funding_rates() -> None:
    standard_last_funding_rates_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        [
            FundingRateData(
                Decimal("0.00581301"), timedelta(hours=8), timedelta(milliseconds=1), datetime(2020, 11, 9, 0, 0, 0, tzinfo=UTC)
            ),
            FundingRateData(
                Decimal("-0.66100002"), timedelta(hours=8), timedelta(milliseconds=1), datetime(2020, 11, 9, 1, 0, 0, tzinfo=UTC)
            ),
            FundingRateData(
                Decimal("-0.66100003"), timedelta(hours=8), timedelta(milliseconds=1), datetime(2020, 11, 9, 8, 0, 0, tzinfo=UTC)
            ),
        ],
    )


def test_book() -> None:
    raw_bids = [
        [50125.5, 10870.0],
        [50115.0, 11990.0],
        [50113.0, 10560.0],
        [50110.5, 27590.0],
        [50108.5, 25770.0],
        [50108.0, 28550.0],
        [50107.5, 2000.0],
        [50106.5, 1250.0],
        [50106.0, 31050.0],
    ]
    raw_asks = [
        [50126.0, 49790.0],
        [50126.5, 320.0],
        [50127.0, 20.0],
        [50129.0, 10000.0],
        [50129.5, 510.0],
        [50133.0, 3000.0],
        [50133.5, 10000.0],
        [50134.0, 10.0],
        [50135.5, 3230.0],
        [50138.0, 25100.0],
        [50138.5, 90.0],
        [50139.0, 3000.0],
        [50139.5, 9870.0],
        [50141.0, 32340.0],
        [50142.5, 45130.0],
    ]
    bids = [PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in raw_bids]
    asks = [PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in raw_asks]

    depth = 5
    exchange_time = datetime.now(tz=UTC)
    expected_time = exchange_time.replace(microsecond=int(exchange_time.microsecond / 1000) * 1000)
    book = BookData(BookType.FULL, None, exchange_time, bids, asks)
    expected_book = BookData(BookType.FULL, None, expected_time, bids[:depth], asks[:depth])
    standard_book_test(EXCHANGE_NAME, BTC_USD_PERPETUAL, depth, book, expected_book)


def test_book_future_stream() -> None:
    messages = """
       {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "book.BTC-PERPETUAL.100ms", "data": {"type": "snapshot", "timestamp": 1659352799389, "instrument_name": "BTC-PERPETUAL", "change_id": 48264685386, "bids": [["new", "23143.5", "94910.0"], ["new", "23143.0", "5000.0"], ["new", "23142.0", "16460.0"]], "asks": [["new", "23144.0", "110.0"], ["new", "23145.5", "16210.0"], ["new", "23147.0", "770.0"]]}}}
       {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "book.BTC-PERPETUAL.100ms", "data": {"type": "change", "timestamp": 1659352799726, "prev_change_id": 48264685386, "instrument_name": "BTC-PERPETUAL", "change_id": 48264685504, "bids": [["change", "23143.5", "25600.0"]], "asks": [["change", "23144.0", "43060.0"]]}}}
       {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "book.BTC-PERPETUAL.100ms", "data": {"type": "change", "timestamp": 1659352800388, "prev_change_id": 48264685657, "instrument_name": "BTC-PERPETUAL", "change_id": 48264686079, "bids": [["new", "23142.6", "2500.0"], ["delete", "23143.5", "0.0"], ["delete", "23143.0", "0.0"]], "asks": [["new", "23143.5", "89750.0"], ["change", "23144.0", "54300.0"]]}}}
   """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_ms_aware(1659352800388),
        bids=[
            PriceLevel(price=Decimal("23142.6"), amount=Decimal("2500.0")),
            PriceLevel(price=Decimal("23142.0"), amount=Decimal("16460.0")),
        ],
        asks=[
            PriceLevel(price=Decimal("23143.5"), amount=Decimal("89750.0")),
            PriceLevel(price=Decimal("23144.0"), amount=Decimal("54300.0")),
            PriceLevel(price=Decimal("23145.5"), amount=Decimal("16210.0")),
            PriceLevel(price=Decimal("23147.0"), amount=Decimal("770.0")),
        ],
    )
    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USD_PERPETUAL,
        messages,
        4,
        expected_book,
    )


def test_quote_future_standard_test_without_updates() -> None:
    instrument = BTC_USD_PERPETUAL
    msg = """
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283104, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "44572.0", "best_bid_amount": "129440.0", "best_ask_price": "44572.5", "best_ask_amount": "26460.0"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283222, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "44572.0", "best_bid_amount": "129440.0", "best_ask_price": "44572.5", "best_ask_amount": "17960.0"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283364, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "44572.0", "best_bid_amount": "129440.0", "best_ask_price": "44572.5", "best_ask_amount": "53560.0"}}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_ms_aware(1648210283364),
        bids=[PriceLevel(price=Decimal("44572.0"), amount=Decimal("129440.0"))],
        asks=[PriceLevel(price=Decimal("44572.5"), amount=Decimal("53560.0"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, instrument, msg, expected_book, market_type=MarketType.FUTURES)


def test_quote_zero_quote_goes_after_non_zero_should_be_saved_as_empty_quote() -> None:
    instrument = BTC_USD_PERPETUAL
    msg = """
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283104, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "1.2", "best_bid_amount": "2.3", "best_ask_price": "2.3", "best_ask_amount": "3.4"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283222, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "0", "best_bid_amount": "0", "best_ask_price": "0", "best_ask_amount": "0"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283364, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "0", "best_bid_amount": "0", "best_ask_price": "0", "best_ask_amount": "0"}}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL, exchange_sequence_id=None, exchange_time=dt_from_ms_aware(1648210283222), bids=[], asks=[]
    )
    standard_quote_stream_test(EXCHANGE_NAME, instrument, msg, expected_book, market_type=MarketType.FUTURES)


def test_quote_zero_quote_from_beginning_is_not_saved() -> None:
    instrument = BTC_USD_PERPETUAL
    msg = """
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283104, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "0", "best_bid_amount": "0", "best_ask_price": "0", "best_ask_amount": "0"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283222, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "0", "best_bid_amount": "0", "best_ask_price": "0", "best_ask_amount": "0"}}}
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283364, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "0", "best_bid_amount": "0", "best_ask_price": "0", "best_ask_amount": "0"}}}
    """  # noqa: E501
    # no quotes have been saved
    standard_quote_stream_test(EXCHANGE_NAME, instrument, msg, None, market_type=MarketType.FUTURES)


def test_quote_partially_zero_quote_must_be_saved() -> None:
    instrument = BTC_USD_PERPETUAL
    msg = """
        {"jsonrpc": "2.0", "method": "subscription", "params": {"channel": "quote.BTC-PERPETUAL", "data": {"timestamp": 1648210283104, "instrument_name": "BTC-PERPETUAL", "best_bid_price": "2.3", "best_bid_amount": "1.2", "best_ask_price": "0", "best_ask_amount": "0"}}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_ms_aware(1648210283104),
        bids=[PriceLevel(price=Decimal("2.3"), amount=Decimal("1.2"))],
        asks=[],
    )
    standard_quote_stream_test(EXCHANGE_NAME, instrument, msg, expected_book, market_type=MarketType.FUTURES)


def get_spot_markets_info(self: Any, client: Any, kind: Any, expired: Any) -> List[Dict[str, Any]]:
    return [
        {
            "tick_size_steps": [],
            "tick_size": "0.01",
            "taker_commission": "0.0",
            "rfq": False,
            "quote_currency": "USDC",
            "price_index": "btc_usdc",
            "min_trade_amount": "0.0001",
            "maker_commission": "0.0",
            "kind": "spot",
            "is_active": True,
            "instrument_type": "linear",
            "instrument_name": "BTC_USDC",
            "instrument_id": 254613,
            "expiration_timestamp": 32503708800000,
            "creation_timestamp": 1682341202000,
            "counter_currency": "USDC",
            "contract_size": "0.0001",
            "block_trade_tick_size": "0.0001",
            "block_trade_min_trade_amount": 10,
            "block_trade_commission": "0.0",
            "base_currency": "BTC",
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDC,
            SpotContractData(
                symbol="BTC_USDC",
                base_id=0,
                quote_id=912,
                base_name="btc",
                quote_name="usdc",
                native_base_name="BTC",
                native_quote_name="USDC",
                listing_date=dt_from_ms_aware(1682341202000),
                end_date=dt_from_ms_aware(32503708800000),
                is_current=True,
                status="online",
                amount_size_min=Decimal("0.0001"),
                price_increment=Decimal("0.01"),
                taker_fee=Decimal("0.0"),
                maker_fee=Decimal("0.0"),
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.deribit.DeribitHttpApi._get_markets_info", get_spot_markets_info)
def test_spot_metadata(instrument: Instrument, expected_result: SpotContractData) -> None:
    diagnostics_mock = mock.Mock()
    http_client_mock = mock.Mock()
    http_api = exchange_http_api("Deribit", [], diagnostics_mock)
    for contract_data in http_api.spot_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


FROZEN_TIME = dt_from_any_aware(1740557153124)


@freeze_time(FROZEN_TIME, auto_tick_seconds=7)
def test_option_ticker_stream() -> None:
    messages = """
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"incremental_ticker.BTC-27FEB25-86000-C","data":{"timestamp":1740557141123,"type":"snapshot","state":"open","stats":{"high":null,"low":null,"price_change":null,"volume":0.0,"volume_usd":0.0},"greeks":{"delta":0.99945,"gamma":0.0,"vega":0.08218,"theta":-3.08257,"rho":1.681},"index_price":95775.52,"instrument_name":"BTC-27FEB25-86000-C","last_price":null,"settlement_price":0.10929568,"min_price":0.07,"max_price":0.133,"open_interest":0.0,"mark_price":0.1021,"best_ask_price":0.1335,"best_bid_price":0.07,"interest_rate":0.0,"mark_iv":75.02,"bid_iv":0.0,"ask_iv":425.47,"underlying_price":95782.8585,"underlying_index":"SYN.BTC-24FEB25","estimated_delivery_price":95775.52,"best_ask_amount":6.0,"best_bid_amount":6.0}}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"incremental_ticker.BNB_USDC-27FEB25-530-C","data":{"timestamp":1740557141125,"type":"snapshot","state":"open","stats":{"high":null,"low":null,"price_change":null,"volume":0.0,"volume_usd":0.0,"volume_notional":0.0},"greeks":{"delta":0.99939,"gamma":0.000080,"vega":0.00053,"theta":-0.03045,"rho":0.00836},"index_price":614.4835,"instrument_name":"BNB_USDC-27FEB25-530-C","last_price":null,"settlement_price":69.867219,"min_price":53.7,"max_price":116.6,"open_interest":0.0,"mark_price":84.4824,"best_ask_price":0.0,"best_bid_price":0.0,"interest_rate":0,"mark_iv":115.98,"bid_iv":0.0,"ask_iv":0.0,"underlying_price":614.4835,"underlying_index":"index_price","estimated_delivery_price":614.4835,"best_ask_amount":0.0,"best_bid_amount":0.0}}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"incremental_ticker.BTC-27FEB25-86000-C","data":{"timestamp":1740557141121,"type":"change","greeks":{"vega":0.08184,"theta":-3.06993,"rho":1.68101},"index_price":95781.15,"instrument_name":"BTC-27FEB25-86000-C","mark_price":0.1022,"ask_iv":425.29,"underlying_price":95786.8981,"estimated_delivery_price":95781.15}}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"incremental_ticker.BNB_USDC-27FEB25-530-C","data":{"timestamp":1740557141123,"type":"change","greeks":{"delta":0.9994,"gamma":0.000070,"vega":0.00052,"theta":-0.02993},"index_price":614.6365,"instrument_name":"BNB_USDC-27FEB25-530-C","min_price":53.9,"max_price":116.8,"mark_price":84.4882,"underlying_price":614.6365,"estimated_delivery_price":614.6365}}}
        {"jsonrpc":"2.0","method":"subscription","params":{"channel":"incremental_ticker.BTC-27FEB25-86000-C","data":{"timestamp":1740557153120,"type":"change","greeks":{"vega":0.08243,"theta":-3.09206,"rho":1.681},"index_price":95764.4,"instrument_name":"BTC-27FEB25-86000-C","mark_price":0.1021,"ask_iv":425.61,"underlying_price":95779.8339,"estimated_delivery_price":95764.4}}}
    """  # noqa: E501
    expected_ticker = [
        OptionTicker(
            market=Market(exchange_id=37, instrument=BTC_27FEB25_86000_C),
            data=OptionTickerData(
                time=truncate_to_minute(dt_from_any_aware(1740557153120)),
                exchange_time=dt_from_any_aware(1740557153120),
                price_last=None,
                price_bid=Decimal("0.07"),
                price_ask=Decimal("0.1335"),
                price_mark=Decimal("0.1021"),
                price_index=Decimal("95764.4"),
                amount_bid=Decimal("6.0"),
                amount_ask=Decimal("6.0"),
                index_name="SYN.BTC-24FEB25",
                implied_vol_trade=None,
                implied_vol_bid=Decimal("0.0") / Decimal(100),
                implied_vol_ask=Decimal("425.61") / Decimal(100),
                implied_vol_mark=Decimal("75.02") / Decimal(100),
                greek_delta=Decimal("0.99945"),
                greek_gamma=Decimal("0.0"),
                greek_theta=Decimal("-3.09206"),
                greek_vega=Decimal("0.08243"),
                greek_rho=Decimal("1.681"),
                estimated_settlement_price=Decimal("95764.4"),
            ),
        ),
        OptionTicker(
            market=Market(exchange_id=37, instrument=BNB_USDC_27FEB25_530_C),
            data=OptionTickerData(
                time=truncate_to_minute(dt_from_any_aware(1740557141123)),
                exchange_time=dt_from_any_aware(1740557141123),
                price_last=None,
                price_bid=None,
                price_ask=None,
                price_mark=Decimal("84.4882"),
                price_index=Decimal("614.6365"),
                amount_bid=None,
                amount_ask=None,
                index_name="index_price",
                implied_vol_trade=None,
                implied_vol_bid=Decimal("0.0") / Decimal(100),
                implied_vol_ask=Decimal("0.0") / Decimal(100),
                implied_vol_mark=Decimal("115.98") / Decimal(100),
                greek_delta=Decimal("0.9994"),
                greek_gamma=Decimal("0.000070"),
                greek_theta=Decimal("-0.02993"),
                greek_vega=Decimal("0.00052"),
                greek_rho=Decimal("0.00836"),
                estimated_settlement_price=Decimal("614.6365"),
            ),
        ),
    ]

    standard_ticker_stream_test(
        EXCHANGE_NAME,
        [BTC_27FEB25_86000_C, BNB_USDC_27FEB25_530_C],
        messages,
        expected_ticker,
        market_type=MarketType.OPTION,
        expected_connect_count=1,
    )
