from decimal import Decimal

from src.octopus.data import Instrument, MarketType, TradeData
from src.octopus.exchange.binance_aggregate import EXCHANGE_NAME
from src.octopus.qa_utils import standard_trade_stream_test
from src.utils.timeutil import dt_from_us

BTC_USDT_FUTURE = Instrument.futures("BTCUSDT")
ETH_USDT_FUTURE = Instrument.futures("ETHUSDT")


def test_futures_trades_stream() -> None:
    messages = """
        {"stream":"btcusdt@aggTrade","data":{"e":"aggTrade","E":123456789,"s":"BTCUSDT","a":12345,"p":"10000","q":"100","f":100,"l":105,"T":1564964484115,"m":true,"M":true}}
        {"stream":"btcusdt@aggTrade","data":{"e":"aggTrade","E":123456789,"s":"BTCUSDT","a":12345,"p":"10000","q":"100","f":100,"l":105,"T":1564964484115,"m":false,"M":true}}
        {"stream":"ethusdt@aggTrade","data":{"e":"aggTrade","E":123456789,"s":"ETHUSDT","a":12345,"p":"10000","q":"100","f":100,"l":105,"T":1564964484115,"m":true,"M":true}}
        {"stream":"ethusdt@aggTrade","data":{"e":"aggTrade","E":123456789,"s":"ETHUSDT","a":12345,"p":"10000","q":"100","f":100,"l":105,"T":1564964484115,"m":false,"M":true}}
    """
    expected_trades = [
        (
            BTC_USDT_FUTURE,
            TradeData(
                trade_id=12345,
                first_trade_id=100,
                last_trade_id=105,
                price=Decimal("10000"),
                amount=Decimal("100"),
                time=dt_from_us(1564964484115000),
                is_buy=False,
            ),
        ),
        (
            BTC_USDT_FUTURE,
            TradeData(
                trade_id=12345,
                first_trade_id=100,
                last_trade_id=105,
                price=Decimal("10000"),
                amount=Decimal("100"),
                time=dt_from_us(1564964484115000),
                is_buy=True,
            ),
        ),
        (
            ETH_USDT_FUTURE,
            TradeData(
                trade_id=12345,
                first_trade_id=100,
                last_trade_id=105,
                price=Decimal("10000"),
                amount=Decimal("100"),
                time=dt_from_us(1564964484115000),
                is_buy=False,
            ),
        ),
        (
            ETH_USDT_FUTURE,
            TradeData(
                trade_id=12345,
                first_trade_id=100,
                last_trade_id=105,
                price=Decimal("10000"),
                amount=Decimal("100"),
                time=dt_from_us(1564964484115000),
                is_buy=True,
            ),
        ),
    ]
    standard_trade_stream_test(
        exchange=EXCHANGE_NAME,
        instruments=[BTC_USDT_FUTURE, ETH_USDT_FUTURE],
        market_type=MarketType.FUTURES,
        messages=messages,
        expected_trades=expected_trades,
    )
