from datetime import UTC, datetime, timedelta

from src.octopus.data import Instrument
from src.octopus.exchange.localbitcoins import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST, TRADE_TRAVERSAL_STEP
from src.octopus.qa_utils import standard_trade_history_traversal_test
from src.octopus.vex.generate import GeneratedTradeIdRange, monotonic_id_trade_sequence
from src.utils.timeutil import align_dt_to_interval

BTC_USD = Instrument.spot("USD", "btc", "usd")


def test_trade_history_traversal() -> None:
    trade_time = align_dt_to_interval(datetime.now(UTC), timedelta(seconds=1))
    first_btcusd_id_of_real_exchange = 170892

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_id_trade_sequence(
            [
                GeneratedTradeIdRange(first_btcusd_id_of_real_exchange, MAX_TRADES_PER_REQUEST - 1),
            ],
            time_gen=lambda: trade_time,
            side_gen=lambda: None,
        ),
    )

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_id_trade_sequence(
            [
                GeneratedTradeIdRange(first_btcusd_id_of_real_exchange, MAX_TRADES_PER_REQUEST - 1),
                GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST // 2, MAX_TRADES_PER_REQUEST + 1),
                GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST * 2, MAX_TRADES_PER_REQUEST - 1),
                GeneratedTradeIdRange(TRADE_TRAVERSAL_STEP // 4, MAX_TRADES_PER_REQUEST // 2),
                GeneratedTradeIdRange(TRADE_TRAVERSAL_STEP // 4, MAX_TRADES_PER_REQUEST // 2),
                GeneratedTradeIdRange(TRADE_TRAVERSAL_STEP // 4, MAX_TRADES_PER_REQUEST // 2),
                GeneratedTradeIdRange(TRADE_TRAVERSAL_STEP // 4, MAX_TRADES_PER_REQUEST // 2),
                GeneratedTradeIdRange(TRADE_TRAVERSAL_STEP * 100, MAX_TRADES_PER_REQUEST - 1),
            ],
            time_gen=lambda: trade_time,
            side_gen=lambda: None,
        ),
    )
