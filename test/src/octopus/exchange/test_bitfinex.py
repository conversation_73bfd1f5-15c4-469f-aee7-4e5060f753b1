from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import Tuple

import pytest

from src.octopus.data import (
    BookData,
    BookType,
    FundingRateData,
    Instrument,
    LiquidationData,
    OpenInterestData,
    PriceLevel,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.bitfinex import (
    EXCHANGE_BIRTHDAY,
    EXCHANGE_NAME,
    FUTURES_START_DATE,
    MAX_TRADES_PER_REQUEST,
    TIMESTAMP_RESOLUTION,
    _get_liquidation_id,
)
from src.octopus.qa_utils import (
    _get_virtual_exchange_client,
    standard_book_stream_test,
    standard_futures_open_interest_test,
    standard_last_funding_rates_test,
    standard_last_liquidations_test,
    standard_liquidation_history_traversal_test,
    standard_trade_history_traversal_test,
    standard_trade_stream_test,
)
from src.octopus.vex.exchange.bitfinex import INSTRUMENT_ERROR
from src.octopus.vex.generate import (
    GeneratedLiquidationTimeRange,
    GeneratedTradeTimeRange,
    monotonic_time_liquidation_sequence,
    monotonic_time_trade_sequence,
)
from src.utils.diagnostics import Diagnostics
from src.utils.timeutil import dt_from_us, open_interest_timestamp

BTC_USD = Instrument.spot("tBTCUSD", "btc", "usd")
ETH_BTC = Instrument.spot("tETHBTC", "eth", "btc")
BTC_USD_FUTURES = Instrument.futures("tBTCF0:USTF0")


def test_trade_history_traversal() -> None:
    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(), MAX_TRADES_PER_REQUEST - 1),
            ],
            align_to=TIMESTAMP_RESOLUTION,
        ),
    )

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(), timedelta(), MAX_TRADES_PER_REQUEST * 2 - 2),
            ],
            align_to=TIMESTAMP_RESOLUTION,
        ),
    )

    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        monotonic_time_trade_sequence(
            EXCHANGE_BIRTHDAY,
            [
                GeneratedTradeTimeRange(timedelta(days=1), timedelta(days=1), MAX_TRADES_PER_REQUEST),
                GeneratedTradeTimeRange(timedelta(), timedelta(), MAX_TRADES_PER_REQUEST * 2 - 1),
            ],
            align_to=TIMESTAMP_RESOLUTION,
        ),
        trades_should_be_missed=True,
    )


def test_trade_stream() -> None:
    """
    Scraper should reconnect if:
    1. Received one of the maintenance codes: 20051, 20060, 20061.
    2. Exchange is under maintenace (status code != 1).
    3. Configuration request resulted failed (status != OK).
    """
    messages = """
        {"event":"info","version":2,"serverId":"fd558456-b817-4839-8795-ed8733b8d49b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}

        {"event":"subscribed","channel":"trades","chanId":4601,"symbol":"tETHBTC","pair":"ETHBTC"}
        [4601,[[423140848,1583840514202,-0.3,0.025196],[423140847,1583840511168,-0.3,0.025196]],1]
        {"event":"subscribed","channel":"trades","chanId":1,"symbol":"tBTCUSD","pair":"BTCUSD"}
        [1,[[423149317,1583840519522,0.005,8095.1],[423149315,1583840518403,-0.005,8095]],2]

        [1,"te",[423149319,1583840532331,-0.05,8095.1],3]
        [1,"tu",[423149319,1583840532331,-0.05,8095.1],4]
        [1,"hb",5]
        [4601,"hb",6]
        [4601,"te",[423140870,1583840539514,-1.238,0.025196],7]
        [4601,"tu",[423140870,1583840539514,-1.238,0.025196],8]

        {"event":"info","code":20051}

        {"event":"info","version":2,"serverId":"fd558456-b817-4839-8795-ed8733b8d49b","platform":{"status":0}}

        {"event":"info","version":2,"serverId":"fd558456-b817-4839-8795-ed8733b8d49b","platform":{"status":1}}
        {"event":"conf","status":"FAIL","flags":65536}

        {"event":"info","version":2,"serverId":"fd558456-b817-4839-8795-ed8733b8d49b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"info","code":20060}

        {"event":"info","version":2,"serverId":"fd558456-b817-4839-8795-ed8733b8d49b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"info","code":20061}
    """

    expected_trades = [
        (ETH_BTC, TradeData(423140848, Decimal("0.3"), Decimal("0.025196"), False, dt_from_us(1583840514202000))),
        (ETH_BTC, TradeData(423140847, Decimal("0.3"), Decimal("0.025196"), False, dt_from_us(1583840511168000))),
        (BTC_USD, TradeData(423149317, Decimal("0.005"), Decimal("8095.1"), True, dt_from_us(1583840519522000))),
        (BTC_USD, TradeData(423149315, Decimal("0.005"), Decimal("8095"), False, dt_from_us(1583840518403000))),
        (BTC_USD, TradeData(423149319, Decimal("0.05"), Decimal("8095.1"), False, dt_from_us(1583840532331000))),
        (ETH_BTC, TradeData(423140870, Decimal("1.238"), Decimal("0.025196"), False, dt_from_us(1583840539514000))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USD, ETH_BTC], messages, expected_trades, expected_connect_count=6)


def test_book_stream() -> None:
    """
    1. Reconnect if snapshot is crossed.
    2. Reconnect if non-incremental message sequence number is encountered.
    3. Reconnect if crossed book is detected.
    """
    messages = """
        {"event":"info","version":2,"serverId":"a30cadca-5e64-4662-bb0c-64af1083568b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"subscribed","channel":"book","chanId":1,"symbol":"tBTCUSD","prec":"P0","freq":"F0","len":"4","pair":"BTCUSD"}
        [1,[[8127.5,1,2.15212441],[8126.5,1,0.3006],[8127.0,1,-0.07165714],[8128.5,1,-0.19884564]],1]

        {"event":"info","version":2,"serverId":"a30cadca-5e64-4662-bb0c-64af1083568b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"subscribed","channel":"book","chanId":1,"symbol":"tBTCUSD","prec":"P0","freq":"F0","len":"4","pair":"BTCUSD"}
        [1,[[8127.5,1,2.15212441],[8126.5,1,0.3006],[8127.9,1,-0.07165714],[8128.5,1,-0.19884564]],1]
        [1,[8155.1,1,-0.01230357],3]

        {"event":"info","version":2,"serverId":"a30cadca-5e64-4662-bb0c-64af1083568b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"subscribed","channel":"book","chanId":1,"symbol":"tBTCUSD","prec":"P0","freq":"F0","len":"4","pair":"BTCUSD"}
        [1,[[8127.5,1,2.15212441],[8126.5,1,0.3006],[8127.9,1,-0.07165714],[8128.5,1,-0.19884564]],1]
        [1,[8128.1,1,0.01230357],2]

        {"event":"info","version":2,"serverId":"a30cadca-5e64-4662-bb0c-64af1083568b","platform":{"status":1}}
        {"event":"conf","status":"OK","flags":65536}
        {"event":"subscribed","channel":"book","chanId":1,"symbol":"tBTCUSD","prec":"P0","freq":"F0","len":"4","pair":"BTCUSD"}
        [1,[[8127.5,1,2.15212441],[8126.5,1,0.3006],[8125.6,1,0.07],[8124.5,1,0.9],[8137.5,1,-2.15212441],[8138.5,1,-0.3006],[8139.6,1,-0.07],[8140.5,1,-0.9]],1]
        [1,[8127.5,0,2.15212441],2]
        [1,[8137.5,0,-2.15212441],3]
        [1,[8127.6,1,1.15],4]
        [1,[8137.6,1,-1.12],5]
        [1,[8126.5,1,1.3006],6]
        [1,[8138.5,1,-0.6006],7]
    """

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=None,
        bids=[
            PriceLevel(price=Decimal("8127.6"), amount=Decimal("1.15")),
            PriceLevel(price=Decimal("8126.5"), amount=Decimal("1.3006")),
            PriceLevel(price=Decimal("8125.6"), amount=Decimal("0.07")),
            PriceLevel(price=Decimal("8124.5"), amount=Decimal("0.9")),
        ],
        asks=[
            PriceLevel(price=Decimal("8137.6"), amount=Decimal("1.12")),
            PriceLevel(price=Decimal("8138.5"), amount=Decimal("0.6006")),
            PriceLevel(price=Decimal("8139.6"), amount=Decimal("0.07")),
            PriceLevel(price=Decimal("8140.5"), amount=Decimal("0.9")),
        ],
    )

    standard_book_stream_test(EXCHANGE_NAME, BTC_USD, messages, 2, expected_book, expected_connect_count=4)


def test_liquidation_history_traversal() -> None:
    second = timedelta(seconds=1)

    standard_liquidation_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD_FUTURES,
        monotonic_time_liquidation_sequence(
            FUTURES_START_DATE,
            [
                GeneratedLiquidationTimeRange(timedelta(days=4), timedelta(days=1) - second, 2),
                GeneratedLiquidationTimeRange(second, timedelta(days=4) - second, 2),
                GeneratedLiquidationTimeRange(timedelta(days=4) + second, timedelta(days=1), 2),
            ],
            use_time_as_id=True,
        )[:1],
    )


def test_last_liquidations() -> None:
    standard_last_liquidations_test(
        EXCHANGE_NAME,
        BTC_USD_FUTURES,
        [
            LiquidationData(
                1564964532973000000, Decimal("8.66100000"), Decimal("10980.02030000"), False, False, dt_from_us(1564964532973000)
            ),
        ],
    )


def test_last_funding_rates() -> None:
    standard_last_funding_rates_test(
        EXCHANGE_NAME,
        BTC_USD_FUTURES,
        [
            FundingRateData(Decimal("0.00581300"), timedelta(hours=8), timedelta(hours=8), dt_from_us(1564964484115000)),
            FundingRateData(Decimal("-0.66100000"), timedelta(hours=8), timedelta(hours=8), dt_from_us(1564964532973000)),
        ],
    )


def test_open_interest() -> None:
    standard_futures_open_interest_test(
        EXCHANGE_NAME, BTC_USD_FUTURES, [OpenInterestData(Decimal(5), Decimal(1000.0), open_interest_timestamp())], True
    )


def test_empty_open_interest() -> None:
    http_api = exchange_http_api(EXCHANGE_NAME, [], Diagnostics())
    client = _get_virtual_exchange_client(EXCHANGE_NAME, BTC_USD_FUTURES, open_interest=None)
    open_interest = http_api.futures_open_interest(client, BTC_USD_FUTURES)
    assert open_interest is None


def test_open_interest_invalid() -> None:
    with pytest.raises(ValueError):
        standard_futures_open_interest_test(
            EXCHANGE_NAME, INSTRUMENT_ERROR, [OpenInterestData(Decimal(0), Decimal(0), open_interest_timestamp())]
        )


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        ((1634770000000, Decimal("12345"), Decimal("67890")), 1634770000000000000),  # legacy style id
        ((1634774400000, Decimal("12345"), Decimal("67890")), 16347744000001234567890),  # new style id
        ((1637280968184, Decimal("1.0473"), Decimal("1E-8")), 163728096818410473000000001),  # exponent conversion
    ],
)
def test_liquidation_id(test_input: Tuple[int, Decimal, Decimal], expected_result: int) -> None:
    assert _get_liquidation_id(test_input[0], test_input[1], test_input[2]) == expected_result
