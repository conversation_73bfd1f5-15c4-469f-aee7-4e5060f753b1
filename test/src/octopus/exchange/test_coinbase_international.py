from decimal import Decimal
from typing import Any, Dict, List
from unittest import mock
from unittest.mock import Mock

import ciso8601
import pytest

from src.octopus.data import BookData, BookType, FuturesContractData, Instrument, MarketType, PriceLevel, TradeData
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.qa_utils import standard_book_stream_test, standard_trade_stream_test

EXCHANGE_NAME = "CoinbaseInt"

BTC_PERP = Instrument.futures("BTC-PERP")
ETH_PERP = Instrument.futures("ETH-PERP")


def get_futures_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "instrument_id": "149264167780483072",
            "instrument_uuid": "b3469e0b-222c-4f8a-9f68-1f9e44d7e5e0",
            "symbol": "BTC-PERP",
            "type": "PERP",
            "base_asset_id": "118059611751202816",
            "base_asset_uuid": "5b71fc48-3dd3-540c-809b-f8c94d0e68b5",
            "base_asset_name": "BTC",
            "quote_asset_id": "1",
            "quote_asset_uuid": "2b92315d-eab7-5bef-84fa-089a131333f5",
            "quote_asset_name": "USDC",
            "base_increment": "0.0001",
            "quote_increment": "0.1",
            "price_band_percent": 0.02,
            "market_order_percent": 0.0075,
            "qty_24hr": "1070.8785",
            "notional_24hr": "39689581.66408",
            "avg_daily_qty": "75644.7411",
            "avg_daily_notional": "2493387265.29164",
            "previous_day_qty": "1005.2226",
            "open_interest": "44.4437",
            "position_limit_qty": "126.0746",
            "position_limit_adq_pct": 0.05,
            "replacement_cost": "0.19",
            "base_imf": 0.2,
            "min_notional_value": "10",
            "funding_interval": "3600000000000",
            "trading_state": "TRADING",
        },
        {
            "instrument_id": "149264164756389888",
            "instrument_uuid": "e9360798-6a10-45d6-af05-67c30eb91e2d",
            "symbol": "ETH-PERP",
            "type": "PERP",
            "base_asset_id": "118059611793145856",
            "base_asset_uuid": "d85dce9b-5b73-5c3c-8978-522ce1d1c1b4",
            "base_asset_name": "ETH",
            "quote_asset_id": "1",
            "quote_asset_uuid": "2b92315d-eab7-5bef-84fa-089a131333f5",
            "quote_asset_name": "USDC",
            "base_increment": "0.0001",
            "quote_increment": "0.01",
            "price_band_percent": 0.02,
            "market_order_percent": 0.0075,
            "qty_24hr": "22856.7108",
            "notional_24hr": "47081010.777063",
            "avg_daily_qty": "863742.2966",
            "avg_daily_notional": "1549697167.351998",
            "previous_day_qty": "29109.7133",
            "open_interest": "640.189",
            "position_limit_qty": "1439.5705",
            "position_limit_adq_pct": 0.05,
            "replacement_cost": "0.23",
            "base_imf": 0.2,
            "min_notional_value": "10",
            "funding_interval": "3600000000000",
            "trading_state": "NO_TRADING",
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_PERP,
            FuturesContractData(
                symbol="BTC-PERP",
                underlying_base_id=0,
                underlying_quote_id=912,
                size_asset_id=0,
                margin_asset_id=912,
                underlying_base_name="btc",
                underlying_quote_name="usdc",
                size_asset_name="btc",
                margin_asset_name="usdc",
                underlying_native_base_name="BTC",
                underlying_native_quote_name="USDC",
                listing_date=None,
                expiry_date=None,
                contract_size=Decimal("1"),
                tick_size=None,
                status="online",
                amount_increment=Decimal("0.0001"),
                amount_size_min=Decimal("0.0001"),
                price_increment=Decimal("0.1"),
                order_size_min=Decimal("10"),
            ),
        ),
        (
            ETH_PERP,
            FuturesContractData(
                symbol="ETH-PERP",
                underlying_base_id=6,
                underlying_quote_id=912,
                size_asset_id=6,
                margin_asset_id=912,
                underlying_base_name="eth",
                underlying_quote_name="usdc",
                size_asset_name="eth",
                margin_asset_name="usdc",
                underlying_native_base_name="ETH",
                underlying_native_quote_name="USDC",
                listing_date=None,
                expiry_date=None,
                contract_size=Decimal("1"),
                tick_size=None,
                status="offline",
                amount_increment=Decimal("0.0001"),
                amount_size_min=Decimal("0.0001"),
                price_increment=Decimal("0.01"),
                order_size_min=Decimal("10"),
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.coinbase_international.CoinbaseInternationalHttpApi._get_futures_markets", get_futures_markets)
def test_futures_metadata_base(instrument: Instrument, expected_result: FuturesContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api(EXCHANGE_NAME, [], diagnostics_mock)
    for contract_data in http_api.futures_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def test_trade_stream() -> None:
    messages = """
        {"sequence": 1, "match_id": "276987015252148230", "trade_price": "42881.1", "trade_qty": "0.173", "aggressor_side": "BUY", "channel": "MATCH", "type": "UPDATE", "time": "2024-02-04T09:27:30.606Z", "product_id": "BTC-PERP"}
        {"sequence": 2, "match_id": "276987015252148233", "trade_price": "42881.2", "trade_qty": "0.0625", "aggressor_side": "SELL", "channel": "MATCH", "type": "UPDATE", "time": "2024-02-04T09:27:30.609Z", "product_id": "BTC-PERP"}
   """  # noqa: E501

    expected_trades = [
        (
            BTC_PERP,
            TradeData(
                trade_id=276987015252148230,
                price=Decimal("42881.1"),
                amount=Decimal("0.173"),
                is_buy=True,
                time=ciso8601.parse_datetime("2024-02-04T09:27:30.606Z"),
            ),
        ),
        (
            BTC_PERP,
            TradeData(
                trade_id=276987015252148233,
                price=Decimal("42881.2"),
                amount=Decimal("0.0625"),
                is_buy=False,
                time=ciso8601.parse_datetime("2024-02-04T09:27:30.609Z"),
            ),
        ),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_PERP], messages, expected_trades, market_type=MarketType.FUTURES)


def test_book_stream() -> None:
    messages = """
        {"sequence": 0, "bids": [["42919.4", "1.5729"], ["42918.2", "0.5941"], ["42917.1", "0.037"]], "asks": [["42919.5", "3.4154"], ["42919.9", "0.06"], ["42920", "0.15"]], "channel": "LEVEL2", "type": "SNAPSHOT", "time": "2024-02-04T10:29:39.891Z", "product_id": "BTC-PERP"}
        {"sequence": 1, "changes": [["BUY", "42919.4", "0"], ["SELL", "42919.49", "0.02"]], "channel": "LEVEL2", "type": "UPDATE", "time": "2024-02-04T10:29:39.907Z", "product_id": "BTC-PERP"}
        {"sequence": 2, "changes": [["BUY", "42916.7", "0.0476"]], "channel": "LEVEL2", "type": "UPDATE", "time": "2024-02-04T10:29:39.907Z", "product_id": "BTC-PERP"}
        {"sequence": 3, "changes": [["BUY", "42918.2", "0.03"], ["SELL", "42919.9", "0"]], "channel": "LEVEL2", "type": "UPDATE", "time": "2024-02-04T10:29:39.907Z", "product_id": "BTC-PERP"}
    """  # noqa: E501

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=ciso8601.parse_datetime("2024-02-04T10:29:39.907Z"),
        bids=[
            PriceLevel(price=Decimal("42918.2"), amount=Decimal("0.03")),
            PriceLevel(price=Decimal("42917.1"), amount=Decimal("0.037")),
            PriceLevel(price=Decimal("42916.7"), amount=Decimal("0.0476")),
        ],
        asks=[
            PriceLevel(price=Decimal("42919.49"), amount=Decimal("0.02")),
            PriceLevel(price=Decimal("42919.5"), amount=Decimal("3.4154")),
            PriceLevel(price=Decimal("42920"), amount=Decimal("0.15")),
        ],
    )

    standard_book_stream_test(EXCHANGE_NAME, BTC_PERP, messages, 3, expected_book)
