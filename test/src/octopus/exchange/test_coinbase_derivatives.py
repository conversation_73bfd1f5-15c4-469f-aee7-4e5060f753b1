from datetime import UTC, datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest import mock
from unittest.mock import Mock

import pytest

from src.octopus.data import (
    BookDataWithId,
    BookType,
    FuturesContractData,
    Instrument,
    MarketType,
    MutableBookDataWithId,
    PriceLevelWithId,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.qa_utils import standard_book_stream_test, standard_trade_stream_test

ETF24 = Instrument.futures("ETF24")
BITZ23 = Instrument.futures("BITZ23")
ETIF24 = Instrument.futures("ETIF24")


def get_futures_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return b"35=y\x0149=COIND\x0156=CMDMK0\x0134=2\x0150=TEST\x01369=1\x0152=20231205-14:37:09.120696\x01320=1701787028.7973492\x01322=24\x01560=0\x01393=39\x01893=Y\x01146=20\x0155=ETF24\x01167=FUT\x01107=Nano Ether Fut Jan24\x01200=202401\x016937=ET\x01461=FXXXXX\x01462=99\x011682=17\x0115=USD\x01120=USD\x01562=0\x011140=1000\x01969=0.5\x01864=2\x01865=5\x011145=20231030\x01865=7\x011145=20240126\x01231=0.1\x01996=USD\x011147=0.1\x011149=2421.5\x011148=1981.5\x01734=1816.5\x01730=2228\x015796=20231205\x01"  # noqa: E501


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            ETF24,
            FuturesContractData(
                symbol="ETF24",
                underlying_base_id=6,
                underlying_quote_id=3,
                size_asset_id=6,
                margin_asset_id=3,
                underlying_base_name="eth",
                underlying_quote_name="usd",
                size_asset_name="eth",
                margin_asset_name="usd",
                underlying_native_base_name="ET",
                underlying_native_quote_name="USD",
                listing_date=datetime(year=2023, month=10, day=29, hour=17, tzinfo=UTC),
                expiry_date=datetime(year=2024, month=1, day=26, hour=16, tzinfo=UTC),
                contract_size=Decimal("0.1"),
                tick_size=Decimal("0.5"),
                multiplier_size=Decimal("0.1"),
                multiplier_size_asset_id=3,
                status="online",
                amount_size_min=Decimal(0),
                amount_size_max=Decimal(1000),
                price_increment=Decimal("0.5"),
                price_size_min=Decimal("1981.5"),
                price_size_max=Decimal("2421.5"),
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.coinbase_derivatives.CoinbaseDerivativesHttpApi._read_data_from_socket", get_futures_markets)
def test_futures_metadata_base(instrument: Instrument, expected_result: FuturesContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("CoinbaseDer", ["local"], diagnostics_mock)
    for contract_data in http_api.futures_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def test_trade_stream() -> None:
    messages = [
        "35=A|49=COIND|56=CMDMK0|34=1|50=TEST|369=1|52=20231219-15:12:58.176092|98=0|108=30|141=Y|",
        (
            "35=X|49=COIND|56=CMDMK0|34=2|50=TEST|369=2|52=20231219-15:13:07.226047|262=1702998778.220104|266=N|268=1|"
            "279=0|269=0|278=861566279|55=BITZ23|167=FUT|270=42560|271=42|60=20231219-15:13:07.216604|5796=20231219|"
        ),
        (
            "35=X|49=COIND|56=CMDMK0|34=3|50=TEST|369=2|52=20231219-15:13:07.226053|262=1702998778.220104|268=1|279=0|"
            "269=2|278=861566281|55=BITZ23|167=FUT|270=42565|271=36|60=20231219-15:13:07.216632|5796=20231219|5797=1|"
        ),
        (
            "35=X|49=COIND|56=CMDMK0|34=4|50=TEST|369=2|52=20231219-15:13:07.226063|262=1702998778.220104|268=1|279=0|"
            "269=2|278=861566285|55=BITZ23|167=FUT|270=42566|271=18|60=20231219-15:13:07.216634|5796=20231219|5797=2|"
        ),
    ]
    expected_trades = [
        (
            BITZ23,
            TradeData(
                trade_id=861566281,
                price=Decimal("42565"),
                amount=Decimal("36"),
                is_buy=True,
                time=datetime.strptime("20231219-15:13:07.216632", "%Y%m%d-%H:%M:%S.%f"),
            ),
        ),
        (
            BITZ23,
            TradeData(
                trade_id=861566285,
                price=Decimal("42566"),
                amount=Decimal("18"),
                is_buy=False,
                time=datetime.strptime("20231219-15:13:07.216634", "%Y%m%d-%H:%M:%S.%f"),
            ),
        ),
    ]

    standard_trade_stream_test(
        "CoinbaseDer",
        [BITZ23],
        [msg.replace("|", "\x01").encode() for msg in messages],
        expected_trades,
        market_type=MarketType.FUTURES,
        api_params=["local"],
    )


def test_book_stream() -> None:
    """
    bids:
    2274.5    30    893273405
    2271      72    893272446
    2270.5    78    893272501

    asks:
    2276.5    48    893268500
    2278      66    893268497
    2278      72    893272004
    """
    messages = [
        "35=W|49=COIND|56=CMDMK1|34=16|50=TEST|369=1|52=20240109-13:59:35.612661|262=1704808775.493011|55=ETIF24|167=FUT|268=70|269=1|278=893268497|270=2278|271=66|60=20240109-13:56:41.748843|269=0|278=893272501|270=2270.5|271=78|60=20240109-13:59:04.748881|269=1|278=893272004|270=2278|271=72|60=20240109-13:58:21.748860|269=0|278=893272446|270=2271|271=72|60=20240109-13:59:03.748887|269=1|278=893268500|270=2276.5|271=48|60=20240109-13:56:41.748851|269=0|278=893273405|270=2274.5|271=30|60=20240109-13:59:03.748936|",  # noqa: E501
        "35=X|49=COIND|56=CMDMK1|34=32|50=TEST|369=2|52=20240109-13:59:35.757106|262=1704808775.493011|266=N|268=1|279=0|269=0|278=893274654|55=ETIF24|167=FUT|270=2275|271=64|60=20240109-13:59:35.748823|5796=20240109|",  # noqa: E501
        "35=X|49=COIND|56=CMDMK1|34=32|50=TEST|369=2|52=20240109-13:59:35.857106|262=1704808775.493011|266=N|268=1|279=2|269=0|278=893272446|55=ETIF24|167=FUT|60=20240109-13:59:35.748823|5796=20240109|",  # noqa: E501
        "35=X|49=COIND|56=CMDMK1|34=32|50=TEST|369=2|52=20240109-13:59:36.1237106|262=1704808775.493011|266=N|268=1|279=1|269=1|278=893272004|55=ETIF24|167=FUT|270=2278|271=64|60=20240109-13:59:36.123823|5796=20240109|",  # noqa: E501
    ]
    expected_book = BookDataWithId(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=None,
        bids=[
            PriceLevelWithId(price=Decimal("2275"), amount=Decimal("64"), id="893274654"),
            PriceLevelWithId(price=Decimal("2274.5"), amount=Decimal("30"), id="893273405"),
            PriceLevelWithId(price=Decimal("2270.5"), amount=Decimal("78"), id="893272501"),
        ],
        asks=[
            PriceLevelWithId(price=Decimal("2276.5"), amount=Decimal("48"), id="893268500"),
            PriceLevelWithId(price=Decimal("2278"), amount=Decimal("66"), id="893268497"),
            PriceLevelWithId(price=Decimal("2278"), amount=Decimal("64"), id="893272004"),
        ],
    )
    standard_book_stream_test(
        "CoinbaseDer",
        ETIF24,
        [msg.replace("|", "\x01").encode() for msg in messages],
        3,
        expected_book,
        api_params=["local"],
        mutable_book_data_class=MutableBookDataWithId,
    )
