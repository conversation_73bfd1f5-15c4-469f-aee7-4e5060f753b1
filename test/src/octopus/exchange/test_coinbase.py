from decimal import Decimal
from typing import Any, Dict, List
from unittest import mock

import ciso8601
import pytest
from freezegun import freeze_time  # type: ignore

from src.octopus.data import BookData, BookType, Instrument, PriceLevel, SpotContractData, TradeData
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.coinbase import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.qa_utils import (
    standard_book_stream_test,
    standard_quote_stream_test,
    standard_trade_history_traversal_test,
    standard_trade_stream_test,
)
from src.octopus.vex.generate import GeneratedTradeIdRange, monotonic_id_trade_sequence
from src.utils.timeutil import dt_from_us, dt_from_us_aware

BTC_USD = Instrument.spot("BTC-USD", "btc", "usd")
BTC_EUR = Instrument.spot("BTC-EUR", "btc", "eur")
ETH_BTC = Instrument.spot("ETH-BTC", "eth", "btc")
SUSHI_ETH = Instrument.spot("SUSHI-ETH", "sushi", "eth")

FROZEN_TIME = "2022-02-25 03:21:34Z"


def get_spot_markets(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "id": "BTC-EUR",
            "base_currency": "BTC",
            "quote_currency": "EUR",
            "quote_increment": "0.01",
            "base_increment": "0.00000001",
            "display_name": "BTC/EUR",
            "min_market_funds": "0.84",
            "margin_enabled": False,
            "post_only": False,
            "limit_only": False,
            "cancel_only": False,
            "status": "online",
            "status_message": "",
            "trading_disabled": False,
            "fx_stablecoin": False,
            "max_slippage_percentage": "0.03000000",
            "auction_mode": False,
            "high_bid_limit_percentage": "",
        },
        {
            "id": "SUSHI-ETH",
            "base_currency": "SUSHI",
            "quote_currency": "ETH",
            "quote_increment": "0.0000001",
            "base_increment": "0.01",
            "display_name": "SUSHI/ETH",
            "min_market_funds": "0.00022",
            "margin_enabled": True,
            "post_only": False,
            "limit_only": False,
            "cancel_only": False,
            "status": "delisted",
            "status_message": "",
            "trading_disabled": False,
            "fx_stablecoin": False,
            "max_slippage_percentage": "0.03000000",
            "auction_mode": False,
            "high_bid_limit_percentage": "",
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_EUR,
            SpotContractData(
                symbol="BTC-EUR",
                base_id=0,
                quote_id=4,
                base_name="btc",
                quote_name="eur",
                native_base_name="BTC",
                native_quote_name="EUR",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.00000001"),
                price_increment=Decimal("0.01"),
                price_size_min=Decimal("0.01"),
                order_size_min=Decimal("0.84"),
                margin_trading_enabled=False,
                status="online",
            ),
        ),
        (
            SUSHI_ETH,
            SpotContractData(
                symbol="SUSHI-ETH",
                base_id=1751,
                quote_id=6,
                base_name="sushi",
                quote_name="eth",
                native_base_name="SUSHI",
                native_quote_name="ETH",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.01"),
                price_increment=Decimal("0.0000001"),
                price_size_min=Decimal("0.0000001"),
                order_size_min=Decimal("0.00022"),
                margin_trading_enabled=True,
                status="offline",
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.coinbase.CoinbaseHttpApi._get_spot_markets", get_spot_markets)
def test_spot_metadata_base(instrument: Instrument, expected_result: SpotContractData) -> None:
    diagnostics_mock = mock.Mock()
    http_client_mock = mock.Mock()
    http_api = exchange_http_api("Coinbase", [], diagnostics_mock)
    for contract_data in http_api.spot_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def test_trade_history_traversal() -> None:
    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        Instrument.spot("BTC-USD", "btc", "usd"),
        monotonic_id_trade_sequence([
            GeneratedTradeIdRange(1, MAX_TRADES_PER_REQUEST),
            GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST - 1, MAX_TRADES_PER_REQUEST * 2),
            GeneratedTradeIdRange(1, 1),
        ]),
    )


def test_trade_stream() -> None:
    messages = """
        {"type":"subscriptions","channels":[{"name":"full","product_ids":["BTC-USD"]}]}
        {"type":"subscriptions","channels":[{"name":"full","product_ids":["ETH-BTC","BTC-USD"]}]}
        {"type":"match","side":"sell","product_id":"BTC-USD","time":"2020-03-11T08:32:35.597046Z","sequence":12797760156,"trade_id":84615451,"maker_order_id":"14e98266-3231-4062-a457-e890136c3e56","taker_order_id":"3d99425f-62ec-4e6c-ae55-95949fcd447d","size":"0.00610761","price":"7821.08"}
        {"type":"match","side":"buy","product_id":"ETH-BTC","time":"2020-03-11T08:32:38.648408Z","sequence":12797761431,"trade_id":84615454,"maker_order_id":"560cfb54-d820-4f16-913d-a8c73baec84b","taker_order_id":"95d0fa3c-8025-407e-a141-a1b543af3b1d","size":"0.53965058","price":"7819"}
   """

    expected_trades = [
        (BTC_USD, TradeData(84615451, Decimal("0.00610761"), Decimal("7821.08"), True, dt_from_us(1583915555597046))),
        (ETH_BTC, TradeData(84615454, Decimal("0.53965058"), Decimal("7819"), False, dt_from_us(1583915558648408))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USD, ETH_BTC], messages, expected_trades)


def test_book_stream() -> None:
    """
    1. Reconnect if snapshot is crossed.
    2. Reconnect if subscription request returns an error.
    3. Reconnect if crossed book is detected.
    """
    messages = """
        {"type":"subscriptions","channels":[{"name":"level2","product_ids":["BTC-USD"]}], "time":"2023-04-24T08:29:08.501872Z"}
        {"type":"snapshot","product_id":"BTC-USD","asks":[["7963.24","0.03"],["7965", "0.05"]],"bids":[["7964","0.01"],["7963","0.02"]], "time":"2023-04-24T08:29:08.501872Z"}

        {"type":"error","message":"Failed to initialize level 2 channel for BTC-USD. Please try connecting again."}

        {"type":"snapshot","product_id":"BTC-USD","asks":[["7963.24","0.03"],["7965", "0.05"]],"bids":[["7962.8","0.01"],["7962.7","0.02"]], "time":"2023-04-24T08:29:08.501872Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7966.19","0.37711852"]],"time":"2020-04-29T07:11:15.486793Z"}

        {"type":"snapshot","product_id":"BTC-USD","asks":[["7963.24","0.03"],["7965", "0.05"]],"bids":[["7962.8","0.01"],["7962.7","0.02"]], "time":"2023-04-24T08:29:08.501872Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7961.19","0.37711852"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7962.75","0.677"]],"time":"2020-04-29T07:11:15.486794Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7961.19","0.00000"]],"time":"2020-04-29T07:11:15.486795Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["sell","7968.19","0.37"]],"time":"2020-04-29T07:11:15.486796Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["sell","7965","0.23"]],"time":"2020-04-29T07:11:15.486797Z"}
    """  # noqa: E501

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1588144275486797),
        bids=[
            PriceLevel(price=Decimal("7962.8"), amount=Decimal("0.01")),
            PriceLevel(price=Decimal("7962.75"), amount=Decimal("0.677")),
            PriceLevel(price=Decimal("7962.7"), amount=Decimal("0.02")),
        ],
        asks=[
            PriceLevel(price=Decimal("7963.24"), amount=Decimal("0.03")),
            PriceLevel(price=Decimal("7965"), amount=Decimal("0.23")),
            PriceLevel(price=Decimal("7968.19"), amount=Decimal("0.37")),
        ],
    )

    standard_book_stream_test(EXCHANGE_NAME, BTC_USD, messages, 2, expected_book, expected_connect_count=4)


@freeze_time(FROZEN_TIME)
def test_quote_spot_should_return_initial_top_of_book() -> None:
    msg = """
        {"type":"subscriptions","channels":[{"name":"level2","product_ids":["BTC-USD"]}], "time":"2022-02-25 03:21:34Z"}
        {"type":"snapshot","product_id":"BTC-USD","bids":[["7964","0.01"],["7963","0.02"]],"asks":[["7965","0.03"],["7966", "0.05"]], "time":"2022-02-25 03:21:34Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7963.5","0.07"]],"time":"2020-04-29T07:11:15.486793Z"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=ciso8601.parse_datetime(FROZEN_TIME),
        bids=[
            PriceLevel(price=Decimal("7964"), amount=Decimal("0.01")),
        ],
        asks=[
            PriceLevel(price=Decimal("7965"), amount=Decimal("0.03")),
        ],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)


def test_quote_spot_must_store_the_whole_book_in_case_top_positions_are_changed() -> None:
    msg = """
        {"type":"subscriptions","channels":[{"name":"level2","product_ids":["BTC-USD"]}]}
        {"type":"snapshot","product_id":"BTC-USD","bids":[["7964","0.01"],["7963","0.02"],["7962","0.4"]],"asks":[["7965","0.03"],["7966", "0.05"]]}
        {"type":"l2update","product_id":"BTC-USD","changes":[["sell","7965","0.00"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7964","0.00"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7963","0.00"]],"time":"2020-04-29T07:11:15.486793Z"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1588144275486793),
        bids=[PriceLevel(price=Decimal("7962"), amount=Decimal("0.4"))],
        asks=[PriceLevel(price=Decimal("7966"), amount=Decimal("0.05"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)


def test_empty_book_should_update_bids_only() -> None:
    msg = """
        {"type":"subscriptions","channels":[{"name":"level2","product_ids":["BTC-USD"]}]}
        {"type":"snapshot","product_id":"BTC-USD","bids":[],"asks":[]}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7964","2.00"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7965","2.00"]],"time":"2020-04-29T07:11:15.486793Z"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1588144275486793),
        bids=[
            PriceLevel(price=Decimal("7965"), amount=Decimal("2.00")),
        ],
        asks=[],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)


def test_empty_book_should_update_bids_and_then_update_asks() -> None:
    msg = """
        {"type":"subscriptions","channels":[{"name":"level2","product_ids":["BTC-USD"]}]}
        {"type":"snapshot","product_id":"BTC-USD","bids":[],"asks":[]}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7964","2.00"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7965","2.00"]],"time":"2020-04-29T07:11:15.486793Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7965","0.00"]],"time":"2020-04-29T07:11:15.486794Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["buy","7964","0.00"]],"time":"2020-04-29T07:11:15.486795Z"}
        {"type":"l2update","product_id":"BTC-USD","changes":[["sell","7969","3.00"]],"time":"2020-04-29T07:11:15.486796Z"}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=dt_from_us_aware(1588144275486796),
        bids=[],
        asks=[PriceLevel(price=Decimal("7969"), amount=Decimal("3.00"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USD, msg, expected_book)
