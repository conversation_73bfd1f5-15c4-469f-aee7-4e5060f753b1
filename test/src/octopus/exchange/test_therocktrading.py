from src.octopus.data import Instrument
from src.octopus.exchange.therocktrading import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.qa_utils import standard_trade_history_traversal_test
from src.octopus.vex.generate import GeneratedTradeIdRange, monotonic_id_trade_sequence


def test_trade_history_traversal() -> None:
    standard_trade_history_traversal_test(
        EXCHANGE_NAME,
        Instrument.spot("BTCUSD", "btc", "usd"),
        monotonic_id_trade_sequence([
            GeneratedTradeIdRange(1, MAX_TRADES_PER_REQUEST),
            GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST // 2, MAX_TRADES_PER_REQUEST + 1),
            GeneratedTradeIdRange(MAX_TRADES_PER_REQUEST * 2, MAX_TRADES_PER_REQUEST - 1),
        ]),
    )
