from unittest.mock import Mock

from src.octopus.data import Instrument, MarketType
from src.octopus.exchange.binance_us import BinanceUsHttpApi, BinanceUsWebSocketApi

BTC_USD = Instrument.spot("BTCUSD", "btc", "usd")
BTC_USDT_FUTURES = Instrument.futures("BTCUSDT")
BTC_USD_PERP = Instrument.futures("BTCUSD_PERP")


def test_get_http_base_url() -> None:
    binance_us_websocket_api = BinanceUsWebSocketApi(exchange_name="Binance.US", client=Mock(), ticker_translator=Mock())
    assert binance_us_websocket_api._get_http_base_url(BTC_USD) == "https://api.binance.us/api/v3"
    assert binance_us_websocket_api._get_http_base_url(BTC_USDT_FUTURES) == "https://api.binance.us/api/v3"
    assert binance_us_websocket_api._get_http_base_url(BTC_USD_PERP) == "https://api.binance.us/api/v3"


def test_get_exchange_info_url() -> None:
    binance_http_api = BinanceUsHttpApi(exchange_name="Binance", ticker_translator=Mock(), diagnostics=Mock())
    assert binance_http_api._get_exchange_info_url(MarketType.SPOT, False) == "https://api.binance.us/api/v3/exchangeInfo"
    assert binance_http_api._get_exchange_info_url(MarketType.FUTURES, True) == ("https://api.binance.us/api/v3/exchangeInfo")
    assert binance_http_api._get_exchange_info_url(MarketType.FUTURES, False) == ("https://api.binance.us/api/v3/exchangeInfo")
