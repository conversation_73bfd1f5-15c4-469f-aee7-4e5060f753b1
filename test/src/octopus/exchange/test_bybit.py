from datetime import datetime, timedelta, timezone
from decimal import Decimal
import gzip
from pathlib import Path
from typing import Any, Dict, List
from unittest import mock
from unittest.mock import Mock

import pytest
from freezegun import freeze_time

from src.octopus.data import (
    BookData,
    BookType,
    FundingRateData,
    FuturesContractData,
    Instrument,
    LiquidationData,
    MarketType,
    OpenInterestData,
    OptionContractData,
    OptionTickerData,
    OptionType,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.bybit import (
    EXCHANGE_BIRTHDAY,
    EXCHANGE_NAME,
    BybitFuturesType,
    _iter_trade_rows_csv,
    _parse_trade_from_csv,
)
from src.octopus.qa_utils import (
    standard_book_stream_test,
    standard_book_test,
    standard_funding_rate_history_traversal_test,
    standard_futures_open_interest_test,
    standard_last_funding_rates_test,
    standard_liquidations_stream_test,
    standard_quote_stream_test,
    standard_trade_stream_test,
)
from src.utils.timeutil import dt_from_any, dt_from_any_aware, dt_from_ms, dt_from_ms_aware, dt_from_us_aware, truncate_to_minute

BTC_USDT_SPOT = Instrument.spot("BTCUSDT", "btc", "usdt")
ETH_USDT_SPOT = Instrument.spot("ETHUSDT", "eth", "usdt")
BTC_USD = Instrument.futures("BTCUSD", metadata={"funding_interval": 480, "type": BybitFuturesType.LINEAR})
ETH_USD = Instrument.futures("ETHUSD", metadata={"type": BybitFuturesType.INVERSE})
XRP_USD = Instrument.futures("XRPUSD")
BTC_USDT = Instrument.futures("BTCUSDT", metadata={"funding_interval": 480, "type": BybitFuturesType.LINEAR})
BTC_USDU22 = Instrument.futures("BTCUSDU22")
ETH_OPTION = Instrument.option("ETH-26SEP25-14000-P")

BTCUSDT = Instrument.spot("BTCUSDT", "btc", "usdt")
BTC_OPTION = Instrument.option("BTC-1DEC24-93500-P")


def test_trades_spot_stream() -> None:
    messages = """
    {"success": "true", "ret_msg": "subscribe", "conn_id": "718190ce-c69a-4383-a4ca-033a01e01069", "req_id": "BTCUSDT", "op": "subscribe"}
    {"topic": "publicTrade.BTCUSDT", "ts": 1650153599999, "type": "snapshot", "data": [{"i": "2290000000043004247", "T": 1650153599999, "p": "23800", "v": "0.0071", "S": "Buy", "s": "BTCUSDT", "BT": "false"}]}
    {"topic": "publicTrade.BTCUSDT", "ts": 1662076799999, "type": "snapshot", "data": [{"i": "2290000000043004249", "T": 1662076799998, "p": "23800", "v": "0.0013", "S": "Buy", "s": "BTCUSDT", "BT": "false"}, {"i": "2290000000043004250", "T": 1662076799999, "p": "23800", "v": "0.2", "S": "Buy", "s": "BTCUSDT", "BT": "false"}]}
    {"topic": "publicTrade.BTCUSDT", "ts": 1676527200000, "type": "snapshot", "data": [{"i": "2290000000043004256", "T": 1676527200000, "p": "23802.15", "v": "0.030194", "S": "Sell", "s": "BTCUSDT", "BT": "false"}, {"i": "2290000000043004257", "T": 1676527200001, "p": "23802.16", "v": "0.008132", "S": "Sell", "s": "BTCUSDT", "BT": "false"}]}
    """  # noqa: E501

    expected_trades = [
        # first id calculation function
        (BTCUSDT, TradeData(1650153599999, Decimal("0.0071"), Decimal("23800"), True, dt_from_ms(1650153599999))),
        # second id calculation function
        (
            BTCUSDT,
            TradeData(166207679999815368413695188456838, Decimal("0.0013"), Decimal("23800"), True, dt_from_ms(1662076799998)),
        ),
        (
            BTCUSDT,
            TradeData(166207679999911339428235759511615, Decimal("0.2"), Decimal("23800"), True, dt_from_ms(1662076799999)),
        ),
        # third id calculation function
        (BTCUSDT, TradeData(2290000000043004256, Decimal("0.030194"), Decimal("23802.15"), False, dt_from_ms(1676527200000))),
        (BTCUSDT, TradeData(2290000000043004257, Decimal("0.008132"), Decimal("23802.16"), False, dt_from_ms(1676527200001))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTCUSDT], messages, expected_trades, expected_connect_count=1)


def test_book_spot_stream() -> None:
    messages = """
        {"topic": "orderbook.50.BTCUSDT", "ts": 1687446177896, "type": "snapshot", "data": {"s": "BTCUSDT", "b": [["16.59", "92.46"], ["16.58", "131.68"], ["16.57", "288.13"]], "a": [["16.61", "60.84"], ["16.62", "175.18"], ["16.63", "88.07"]], "u": 4525619, "seq": 15240349277}}
        {"topic": "orderbook.50.BTCUSDT", "ts": 1687446178096, "type": "delta", "data": {"s": "BTCUSDT", "b": [["16.59", "0.00"], ["16.58", "111.11"], ["16.56", "222.22"]], "a": [], "u": 4525620, "seq": 15240349844}}
        {"topic": "orderbook.50.BTCUSDT", "ts": 1687446178195, "type": "delta", "data": {"s": "BTCUSDT", "b": [], "a": [["16.61", "134.62"], ["16.63", "41.72"]], "u": 4525621, "seq": 15240349877}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=4525621,
        exchange_time=dt_from_any_aware(1687446178195),
        bids=[
            PriceLevel(price=Decimal("16.58"), amount=Decimal("111.11")),
            PriceLevel(price=Decimal("16.57"), amount=Decimal("288.13")),
            PriceLevel(price=Decimal("16.56"), amount=Decimal("222.22")),
        ],
        asks=[
            PriceLevel(price=Decimal("16.61"), amount=Decimal("134.62")),
            PriceLevel(price=Decimal("16.62"), amount=Decimal("175.18")),
            PriceLevel(price=Decimal("16.63"), amount=Decimal("41.72")),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        BTCUSDT,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
    )


@pytest.mark.parametrize("instrument", [BTC_USD, BTC_USDT])
def test_book_future_stream(instrument: Instrument) -> None:
    messages = f"""
        {{"topic": "orderbook.500.{instrument.symbol}", "type": "snapshot", "ts": 1657639597527280, "data": {{"s": "{instrument.symbol}", "b": [["19900.50", "12500"], ["19901.00", "36440"], ["19902.00", "149595"]], "a": [["19902.50", "48407"], ["19903.00", "25849"], ["19904.00", "59479"]], "u": 15594779, "seq": 55370419445}}}}
        {{"topic": "orderbook.500.{instrument.symbol}", "type": "delta", "ts": 1657639597528882, "data": {{"s": "{instrument.symbol}", "b": [["19900.50", "0"], ["19901.50", "21762"]], "a": [], "u": 15594780, "seq": 55370419579}}}}
        {{"topic": "orderbook.500.{instrument.symbol}", "type": "delta", "ts": 1657639597528982, "data": {{"s": "{instrument.symbol}", "b": [["19901.00", "36540"]], "a": [["19902.50", "50407"]], "u": 15594781, "seq": 55370419579}}}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=15594781,
        exchange_time=dt_from_us_aware(1657639597528982),
        bids=[
            PriceLevel(price=Decimal("19902.00"), amount=Decimal("149595")),
            PriceLevel(price=Decimal("19901.50"), amount=Decimal("21762")),
            PriceLevel(price=Decimal("19901.00"), amount=Decimal("36540")),
        ],
        asks=[
            PriceLevel(price=Decimal("19902.50"), amount=Decimal("50407")),
            PriceLevel(price=Decimal("19903.00"), amount=Decimal("25849")),
            PriceLevel(price=Decimal("19904.00"), amount=Decimal("59479")),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        instrument,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
    )


def test_trade_futures_stream() -> None:
    messages = """
        {"topic": "publicTrade.BTCUSD", "type": "snapshot", "ts": 1682857439141, "data": [{"T": 1682857439139, "s": "BTCUSD", "S": "Sell", "v": "619", "p": "29208.00", "L": "ZeroMinusTick", "i": "c33197cb-eadb-5344-9ddf-7d81de1e608f", "BT": false}, {"T": 1682857439139, "s": "BTCUSD", "S": "Buy", "v": "2131", "p": "29208.00", "L": "ZeroMinusTick", "i": "66fad775-c499-547e-b7d5-e6f88d6e485c", "BT": false}]}
    """  # noqa: E501
    expected_trades = [
        (
            BTC_USD,
            TradeData(
                trade_id=259456960523978583842797892843318698127,
                amount=Decimal("619"),
                price=Decimal("29208.00"),
                is_buy=False,
                time=dt_from_ms(1682857439139),
            ),
        ),
        (
            BTC_USD,
            TradeData(
                trade_id=136883699833299351813095325240136714332,
                amount=Decimal("2131"),
                price=Decimal("29208.00"),
                is_buy=True,
                time=dt_from_ms(1682857439139),
            ),
        ),
    ]
    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USD], messages, expected_trades, market_type=MarketType.FUTURES)


def test_liquidation_stream() -> None:
    messages = """
        {"success":true,"retMsg":"","conn_id":"01ac84da-f1b6-42db-a5f8-3efacb306cd8","request":{"op":"subscribe","args":["liquidation.BTCUSDT"]}}
        {"topic":"liquidation.BTCUSDT","data":{"symbol":"BTCUSDT","side":"Sell","price":"41606.50","size":"0.003","updatedTime":1632942589191}}
        {"topic":"liquidation.BTCUSDT","data":{"symbol":"BTCUSDT","side":"Buy","price":"41611.50","size":"0.043","updatedTime":1632943101830}}
    """  # noqa: E501

    expected_liquidations = [
        (
            BTC_USDT,
            LiquidationData(
                liquidation_id=163294258919100034160650,
                amount=Decimal("0.003"),
                price=Decimal("41606.50"),
                is_buy=True,
                is_order=True,
                time=dt_from_any_aware(1632942589191),
            ),
        ),
        (
            BTC_USDT,
            LiquidationData(
                liquidation_id=163294310183000434161150,
                amount=Decimal("0.043"),
                price=Decimal("41611.50"),
                is_buy=False,
                is_order=True,
                time=dt_from_any_aware(1632943101830),
            ),
        ),
    ]

    standard_liquidations_stream_test(EXCHANGE_NAME, [BTC_USDT], messages, expected_liquidations)


def test_last_funding_rates() -> None:
    standard_last_funding_rates_test(
        EXCHANGE_NAME,
        BTC_USDT,
        [FundingRateData(Decimal("0.001"), timedelta(hours=8), timedelta(hours=8), dt_from_any(1683648000000))],
    )


def test_funding_rate_history_traversal() -> None:
    standard_funding_rate_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USD,
        [
            FundingRateData(
                Decimal(f"0.0{i}"), timedelta(hours=8), timedelta(hours=8), EXCHANGE_BIRTHDAY + timedelta(hours=8 * i)
            )
            for i in range(1, min(int((datetime.utcnow() - EXCHANGE_BIRTHDAY).total_seconds()) // 3600 // 8, 2000))
        ],
    )
    standard_funding_rate_history_traversal_test(
        EXCHANGE_NAME,
        BTC_USDT,
        [
            FundingRateData(
                Decimal(f"0.0{i}"), timedelta(hours=8), timedelta(hours=8), EXCHANGE_BIRTHDAY + timedelta(hours=8 * i)
            )
            for i in range(1, min(int((datetime.utcnow() - EXCHANGE_BIRTHDAY).total_seconds()) // 3600 // 8, 2000))
        ],
    )


def test_open_interest() -> None:
    standard_futures_open_interest_test(
        EXCHANGE_NAME, BTC_USD, [OpenInterestData(Decimal(5), Decimal(5), datetime.now(tz=timezone.utc))], True
    )
    standard_futures_open_interest_test(
        EXCHANGE_NAME, BTC_USDT, [OpenInterestData(Decimal(5.25), Decimal(10.5), datetime.now(tz=timezone.utc))], True
    )


def test_future_book() -> None:
    book_time = dt_from_any_aware(int(1633366087.196223 * 1e6))
    bids = [
        PriceLevel(price=Decimal("48575.5"), amount=Decimal(3371194)),
        PriceLevel(price=Decimal("48575"), amount=Decimal(341441)),
        PriceLevel(price=Decimal("48574.5"), amount=Decimal(235764)),
        PriceLevel(price=Decimal("48574"), amount=Decimal(168674)),
        PriceLevel(price=Decimal("48573.5"), amount=Decimal(90182)),
    ]
    asks = [
        PriceLevel(price=Decimal("48576"), amount=Decimal(1628817)),
        PriceLevel(price=Decimal("48576.5"), amount=Decimal(59)),
        PriceLevel(price=Decimal("48577.5"), amount=Decimal(2594)),
        PriceLevel(price=Decimal("48578"), amount=Decimal(13353)),
        PriceLevel(price=Decimal("48578.5"), amount=Decimal(1188)),
        PriceLevel(price=Decimal("48579"), amount=Decimal(1345)),
        PriceLevel(price=Decimal("48579.5"), amount=Decimal(9529)),
        PriceLevel(price=Decimal("48580"), amount=Decimal(9826)),
    ]
    book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=123,
        exchange_time=book_time,
        bids=bids,
        asks=asks,
    )
    standard_book_test(EXCHANGE_NAME, BTC_USD, 100, book, book)


def test_quote_spot_standard_test_without_updates() -> None:
    msg = """
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675722683, "type": "snapshot", "data": {"s": "BTCUSDT", "b": [["41440.86", "0.119931"]], "a": [["41440.87", "0.0052"]], "u": 370828, "seq": 14366944777}}
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675722936, "type": "delta", "data": {"s": "BTCUSDT", "b": [], "a": [[["41440.84", "0.0055"]], "u": 370829, "seq": 14366945619}}
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675723189, "type": "delta", "data": {"s": "BTCUSDT", "b": [["41440.86", "0.095931"]], "a": [["41440.87", "0.110053"]], "u": 370830, "seq": 14366945683}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=370830,
        exchange_time=dt_from_ms_aware(1649675723189),
        bids=[PriceLevel(price=Decimal("41440.86"), amount=Decimal("0.095931"))],
        asks=[PriceLevel(price=Decimal("41440.87"), amount=Decimal("0.110053"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USDT, msg, expected_book)


@pytest.mark.parametrize(
    "msg, instrument",
    [
        (
            """
                {"topic": "orderbook.1.BTCUSD", "ts": 1649676283066, "type": "snapshot", "data": {"s": "BTCUSD", "b": [["41440.86", "0.119931"]], "a": [["41440.87", "0.0052"]], "u": 370828, "seq": 14366944777}}
                {"topic": "orderbook.1.BTCUSD", "ts": 1649676283087, "type": "delta", "data": {"s": "BTCUSD", "b": [], "a": [], "u": 370829, "seq": 14366945619}}}
            """,  # noqa: E501
            BTC_USD,
        ),
        (
            """
               {"topic": "orderbook.1.BTCUSDT", "ts": 1649676283066, "type": "snapshot", "data": {"s": "BTCUSD", "b": [["41440.86", "0.119931"]], "a": [["41440.87", "0.0052"]], "u": 370828, "seq": 14366944777}}
                {"topic": "orderbook.1.BTCUSDT", "ts": 1649676283087, "type": "delta", "data": {"s": "BTCUSD", "b": [], "a": [], "u": 370829, "seq": 14366945619}}}
            """,  # noqa: E501
            BTC_USDT,
        ),
    ],
)
def test_quote_should_return_initial_book_without_changes(msg: str, instrument: Instrument) -> None:
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=370828,
        exchange_time=dt_from_ms_aware(1649676283066),
        bids=[PriceLevel(price=Decimal("41440.86"), amount=Decimal("0.119931"))],
        asks=[PriceLevel(price=Decimal("41440.87"), amount=Decimal("0.0052"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, instrument, msg, expected_book, market_type=MarketType.FUTURES)


def test_quote_futures_standard_test() -> None:
    msg = """
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675722683, "type": "snapshot", "data": {"s": "BTCUSDT", "b": [["41440.86", "0.119931"]], "a": [["41440.87", "0.0052"]], "u": 370828, "seq": 14366944777}}
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675722936, "type": "delta", "data": {"s": "BTCUSDT", "b": [], "a": [], "u": 370829, "seq": 14366945619}}
        {"topic": "orderbook.1.BTCUSDT", "ts": 1649675723189, "type": "delta", "data": {"s": "BTCUSDT", "b": [["41440.86", "0.095931"]], "a": [["41440.87", "0.110053"]], "u": 370830, "seq": 14366945683}}
    """  # noqa: E501
    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=370830,
        exchange_time=dt_from_ms_aware(1649675723189),
        bids=[PriceLevel(price=Decimal("41440.86"), amount=Decimal("0.095931"))],
        asks=[PriceLevel(price=Decimal("41440.87"), amount=Decimal("0.110053"))],
    )
    standard_quote_stream_test(EXCHANGE_NAME, BTC_USDT, msg, expected_book, market_type=MarketType.FUTURES)


def get_futures_markets_all_valid(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "BTCUSDT",
            "contractType": "LinearPerpetual",
            "status": "Trading",
            "baseCoin": "BTC",
            "quoteCoin": "USDT",
            "launchTime": "1584230400000",
            "deliveryTime": "0",
            "deliveryFeeRate": "",
            "priceScale": "2",
            "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"},
            "priceFilter": {"minPrice": "0.10", "maxPrice": "199999.80", "tickSize": "0.10"},
            "lotSizeFilter": {
                "maxOrderQty": "100.000",
                "minOrderQty": "0.001",
                "qtyStep": "0.001",
                "postOnlyMaxOrderQty": "1000.000",
            },
            "unifiedMarginTrade": True,
            "fundingInterval": 480,
            "settleCoin": "USDT",
        },
        {
            "symbol": "ETHUSD",
            "contractType": "InversePerpetual",
            "status": "Trading",
            "baseCoin": "ETH",
            "quoteCoin": "USD",
            "launchTime": "1546300800000",
            "deliveryTime": "1687950000000",
            "deliveryFeeRate": "",
            "priceScale": "2",
            "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"},
            "priceFilter": {"minPrice": "0.05", "maxPrice": "99999.90", "tickSize": "0.05"},
            "lotSizeFilter": {"maxOrderQty": "1000000", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "5000000"},
            "unifiedMarginTrade": False,
            "fundingInterval": 480,
            "settleCoin": "ETH",
        },
    ]


def get_first_date_from_html(var1: Any, var2: Any, var3: Any) -> datetime:
    return datetime(2019, 10, 1, 0, 0)


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDT,
            FuturesContractData(
                symbol="BTCUSDT",
                underlying_base_id=0,
                underlying_quote_id=100,
                size_asset_id=0,
                margin_asset_id=100,
                underlying_base_name="btc",
                underlying_quote_name="usdt",
                size_asset_name="btc",
                margin_asset_name="usdt",
                underlying_native_base_name="BTC",
                underlying_native_quote_name="USDT",
                listing_date=datetime(2019, 10, 1, 0, 0),
                expiry_date=None,
                contract_size=Decimal("1"),
                tick_size=Decimal("0.10"),
                multiplier_size=None,
                multiplier_size_asset_id=None,
                amount_increment=Decimal("0.001"),
                amount_size_min=Decimal("0.001"),
                amount_size_max=Decimal("100.000"),
                price_increment=Decimal("0.1") ** 2,
                price_size_min=Decimal("0.10"),
                price_size_max=Decimal("199999.80"),
            ),
        ),
        (
            ETH_USD,
            FuturesContractData(
                symbol="ETHUSD",
                underlying_base_id=6,
                underlying_quote_id=3,
                size_asset_id=3,
                margin_asset_id=6,
                underlying_base_name="eth",
                underlying_quote_name="usd",
                size_asset_name="usd",
                margin_asset_name="eth",
                underlying_native_base_name="ETH",
                underlying_native_quote_name="USD",
                # Actual: listing_date=datetime(2022, 3, 11, 0, 0),
                listing_date=datetime(2019, 10, 1, 0, 0),  # Phony date for mock test
                expiry_date=datetime(2023, 6, 28, 11, 0),
                contract_size=Decimal("1"),
                tick_size=Decimal("0.05"),
                multiplier_size=None,
                multiplier_size_asset_id=None,
                amount_increment=Decimal(1),
                amount_size_min=Decimal(1),
                amount_size_max=Decimal(1000000),
                price_increment=Decimal("0.1") ** 2,
                price_size_min=Decimal("0.05"),
                price_size_max=Decimal("99999.90"),
            ),
        ),
    ],
)
@mock.patch("src.octopus.exchange.bybit.BybitHttpApi._get_futures_markets", get_futures_markets_all_valid)
@mock.patch("src.octopus.exchange.bybit.BybitHttpApi._get_first_date_from_html", get_first_date_from_html)
def test_futures_metadata_base(instrument: Instrument, expected_result: FuturesContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    for contract_data in http_api.futures_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def get_spot_markets_all_valid(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "BTCUSDT",
            "baseCoin": "BTC",
            "quoteCoin": "USDT",
            "innovation": "0",
            "status": "1",
            "lotSizeFilter": {
                "basePrecision": "0.000001",
                "quotePrecision": "0.00000001",
                "minOrderQty": "0.000048",
                "maxOrderQty": "46.13",
                "minOrderAmt": "1",
                "maxOrderAmt": "938901",
            },
            "priceFilter": {"tickSize": "0.01"},
        },
        {
            "symbol": "ETHUSDT",
            "baseCoin": "ETH",
            "quoteCoin": "USDT",
            "innovation": "0",
            "status": "0",
            "lotSizeFilter": {
                "basePrecision": "0.00001",
                "quotePrecision": "0.0000001",
                "minOrderQty": "0.00062",
                "maxOrderQty": "544.03",
                "minOrderAmt": "1",
                "maxOrderAmt": "700891",
            },
            "priceFilter": {"tickSize": "0.01"},
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_USDT_SPOT,
            SpotContractData(
                symbol="BTCUSDT",
                base_id=0,
                quote_id=100,
                base_name="btc",
                quote_name="usdt",
                native_base_name="BTC",
                native_quote_name="USDT",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.000001"),
                amount_size_min=Decimal("0.000048"),
                order_size_min=Decimal("1"),
                amount_size_max=Decimal("46.13"),
                price_increment=Decimal("0.01"),
                status="online",
            ),
        ),
        (
            ETH_USDT_SPOT,
            SpotContractData(
                symbol="ETHUSDT",
                base_id=6,
                quote_id=100,
                base_name="eth",
                quote_name="usdt",
                native_base_name="ETH",
                native_quote_name="USDT",
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
                amount_increment=Decimal("0.00001"),
                amount_size_min=Decimal("0.00062"),
                order_size_min=Decimal("1"),
                amount_size_max=Decimal("544.03"),
                price_increment=Decimal("0.01"),
                status="offline",
            ),
        ),
    ],
)  # type: ignore
@mock.patch("src.octopus.exchange.bybit.BybitHttpApi._get_spot_markets", get_spot_markets_all_valid)
def test_spot_metadata_base(instrument: Instrument, expected_result: SpotContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    for contract_data in http_api.spot_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def get_option_markets_all_valid(var1: Any, var2: Any) -> List[Dict[str, Any]]:
    return [
        {
            "symbol": "BTC-26SEP25-300000-P",
            "status": "Trading",
            "baseCoin": "BTC",
            "quoteCoin": "USDC",
            "settleCoin": "USDC",
            "optionsType": "Put",
            "launchTime": "1727942400000",
            "deliveryTime": "1758873600000",
            "deliveryFeeRate": "0.00015",
            "priceFilter": {"minPrice": "5", "maxPrice": "10000000", "tickSize": "5"},
            "lotSizeFilter": {"maxOrderQty": "500", "minOrderQty": "0.01", "qtyStep": "0.01"},
        },
        {
            "symbol": "ETH-26SEP25-14000-P",
            "status": "Trading",
            "baseCoin": "ETH",
            "quoteCoin": "USDC",
            "settleCoin": "USDC",
            "optionsType": "Put",
            "launchTime": "1727942400000",
            "deliveryTime": "1758873600000",
            "deliveryFeeRate": "0.00015",
            "priceFilter": {"minPrice": "0.1", "maxPrice": "10000000", "tickSize": "0.1"},
            "lotSizeFilter": {"maxOrderQty": "5000", "minOrderQty": "0.1", "qtyStep": "0.1"},
        },
    ]


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_OPTION,
            OptionContractData(
                symbol="BTC-26SEP25-300000-P",
                underlying_base_id=0,
                underlying_quote_id=912,
                underlying_base_name="btc",
                underlying_quote_name="usdc",
                listing_date=datetime(2024, 10, 3, 8),
                expiry_date=datetime(2025, 9, 26, 8),
                amount_increment=Decimal("0.01"),
                amount_size_min=Decimal("0.01"),
                amount_size_max=Decimal("500"),
                price_increment=Decimal("5"),
                status="online",
                size_asset_id=0,
                margin_asset_id=912,
                size_asset_name="btc",
                margin_asset_name="usdc",
                strike=Decimal("300000"),
                option_type=OptionType.PUT,
                size=Decimal("1"),
                is_european=True,
                tick_size=Decimal("5"),
                price_size_min=Decimal("5"),
                price_asset_id=912,
                price_asset_name="usdc",
                price_size_max=Decimal("10000000"),
                taker_fee=None,
            ),
        ),
        (
            ETH_OPTION,
            OptionContractData(
                symbol="ETH-26SEP25-14000-P",
                underlying_base_id=6,
                underlying_quote_id=912,
                underlying_base_name="eth",
                underlying_quote_name="usdc",
                listing_date=datetime(2024, 10, 3, 8, 0),
                expiry_date=datetime(2025, 9, 26, 8, 0),
                amount_increment=Decimal("0.1"),
                amount_size_min=Decimal("0.1"),
                amount_size_max=Decimal("5000"),
                price_increment=Decimal("0.1"),
                status="online",
                size_asset_id=6,
                margin_asset_id=912,
                size_asset_name="eth",
                margin_asset_name="usdc",
                strike=Decimal("14000"),
                option_type=OptionType.PUT,
                size=Decimal("1"),
                is_european=True,
                tick_size=Decimal("0.1"),
                price_size_min=Decimal("0.1"),
                price_size_max=Decimal("10000000"),
                taker_fee=None,
                price_asset_id=912,
                price_asset_name="usdc",
            ),
        ),
    ],
)  # type: ignore
@mock.patch("src.octopus.exchange.bybit.BybitHttpApi._get_option_markets", get_option_markets_all_valid)
def test_option_metadata_base(instrument: Instrument, expected_result: OptionContractData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    for contract_data in http_api.option_metadata(http_client_mock):
        if contract_data.symbol == instrument.symbol:
            assert contract_data == expected_result


def get_option_settle_price(*args, **kwargs) -> Decimal:  # type: ignore
    symbol = args[2]
    if symbol == BTC_OPTION.symbol:
        return Decimal("200000")
    elif symbol == ETH_OPTION.symbol:
        return Decimal("10000")
    raise ValueError("Invalid test symbol")


@pytest.mark.parametrize(
    "symbol, expected_result",
    [
        (BTC_OPTION.symbol, Decimal("200000")),
        (ETH_OPTION.symbol, Decimal("10000")),
    ],
)  # type: ignore
@mock.patch("src.octopus.exchange.bybit.BybitHttpApi._get_option_settle_price", get_option_settle_price)
def test_option_settle_price(symbol: str, expected_result: Decimal) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    assert http_api.option_settle_price(http_client_mock, symbol) == expected_result


def option_ticker_get_json(var: Any) -> Dict[str, Any]:
    return {
        "retCode": 0,
        "retMsg": "SUCCESS",
        "result": {
            "category": "option",
            "list": [
                {
                    "symbol": "BTC-1DEC24-93500-P",
                    "bid1Price": "5",
                    "bid1Size": "0.1",
                    "bid1Iv": "0.3054",
                    "ask1Price": "125",
                    "ask1Size": "83.09",
                    "ask1Iv": "0.5515",
                    "lastPrice": "40",
                    "highPrice24h": "330",
                    "lowPrice24h": "40",
                    "markPrice": "21.86536915",
                    "indexPrice": "96910.34668978",
                    "markIv": "0.3797",
                    "underlyingPrice": "96969.50503277",
                    "openInterest": "2.82",
                    "turnover24h": "333343.75670639",
                    "volume24h": "3.43",
                    "totalVolume": "4",
                    "totalTurnover": "377351",
                    "delta": "-0.02977902",
                    "gamma": "0.00003433",
                    "vega": "3.3574236",
                    "theta": "-63.7370878",
                    "predictedDeliveryPrice": "12",
                    "change24h": "-0.87878788",
                }
            ],
        },
    }


FROZEN_TIME = "2024-12-02 17:21:34Z"


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_OPTION,
            OptionTickerData(
                time=truncate_to_minute(dt_from_any_aware(FROZEN_TIME)),
                exchange_time=dt_from_any_aware(FROZEN_TIME),
                price_last=Decimal("40"),
                price_bid=Decimal("5"),
                price_ask=Decimal("125"),
                price_mark=Decimal("21.86536915"),
                price_index=Decimal("96910.34668978"),
                amount_bid=Decimal("0.1"),
                amount_ask=Decimal("83.09"),
                index_name="",
                implied_vol_trade=None,
                implied_vol_bid=Decimal("0.3054"),
                implied_vol_ask=Decimal("0.5515"),
                implied_vol_mark=Decimal("0.3797"),
                greek_delta=Decimal("-0.02977902"),
                greek_gamma=Decimal("0.00003433"),
                greek_theta=Decimal("-63.7370878"),
                greek_vega=Decimal("3.3574236"),
                greek_rho=None,
                estimated_settlement_price=Decimal("12"),
            ),
        ),
    ],
)
@freeze_time(FROZEN_TIME)
@mock.patch("src.octopus.exchange.bybit.get_json", option_ticker_get_json)
def test_option_ticker(instrument: Instrument, expected_result: OptionTickerData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    contract_data = http_api.option_ticker(http_client_mock, instrument)
    assert contract_data == expected_result


def get_option_open_interest_json(arg1: Any):
    return {
        "result": {
            "list": [
                {
                    "symbol": "BTC-1DEC24-93500-P",
                    "bid1Price": "1900",
                    "bid1Size": "0.06",
                    "bid1Iv": "0.6512",
                    "ask1Price": "0",
                    "ask1Size": "0",
                    "ask1Iv": "0",
                    "lastPrice": "1900",
                    "highPrice24h": "1900",
                    "lowPrice24h": "1900",
                    "markPrice": "1086.1251787",
                    "indexPrice": "104735.60261679",
                    "markIv": "0.5698",
                    "underlyingPrice": "116979.53",
                    "openInterest": "0.41",
                    "turnover24h": "1160.7845",
                    "volume24h": "0.01",
                    "totalVolume": "1",
                    "totalTurnover": "43440",
                    "delta": "-0.03446077",
                    "gamma": "0.00000119",
                    "vega": "86.24676537",
                    "theta": "-7.20794015",
                    "predictedDeliveryPrice": "0",
                    "change24h": "-0.17391305",
                },
            ]
        },
    }


@pytest.mark.parametrize(
    "instrument, expected_result",
    [
        (
            BTC_OPTION,
            OpenInterestData(
                contract_count=Decimal("0.41"),
                contract_value_usd=Decimal("0.41") * Decimal("116979.53"),
                time=truncate_to_minute(dt_from_any_aware(FROZEN_TIME)),
            ),
        ),
    ],
)
@freeze_time(FROZEN_TIME)
@mock.patch("src.octopus.exchange.bybit.get_json", get_option_open_interest_json)
def test_option_open_interest(instrument: Instrument, expected_result: OpenInterestData) -> None:
    diagnostics_mock = Mock()
    http_client_mock = Mock()
    http_api = exchange_http_api("Bybit", [], diagnostics_mock)
    http_api.OPTION_BASE_COINS = ["BTC"]
    assert http_api.option_open_interest(http_client_mock, instrument) == expected_result


def test_iter_trade_rows_csv_batch_processing() -> None:
    """Test the batch processing functionality of _iter_trade_rows_csv."""
    # Create test CSV data
    test_csv_data = """trdMatchID,execID,side,size,price,priceEp,symbol,grossValue,homeNotional,foreignNotional,timestamp
1234567890abcdef,exec1,Buy,0.001,50000.0,5000000000000,BTCUSDT,50.0,0.001,50.0,1640995200.123
2234567890abcdef,exec2,Sell,0.002,49999.0,4999900000000,BTCUSDT,99.998,0.002,99.998,1640995201.456
3234567890abcdef,exec3,Buy,0.003,50001.0,5000100000000,BTCUSDT,150.003,0.003,150.003,1640995202.789
4234567890abcdef,exec4,Sell,0.004,49998.0,4999800000000,BTCUSDT,199.992,0.004,199.992,1640995203.012
5234567890abcdef,exec5,Buy,0.005,50002.0,5000200000000,BTCUSDT,250.01,0.005,250.01,1640995204.345
"""

    # Create a mock HTTP client
    mock_client = Mock()

    # Compress the test data
    compressed_data = gzip.compress(test_csv_data.encode("utf-8"))
    mock_client.get.return_value.raw_body = compressed_data

    symbol = "BTCUSDT"
    trade_date = datetime(2022, 1, 1)

    cache_dir = Path() / "bybit_trade_cache"
    cache_file = cache_dir / f"{symbol}{trade_date.date()}.csv.gz"
    state_file = cache_dir / f"{symbol}{trade_date.date()}.csv.gz.state"

    if cache_file.exists():
        cache_file.unlink()
    if state_file.exists():
        state_file.unlink()

    try:
        # Test batch processing with batch_size=2
        batch1 = list(_iter_trade_rows_csv(symbol, trade_date, mock_client, batch_size=2))
        assert len(batch1) == 2
        assert batch1[0].trade_id == int("1234567890abcdef", 16)
        assert batch1[0].amount == Decimal("0.001")
        assert batch1[0].price == Decimal("50000.0")
        assert batch1[0].is_buy is True

        assert batch1[1].trade_id == int("2234567890abcdef", 16)
        assert batch1[1].amount == Decimal("0.002")
        assert batch1[1].price == Decimal("49999.0")
        assert batch1[1].is_buy is False

        # Get next batch
        batch2 = list(_iter_trade_rows_csv(symbol, trade_date, mock_client, batch_size=2))
        assert len(batch2) == 2
        assert batch2[0].trade_id == int("3234567890abcdef", 16)
        assert batch2[1].trade_id == int("4234567890abcdef", 16)

        # Get final batch
        batch3 = list(_iter_trade_rows_csv(symbol, trade_date, mock_client, batch_size=2))
        assert len(batch3) == 1  # Only one row left
        assert batch3[0].trade_id == int("5234567890abcdef", 16)

        # Next call should return first items again.
        batch4 = list(_iter_trade_rows_csv(symbol, trade_date, mock_client, batch_size=2))
        assert len(batch4) == 2

    finally:
        # Clean up
        if cache_file.exists():
            cache_file.unlink()
        if state_file.exists():
            state_file.unlink()


def test_parse_trade_from_csv() -> None:
    """Test the CSV parsing function."""
    row = {"trdMatchID": "1234567890abcdef", "side": "Buy", "size": "0.001", "price": "50000.0", "timestamp": "1640995200.123"}

    trade = _parse_trade_from_csv(row)
    assert trade.trade_id == int("1234567890abcdef", 16)
    assert trade.amount == Decimal("0.001")
    assert trade.price == Decimal("50000.0")
    assert trade.is_buy is True
    # dt_from_s truncates to seconds and returns naive datetime, so 1640995200.123 becomes 1640995200
    assert trade.time == datetime.utcfromtimestamp(1640995200)
