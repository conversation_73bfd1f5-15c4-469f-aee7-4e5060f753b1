from decimal import Decimal

from src.octopus.data import Instrument, TradeData
from src.octopus.exchange.upbit import EXCHANGE_NAME
from src.octopus.qa_utils import standard_trade_stream_test
from src.utils.timeutil import dt_from_us

BTC_USDT = Instrument.spot("USDT-BTC", "btc", "usdt")
ETH_BTC = Instrument.spot("BTC-ETH", "btc", "eth")


def test_trade_stream() -> None:
    messages = """
        {"ty":"trade","cd":"USDT-BTC","tms":1582798941664,"td":"2020-02-27","ttm":"10:22:21","ttms":1582798941000,"tp":9398.03499999,"tv":0.00011994,"ab":"BID","pcp":8732.01600005,"c":"RISE","cp":666.01899994,"sid":15827989410000000,"st":"SNAPSHOT"}
        {"ty":"trade","cd":"BTC-ETH","tms":1582800584119,"td":"2020-02-27","ttm":"10:49:43","ttms":1582800583000,"tp":0.02573703,"tv":0.27034953,"ab":"ASK","pcp":0.02498940,"c":"FALL","cp":0.00074763,"sid":15828005830000001,"st":"SNAPSHOT"}
    """

    expected_trades = [
        (
            BTC_USDT,
            TradeData(15827989410000000, Decimal("0.00011994"), Decimal("9398.03499999"), True, dt_from_us(1582798941664000)),
        ),
        (
            ETH_BTC,
            TradeData(15828005830000001, Decimal("0.27034953"), Decimal("0.02573703"), False, dt_from_us(1582800584119000)),
        ),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_USDT, ETH_BTC], messages, expected_trades)
