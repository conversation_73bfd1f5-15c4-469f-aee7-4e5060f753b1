from decimal import Decimal
from http import HTTPStatus

import ciso8601
from freezegun import freeze_time  # type: ignore

from src.octopus.data import BookData, BookType, Instrument, MutableBookDataTolerateMissingZeroLevels, PriceLevel, TradeData
from src.octopus.exchange.bitflyer import EXCHANGE_NAME
from src.octopus.qa_utils import (
    MessageReplayHttpClient,
    standard_book_stream_test,
    standard_book_test,
    standard_trade_stream_test,
)
from src.utils.http import HttpResponse
from src.utils.timeutil import dt_from_us_aware

BTC_JPY = Instrument.spot("BTC_JPY", "btc", "jpy")
ETH_JPY = Instrument.spot("ETH_JPY", "eth", "jpy")
BTC_USD = Instrument.spot("BTC_USD", "btc", "usd")
FROZEN_TIME = "2022-02-25 03:21:34Z"


def test_book() -> None:
    standard_book_test(
        EXCHANGE_NAME,
        BTC_JPY,
        2,
        BookData(
            BookType.FULL,
            None,
            None,
            [
                PriceLevel(price=Decimal("2.0"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("1.95"), amount=Decimal("1.2")),
                PriceLevel(price=Decimal("1.9"), amount=Decimal("1.3")),
                PriceLevel(price=Decimal("1.8"), amount=Decimal("1.4")),
            ],
            [
                PriceLevel(price=Decimal("2.1"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("2.2"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.25"), amount=Decimal("0.5")),
                PriceLevel(price=Decimal("2.3"), amount=Decimal("0.7")),
            ],
        ),
        BookData(
            BookType.FULL,
            None,
            None,
            [
                PriceLevel(price=Decimal("2.0"), amount=Decimal("1.1")),
                PriceLevel(price=Decimal("1.95"), amount=Decimal("1.2")),
            ],
            [PriceLevel(price=Decimal("2.1"), amount=Decimal("1.1")), PriceLevel(price=Decimal("2.2"), amount=Decimal("0.5"))],
        ),
    )


def test_trade_stream() -> None:
    messages = """
        {"jsonrpc":"2.0","id":1,"result":true}
        {"jsonrpc":"2.0","id":2,"result":true}
        {"jsonrpc":"2.0","method":"channelMessage","params":{"channel":"lightning_executions_BTC_JPY","message":[{"id":1588629364,"side":"BUY","price":969999.0,"size":0.1,"exec_date":"2020-02-27T11:49:12.5861889Z","buy_child_order_acceptance_id":"JRF20200227-114912-806704","sell_child_order_acceptance_id":"JRF20200227-114902-825727"},{"id":1588629365,"side":"BUY","price":970000.0,"size":0.06148,"exec_date":"2020-02-27T11:49:12.5861889Z","buy_child_order_acceptance_id":"JRF20200227-114912-806704","sell_child_order_acceptance_id":"JRF20200227-112832-041847"}]}}
        {"jsonrpc":"2.0","method":"channelMessage","params":{"channel":"lightning_executions_ETH_JPY","message":[{"id":1588629409,"side":"SELL","price":25058.0,"size":0.01,"exec_date":"2020-02-27T11:49:15.0840542Z","buy_child_order_acceptance_id":"JRF20200227-114900-931013","sell_child_order_acceptance_id":"JRF20200227-114914-936475"}]}}
    """  # noqa: E501

    expected_trades = [
        (BTC_JPY, TradeData(1588629364, Decimal("0.1"), Decimal("969999.0"), True, dt_from_us_aware(1582804152586188))),
        (BTC_JPY, TradeData(1588629365, Decimal("0.06148"), Decimal("970000.0"), True, dt_from_us_aware(1582804152586188))),
        (ETH_JPY, TradeData(1588629409, Decimal("0.01"), Decimal("25058.0"), False, dt_from_us_aware(1582804155084054))),
    ]

    standard_trade_stream_test(EXCHANGE_NAME, [BTC_JPY, ETH_JPY], messages, expected_trades)


@freeze_time(FROZEN_TIME)
def test_book_stream() -> None:
    messages = """
        {"jsonrpc": "2.0", "method": "channelMessage", "params": {"channel": "lightning_board_BTC_USD", "message": {"mid_price": "19400.36", "bids": [], "asks": []}}}
        {"jsonrpc": "2.0", "method": "channelMessage", "params": {"channel": "lightning_board_BTC_USD", "message": {"mid_price": "19400.36", "bids": [{"price": "18.245", "size": "0.0"}, {"price": "18.243", "size": "0.0"}], "asks": [{"price": "18.256", "size": "1220.0"}]}}}
        {"jsonrpc": "2.0", "method": "channelMessage", "params": {"channel": "lightning_board_BTC_USD", "message": {"mid_price": "19400.36", "bids": [{"price": "18.241", "size": "3000.0"}], "asks": []}}}
    """  # noqa: E501
    snapshot_body = """{"mid_price": "18.249", "bids": [{"price": "18.243", "size": "24.46"}, {"price": "18.242", "size": "4000.0"}, {"price": "18.229", "size": "991.0"}], "asks": [{"price": "18.256", "size": "3999.76"}, {"price": "18.285", "size": "24.46"}, {"price": "18.286", "size": "30190.0"}]}"""  # noqa: E501
    http_client = MessageReplayHttpClient([HttpResponse(HTTPStatus.OK, snapshot_body)])

    expected_book = BookData(
        book_type=BookType.FULL,
        exchange_sequence_id=None,
        exchange_time=ciso8601.parse_datetime(FROZEN_TIME),
        bids=[
            PriceLevel(price=Decimal("18.242"), amount=Decimal("4000.0")),
            PriceLevel(price=Decimal("18.241"), amount=Decimal("3000.0")),
            PriceLevel(price=Decimal("18.229"), amount=Decimal("991.0")),
        ],
        asks=[
            PriceLevel(price=Decimal("18.256"), amount=Decimal("1220.0")),
            PriceLevel(price=Decimal("18.285"), amount=Decimal("24.46")),
            PriceLevel(price=Decimal("18.286"), amount=Decimal("30190.0")),
        ],
    )

    standard_book_stream_test(
        EXCHANGE_NAME,
        BTC_USD,
        messages,
        3,
        expected_book,
        expected_connect_count=1,
        http_client=http_client,
        mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
    )
