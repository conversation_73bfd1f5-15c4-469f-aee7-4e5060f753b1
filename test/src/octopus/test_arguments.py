from unittest.mock import Mock

import pytest

from src.octopus.applications.proxies.groups import PROXY_GROUPS
from src.octopus.arguments import get_instance_number, proxies_from_args
from src.utils.http import ProxyParams


def test_proxies_from_args_for_prod() -> None:
    for proxy_group_name in PROXY_GROUPS:
        namespace = Mock(exclude_proxies=[])
        namespace.machine = "1"
        namespace.proxies = []
        namespace.proxy_groups = [[proxy_group_name]]
        namespace.exclusive_proxies = False
        assert proxies_from_args(namespace) == [
            (1, ProxyParams.from_string(proxy_str)) for proxy_str in sorted(PROXY_GROUPS[proxy_group_name])
        ], proxy_group_name


def test_proxies_from_args_with_error() -> None:
    namespace = Mock(exclude_proxies=[])
    namespace.machine = "1"
    namespace.proxies = []
    namespace.proxy_groups = ["not-existing-group"]
    with pytest.raises(ValueError):
        proxies_from_args(namespace)


@pytest.mark.parametrize(
    "instance_hostname, expected_instance_number",
    [
        ("feed-handler-zbcom-meta-spot-http-1-8d8dc859c-hkkcc", 1),
        ("prod-cryptocom-meta-spot-http-scrapers-production-1", 1),
        ("prod-binance-1h-book-spot-http-feed-handlers-prod-1", 1),
        ("prod-binance-book-spot-http-fh-books-prod-1", 1),
        ("prod-lbank-meta-spot-http-scrapers-production-2", 2),
        ("shipper-spot-trade-exch-2-12345c6d8-tgbtb", 2),
        ("shipper-spot-trade-exch-2-6f468c6d8-tgbtb", 2),
        ("prod-binance-1h-book-spot-http-feed-handlers-prod-2", 2),
        ("prod-binance-book-spot-http-fh-books-prod-2", 2),
    ],
)
def test_instance_number_from_machine_name(instance_hostname: str, expected_instance_number: int) -> None:
    assert get_instance_number(instance_hostname) == expected_instance_number
