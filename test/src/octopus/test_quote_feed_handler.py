from argparse import Namespace
from copy import deepcopy
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any, Dict, Optional
from unittest.mock import MagicMock, Mock

from src.octopus.data import Book, BookData, BookType, Instrument, Market, MarketType, PriceLevel
from src.octopus.exchange.api import IExchangeHttpApi
from src.octopus.run import Quote<PERSON>craper, ScraperParams
from src.utils.application import Application
from src.utils.diagnostics import Diagnostics
from src.utils.http import IHttpClient
from src.utils.rate_limit import RateLimitedResourcePool

BTCUSDT = Instrument.spot(symbol="BTCUSDT", base="btc", quote="usdt")

book1_parameters = {
    "market": Market(exchange_id=11, instrument=BTCUSDT),
    "depth_limit": 1,
    "collect_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=12, microsecond=672327, tzinfo=UTC),
    "deduplication_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=12, microsecond=972327, tzinfo=UTC),
    "scraper_session_id": 1644571092399540,
    "scraper_sequence_id": 123,
    "data": BookData(
        asks=[PriceLevel(price=Decimal("43595.00"), amount=Decimal("0.00465"))],
        bids=[PriceLevel(price=Decimal("43586.28"), amount=Decimal("0.46900"))],
        book_type=BookType.FULL,
        exchange_sequence_id=54646,
        exchange_time=datetime(year=2022, month=2, day=11, hour=12, minute=21, second=11, microsecond=672327, tzinfo=UTC),
    ),
}
book1 = Book(**book1_parameters)  # type: ignore

book2_parameters = {
    "market": Market(exchange_id=11, instrument=BTCUSDT),
    "depth_limit": 1,
    "collect_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=13, microsecond=672327, tzinfo=UTC),
    "deduplication_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=13, microsecond=972327, tzinfo=UTC),
    "scraper_session_id": 1644571092399540,
    "scraper_sequence_id": 124,
    "data": BookData(
        asks=[PriceLevel(price=Decimal("43595.00"), amount=Decimal("0.00465"))],
        bids=[PriceLevel(price=Decimal("43586.28"), amount=Decimal("0.46900"))],
        book_type=BookType.FULL,
        exchange_sequence_id=54648,
        exchange_time=datetime(year=2022, month=2, day=11, hour=12, minute=21, second=12, microsecond=672327, tzinfo=UTC),
    ),
}
book2 = Book(**book2_parameters)  # type: ignore

book3_parameters = {
    "market": Market(exchange_id=11, instrument=BTCUSDT),
    "depth_limit": 1,
    "collect_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=14, microsecond=672327, tzinfo=UTC),
    "deduplication_time": datetime(year=2022, month=2, day=11, hour=12, minute=21, second=14, microsecond=972327, tzinfo=UTC),
    "scraper_session_id": 1644571092399540,
    "scraper_sequence_id": 125,
    "data": BookData(
        asks=[PriceLevel(price=Decimal("43595.07"), amount=Decimal("0.00465"))],
        bids=[PriceLevel(price=Decimal("43586.28"), amount=Decimal("0.46900"))],
        book_type=BookType.FULL,
        exchange_sequence_id=54649,
        exchange_time=datetime(year=2022, month=2, day=11, hour=12, minute=21, second=13, microsecond=672327, tzinfo=UTC),
    ),
}
book3 = Book(**book3_parameters)  # type: ignore


def get_quote_feed_handler(market_type: MarketType, exchange_id: int, args: Optional[Dict[str, Any]] = None) -> QuoteScraper:
    args = args or {}
    args_mock = MagicMock(Namespace, **args)
    args_mock.spot = market_type
    return QuoteScraper(
        params=Mock(ScraperParams),
        app=Mock(Application),
        args=args_mock,
        exchange_id=exchange_id,
        http_api=Mock(IExchangeHttpApi),
        client=IHttpClient(),
        ws_proxy_pool=Mock(RateLimitedResourcePool),
        diagnostics=Mock(Diagnostics),
    )


def test_handle_new_book_should_create_book_with_its_own_feed_handler_sequence_id() -> None:
    quote_feed_handler = get_quote_feed_handler(MarketType.SPOT, 11)
    handled_book1 = quote_feed_handler.item_handler.handle_new_book(book1)
    handled_book2 = quote_feed_handler.item_handler.handle_new_book(book2)
    handled_book3 = quote_feed_handler.item_handler.handle_new_book(book3)
    book1_parameters_new = deepcopy(book1_parameters)
    book1_parameters_new["scraper_sequence_id"] = 1
    book3_parameters_new = deepcopy(book3_parameters)
    book3_parameters_new["scraper_sequence_id"] = 2
    assert handled_book1 == Book(**book1_parameters_new)  # type: ignore
    assert handled_book2 is None
    assert handled_book3 == Book(**book3_parameters_new)  # type: ignore


def test_check_kafka_topic_name() -> None:
    quote_feed_handler = get_quote_feed_handler(MarketType.SPOT, 11, {"exchange": "HitBTC"})
    assert quote_feed_handler.kafka_topic_name == "quotes_11"
