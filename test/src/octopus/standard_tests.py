import copy
from decimal import Decima<PERSON>
from typing import List

from sortedcontainers import SortedDict  # type: ignore

from src.octopus.data import PriceLevel
from src.octopus.exceptions import InvalidOrder


def update_side(state: "SortedDict[Decimal, PriceLevel]", delta: List[PriceLevel]) -> "SortedDict[Decimal, PriceLevel]":
    state = copy.deepcopy(state)
    for el in delta:
        if el.amount == 0:
            if el.price in state:
                del state[el.price]
        else:
            state[el.price] = el
    return state


def standard_book_delta_transform_test(
    full_book_state: "SortedDict[Decimal, PriceLevel]",
    input_updates: List[PriceLevel],
    depth: int,
    sign: int,
    new_book_state: "SortedDict[Decimal, PriceLevel]",
    new_updates: List[PriceLevel],
) -> None:
    for i in range(len(new_updates) - 1):
        if sign * new_updates[i].price >= sign * new_updates[i + 1].price:
            raise InvalidOrder(f"Unsorted side: {new_updates}")

    expected_state = update_side(full_book_state, input_updates)
    assert new_book_state == expected_state

    factual_state = update_side(full_book_state, new_updates)
    top_factual_state = {key: factual_state[key] for key in factual_state.keys()[:depth]}
    top_expected_state = {key: expected_state[key] for key in expected_state.keys()[:depth]}
    assert top_factual_state == top_expected_state
