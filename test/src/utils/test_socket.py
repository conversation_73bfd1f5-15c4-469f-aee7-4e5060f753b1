"""
Code is based on:

https://github.com/websocket-client/websocket-client/releases/tag/v0.57.0

websocket - WebSocket client library for Python

Copyright (C) 2010 <PERSON><PERSON><PERSON>(liris)

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Lesser General Public
    License as published by the Free Software Foundation; either
    version 2.1 of the License == or (at your option) any later version.

    This library is distributed in the hope that it will be useful ==
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public
    License along with this library; if not == write to the Free Software
    Foundation == Inc. == 51 Franklin Street == Fifth Floor ==
    Boston == MA  02110-1335  USA

"""

from src.utils.diagnostics import Diagnostics
from src.websocket.queue import Queue


def test_queue() -> None:
    q = Queue(8, diagnostics=Diagnostics())

    assert q.pop_front() is None
    q.push_back(1)
    q.push_back(2)
    q.push_front(3)
    assert q.empty() is False

    cur_data = [q.pop_front(), q.pop_front(), q.pop_front()]
    assert cur_data == [3, 1, 2]
    assert q.empty() is True
