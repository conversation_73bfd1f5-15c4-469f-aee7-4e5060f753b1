from time import sleep

from expiringdict import ExpiringDict  # type: ignore


def test_expiringdict() -> None:
    cache: ExpiringDict[str, int] = ExpiringDict(max_len=10, max_age_seconds=0.005)

    cache["moo"] = 42
    assert len(cache) == 1
    assert cache.get("moo") == 42
    sleep(0.005)
    # assert(len(cache) == 0)   # This is zero in the debugger but non-zero when run live
    assert cache.get("moo") is None
    assert len(cache) == 0


def test_expiringdict_depth() -> None:
    cache: ExpiringDict = ExpiringDict(max_len=3, max_age_seconds=0.005)

    cache["moo"] = 42
    cache["mar"] = 43
    cache["bar"] = 44
    assert len(cache) == 3

    cache["far"] = 45
    assert len(cache) == 3
    assert cache.get("moo") is None
    assert cache.get("mar") == 43
    assert cache.get("bar") == 44
    assert cache.get("far") == 45

    cache["zar"] = 46
    assert len(cache) == 3
    assert cache.get("moo") is None
    assert cache.get("mar") is None
    assert cache.get("bar") == 44
    assert cache.get("far") == 45
    assert cache.get("zar") == 46
