def package_type():
    return "composite"

def package_script():
    return ["solana"]

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    evm_l2_base_layer_assets = sorted(set([
        "eth_base",
        "eth_op",
    ]))

    erc_20_non_rebasing = sorted(set([
        'cbbtc_base.eth',
        'cbbtc_eth',
        'dai.e_base.eth',
        'dola.e_base.eth',
        'op_op.eth',
        'usdc.e_op.eth',
        'usdc_arb.eth',
        'usdc_base.eth',
        'usdc_op.eth',
        'usds_eth',
        'usdt.e_op.eth',
        'wsteth.e_base.eth',
        'wsteth.e_op.eth',
    ]))

    erc_20_non_rebasing_without_reference_rate = sorted(set([
        'cbbtc_base.eth',
        'cbbtc_eth',
        'dola.e_base.eth'
    ]))

    erc_20_non_rebasing_with_reference_rate = sorted(set(erc_20_non_rebasing) - set(erc_20_non_rebasing_without_reference_rate))

    evm_l2_base_layers_metrics = [
        'AssetCompletionTime',
        'AssetEODCompletionTime',
        'BlkCnt',
        'BlkHgt',
        'BlkIntMean',
        'BlkSizeByte',
        'BlkSizeMeanByte',
        'FeeByteMeanNtv',
        'FeeMeanNtv',
        'FeeMedNtv',
        'FeePrioMeanNtv',
        'FeePrioMedNtv',
        'FeePrioTotNtv',
        'FeeTotNtv',
        'GasBaseBlkMean',
        'GasLmtBlk',
        'GasLmtBlkMean',
        'GasLmtTx',
        'GasLmtTxMean',
        'GasUsedTx',
        'GasUsedTxMean',
        'TxCnt',
        'TxCntSec',
        'TxEIP1559Cnt',
        'TxMeanByte',
    ]

    erc20_tokens = [
        'eurc_avaxc'
    ]

    metrics_for_erc20_tokens = [
        'AdrAct30dCnt',
        'AdrAct7dCnt',
        'AdrActCnt',
        'AdrActContCnt',
        'AdrActRecCnt',
        'AdrActSentCnt',
        'AdrBal1in100KCnt',
        'AdrBal1in100MCnt',
        'AdrBal1in10BCnt',
        'AdrBal1in10KCnt',
        'AdrBal1in10MCnt',
        'AdrBal1in1BCnt',
        'AdrBal1in1KCnt',
        'AdrBal1in1MCnt',
        'AdrBalCnt',
        'AdrBalNtv0.001Cnt',
        'AdrBalNtv0.01Cnt',
        'AdrBalNtv0.1Cnt',
        'AdrBalNtv100Cnt',
        'AdrBalNtv100KCnt',
        'AdrBalNtv10Cnt',
        'AdrBalNtv10KCnt',
        'AdrBalNtv1Cnt',
        'AdrBalNtv1KCnt',
        'AdrBalNtv1MCnt',
        'AdrBalUSD100Cnt',
        'AdrBalUSD100KCnt',
        'AdrBalUSD10Cnt',
        'AdrBalUSD10KCnt',
        'AdrBalUSD10MCnt',
        'AdrBalUSD1Cnt',
        'AdrBalUSD1KCnt',
        'AdrBalUSD1MCnt',
        'AdrNewBalCnt',
        'AdrNewCnt',
        'AssetCompletionTime',
        'AssetEODCompletionTime',
        'CapAct1yrUSD',
        'CapMVRVCur',
        'CapMVRVFF',
        'CapMrktCurUSD',
        'CapMrktFFUSD',
        'CapRealUSD',
        'ContBalCnt',
        'ContCnt',
        'IssTotNtv',
        'IssTotUSD',
        'NDF',
        'NVTAdj',
        'NVTAdj90',
        'NVTAdjFF',
        'NVTAdjFF90',
        'PriceBTC',
        'PriceUSD',
        'ROI1yr',
        'ROI30d',
        'RVTAdj',
        'RVTAdj90',
        'SER',
        'SplyAct10yr',
        'SplyAct180d',
        'SplyAct1d',
        'SplyAct1yr',
        'SplyAct2yr',
        'SplyAct30d',
        'SplyAct3yr',
        'SplyAct4yr',
        'SplyAct5yr',
        'SplyAct7d',
        'SplyAct90d',
        'SplyActEver',
        'SplyActPct1yr',
        'SplyAdrBal1in100K',
        'SplyAdrBal1in100M',
        'SplyAdrBal1in10B',
        'SplyAdrBal1in10K',
        'SplyAdrBal1in10M',
        'SplyAdrBal1in1B',
        'SplyAdrBal1in1K',
        'SplyAdrBal1in1M',
        'SplyAdrBalNtv0.001',
        'SplyAdrBalNtv0.01',
        'SplyAdrBalNtv0.1',
        'SplyAdrBalNtv1',
        'SplyAdrBalNtv10',
        'SplyAdrBalNtv100',
        'SplyAdrBalNtv100K',
        'SplyAdrBalNtv10K',
        'SplyAdrBalNtv1K',
        'SplyAdrBalNtv1M',
        'SplyAdrBalUSD1',
        'SplyAdrBalUSD10',
        'SplyAdrBalUSD100',
        'SplyAdrBalUSD100K',
        'SplyAdrBalUSD10K',
        'SplyAdrBalUSD10M',
        'SplyAdrBalUSD1K',
        'SplyAdrBalUSD1M',
        'SplyAdrTop100',
        'SplyAdrTop10Pct',
        'SplyAdrTop1Pct',
        'SplyContNtv',
        'SplyContUSD',
        'SplyCur',
        'SplyExpFut10yrCMBI',
        'SplyFF',
        'SplyFound',
        'SplyLost',
        'SplyRes',
        'SplyTeam',
        'TxCnt',
        'TxCntSec',
        'TxContCnt',
        'TxTfrCnt',
        'TxTfrValAbUSD100MCnt',
        'TxTfrValAbUSD100MNtv',
        'TxTfrValAbUSD100MUSD',
        'TxTfrValAbUSD100kCnt',
        'TxTfrValAbUSD100kNtv',
        'TxTfrValAbUSD100kUSD',
        'TxTfrValAbUSD10MCnt',
        'TxTfrValAbUSD10MNtv',
        'TxTfrValAbUSD10MUSD',
        'TxTfrValAbUSD1MCnt',
        'TxTfrValAbUSD1MNtv',
        'TxTfrValAbUSD1MUSD',
        'TxTfrValAdjNtv',
        'TxTfrValAdjUSD',
        'TxTfrValBelUSD5Cnt',
        'TxTfrValBelUSD5Ntv',
        'TxTfrValBelUSD5USD',
        'TxTfrValBelUSD10Cnt',
        'TxTfrValBelUSD10Ntv',
        'TxTfrValBelUSD10USD',
        'TxTfrValBelUSD20Cnt',
        'TxTfrValBelUSD20Ntv',
        'TxTfrValBelUSD20USD',
        'TxTfrValBelUSD50Cnt',
        'TxTfrValBelUSD50Ntv',
        'TxTfrValBelUSD50USD',
        'TxTfrValBelUSD100Cnt',
        'TxTfrValBelUSD100Ntv',
        'TxTfrValBelUSD100USD',
        'TxTfrValBelUSD10kCnt',
        'TxTfrValBelUSD10kNtv',
        'TxTfrValBelUSD10kUSD',
        'TxTfrValBelUSD1kCnt',
        'TxTfrValBelUSD1kNtv',
        'TxTfrValBelUSD1kUSD',
        'TxTfrValBelUSD500Cnt',
        'TxTfrValBelUSD500Ntv',
        'TxTfrValBelUSD500USD',
        'TxTfrValContCallNtv',
        'TxTfrValContCallUSD',
        'TxTfrValMeanNtv',
        'TxTfrValMeanUSD',
        'TxTfrValMedNtv',
        'TxTfrValMedUSD',
        'TxTfrValNtv',
        'TxTfrValUSD',
        'VelAct1yr',
        'VelActAdj1yr',
        'VelCur1yr',
        'VelCurAdj1yr',
        'VtyDayRet180d',
        'VtyDayRet30d',
        'VtyDayRet60d',
    ]

    append_asset_metrics(erc20_tokens, metrics_for_erc20_tokens, '1d', dest)

    aggregated_coins_without_supply = [
        'tusd',
    ]

    metrics_for_aggregated_coins_without_supply = [
        'AdrAct30dCnt',
        'AdrAct7dCnt',
        'AdrActCnt',
        'AdrActRecCnt',
        'AdrActSentCnt',
        'AdrNewBalCnt',
        'AdrNewCnt',
        'AggAssetCnt',
        'AssetCompletionTime',
        'AssetEODCompletionTime',
        'CapMrktEstUSD',
        'IssTotNtv',
        'IssTotUSD',
        'NVTAdj',
        'NVTAdj90',
        'PriceBTC',
        'PriceUSD',
        'ROI1yr',
        'ROI30d',
        'RVTAdj',
        'RVTAdj90',
        'SplyEstNtv',
        'TxCnt',
        'TxCntSec',
        'TxTfrCnt',
        'TxTfrValAbUSD100MCnt',
        'TxTfrValAbUSD100MNtv',
        'TxTfrValAbUSD100MUSD',
        'TxTfrValAbUSD100kCnt',
        'TxTfrValAbUSD100kNtv',
        'TxTfrValAbUSD100kUSD',
        'TxTfrValAbUSD10MCnt',
        'TxTfrValAbUSD10MNtv',
        'TxTfrValAbUSD10MUSD',
        'TxTfrValAbUSD1MCnt',
        'TxTfrValAbUSD1MNtv',
        'TxTfrValAbUSD1MUSD',
        'TxTfrValAdjNtv',
        'TxTfrValAdjUSD',
        'TxTfrValBelUSD5Cnt',
        'TxTfrValBelUSD5Ntv',
        'TxTfrValBelUSD5USD',
        'TxTfrValBelUSD10Cnt',
        'TxTfrValBelUSD10Ntv',
        'TxTfrValBelUSD10USD',
        'TxTfrValBelUSD20Cnt',
        'TxTfrValBelUSD20Ntv',
        'TxTfrValBelUSD20USD',
        'TxTfrValBelUSD50Cnt',
        'TxTfrValBelUSD50Ntv',
        'TxTfrValBelUSD50USD',
        'TxTfrValBelUSD100Cnt',
        'TxTfrValBelUSD100Ntv',
        'TxTfrValBelUSD100USD',
        'TxTfrValBelUSD10kCnt',
        'TxTfrValBelUSD10kNtv',
        'TxTfrValBelUSD10kUSD',
        'TxTfrValBelUSD1kCnt',
        'TxTfrValBelUSD1kNtv',
        'TxTfrValBelUSD1kUSD',
        'TxTfrValBelUSD500Cnt',
        'TxTfrValBelUSD500Ntv',
        'TxTfrValBelUSD500USD',
        'TxTfrValMeanNtv',
        'TxTfrValMeanUSD',
        'TxTfrValNtv',
        'TxTfrValUSD',
        'VtyDayRet180d',
        'VtyDayRet30d',
        'VtyDayRet60d',
    ]

    aggregated_coins_without_free_float = [
        'eurc',
        'pyusd',
        'usdc',
        'usdt',
    ]

    metrics_for_aggregated_coins_without_free_float = metrics_for_aggregated_coins_without_supply + [
        'AdrBalCnt',
        'AdrBalNtv0.001Cnt',
        'AdrBalNtv0.01Cnt',
        'AdrBalNtv0.1Cnt',
        'AdrBalNtv100Cnt',
        'AdrBalNtv100KCnt',
        'AdrBalNtv10Cnt',
        'AdrBalNtv10KCnt',
        'AdrBalNtv1Cnt',
        'AdrBalNtv1KCnt',
        'AdrBalNtv1MCnt',
        'AdrBalUSD100Cnt',
        'AdrBalUSD100KCnt',
        'AdrBalUSD10Cnt',
        'AdrBalUSD10KCnt',
        'AdrBalUSD10MCnt',
        'AdrBalUSD1Cnt',
        'AdrBalUSD1KCnt',
        'AdrBalUSD1MCnt',
        'CapAct1yrUSD',
        'CapMVRVCur',
        'CapMrktCurUSD',
        'CapRealUSD',
        'SplyAct10yr',
        'SplyAct180d',
        'SplyAct1d',
        'SplyAct1yr',
        'SplyAct2yr',
        'SplyAct30d',
        'SplyAct3yr',
        'SplyAct4yr',
        'SplyAct5yr',
        'SplyAct7d',
        'SplyAct90d',
        'SplyActEver',
        'SplyActPct1yr',
        'SplyAdrBalNtv0.001',
        'SplyAdrBalNtv0.01',
        'SplyAdrBalNtv0.1',
        'SplyAdrBalNtv1',
        'SplyAdrBalNtv10',
        'SplyAdrBalNtv100',
        'SplyAdrBalNtv100K',
        'SplyAdrBalNtv10K',
        'SplyAdrBalNtv1K',
        'SplyAdrBalNtv1M',
        'SplyAdrBalUSD1',
        'SplyAdrBalUSD10',
        'SplyAdrBalUSD100',
        'SplyAdrBalUSD100K',
        'SplyAdrBalUSD10K',
        'SplyAdrBalUSD10M',
        'SplyAdrBalUSD1K',
        'SplyAdrBalUSD1M',
        'SplyCur',
        'VelAct1yr',
        'VelActAdj1yr',
        'VelCur1yr',
        'VelCurAdj1yr',
    ]

    aggregated_coins = [
        'cbbtc',
        'usde',
    ]

    metrics_for_aggregated_coins = metrics_for_aggregated_coins_without_free_float + [
        'CapMVRVFF',
        'CapMrktFFUSD',
        'NVTAdjFF',
        'NVTAdjFF90',
        'SplyExpFut10yrCMBI',
        'SplyFF',
        'SplyFound',
        'SplyLost',
        'SplyRes',
        'SplyTeam',
    ]

    append_asset_metrics(aggregated_coins_without_supply, metrics_for_aggregated_coins_without_supply, '1d', dest)
    append_asset_metrics(aggregated_coins_without_free_float, metrics_for_aggregated_coins_without_free_float, '1d', dest)
    append_asset_metrics(aggregated_coins, metrics_for_aggregated_coins, '1d', dest)

    append_asset_metrics(evm_l2_base_layer_assets, evm_l2_base_layers_metrics, '1d', dest)

    default_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'avaxx',
                      'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd',
                      'comp', 'cro', 'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge',
                      'dot', 'drgn', 'elf', 'eos', 'eos_eth', 'etc', 'eth', 'eth_cl', 'eurc_eth', 'eurcv_eth',
                      'fdusd_eth', 'flow', 'flow_evm', 'flow_native', 'frax_eth', 'ftt', 'fun', 'fxc_eth', 'gas', 'gno',
                      'gnt', 'grin', 'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp', 'icx_eth', 'kcs', 'knc', 'ldo', 'lend', 'leo_eos',
                      'leo_eth', 'link', 'loom', 'lpt', 'lrc_eth', 'ltc', 'lusd_eth', 'maid', 'mana', 'matic_eth',
                      'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr',
                      'ppt', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth', 'ren', 'renbtc', 'rep', 'rev_eth', 'sai',
                      'sdai_eth', 'shib_eth', 'snt', 'snx', 'srm', 'steth_lido', 'susde_eth', 'sushi', 'swrv', 'trx',
                      'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth',
                      'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt_avaxc', 'usdt_eth',
                      'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xem', 'xlm',
                      'xmr', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx'] + erc_20_non_rebasing
    group_metrics = ['AssetCompletionTime', 'AssetEODCompletionTime']
    append_asset_metrics(default_assets, group_metrics, '1d', dest)

    group1_assets = ['1inch', 'aave', 'ada', 'ae', 'ae_eth', 'ageur_eth', 'aion_eth', 'alcx', 'algo', 'alpha', 'ant',
                     'ape', 'api3', 'atom', 'audio', 'audio_eth', 'avax', 'avaxc', 'avaxp', 'avaxx', 'badger', 'bal',
                     'bat', 'bch', 'bit', 'bnb', 'bnb_eth', 'bnt', 'bsv', 'btc', 'btg', 'btm', 'btm_eth', 'buidl_eth',
                     'busd', 'cel', 'comp', 'cro', 'crv', 'crvusd_eth', 'cvc', 'cvx', 'dai', 'dar', 'dash', 'dcr',
                     'dgb', 'doge', 'dot', 'drgn', 'elf', 'enj', 'ens', 'eos', 'eos_eth', 'etc', 'eth', 'eurc_eth',
                     'fdusd_eth', 'fil', 'flow', 'flow_evm', 'flow_native', 'frax_eth', 'ftt', 'fun', 'fxs', 'gala',
                     'gas', 'glm', 'gno', 'gnt', 'grin', 'grt', 'grt_eth', 'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp',
                     'icx', 'icx_eth', 'imx', 'kcs', 'knc', 'ldo', 'lend', 'leo_eos', 'leo_eth', 'link', 'loom', 'lpt',
                     'lrc', 'lrc_eth', 'lsk', 'ltc', 'luna', 'lusd_eth', 'maid', 'mana', 'matic_eth', 'mkr', 'mnt',
                     'mpl', 'mtl_metal', 'nas', 'nas_eth', 'neo', 'nexo', 'nftx', 'nmr', 'nxm', 'ogn', 'okb', 'omg',
                     'pax', 'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash', 'qnt', 'qtum',
                     'qtum_eth', 'rad', 'rad_eth', 'ren', 'renbtc', 'rep', 'rev_eth', 'rook', 'rsr', 'sai', 'sand',
                     'sdai_eth', 'shib', 'shib_eth', 'skl', 'snt', 'snx', 'spell', 'srm', 'stmx', 'storj', 'sui',
                     'susde_eth', 'sushi', 'swrv', 'toke', 'toke_eth', 'trx', 'trx_eth', 'tusd_eth', 'tusd_trx',
                     'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk',
                     'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet', 'vet_eth', 'vtc',
                     'waves', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'yfi',
                     'zec', 'zil', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group1_metrics = ['PriceUSD']
    append_asset_metrics(group1_assets, group1_metrics, '1d', dest)

    group2_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth',
                     'etc', 'eth', 'eurc_eth', 'eurcv_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'fxc_eth', 'gno',
                     'gnt', 'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth',
                     'link', 'loom', 'lpt', 'lrc_eth', 'ltc', 'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo',
                     'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash',
                     'qnt', 'qtum_eth', 'ren', 'renbtc', 'rev_eth', 'sai', 'sdai_eth', 'shib_eth', 'snt', 'snx',
                     'susde_eth', 'sushi', 'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni', 'usdc_avaxc',
                     'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt_avaxc',
                     'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut',
                     'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx'] + erc_20_non_rebasing
    group2_metrics = ['NVTAdjFF']
    append_asset_metrics(group2_assets, group2_metrics, '1d', dest)

    group3_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht',
                     'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'lrc_eth', 'ltc',
                     'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay',
                     'perp', 'pol_eth', 'poly', 'powr', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth', 'ren', 'renbtc',
                     'rev_eth', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdk', 'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc',
                     'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group3_metrics = ['NVTAdjFF90']
    append_asset_metrics(group3_assets, group3_metrics, '1d', dest)

    group4_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht',
                     'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'lrc_eth', 'ltc',
                     'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay',
                     'perp', 'pol_eth', 'poly', 'powr', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth', 'ren', 'renbtc',
                     'rev_eth', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc', 'wbtc',
                     'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group4_metrics = ['CapMVRVFF']
    append_asset_metrics(group4_assets, group4_metrics, '1d', dest)

    group5_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'avaxc', 'avaxp', 'bal', 'bat', 'bch', 'bnb',
                     'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro', 'crv', 'crvusd_eth',
                     'dash', 'dcr', 'dgb', 'doge', 'dot', 'etc', 'eth', 'eurc_eth', 'eurcv_eth', 'fdusd_eth',
                     'frax_eth', 'ftt', 'fxc_eth', 'gno', 'grin', 'ht', 'husd', 'icx_eth', 'ldo', 'link', 'lrc_eth',
                     'ltc', 'lusd_eth', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'pax', 'paxg', 'pol_eth', 'pyusd_eth',
                     'qtum_eth', 'ren', 'sdai_eth', 'shib_eth', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'xaut', 'xlm',
                     'xmr', 'xrp', 'xtz', 'yfi', 'zec', 'zil_eth', 'zrx']
    group5_metrics = ['SplyExpFut10yrCMBI']
    append_asset_metrics(group5_assets, group5_metrics, '1d', dest)

    group6_assets = ['1inch', 'aave', 'ada', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal', 'bat', 'bch',
                     'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro', 'crv',
                     'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'grin', 'gusd', 'hbtc',
                     'hedg', 'ht', 'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt',
                     'lrc_eth', 'ltc', 'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax',
                     'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth',
                     'ren', 'renbtc', 'rev_eth', 'sai', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi',
                     'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx',
                     'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni',
                     'usdt_trx', 'vet_eth', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xmr', 'xrp', 'xtz',
                     'xvg', 'yfi', 'zec', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group6_metrics = ['CapMrktFFUSD']
    append_asset_metrics(group6_assets, group6_metrics, '1d', dest)

    group7_assets = ['1inch', 'aave', 'ada', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal', 'bat', 'bch', 'bnb',
                     'bnb_eth', 'bsv', 'btc', 'btg', 'buidl_eth', 'busd', 'comp', 'cro', 'crv', 'crvusd_eth', 'cvc',
                     'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth', 'etc', 'eth', 'eurc_eth',
                     'eurcv_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'fxc_eth', 'gno', 'gnt', 'grin', 'gusd',
                     'hbtc', 'hedg', 'ht', 'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'ltc',
                     'lusd_eth', 'mana', 'matic_eth', 'mkr', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp',
                     'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash', 'qnt', 'ren', 'renbtc', 'rev_eth', 'sai',
                     'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth', 'tusd_trx',
                     'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk',
                     'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vtc', 'wbtc', 'weth',
                     'wnxm', 'wtc', 'xaut', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zrx'] + erc_20_non_rebasing
    group7_metrics = ['SplyFF']
    append_asset_metrics(group7_assets, group7_metrics, '1d', dest)

    group8_assets = ['1inch', 'aave', 'ae_eth', 'aion_eth', 'alpha', 'ant', 'avaxc', 'avaxp', 'avaxx', 'bal', 'bat',
                     'btc', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'crvusd_eth', 'dot', 'etc', 'eth', 'eurc_eth',
                     'fdusd_eth', 'flow_native', 'frax_eth', 'husd', 'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth',
                     'lusd_eth', 'maid', 'matic_eth', 'mkr', 'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth',
                     'qnt', 'qtum_eth', 'rev_eth', 'sdai_eth', 'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'yfi',
                     'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group8_metrics = ['TxTfrValAbUSD100MCnt', 'TxTfrValAbUSD100MUSD', 'TxTfrValAbUSD100kCnt', 'TxTfrValAbUSD100kUSD',
                      'TxTfrValAbUSD10MCnt', 'TxTfrValAbUSD10MUSD', 'TxTfrValAbUSD1MCnt', 'TxTfrValAbUSD1MUSD']
    append_asset_metrics(group8_assets, group8_metrics, '1d', dest)

    group9_assets = ['1inch', 'aave', 'aion_eth', 'alpha', 'ant', 'avaxc', 'avaxp', 'avaxx', 'bal', 'bat', 'btc',
                     'btm_eth', 'buidl_eth', 'busd', 'comp', 'crvusd_eth', 'dot', 'etc', 'eth', 'eurc_eth', 'fdusd_eth',
                     'flow_native', 'frax_eth', 'husd', 'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth', 'lusd_eth',
                     'maid', 'matic_eth', 'mkr', 'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth', 'qnt',
                     'qtum_eth', 'rev_eth', 'sdai_eth', 'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth', 'tusd_trx',
                     'uma', 'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth',
                     'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'yfi', 'zil_eth',
                     'zrx'] + erc_20_non_rebasing_with_reference_rate
    group9_metrics = ['TxTfrValAbUSD100MNtv', 'TxTfrValAbUSD100kNtv', 'TxTfrValAbUSD10MNtv', 'TxTfrValAbUSD1MNtv']
    append_asset_metrics(group9_assets, group9_metrics, '1d', dest)

    group10_assets = ['aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'avaxc', 'avaxp', 'btc', 'btm_eth', 'buidl_eth',
                      'busd', 'comp', 'crvusd_eth', 'dot', 'eth', 'eurc_eth', 'eurcv_eth', 'fdusd_eth', 'flow_native',
                      'frax_eth', 'fxc_eth', 'husd', 'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth', 'lusd_eth',
                      'maid', 'matic_eth', 'mkr', 'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth', 'qtum_eth',
                      'rev_eth', 'sdai_eth', 'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth', 'tusd_trx', 'uma',
                      'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth',
                      'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'xrp', 'yfi', 'zil_eth',
                      'zrx'] + erc_20_non_rebasing
    group10_metrics = ['AdrAct30dCnt', 'AdrAct7dCnt']
    append_asset_metrics(group10_assets, group10_metrics, '1d', dest)

    group11_assets = ['aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'avaxc', 'avaxp', 'btc', 'btm_eth', 'buidl_eth',
                      'busd', 'comp', 'crvusd_eth', 'dot', 'eth', 'eurc_eth', 'eurcv_eth', 'fdusd_eth', 'frax_eth',
                      'fxc_eth', 'husd', 'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth', 'lusd_eth', 'maid',
                      'matic_eth', 'mkr', 'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth', 'qtum_eth',
                      'rev_eth', 'sdai_eth', 'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth', 'tusd_trx', 'uma',
                      'uni', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth',
                      'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'xrp', 'yfi', 'zil_eth',
                      'zrx'] + erc_20_non_rebasing
    group11_metrics = ['AdrNewBalCnt', 'AdrNewCnt']
    append_asset_metrics(group11_assets, group11_metrics, '1d', dest)

    group12_assets = ['aave', 'ae_eth', 'aion_eth', 'algo', 'avaxc', 'avaxp', 'avaxx', 'btc', 'btm_eth', 'buidl_eth',
                      'busd', 'comp', 'crvusd_eth', 'dot', 'eth', 'eurc_eth', 'fdusd_eth', 'flow_native', 'frax_eth',
                      'husd', 'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth', 'lusd_eth', 'maid', 'matic_eth', 'mkr',
                      'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth', 'qtum_eth', 'rev_eth', 'sdai_eth',
                      'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth', 'tusd_trx', 'uma', 'uni',
                      'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth', 'usdt_avaxc',
                      'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'yfi', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group12_metrics = ['TxTfrValBelUSD100Cnt', 'TxTfrValBelUSD100USD', 'TxTfrValBelUSD10Cnt', 'TxTfrValBelUSD10USD',
                       'TxTfrValBelUSD10kCnt', 'TxTfrValBelUSD10kUSD', 'TxTfrValBelUSD1kCnt', 'TxTfrValBelUSD1kUSD',
                       'TxTfrValBelUSD20Cnt', 'TxTfrValBelUSD20USD', 'TxTfrValBelUSD500Cnt', 'TxTfrValBelUSD500USD',
                       'TxTfrValBelUSD50Cnt', 'TxTfrValBelUSD50USD', 'TxTfrValBelUSD5Cnt', 'TxTfrValBelUSD5USD']
    append_asset_metrics(group12_assets, group12_metrics, '1d', dest)

    group13_assets = ['aave', 'aion_eth', 'algo', 'avaxc', 'avaxp', 'avaxx', 'btc', 'btm_eth', 'buidl_eth', 'busd',
                      'comp', 'crvusd_eth', 'dot', 'eth', 'eurc_eth', 'fdusd_eth', 'flow_native', 'frax_eth', 'husd',
                      'icp', 'icx_eth', 'ldo', 'link', 'lpt', 'lrc_eth', 'lusd_eth', 'maid', 'matic_eth', 'mkr',
                      'nas_eth', 'pax', 'paxg', 'perp', 'pol_eth', 'pyusd_eth', 'qtum_eth', 'rev_eth', 'sdai_eth',
                      'shib_eth', 'snx', 'susde_eth', 'sushi', 'tusd_eth', 'tusd_trx', 'uma', 'uni',
                      'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth', 'usdt_avaxc',
                      'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'wbtc', 'yfi', 'zil_eth', 'zrx'] + erc_20_non_rebasing_with_reference_rate
    group13_metrics = ['TxTfrValBelUSD100Ntv', 'TxTfrValBelUSD10Ntv', 'TxTfrValBelUSD10kNtv', 'TxTfrValBelUSD1kNtv',
                       'TxTfrValBelUSD20Ntv', 'TxTfrValBelUSD500Ntv', 'TxTfrValBelUSD50Ntv', 'TxTfrValBelUSD5Ntv']
    append_asset_metrics(group13_assets, group13_metrics, '1d', dest)

    group14_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'eos', 'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo',
                      'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group14_metrics = ['BlkCnt', 'BlkHgt', 'BlkIntMean']
    append_asset_metrics(group14_assets, group14_metrics, '1d', dest)

    group15_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo', 'vtc',
                      'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group15_metrics = ['FeeMeanUSD', 'FeeTotUSD']
    append_asset_metrics(group15_assets, group15_metrics, '1d', dest)

    group16_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'vtc', 'xem',
                      'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group16_metrics = ['FeeMeanNtv', 'FeeTotNtv']
    append_asset_metrics(group16_assets, group16_metrics, '1d', dest)

    group17_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'etc', 'eth', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo', 'vtc', 'xem', 'xlm',
                      'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group17_metrics = ['FeeMedUSD']
    append_asset_metrics(group17_assets, group17_metrics, '1d', dest)

    group18_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'etc', 'eth', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'vtc', 'xem', 'xlm', 'xmr',
                      'xrp', 'xtz', 'xvg', 'zec']
    group18_metrics = ['FeeMedNtv']
    append_asset_metrics(group18_assets, group18_metrics, '1d', dest)

    group19_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'eth_cl', 'flow',
                      'flow_native', 'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group19_metrics = ['IssContNtv']
    append_asset_metrics(group19_assets, group19_metrics, '1d', dest)

    group20_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow',
                      'flow_native', 'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group20_metrics = ['IssContPctAnn', 'IssContPctDay']
    append_asset_metrics(group20_assets, group20_metrics, '1d', dest)

    group21_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow_native',
                      'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group21_metrics = ['IssContUSD']
    append_asset_metrics(group21_assets, group21_metrics, '1d', dest)

    group22_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'neo',
                      'vtc', 'xmr', 'xvg', 'zec']
    group22_metrics = ['BlkSizeByte', 'BlkSizeMeanByte', 'TxMeanByte', 'TxTfrMeanByte']
    append_asset_metrics(group22_assets, group22_metrics, '1d', dest)

    group23_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'neo',
                      'vtc', 'xvg', 'zec']
    group23_metrics = ['TxTfrValAdjByte']
    append_asset_metrics(group23_assets, group23_metrics, '1d', dest)

    group24_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'gas', 'grin', 'ltc', 'neo',
                      'vtc', 'xvg', 'zec']
    group24_metrics = ['UTXOCnt']
    append_asset_metrics(group24_assets, group24_metrics, '1d', dest)

    group25_assets = ['ae_eth', 'aion_eth', 'btm_eth', 'buidl_eth', 'crvusd_eth', 'etc', 'eth', 'eurc_eth', 'eurcv_eth',
                      'fdusd_eth', 'frax_eth', 'fxc_eth', 'icx_eth', 'ldo', 'lrc_eth', 'lusd_eth', 'nas_eth', 'pol_eth',
                      'pyusd_eth', 'qtum_eth', 'sdai_eth', 'shib_eth', 'susde_eth', 'tusd_eth', 'tusd_trx',
                      'usdc_avaxc', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdm_eth', 'usdt_avaxc', 'usdt_eth', 'vet_eth',
                      'xtz', 'zil_eth']
    group25_metrics = ['AdrActContCnt', 'ContBalCnt', 'ContCnt']
    append_asset_metrics(group25_assets, group25_metrics, '1d', dest)

    group26_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'eth_cl', 'flow_native',
                      'grin', 'ltc', 'vtc', 'xmr', 'xtz', 'xvg', 'zec']
    group26_metrics = ['RevNtv']
    append_asset_metrics(group26_assets, group26_metrics, '1d', dest)

    group27_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow_native', 'grin',
                      'ltc', 'vtc', 'xmr', 'xtz', 'xvg', 'zec']
    group27_metrics = ['RevUSD']
    append_asset_metrics(group27_assets, group27_metrics, '1d', dest)

    group28_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc', 'vtc',
                      'xmr', 'xtz', 'xvg', 'zec']
    group28_metrics = ['FeeRevPct', 'RevAllTimeUSD']
    append_asset_metrics(group28_assets, group28_metrics, '1d', dest)

    group29_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc', 'vtc',
                      'xmr', 'xvg', 'zec']
    group29_metrics = ['DiffLast', 'DiffMean']
    append_asset_metrics(group29_assets, group29_metrics, '1d', dest)

    group30_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'vtc', 'xmr',
                      'xvg', 'zec']
    group30_metrics = ['FeeByteMeanNtv']
    append_asset_metrics(group30_assets, group30_metrics, '1d', dest)

    group31_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'vtc', 'xtz',
                      'xvg', 'zec']
    group31_metrics = ['RCTC']
    append_asset_metrics(group31_assets, group31_metrics, '1d', dest)

    group32_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'ltc', 'vtc', 'xvg', 'zec']
    group32_metrics = ['SOPR', 'SOPROut', 'SplyRvv180d', 'SplyRvv1yr', 'SplyRvv2yr', 'SplyRvv30d', 'SplyRvv3yr',
                       'SplyRvv4yr', 'SplyRvv5yr', 'SplyRvv7d', 'SplyRvv90d', 'TxTfrValDayDst', 'TxTfrValDayDstMean']
    append_asset_metrics(group32_assets, group32_metrics, '1d', dest)

    group33_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'doge', 'etc', 'eth', 'ltc', 'vtc', 'xmr', 'zec']
    group33_metrics = ['HashRate', 'RevHash1yAvgNtv', 'RevHash1yAvgUSD', 'RevHashNtv', 'RevHashRateNtv',
                       'RevHashRateUSD', 'RevHashUSD']
    append_asset_metrics(group33_assets, group33_metrics, '1d', dest)

    group34_assets = ['bch', 'bsv', 'btc', 'btg', 'doge', 'etc', 'eth', 'grin', 'ltc', 'neo', 'xlm', 'xmr', 'xrp',
                      'xtz', 'zec']
    group34_metrics = ['CapFutExp10yrUSD', 'SplyExpFut10yr']
    append_asset_metrics(group34_assets, group34_metrics, '1d', dest)

    group35_assets = ['bch', 'bsv', 'btc', 'btg', 'doge', 'ltc', 'xvg', 'zec']
    group35_metrics = ['TxOpRetCnt']
    append_asset_metrics(group35_assets, group35_metrics, '1d', dest)

    group36_assets = ['btc']
    group36_metrics = ['CapMrktEstDomPct', 'FlowInBMXNtv', 'FlowInBMXUSD', 'FlowInHUONtv', 'FlowInHUOUSD', 'FlowMinerIn1HopAllBMXNtv',
                       'FlowMinerIn1HopAllBMXUSD', 'FlowMinerIn1HopAllHUONtv', 'FlowMinerIn1HopAllHUOUSD',
                       'FlowMinerOut1HopAllBMXNtv', 'FlowMinerOut1HopAllBMXUSD', 'FlowMinerOut1HopAllHUONtv',
                       'FlowMinerOut1HopAllHUOUSD', 'FlowNetBMXNtv', 'FlowNetBMXUSD', 'FlowOutBMXNtv', 'FlowOutBMXUSD',
                       'FlowOutHUONtv', 'FlowOutHUOUSD', 'FlowTfrInBMXCnt', 'FlowTfrInHUOCnt', 'FlowTfrOutBMXCnt',
                       'FlowTfrOutHUOCnt', 'HashRate30d', 'HashRate30dOtherHardware', 'HashRate30dOtherHardwarePct',
                       'HashRate30dS7', 'HashRate30dS7Pct', 'HashRate30dS9', 'HashRate30dS9Pct', 'MRI0HopAll30d',
                       'MRI1HopAll30d', 'SplyBMXNtv', 'SplyBMXUSD', 'SplyUTXOLoss', 'SplyUTXOProf', 'SplyWalBal1in100M',
                       'SplyWalBal1in100k', 'SplyWalBal1in10B', 'SplyWalBal1in10M', 'SplyWalBal1in10k',
                       'SplyWalBal1in1B', 'SplyWalBal1in1M', 'SplyWalBal1in1k', 'SplyWalBalNtv0.001',
                       'SplyWalBalNtv0.01', 'SplyWalBalNtv0.1', 'SplyWalBalNtv1', 'SplyWalBalNtv10', 'SplyWalBalNtv100',
                       'SplyWalBalNtv100k', 'SplyWalBalNtv10k', 'SplyWalBalNtv1M', 'SplyWalBalNtv1k', 'SplyWalBalUSD1',
                       'SplyWalBalUSD10', 'SplyWalBalUSD100', 'SplyWalBalUSD100k', 'SplyWalBalUSD10M',
                       'SplyWalBalUSD10k', 'SplyWalBalUSD1M', 'SplyWalBalUSD1k', 'UTXOAgeMean', 'UTXOAgeMed',
                       'UTXOAgeValMean', 'UTXODay', 'UTXOLossCnt', 'UTXOLossUnrealUSD', 'UTXOProfCnt',
                       'UTXOProfUnrealUSD', 'WalActCnt', 'WalActRecCnt', 'WalActSentCnt', 'WalBalCnt',
                       'FlowTfrInARKCnt', 'FlowTfrOutARKCnt', 'FlowInARKNtv', 'FlowNetARKNtv', 'FlowOutARKNtv',
                       'SplyARKNtv', 'FlowInARKUSD', 'FlowNetARKUSD', 'FlowOutARKUSD', 'SplyARKUSD', 'FlowTfrInBLKCnt',
                       'FlowTfrOutBLKCnt', 'FlowInBLKNtv', 'FlowNetBLKNtv', 'FlowOutBLKNtv', 'SplyBLKNtv', 'FlowInBLKUSD',
                       'FlowNetBLKUSD', 'FlowOutBLKUSD', 'SplyBLKUSD', 'FlowTfrInBWSCnt', 'FlowTfrOutBWSCnt', 'FlowInBWSNtv',
                       'FlowNetBWSNtv', 'FlowOutBWSNtv', 'SplyBWSNtv', 'FlowInBWSUSD', 'FlowNetBWSUSD', 'FlowOutBWSUSD', 'SplyBWSUSD',
                       'FlowTfrInGSCCnt', 'FlowTfrOutGSCCnt', 'FlowInGSCNtv', 'FlowNetGSCNtv', 'FlowOutGSCNtv', 'SplyGSCNtv',
                       'FlowInGSCUSD', 'FlowNetGSCUSD', 'FlowOutGSCUSD', 'SplyGSCUSD', 'FlowTfrInINVCnt', 'FlowTfrOutINVCnt',
                       'FlowInINVNtv', 'FlowNetINVNtv', 'FlowOutINVNtv', 'SplyINVNtv', 'FlowInINVUSD', 'FlowNetINVUSD',
                       'FlowOutINVUSD', 'SplyINVUSD', 'FlowTfrInTMPCnt', 'FlowTfrOutTMPCnt', 'FlowInTMPNtv', 'FlowNetTMPNtv',
                       'FlowOutTMPNtv', 'SplyTMPNtv', 'FlowInTMPUSD', 'FlowNetTMPUSD', 'FlowOutTMPUSD', 'SplyTMPUSD',
                       'FlowTfrInVANCnt', 'FlowTfrOutVANCnt', 'FlowInVANNtv', 'FlowNetVANNtv', 'FlowOutVANNtv', 'SplyVANNtv',
                       'FlowInVANUSD', 'FlowNetVANUSD', 'FlowOutVANUSD', 'SplyVANUSD', 'FlowTfrInVLKCnt', 'FlowTfrOutVLKCnt',
                       'FlowInVLKNtv', 'FlowNetVLKNtv', 'FlowOutVLKNtv', 'SplyVLKNtv', 'FlowInVLKUSD', 'FlowNetVLKUSD',
                       'FlowOutVLKUSD', 'SplyVLKUSD', 'FlowTfrInWDTCnt', 'FlowTfrOutWDTCnt', 'FlowInWDTNtv', 'FlowNetWDTNtv',
                       'FlowOutWDTNtv', 'SplyWDTNtv', 'FlowInWDTUSD', 'FlowNetWDTUSD', 'FlowOutWDTUSD', 'SplyWDTUSD',
                       'FlowTfrFromEtfCnt', 'FlowTfrToEtfCnt', 'TxEtfCnt', 'FlowInEtfNtv', 'FlowOutEtfNtv', 'SplyEtfNtv',
                       'FlowInEtfUSD', 'FlowOutEtfUSD', 'SplyEtfUSD', 'FlowTfrFromEtfInclCnt', 'FlowTfrToEtfInclCnt', 'FlowInEtfInclNtv',
                       'FlowOutEtfInclNtv', 'FlowInEtfInclUSD', 'FlowOutEtfInclUSD', 'SOPRLth30d', 'SOPRSth30d', 'SOPRLthOut30d',
                       'SOPRSthOut30d', 'SOPRLth90d', 'SOPRSth90d', 'SOPRLthOut90d', 'SOPRSthOut90d', 'SOPRLth155d', 'SOPRSth155d',
                       'SOPRLthOut155d', 'SOPRSthOut155d', 'SOPRLth1y', 'SOPRSth1y', 'SOPRLthOut1y', 'SOPRSthOut1y',
                       'SOPRLth5y', 'SOPRSth5y', 'SOPRLthOut5y', 'SOPRSthOut5y']
    append_asset_metrics(group36_assets, group36_metrics, '1d', dest)

    group37_assets = ['btc', 'etc', 'eth', 'xmr']
    group37_metrics = ['PuellMulCont', 'PuellMulRev', 'PuellMulTot']
    append_asset_metrics(group37_assets, group37_metrics, '1d', dest)

    group38_assets = ['btc', 'eth']
    group38_metrics = ['FlowInBFXNtv', 'FlowInBFXUSD', 'FlowInBITNtv', 'FlowInBITUSD', 'FlowInBNBNtv', 'FlowInBNBUSD',
                       'FlowInBSPNtv', 'FlowInBSPUSD', 'FlowInBTXNtv', 'FlowInBTXUSD', 'FlowInCRONtv', 'FlowInCROUSD',
                       'FlowInDERNtv', 'FlowInDERUSD', 'FlowInExInclNtv', 'FlowInExInclUSD', 'FlowInExNtv',
                       'FlowInExUSD', 'FlowInGEMNtv', 'FlowInGEMUSD', 'FlowInGIONtv', 'FlowInGIOUSD', 'FlowInHBTNtv',
                       'FlowInHBTUSD', 'FlowInKCNNtv', 'FlowInKCNUSD', 'FlowInKORNtv', 'FlowInKORUSD', 'FlowInKRKNtv',
                       'FlowInKRKUSD', 'FlowInMXCNtv', 'FlowInMXCUSD', 'FlowInNBXNtv', 'FlowInNBXUSD', 'FlowInOKXNtv',
                       'FlowInOKXUSD', 'FlowInPOLNtv', 'FlowInPOLUSD', 'FlowInSBGNtv', 'FlowInSBGUSD',
                       'FlowMinerIn0HopAllExNtv', 'FlowMinerIn0HopAllExUSD', 'FlowMinerIn0HopAllNtv',
                       'FlowMinerIn0HopAllUSD', 'FlowMinerIn1HopAllBFXNtv', 'FlowMinerIn1HopAllBFXUSD',
                       'FlowMinerIn1HopAllBITNtv', 'FlowMinerIn1HopAllBITUSD', 'FlowMinerIn1HopAllBNBNtv',
                       'FlowMinerIn1HopAllBNBUSD', 'FlowMinerIn1HopAllBSPNtv', 'FlowMinerIn1HopAllBSPUSD',
                       'FlowMinerIn1HopAllBTXNtv', 'FlowMinerIn1HopAllBTXUSD', 'FlowMinerIn1HopAllCRONtv',
                       'FlowMinerIn1HopAllCROUSD', 'FlowMinerIn1HopAllDERNtv', 'FlowMinerIn1HopAllDERUSD',
                       'FlowMinerIn1HopAllExNtv', 'FlowMinerIn1HopAllExUSD', 'FlowMinerIn1HopAllGEMNtv',
                       'FlowMinerIn1HopAllGEMUSD', 'FlowMinerIn1HopAllGIONtv', 'FlowMinerIn1HopAllGIOUSD',
                       'FlowMinerIn1HopAllHBTNtv', 'FlowMinerIn1HopAllHBTUSD', 'FlowMinerIn1HopAllKCNNtv',
                       'FlowMinerIn1HopAllKCNUSD', 'FlowMinerIn1HopAllKORNtv', 'FlowMinerIn1HopAllKORUSD',
                       'FlowMinerIn1HopAllKRKNtv', 'FlowMinerIn1HopAllKRKUSD', 'FlowMinerIn1HopAllMXCNtv',
                       'FlowMinerIn1HopAllMXCUSD', 'FlowMinerIn1HopAllNBXNtv', 'FlowMinerIn1HopAllNBXUSD',
                       'FlowMinerIn1HopAllNtv', 'FlowMinerIn1HopAllOKXNtv', 'FlowMinerIn1HopAllOKXUSD',
                       'FlowMinerIn1HopAllPOLNtv', 'FlowMinerIn1HopAllPOLUSD', 'FlowMinerIn1HopAllSBGNtv',
                       'FlowMinerIn1HopAllSBGUSD', 'FlowMinerIn1HopAllUSD', 'FlowMinerNet0HopAllNtv',
                       'FlowMinerNet0HopAllUSD', 'FlowMinerNet1HopAllNtv', 'FlowMinerNet1HopAllUSD',
                       'FlowMinerOut0HopAllExNtv', 'FlowMinerOut0HopAllExUSD', 'FlowMinerOut0HopAllNtv',
                       'FlowMinerOut0HopAllUSD', 'FlowMinerOut1HopAllBFXNtv', 'FlowMinerOut1HopAllBFXUSD',
                       'FlowMinerOut1HopAllBITNtv', 'FlowMinerOut1HopAllBITUSD', 'FlowMinerOut1HopAllBNBNtv',
                       'FlowMinerOut1HopAllBNBUSD', 'FlowMinerOut1HopAllBSPNtv', 'FlowMinerOut1HopAllBSPUSD',
                       'FlowMinerOut1HopAllBTXNtv', 'FlowMinerOut1HopAllBTXUSD', 'FlowMinerOut1HopAllCRONtv',
                       'FlowMinerOut1HopAllCROUSD', 'FlowMinerOut1HopAllDERNtv', 'FlowMinerOut1HopAllDERUSD',
                       'FlowMinerOut1HopAllExNtv', 'FlowMinerOut1HopAllExUSD', 'FlowMinerOut1HopAllGEMNtv',
                       'FlowMinerOut1HopAllGEMUSD', 'FlowMinerOut1HopAllGIONtv', 'FlowMinerOut1HopAllGIOUSD',
                       'FlowMinerOut1HopAllHBTNtv', 'FlowMinerOut1HopAllHBTUSD', 'FlowMinerOut1HopAllKCNNtv',
                       'FlowMinerOut1HopAllKCNUSD', 'FlowMinerOut1HopAllKORNtv', 'FlowMinerOut1HopAllKORUSD',
                       'FlowMinerOut1HopAllKRKNtv', 'FlowMinerOut1HopAllKRKUSD', 'FlowMinerOut1HopAllMXCNtv',
                       'FlowMinerOut1HopAllMXCUSD', 'FlowMinerOut1HopAllNBXNtv', 'FlowMinerOut1HopAllNBXUSD',
                       'FlowMinerOut1HopAllNtv', 'FlowMinerOut1HopAllOKXNtv', 'FlowMinerOut1HopAllOKXUSD',
                       'FlowMinerOut1HopAllPOLNtv', 'FlowMinerOut1HopAllPOLUSD', 'FlowMinerOut1HopAllSBGNtv',
                       'FlowMinerOut1HopAllSBGUSD', 'FlowMinerOut1HopAllUSD', 'FlowNetBFXNtv', 'FlowNetBFXUSD',
                       'FlowNetBITNtv', 'FlowNetBITUSD', 'FlowNetBNBNtv', 'FlowNetBNBUSD', 'FlowNetBSPNtv',
                       'FlowNetBSPUSD', 'FlowNetBTXNtv', 'FlowNetBTXUSD', 'FlowNetCRONtv', 'FlowNetCROUSD',
                       'FlowNetDERNtv', 'FlowNetDERUSD', 'FlowNetGEMNtv', 'FlowNetGEMUSD', 'FlowNetGIONtv',
                       'FlowNetGIOUSD', 'FlowNetHBTNtv', 'FlowNetHBTUSD', 'FlowNetHUONtv', 'FlowNetHUOUSD',
                       'FlowNetKCNNtv', 'FlowNetKCNUSD', 'FlowNetKORNtv', 'FlowNetKORUSD', 'FlowNetKRKNtv',
                       'FlowNetKRKUSD', 'FlowNetMXCNtv', 'FlowNetMXCUSD', 'FlowNetNBXNtv', 'FlowNetNBXUSD',
                       'FlowNetOKXNtv', 'FlowNetOKXUSD', 'FlowNetPOLNtv', 'FlowNetPOLUSD', 'FlowNetSBGNtv',
                       'FlowNetSBGUSD', 'FlowOutBFXNtv', 'FlowOutBFXUSD', 'FlowOutBITNtv', 'FlowOutBITUSD',
                       'FlowOutBNBNtv', 'FlowOutBNBUSD', 'FlowOutBSPNtv', 'FlowOutBSPUSD', 'FlowOutBTXNtv',
                       'FlowOutBTXUSD', 'FlowOutCRONtv', 'FlowOutCROUSD', 'FlowOutDERNtv', 'FlowOutDERUSD',
                       'FlowOutExInclNtv', 'FlowOutExInclUSD', 'FlowOutExNtv', 'FlowOutExUSD', 'FlowOutGEMNtv',
                       'FlowOutGEMUSD', 'FlowOutGIONtv', 'FlowOutGIOUSD', 'FlowOutHBTNtv', 'FlowOutHBTUSD',
                       'FlowOutKCNNtv', 'FlowOutKCNUSD', 'FlowOutKORNtv', 'FlowOutKORUSD', 'FlowOutKRKNtv',
                       'FlowOutKRKUSD', 'FlowOutMXCNtv', 'FlowOutMXCUSD', 'FlowOutNBXNtv', 'FlowOutNBXUSD',
                       'FlowOutOKXNtv', 'FlowOutOKXUSD', 'FlowOutPOLNtv', 'FlowOutPOLUSD', 'FlowOutSBGNtv',
                       'FlowOutSBGUSD', 'FlowTfrFromExCnt', 'FlowTfrFromExInclCnt', 'FlowTfrInBFXCnt',
                       'FlowTfrInBITCnt', 'FlowTfrInBNBCnt', 'FlowTfrInBSPCnt', 'FlowTfrInBTXCnt', 'FlowTfrInCROCnt',
                       'FlowTfrInDERCnt', 'FlowTfrInGEMCnt', 'FlowTfrInGIOCnt', 'FlowTfrInHBTCnt', 'FlowTfrInKCNCnt',
                       'FlowTfrInKORCnt', 'FlowTfrInKRKCnt', 'FlowTfrInMXCCnt', 'FlowTfrInNBXCnt', 'FlowTfrInOKXCnt',
                       'FlowTfrInPOLCnt', 'FlowTfrInSBGCnt', 'FlowTfrOutBFXCnt', 'FlowTfrOutBITCnt', 'FlowTfrOutBNBCnt',
                       'FlowTfrOutBSPCnt', 'FlowTfrOutBTXCnt', 'FlowTfrOutCROCnt', 'FlowTfrOutDERCnt',
                       'FlowTfrOutGEMCnt', 'FlowTfrOutGIOCnt', 'FlowTfrOutHBTCnt', 'FlowTfrOutKCNCnt',
                       'FlowTfrOutKORCnt', 'FlowTfrOutKRKCnt', 'FlowTfrOutMXCCnt', 'FlowTfrOutNBXCnt',
                       'FlowTfrOutOKXCnt', 'FlowTfrOutPOLCnt', 'FlowTfrOutSBGCnt', 'FlowTfrToExCnt',
                       'FlowTfrToExInclCnt', 'MCRC', 'MCTC', 'MOMR', 'SplyBFXNtv', 'SplyBFXUSD', 'SplyBITNtv',
                       'SplyBITUSD', 'SplyBNBNtv', 'SplyBNBUSD', 'SplyBSPNtv', 'SplyBSPUSD', 'SplyBTXNtv', 'SplyBTXUSD',
                       'SplyCRONtv', 'SplyCROUSD', 'SplyDERNtv', 'SplyDERUSD', 'SplyExNtv', 'SplyExUSD', 'SplyGEMNtv',
                       'SplyGEMUSD', 'SplyGIONtv', 'SplyGIOUSD', 'SplyHBTNtv', 'SplyHBTUSD', 'SplyHUONtv', 'SplyHUOUSD',
                       'SplyKCNNtv', 'SplyKCNUSD', 'SplyKORNtv', 'SplyKORUSD', 'SplyKRKNtv', 'SplyKRKUSD', 'SplyMXCNtv',
                       'SplyMXCUSD', 'SplyMiner0HopAllNtv', 'SplyMiner0HopAllUSD', 'SplyMiner1HopAllNtv',
                       'SplyMiner1HopAllUSD', 'SplyNBXNtv', 'SplyNBXUSD', 'SplyOKXNtv', 'SplyOKXUSD', 'SplyPOLNtv',
                       'SplyPOLUSD', 'SplySBGNtv', 'SplySBGUSD', 'TxExCnt', 'FlowInCBSNtv', 'FlowInCBSUSD',
                       'FlowMinerIn1HopAllCBSNtv', 'FlowMinerIn1HopAllCBSUSD', 'FlowMinerOut1HopAllCBSNtv',
                       'FlowMinerOut1HopAllCBSUSD', 'FlowNetCBSNtv', 'FlowNetCBSUSD', 'FlowOutCBSNtv',
                       'FlowOutCBSUSD', 'FlowTfrInCBSCnt', 'FlowTfrOutCBSCnt', 'SplyCBSNtv', 'SplyCBSUSD',
                       'NUPL', 'CapMVRVZ']
    append_asset_metrics(group38_assets, group38_metrics, '1d', dest)

    group39_assets = ['btc', 'ltc']
    group39_metrics = ['BlkWghtMean', 'BlkWghtTot', 'FeeWghtMeanNtv']
    append_asset_metrics(group39_assets, group39_metrics, '1d', dest)

    group40_assets = ['etc', 'eth']
    group40_metrics = ['BlkUncCnt', 'BlkUncRevPct', 'BlkUncRwd', 'BlkUncRwdUSD', 'ContERC1155Cnt', 'ContERC20Cnt',
                       'ContERC721Cnt', 'GasLmtBlk', 'GasLmtBlkMean', 'GasLmtTx', 'GasLmtTxMean', 'TxERC1155Cnt',
                       'TxERC20Cnt', 'TxERC721Cnt', 'TxTfrERC1155Cnt', 'TxTfrERC20Cnt', 'TxTfrERC721Cnt', 'TxTfrTknCnt',
                       'TxTknCnt']
    append_asset_metrics(group40_assets, group40_metrics, '1d', dest)

    group41_assets = ['etc', 'eth', 'flow_evm']
    group41_metrics = ['GasUsedTx', 'GasUsedTxMean']
    append_asset_metrics(group41_assets, group41_metrics, '1d', dest)

    group42_assets = ['etc', 'eth', 'flow_native', 'xtz']
    group42_metrics = ['TxContCreatCnt', 'TxContDestCnt']
    append_asset_metrics(group42_assets, group42_metrics, '1d', dest)

    group43_assets = ['etc', 'eth', 'xtz']
    group43_metrics = ['TxContCallCnt', 'TxContCallSuccCnt']
    append_asset_metrics(group43_assets, group43_metrics, '1d', dest)

    group44_assets = ['eth']
    group44_metrics = ['AdrActBlobCnt', 'AdrActBlobContRecCnt', 'AdrActBlobRecCnt', 'AdrActBlobSendCnt',
                       'BlobARBSizeByte', 'BlobBASESizeByte', 'BlobCnt', 'BlobContCnt', 'BlobInscrCnt',
                       'BlobL2SizeByte', 'BlobMeanCnt', 'BlobOPSizeByte', 'BlobSizeAllTimeByte', 'BlobSizeByte',
                       'BlobUniqCnt', 'FeeBlobARBMeanNtv', 'FeeBlobARBMeanUSD', 'FeeBlobARBTotNtv', 'FeeBlobARBTotUSD',
                       'FeeBlobBASEMeanNtv', 'FeeBlobBASEMeanUSD', 'FeeBlobBASETotNtv', 'FeeBlobBASETotUSD',
                       'FeeBlobByteMeanNtv', 'FeeBlobByteMeanUSD', 'FeeBlobL2MeanNtv', 'FeeBlobL2MeanUSD',
                       'FeeBlobL2TotNtv', 'FeeBlobL2TotUSD', 'FeeBlobMeanNtv', 'FeeBlobMeanUSD', 'FeeBlobMedNtv',
                       'FeeBlobMedUSD', 'FeeBlobOPMeanNtv', 'FeeBlobOPMeanUSD', 'FeeBlobOPTotNtv', 'FeeBlobOPTotUSD',
                       'FeeBlobTotNtv', 'FeeBlobTotUSD', 'FeeBlobTxMeanNtv', 'FeeBlobTxMeanUSD', 'FeePrioMeanNtv',
                       'FeePrioMeanUSD', 'FeePrioMedNtv', 'FeePrioMedUSD', 'FeePrioTotNtv', 'FeePrioTotUSD',
                       'FlowToCLCont', 'FlowToLidoCont', 'SenderCntCLCont', 'SenderTotCLCont', 'SplyBurntNtv',
                       'SplyBurntUSD', 'SplyCLCont', 'SplyLidoCont', 'StakingAPYReal', 'TxBlobARBCnt', 'TxBlobBASECnt',
                       'TxBlobCnt', 'TxBlobContCnt', 'TxBlobInscrCnt', 'TxBlobL2Cnt', 'TxBlobMeanCnt', 'TxBlobOPCnt',
                       'TxEIP1559Cnt', 'ValidatorAPYReal']
    append_asset_metrics(group44_assets, group44_metrics, '1d', dest)

    group45_assets = ['eth', 'eth_cl']
    group45_metrics = ['StakingAPRNominal', 'StakingAPYNominal', 'ValidatorAPRNominal', 'ValidatorAPYNominal']
    append_asset_metrics(group45_assets, group45_metrics, '1d', dest)

    group46_assets = ['eth', 'flow_evm']
    group46_metrics = ['GasBaseBlkMean']
    append_asset_metrics(group46_assets, group46_metrics, '1d', dest)

    group47_assets = ['eth', 'sol']
    group47_metrics = ['InfPct', 'InfPct180dAvg', 'InfPct1yAvg', 'InfPct30dAvg', 'InfPct90dAvg', 'InfPctAnn',
                       'InfPctAnn180dAvg', 'InfPctAnn1yAvg', 'InfPctAnn30dAvg', 'InfPctAnn90dAvg']
    append_asset_metrics(group47_assets, group47_metrics, '1d', dest)

    group48_assets = ['eth_cl']
    group48_metrics = ['IssFullParticipation', 'PenaltyNtv', 'SlashedNtv', 'SplyActStkedNtv', 'SplyStkedActMeanNtv',
                       'SplyStkedNtv', 'SplyTotStkedNtv', 'StkConsolidationCnt', 'StkConsolidationTotNtv', 'StkDepositTotNtv', 'StkPartRateMean',
                       'StkWithdrawTotNtv', 'ValidatorActExtCnt', 'ValidatorActOngCnt', 'ValidatorActSlhCnt',
                       'ValidatorAddCnt1d', 'ValidatorCnt', 'ValidatorConsolidationCnt', 'ValidatorConsolidationRate',
                       'ValidatorConsolidationTotRate','ValidatorExtSlhCnt', 'ValidatorExtUnslhCnt',
                       'ValidatorPndInitCnt', 'ValidatorPndQedCnt', 'ValidatorRemCnt1d']
    append_asset_metrics(group48_assets, group48_metrics, '1d', dest)

    group49_assets = ['zec']
    group49_metrics = ['SplyShld', 'TxShldCnt', 'TxShldFullCnt']
    append_asset_metrics(group49_assets, group49_metrics, '1d', dest)

    group50_assets = ['eth']
    group50_metrics = ['SplyCurEL']
    append_asset_metrics(group50_assets, group50_metrics, '1d', dest)

    group51_excluded_assets = [
        'ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv',
        'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'eos',
        'eth_cl', 'flow', 'flow_evm', 'flow_native', 'fxc_eth',
        'gas', 'grin', 'icp', 'leo_eos', 'ltc', 'maid', 'neo', 'sai',
        'steth_lido', 'trx', 'usdt_omni', 'usdt_trx', 'vtc',
        'xem', 'xlm', 'xmr', 'xrp', 'xvg', 'zec'
    ] + erc_20_non_rebasing
    group51_assets = sorted(set(default_assets) - set(group51_excluded_assets))
    group51_metrics = ['SplyContUSD', 'TxTfrValContCallUSD']
    append_asset_metrics(group51_assets, group51_metrics, '1d', dest)

    group52_exclude_assets = [
        'ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv',
        'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'eos',
        'eth_cl', 'flow', 'flow_evm', 'flow_native', 'gas', 'grin',
        'icp', 'leo_eos', 'ltc', 'maid', 'neo', 'steth_lido', 'trx',
        'usdt_omni', 'usdt_trx', 'vtc', 'xem', 'xlm', 'xmr',
        'xrp', 'xvg', 'zec'
    ] + erc_20_non_rebasing
    group52_assets = sorted(set(default_assets) - set(group52_exclude_assets))
    group52_metrics = ['SplyContNtv', 'TxContCnt', 'TxTfrValContCallNtv']
    append_asset_metrics(group52_assets, group52_metrics, '1d', dest)

    group53_exclude_assets = [
        'avaxx', 'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native',
        'fxc_eth', 'grin', 'kcs', 'leo_eos', 'steth_lido', 'trx',
        'xem', 'xmr'
    ] + erc_20_non_rebasing_without_reference_rate
    group53_assets = sorted(set(default_assets) - set(group53_exclude_assets))
    group53_metrics = ['CapAct1yrUSD', 'CapMVRVCur', 'CapRealUSD', 'RVTAdj', 'RVTAdj90']
    append_asset_metrics(group53_assets, group53_metrics, '1d', dest)

    group54_exclude_assets = [
        'avaxx', 'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native',
        'fxc_eth', 'grin', 'kcs', 'steth_lido', 'trx', 'xmr'
    ] + erc_20_non_rebasing_without_reference_rate
    group54_assets = sorted(set(default_assets) - set(group54_exclude_assets))
    group54_metrics = ['NVTAdj90']
    append_asset_metrics(group54_assets, group54_metrics, '1d', dest)

    group55_exclude_assets = [
        'avaxx', 'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native',
        'grin', 'leo_eos', 'steth_lido', 'trx', 'xem', 'xmr'
    ]
    group55_assets = sorted(set(default_assets) - set(group55_exclude_assets))
    group55_metrics = ['SplyAct10yr', 'SplyAct180d', 'SplyAct1d', 'SplyAct1yr', 'SplyAct2yr', 'SplyAct30d',
                       'SplyAct3yr', 'SplyAct4yr', 'SplyAct5yr', 'SplyAct7d', 'SplyAct90d', 'SplyActEver',
                       'SplyActPct1yr', 'VelAct1yr', 'VelActAdj1yr']
    append_asset_metrics(group55_assets, group55_metrics, '1d', dest)

    group56_exclude_assets = [
        'avaxx', 'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native',
        'grin', 'trx', 'xem', 'xmr'
    ]
    group56_assets = sorted(set(default_assets) - set(group56_exclude_assets))
    group56_metrics = ['VelCur1yr', 'VelCurAdj1yr']
    append_asset_metrics(group56_assets, group56_metrics, '1d', dest)

    group57_exclude_assets = [
        'dcr', 'eos', 'eth_cl', 'flow', 'flow_evm', 'fxc_eth', 'kcs',
        'steth_lido', 'xrp'
    ] + erc_20_non_rebasing_without_reference_rate
    group57_assets = sorted(set(default_assets) - set(group57_exclude_assets))
    group57_metrics = ['IssTotUSD']
    append_asset_metrics(group57_assets, group57_metrics, '1d', dest)

    group58_exclude_assets = [
        'dcr', 'eos', 'flow_evm', 'steth_lido', 'xrp'
    ]
    group58_assets = sorted(set(default_assets) - set(group58_exclude_assets))
    group58_metrics = ['IssTotNtv']
    append_asset_metrics(group58_assets, group58_metrics, '1d', dest)

    group59_exclude_assets = [
        'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native',
        'fxc_eth', 'grin', 'kcs', 'leo_eos', 'steth_lido', 'trx',
        'xem', 'xmr'
    ] + erc_20_non_rebasing_without_reference_rate
    group59_assets = sorted(set(default_assets) - set(group59_exclude_assets))
    group59_metrics = ['AdrBalUSD100Cnt', 'AdrBalUSD100KCnt', 'AdrBalUSD10Cnt', 'AdrBalUSD10KCnt', 'AdrBalUSD10MCnt',
                       'AdrBalUSD1Cnt', 'AdrBalUSD1KCnt', 'AdrBalUSD1MCnt', 'SplyAdrBalUSD1', 'SplyAdrBalUSD10',
                       'SplyAdrBalUSD100', 'SplyAdrBalUSD100K', 'SplyAdrBalUSD10K', 'SplyAdrBalUSD10M',
                       'SplyAdrBalUSD1K', 'SplyAdrBalUSD1M']
    append_asset_metrics(group59_assets, group59_metrics, '1d', dest)

    group60_exclude_assets = [
        'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native', 'grin',
        'leo_eos', 'steth_lido', 'trx', 'xem', 'xmr'
    ]
    group60_assets = sorted(set(default_assets) - set(group60_exclude_assets))
    group60_metrics = ['AdrBal1in100KCnt', 'AdrBal1in100MCnt', 'AdrBal1in10BCnt', 'AdrBal1in10KCnt', 'AdrBal1in10MCnt',
                       'AdrBal1in1BCnt', 'AdrBal1in1KCnt', 'AdrBal1in1MCnt', 'AdrBalNtv0.001Cnt', 'AdrBalNtv0.01Cnt',
                       'AdrBalNtv0.1Cnt', 'AdrBalNtv100Cnt', 'AdrBalNtv100KCnt', 'AdrBalNtv10Cnt', 'AdrBalNtv10KCnt',
                       'AdrBalNtv1Cnt', 'AdrBalNtv1KCnt', 'AdrBalNtv1MCnt', 'NDF', 'SER', 'SplyAdrBal1in100K',
                       'SplyAdrBal1in100M', 'SplyAdrBal1in10B', 'SplyAdrBal1in10K', 'SplyAdrBal1in10M',
                       'SplyAdrBal1in1B', 'SplyAdrBal1in1K', 'SplyAdrBal1in1M', 'SplyAdrBalNtv0.001',
                       'SplyAdrBalNtv0.01', 'SplyAdrBalNtv0.1', 'SplyAdrBalNtv1', 'SplyAdrBalNtv10', 'SplyAdrBalNtv100',
                       'SplyAdrBalNtv100K', 'SplyAdrBalNtv10K', 'SplyAdrBalNtv1K', 'SplyAdrBalNtv1M', 'SplyAdrTop100',
                       'SplyAdrTop10Pct', 'SplyAdrTop1Pct']
    append_asset_metrics(group60_assets, group60_metrics, '1d', dest)

    group61_exclude_assets = [
        'eos', 'eth_cl', 'flow', 'flow_evm', 'flow_native', 'grin',
        'trx', 'xmr'
    ]
    group61_assets = sorted(set(default_assets) - set(group61_exclude_assets))
    group61_metrics = ['NVTAdj']
    append_asset_metrics(group61_assets, group61_metrics, '1d', dest)

    group62_exclude_assets = [
        'eos', 'eth_cl', 'flow', 'flow_evm', 'grin', 'trx', 'xem',
        'xmr'
    ]
    group62_assets = sorted(set(default_assets) - set(group62_exclude_assets))
    group62_metrics = ['AdrActRecCnt', 'AdrActSentCnt']
    append_asset_metrics(group62_assets, group62_metrics, '1d', dest)

    group63_exclude_assets = [
        'eos', 'eth_cl', 'flow_evm', 'fxc_eth', 'kcs', 'steth_lido',
        'vet'
    ] + erc_20_non_rebasing_without_reference_rate
    group63_assets = sorted(set(default_assets) - set(group63_exclude_assets))
    group63_metrics = ['CapMrktCurUSD']
    append_asset_metrics(group63_assets, group63_metrics, '1d', dest)

    group64_exclude_assets = [
        'eos', 'flow', 'flow_evm', 'flow_native', 'grin', 'leo_eos',
        'steth_lido', 'trx', 'xem', 'xmr'
    ]
    group64_assets = sorted(set(default_assets) - set(group64_exclude_assets))
    group64_metrics = ['AdrBalCnt']
    append_asset_metrics(group64_assets, group64_metrics, '1d', dest)

    group65_exclude_assets = ['eos', 'flow_evm']
    group65_assets = sorted(set(default_assets) - set(group65_exclude_assets))
    group65_metrics = ['SplyCur']
    append_asset_metrics(group65_assets, group65_metrics, '1d', dest)

    group66_exclude_assets = ['eth_cl']
    group66_assets = sorted(set(default_assets) - set(group66_exclude_assets))
    group66_metrics = ['TxCnt', 'TxCntSec']
    append_asset_metrics(group66_assets, group66_metrics, '1d', dest)

    group67_exclude_assets = ['eth_cl', 'flow', 'flow_evm']
    group67_assets = sorted(set(default_assets) - set(group67_exclude_assets))
    group67_metrics = ['TxTfrCnt']
    append_asset_metrics(group67_assets, group67_metrics, '1d', dest)

    group68_exclude_assets = [
        'eth_cl', 'flow', 'flow_evm', 'fxc_eth', 'grin', 'kcs',
        'steth_lido', 'xmr'
    ] + erc_20_non_rebasing_without_reference_rate
    group68_assets = sorted(set(default_assets) - set(group68_exclude_assets))
    group68_metrics = ['TxTfrValAdjUSD', 'TxTfrValMeanUSD', 'TxTfrValMedUSD', 'TxTfrValUSD']
    append_asset_metrics(group68_assets, group68_metrics, '1d', dest)

    group69_exclude_assets = ['eth_cl', 'flow', 'flow_evm', 'grin', 'xmr']
    group69_assets = sorted(set(default_assets) - set(group69_exclude_assets))
    group69_metrics = ['AdrActCnt', 'TxTfrValAdjNtv', 'TxTfrValMeanNtv', 'TxTfrValMedNtv', 'TxTfrValNtv']
    append_asset_metrics(group69_assets, group69_metrics, '1d', dest)

    group70_exclude_assets = ['eth_cl', 'fxc_eth', 'kcs', 'steth_lido', 'vet'] + erc_20_non_rebasing_without_reference_rate
    group70_assets = sorted(set(default_assets) - set(group70_exclude_assets))
    group70_metrics = ['PriceBTC', 'ROI1yr', 'ROI30d', 'VtyDayRet180d', 'VtyDayRet30d', 'VtyDayRet60d']
    append_asset_metrics(group70_assets, group70_metrics, '1d', dest)
