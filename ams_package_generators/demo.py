def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    assets_1d = ["btc"]
    metrics_1d = ["AdrActSentCnt", "AdrBal1in1BCnt", "CapRealUSD", "IssContPctAnn", "RevAllTimeUSD", "NVTAdj90",
                  "SplyAct1yr", "TxTfrValAdjUSD", "UTXOCnt", "VelActAdj1yr", "PriceUSD"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)
