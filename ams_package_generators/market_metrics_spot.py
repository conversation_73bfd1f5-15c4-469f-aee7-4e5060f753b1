def package_type():
    return "composite"

def package_script():
    from ams_data_generator import TimeRestrictions
    return [
        # todo: filter by spot
        "pair_metrics",
        # todo: filter by spot
        f"exchange_metrics {{'exchange', 'time_restriction': '{TimeRestrictions.NONE.value}'}}",
        # todo: filter by spot
        "exchange_asset_metrics",
        # todo: filter by spot
        "market_metrics",
        "asset_metrics {'asset', 'metric': 'volume_reported_spot_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_spot_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_trusted_spot_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_trusted_spot_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_realized_usd_rolling_24h', 'frequency': '10m,1h,1d'}",
        "asset_metrics {'asset', 'metric': 'volatility_realized_usd_rolling_7d', 'frequency': '10m,1h,1d'}",
        "asset_metrics {'asset', 'metric': 'volatility_realized_usd_rolling_30d', 'frequency': '10m,1h,1d'}",
    ]
