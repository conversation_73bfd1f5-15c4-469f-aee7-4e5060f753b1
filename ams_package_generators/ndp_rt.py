def package_type():
    return "composite"

def package_script():
    return ["eap", "realtime_asset_metrics"]

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics
    from ams_package_generators import eap

    # ndp_rt has all eap metrics
    eap.compute_and_append_asset_metrics(dest)

    # 1b metrics
    group_assets_1b = ['btc', 'eth']
    group_metrics_1b = ['AdrActCnt', 'AdrActRecCnt', 'AdrActSentCnt', 'BlkHgt', 'BlkIntMean', 'BlkSizeByte', 'DiffMean',
                        'FeeMeanNtv', 'FeeMeanUSD', 'FeeMedNtv', 'FeeMedUSD', 'FeeTotNtv', 'FeeTotUSD', 'IssContNtv',
                        'IssContUSD', 'IssTotNtv', 'IssTotUSD', 'PriceBTC', 'PriceUSD', 'RevNtv', 'RevUSD', 'TxCnt',
                        'TxMeanByte', 'TxTfrCnt', 'TxTfrMeanByte', 'TxTfrValMeanNtv', 'TxTfrValMeanUSD',
                        'TxTfrValMedNtv', 'TxTfrValMedUSD', 'TxTfrValNtv', 'TxTfrValUSD']
    append_asset_metrics(group_assets_1b, group_metrics_1b, '1b', dest)

    group1_assets_1b = ['btc']
    group1_metrics_1b = ['FlowInBMXNtv', 'FlowInBMXNtv', 'FlowInBMXUSD', 'FlowInBMXUSD', 'FlowInHUONtv', 'FlowInHUONtv',
                         'FlowInHUOUSD', 'FlowInHUOUSD', 'FlowOutBMXNtv', 'FlowOutBMXNtv', 'FlowOutBMXUSD',
                         'FlowOutBMXUSD', 'FlowOutHUONtv', 'FlowOutHUONtv', 'FlowOutHUOUSD', 'FlowOutHUOUSD',
                         'FlowTfrInBMXCnt', 'FlowTfrInBMXCnt', 'FlowTfrInHUOCnt', 'FlowTfrInHUOCnt', 'FlowTfrOutBMXCnt',
                         'FlowTfrOutBMXCnt', 'FlowTfrOutHUOCnt', 'FlowTfrOutHUOCnt', 'TxTfrValDayDst',
                         'TxTfrValDayDstMean', 'MinerEntity']
    append_asset_metrics(group1_assets_1b, group1_metrics_1b, '1b', dest)

    group2_assets_1b = ['btc', 'eth']
    group2_metrics_1b = ['FlowInBFXNtv', 'FlowInBFXNtv', 'FlowInBFXUSD', 'FlowInBFXUSD', 'FlowInBNBNtv', 'FlowInBNBNtv',
                         'FlowInBNBUSD', 'FlowInBNBUSD', 'FlowInBSPNtv', 'FlowInBSPNtv', 'FlowInBSPUSD', 'FlowInBSPUSD',
                         'FlowInBTXNtv', 'FlowInBTXNtv', 'FlowInBTXUSD', 'FlowInBTXUSD', 'FlowInExInclNtv',
                         'FlowInExInclNtv', 'FlowInExInclUSD', 'FlowInExInclUSD', 'FlowInExNtv', 'FlowInExNtv',
                         'FlowInExUSD', 'FlowInExUSD', 'FlowInGEMNtv', 'FlowInGEMNtv', 'FlowInGEMUSD', 'FlowInGEMUSD',
                         'FlowInKRKNtv', 'FlowInKRKNtv', 'FlowInKRKUSD', 'FlowInKRKUSD', 'FlowInPOLNtv', 'FlowInPOLNtv',
                         'FlowInPOLUSD', 'FlowInPOLUSD', 'FlowOutBFXNtv', 'FlowOutBFXNtv', 'FlowOutBFXUSD',
                         'FlowOutBFXUSD', 'FlowOutBNBNtv', 'FlowOutBNBNtv', 'FlowOutBNBUSD', 'FlowOutBNBUSD',
                         'FlowOutBSPNtv', 'FlowOutBSPNtv', 'FlowOutBSPUSD', 'FlowOutBSPUSD', 'FlowOutBTXNtv',
                         'FlowOutBTXNtv', 'FlowOutBTXUSD', 'FlowOutBTXUSD', 'FlowOutExInclNtv', 'FlowOutExInclNtv',
                         'FlowOutExInclUSD', 'FlowOutExInclUSD', 'FlowOutExNtv', 'FlowOutExNtv', 'FlowOutExUSD',
                         'FlowOutExUSD', 'FlowOutGEMNtv', 'FlowOutGEMNtv', 'FlowOutGEMUSD', 'FlowOutGEMUSD',
                         'FlowOutKRKNtv', 'FlowOutKRKNtv', 'FlowOutKRKUSD', 'FlowOutKRKUSD', 'FlowOutPOLNtv',
                         'FlowOutPOLNtv', 'FlowOutPOLUSD', 'FlowOutPOLUSD', 'FlowTfrFromExCnt', 'FlowTfrFromExCnt',
                         'FlowTfrFromExInclCnt', 'FlowTfrFromExInclCnt', 'FlowTfrInBFXCnt', 'FlowTfrInBFXCnt',
                         'FlowTfrInBNBCnt', 'FlowTfrInBNBCnt', 'FlowTfrInBSPCnt', 'FlowTfrInBSPCnt', 'FlowTfrInBTXCnt',
                         'FlowTfrInBTXCnt', 'FlowTfrInGEMCnt', 'FlowTfrInGEMCnt', 'FlowTfrInKRKCnt', 'FlowTfrInKRKCnt',
                         'FlowTfrInPOLCnt', 'FlowTfrInPOLCnt', 'FlowTfrOutBFXCnt', 'FlowTfrOutBFXCnt',
                         'FlowTfrOutBNBCnt', 'FlowTfrOutBNBCnt', 'FlowTfrOutBSPCnt', 'FlowTfrOutBSPCnt',
                         'FlowTfrOutBTXCnt', 'FlowTfrOutBTXCnt', 'FlowTfrOutGEMCnt', 'FlowTfrOutGEMCnt',
                         'FlowTfrOutKRKCnt', 'FlowTfrOutKRKCnt', 'FlowTfrOutPOLCnt', 'FlowTfrOutPOLCnt',
                         'FlowTfrToExCnt', 'FlowTfrToExCnt', 'FlowTfrToExInclCnt', 'FlowTfrToExInclCnt']
    append_asset_metrics(group2_assets_1b, group2_metrics_1b, '1b', dest)

    group3_assets_1b = ['eth']
    group3_metrics_1b = ['BlkUncCnt', 'BlkUncRwd', 'BlkUncRwdUSD', 'BlobCnt', 'BlobMeanCnt', 'BlobSizeByte',
                         'FeeBlobByteMeanNtv', 'FeeBlobByteMeanUSD', 'FeeBlobMeanNtv', 'FeeBlobMeanUSD',
                         'FeeBlobMedNtv', 'FeeBlobMedUSD', 'FeeBlobTotNtv', 'FeeBlobTotUSD', 'FeeBlobTxMeanNtv',
                         'FeeBlobTxMeanUSD', 'GasLmtBlk', 'GasLmtTx', 'GasLmtTxMean', 'GasUsedTx', 'GasUsedTxMean',
                         'TxBlobCnt', 'MinerEntity']
    append_asset_metrics(group3_assets_1b, group3_metrics_1b, '1b', dest)

    # 1h metrics
    default_assets_1h = ['btc', 'eth', 'eth_cl', 'usdc_eth', 'usdt_eth', 'usdt_trx']
    group_metrics_1h = ['AssetCompletionTime']
    append_asset_metrics(default_assets_1h, group_metrics_1h, '1h', dest)

    group1_assets_1h = ['btc', 'eth']
    group1_metrics_1h = ['BlkCnt', 'BlkHgt', 'BlkIntMean', 'BlkSizeByte', 'DiffLast', 'FeeMeanNtv', 'FeeMeanUSD',
                         'FeeMedNtv', 'FeeMedUSD', 'FeeTotNtv', 'FeeTotUSD']
    append_asset_metrics(group1_assets_1h, group1_metrics_1h, '1h', dest)

    group2_assets_1h = ['eth']
    group2_metrics_1h = ['AdrActBlobCnt', 'AdrActBlobContRecCnt', 'AdrActBlobRecCnt', 'AdrActBlobSendCnt',
                         'BlobARBSizeByte', 'BlobBASESizeByte', 'BlobCnt', 'BlobContCnt', 'BlobInscrCnt',
                         'BlobL2SizeByte', 'BlobMeanCnt', 'BlobOPSizeByte', 'BlobSizeAllTimeByte', 'BlobSizeByte',
                         'BlobUniqCnt', 'FeeBlobARBMeanNtv', 'FeeBlobARBMeanUSD', 'FeeBlobARBTotNtv',
                         'FeeBlobARBTotUSD', 'FeeBlobBASEMeanNtv', 'FeeBlobBASEMeanUSD', 'FeeBlobBASETotNtv',
                         'FeeBlobBASETotUSD', 'FeeBlobByteMeanNtv', 'FeeBlobByteMeanUSD', 'FeeBlobL2MeanNtv',
                         'FeeBlobL2MeanUSD', 'FeeBlobL2TotNtv', 'FeeBlobL2TotUSD', 'FeeBlobMeanNtv', 'FeeBlobMeanUSD',
                         'FeeBlobMedNtv', 'FeeBlobMedUSD', 'FeeBlobOPMeanNtv', 'FeeBlobOPMeanUSD', 'FeeBlobOPTotNtv',
                         'FeeBlobOPTotUSD', 'FeeBlobTotNtv', 'FeeBlobTotUSD', 'FeeBlobTxMeanNtv', 'FeeBlobTxMeanUSD',
                         'FeePrioMeanNtv', 'FeePrioMeanUSD', 'FeePrioMedNtv', 'FeePrioMedUSD', 'FeePrioTotNtv',
                         'FeePrioTotUSD', 'RevUSD', 'SplyBurntNtv', 'SplyBurntUSD', 'TxBlobARBCnt', 'TxBlobBASECnt',
                         'TxBlobCnt', 'TxBlobContCnt', 'TxBlobInscrCnt', 'TxBlobL2Cnt', 'TxBlobMeanCnt', 'TxBlobOPCnt']
    append_asset_metrics(group2_assets_1h, group2_metrics_1h, '1h', dest)

    group3_assets_1h = ['eth', 'eth_cl']
    group3_metrics_1h = ['RevNtv', 'StakingAPRNominal', 'StakingAPYNominal', 'ValidatorAPRNominal',
                         'ValidatorAPYNominal']
    append_asset_metrics(group3_assets_1h, group3_metrics_1h, '1h', dest)

    group4_assets_1h = ['eth_cl']
    group4_metrics_1h = ['IssContNtv', 'IssFullParticipation', 'IssTotNtv', 'PenaltyNtv', 'SlashedNtv',
                         'SplyActStkedNtv', 'SplyStkedActMeanNtv', 'SplyStkedNtv', 'SplyTotStkedNtv',
                         'StkConsolidationCnt', 'StkConsolidationTotNtv', 'StkDepositTotNtv', 'StkPartRateMean', 'StkWithdrawTotNtv',
                         'ValidatorActExtCnt', 'ValidatorActOngCnt', 'ValidatorActSlhCnt', 'ValidatorAddCnt1d',
                         'ValidatorCnt', 'ValidatorConsolidationCnt', 'ValidatorConsolidationRate',
                         'ValidatorConsolidationTotRate', 'ValidatorExtSlhCnt', 'ValidatorExtUnslhCnt',
                         'ValidatorPndInitCnt', 'ValidatorPndQedCnt', 'ValidatorRemCnt1d']
    append_asset_metrics(group4_assets_1h, group4_metrics_1h, '1h', dest)

    group5_exclude_assets_1h = sorted(set(default_assets_1h) - {'eth_cl'})
    group5_metrics_1h = ['AdrActCnt', 'AdrActRecCnt', 'AdrActSentCnt', 'PriceBTC', 'PriceUSD', 'ROI1yr', 'ROI30d',
                         'TxCnt', 'TxTfrCnt', 'TxTfrValMeanNtv', 'TxTfrValMeanUSD', 'TxTfrValMedNtv', 'TxTfrValMedUSD',
                         'TxTfrValNtv', 'TxTfrValUSD']
    append_asset_metrics(group5_exclude_assets_1h, group5_metrics_1h, '1h', dest)

    # 1m metrics
    default_assets_1m = ['eth_cl']
    group_metrics_1m = [
        "AssetCompletionTime", "AttestCnt", "AttestEpochCnt", "AttestEpochValCnt",
        "AttestValCnt", "AttestValLateCnt", "EpochCurr", "EpochFinal",
        "EpochJust", "SlashAttDblEvCnt", "SlashAttEvCnt", "SlashAttSurrEvCnt",
        "SlashEvCnt", "SlashPropEvCnt"
    ]
    append_asset_metrics(default_assets_1m, group_metrics_1m, '1m', dest)
