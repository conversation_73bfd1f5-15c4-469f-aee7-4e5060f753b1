def package_type():
    return "composite"

def package_script():
    from ams_package_generators.tx_tracker import tx_tracker_assets
    from ams_package_generators.asset_alerts import eth_smart_contract_alerts_assets, eth_smart_contract_data_source_alerts, chain_monitor_data_source_alerts
    
    script = [
        "chain_monitor_asset_metrics",
        "asset_chains",
        "mempool_feerates",
        "mining_pool_tips",
        "tx_tracker {'asset': '%s'}" %
        (
            ','.join(tx_tracker_assets)
        ),
        "tx_tracker_settlement {'asset': '%s'}" %
        (
            ','.join(tx_tracker_assets)
        ),
        "asset_alerts {'asset': '%s', 'alert': '%s'}" %
        (
            ','.join(eth_smart_contract_alerts_assets),
            ','.join(eth_smart_contract_data_source_alerts)
        )
    ]

    for asset, alerts in chain_monitor_data_source_alerts.items():
        script.append(
            "asset_alerts {'asset': '%s', 'alert': '%s'}" %
            (
                asset,
                ','.join(alerts)
            )
        )
        
    return script
