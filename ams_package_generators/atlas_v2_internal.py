def package_type():
    return "composite"

# If a key has access to any Atlas data, we give it access to the miner entity block-by-block metric
def compute_and_append_asset_metrics(dest):
    from ams_package_generators.miner_entity import compute_and_append_asset_metrics as add_miner_entity_metrics
    add_miner_entity_metrics(dest)

def package_script():
    from ams_package_generators.ube2 import atlas_v2_internal_assets
    return [
        f"ube2 {{'asset': '%s'}}" % ','.join(atlas_v2_internal_assets)
    ]
