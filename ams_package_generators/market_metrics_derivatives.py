def package_type():
    return "composite"

def package_script():
    from ams_data_generator import TimeRestrictions
    return [
        # todo: filter by derivatives
        "pair_metrics",
        # todo: filter by derivatives
        f"exchange_metrics {{'exchange', 'time_restriction': '{TimeRestrictions.NONE.value}'}}",
        # todo: filter by derivatives
        "exchange_asset_metrics",
        # todo: filter by derivatives
        "market_metrics",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_future_coin_margined_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_future_nonperpetual_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_future_perpetual_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_future_tether_margined_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_future_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_tether_margined_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_usdc_margined_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_coin_margined_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_call_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_put_market_value_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_tether_margined_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_usdc_margined_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_coin_margined_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_call_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'open_interest_reported_option_put_notional_usd', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_coin_margined_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_coin_margined_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_nonperpetual_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_nonperpetual_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_perpetual_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_perpetual_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_tether_margined_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_tether_margined_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_future_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_tether_margined_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_tether_margined_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_usdc_margined_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_usdc_margined_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_coin_margined_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_coin_margined_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_call_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_call_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_put_market_value_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_put_market_value_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_tether_margined_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_tether_margined_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_usdc_margined_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_usdc_margined_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_coin_margined_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_coin_margined_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_call_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_call_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_put_notional_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volume_reported_option_put_notional_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_1d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_2d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_3d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_7d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_14d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_21d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_30d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_60d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_90d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_120d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_180d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_270d_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'volatility_implied_atm_1y_expiration', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_usd_margin_8h_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_usd_margin_1d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_usd_margin_30d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_usd_margin_1y_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_coin_margin_8h_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_coin_margin_1d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_coin_margin_30d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_coin_margin_1y_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_all_margin_8h_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_all_margin_1d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_all_margin_30d_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_aggregate_funding_rate_all_margin_1y_period', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_usd_margin_rolling_1d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_usd_margin_rolling_7d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_usd_margin_rolling_30d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_coin_margin_rolling_1d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_coin_margin_rolling_7d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_coin_margin_rolling_30d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_all_margin_rolling_1d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_all_margin_rolling_7d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'futures_cumulative_funding_rate_all_margin_rolling_30d', 'frequency': '1h,1d'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_units_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_units_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_units_5m', 'frequency': '5m'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_buy_usd_5m', 'frequency': '5m'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_units_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_units_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_units_5m', 'frequency': '5m'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_usd_1d', 'frequency': '1d'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_usd_1h', 'frequency': '1h'}",
        "asset_metrics {'asset', 'metric': 'liquidations_reported_future_sell_usd_5m', 'frequency': '5m'}",
    ]
