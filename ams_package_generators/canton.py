def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    assets_1d = ["cc"]
    metrics_1d = [
        "AdrBal1in100KCnt",
        "AdrBal1in100MCnt",
        "AdrBal1in10BCnt",
        "AdrBal1in10KCnt",
        "AdrBal1in10MCnt",
        "AdrBal1in1BCnt",
        "AdrBal1in1KCnt",
        "AdrBal1in1MCnt",
        "AdrBalCnt",
        "AdrBalNtv0.001Cnt",
        "AdrBalNtv0.01Cnt",
        "AdrBalNtv0.1Cnt",
        "AdrBalNtv100Cnt",
        "AdrBalNtv100KCnt",
        "AdrBalNtv10Cnt",
        "AdrBalNtv10KCnt",
        "AdrBalNtv1Cnt",
        "AdrBalNtv1KCnt",
        "AdrBalNtv1MCnt",
        "AdrBalUSD100Cnt",
        "AdrBalUSD100KCnt",
        "AdrBalUSD10Cnt",
        "AdrBalUSD10KCnt",
        "AdrBalUSD10MCnt",
        "AdrBalUSD1Cnt",
        "AdrBalUSD1KCnt",
        "AdrBalUSD1MCnt",
        "AdrNewBalCnt",
        "AssetCompletionTime",
        "AssetEODCompletionTime",
        "CapAct1yrUSD",
        "CapMVRVCur",
        "CapMrktCurUSD",
        "CapRealUSD",
        "FeeMeanNtv",
        "FeeMeanUSD",
        "FeeMedNtv",
        "FeeMedUSD",
        "FeeTotNtv",
        "FeeTotUSD",
        "InfPct",
        "InfPct180dAvg",
        "InfPct30dAvg",
        "InfPct90dAvg",
        "InfPctAnn",
        "InfPctAnn180dAvg",
        "InfPctAnn30dAvg",
        "InfPctAnn90dAvg",
        "IssTot30dNtv",
        "IssTotNtv",
        "IssTotUSD",
        "NDF",
        "NVTAdj",
        "NVTAdj90",
        "PriceBTC",
        "PriceUSD",
        "ROI30d",
        "RVTAdj",
        "RVTAdj90",
        "SER",
        "SplyAct10yr",
        "SplyAct180d",
        "SplyAct1d",
        "SplyAct1yr",
        "SplyAct2yr",
        "SplyAct30d",
        "SplyAct3yr",
        "SplyAct4yr",
        "SplyAct5yr",
        "SplyAct7d",
        "SplyAct90d",
        "SplyActEver",
        "SplyActPct1yr",
        "SplyAdrBal1in100K",
        "SplyAdrBal1in100M",
        "SplyAdrBal1in10B",
        "SplyAdrBal1in10K",
        "SplyAdrBal1in10M",
        "SplyAdrBal1in1B",
        "SplyAdrBal1in1K",
        "SplyAdrBal1in1M",
        "SplyAdrBalNtv0.001",
        "SplyAdrBalNtv0.01",
        "SplyAdrBalNtv0.1",
        "SplyAdrBalNtv1",
        "SplyAdrBalNtv10",
        "SplyAdrBalNtv100",
        "SplyAdrBalNtv100K",
        "SplyAdrBalNtv10K",
        "SplyAdrBalNtv1K",
        "SplyAdrBalNtv1M",
        "SplyAdrBalUSD1",
        "SplyAdrBalUSD10",
        "SplyAdrBalUSD100",
        "SplyAdrBalUSD100K",
        "SplyAdrBalUSD10K",
        "SplyAdrBalUSD10M",
        "SplyAdrBalUSD1K",
        "SplyAdrBalUSD1M",
        "SplyAdrTop100",
        "SplyAdrTop10Pct",
        "SplyAdrTop1Pct",
        "SplyBurnt30dNtv",
        "SplyBurntNtv",
        "SplyBurntUSD",
        "SplyCur",
        "SplyDeltaNtv",
        "SplyValidatorNtv",
        "TxCnt",
        "TxCntSec",
        "TxTfrCnt",
        "TxTfrValAbUSD100MCnt",
        "TxTfrValAbUSD100MNtv",
        "TxTfrValAbUSD100MUSD",
        "TxTfrValAbUSD100kCnt",
        "TxTfrValAbUSD100kNtv",
        "TxTfrValAbUSD100kUSD",
        "TxTfrValAbUSD10MCnt",
        "TxTfrValAbUSD10MNtv",
        "TxTfrValAbUSD10MUSD",
        "TxTfrValAbUSD1MCnt",
        "TxTfrValAbUSD1MNtv",
        "TxTfrValAbUSD1MUSD",
        "TxTfrValAdjNtv",
        "TxTfrValAdjUSD",
        'TxTfrValBelUSD5Cnt',
        'TxTfrValBelUSD5Ntv',
        'TxTfrValBelUSD5USD',
        'TxTfrValBelUSD10Cnt',
        'TxTfrValBelUSD10Ntv',
        'TxTfrValBelUSD10USD',
        'TxTfrValBelUSD20Cnt',
        'TxTfrValBelUSD20Ntv',
        'TxTfrValBelUSD20USD',
        'TxTfrValBelUSD50Cnt',
        'TxTfrValBelUSD50Ntv',
        'TxTfrValBelUSD50USD',
        "TxTfrValBelUSD100Cnt",
        "TxTfrValBelUSD100Ntv",
        "TxTfrValBelUSD100USD",
        "TxTfrValBelUSD10kCnt",
        "TxTfrValBelUSD10kNtv",
        "TxTfrValBelUSD10kUSD",
        "TxTfrValBelUSD1kCnt",
        "TxTfrValBelUSD1kNtv",
        "TxTfrValBelUSD1kUSD",
        "TxTfrValBelUSD500Cnt",
        "TxTfrValBelUSD500Ntv",
        "TxTfrValBelUSD500USD",
        "TxTfrValMeanNtv",
        "TxTfrValMeanUSD",
        "TxTfrValMedNtv",
        "TxTfrValMedUSD",
        "TxTfrValNtv",
        "TxTfrValUSD",
        "UTXOCnt",
        "ValidatorCnt",
        "ValidatorSuperCnt",
        "VelAct1yr",
        "VelActAdj1yr",
        "VelCur1yr",
        "VelCurAdj1yr",
        "VtyDayRet180d",
        "VtyDayRet30d",
        "VtyDayRet60d"
    ]

    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

def package_script():
    # We also grant access to CC in Atlas if not already accessible
    return [
        f"ube2 {{'asset': 'cc'}}"
    ]
