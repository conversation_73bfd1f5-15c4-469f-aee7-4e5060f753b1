# This package defines internal asset metrics that are not available to customers.
# These metrics are exclusively used by the asset_metrics package, which is referenced by the employee package.

# Most metrics listed here are hidden from both the catalog and reference data endpoints,
# but remain accessible via the time series endpoints for internal keys.

# By including internal metrics in this package:
# - They are automatically excluded from the catalog and reference data.
# - They are omitted from the discovery_script of the asset_metrics package.
# - Consequently, they are excluded from all customer-facing packages that reference asset_metrics.

# IMPORTANT:
# If you release an asset/metric/frequency combination publicly,
# be sure to REMOVE it from this internal package to show it in catalog and reference data.

# The "internal" flag is metrics.json is a legacy field not used by API or AMS and should not be used anywhere.

# "None" means do not create an AMS package for this file
def package_type():
    return None

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    # 1h metrics
    assets_1h = [
        "eth", "eth_cl"
    ]
    metrics_1h = [
        "StakingRate", "ValidatorRate"
    ]
    append_asset_metrics(assets_1h, metrics_1h, "1h", dest)

    assets_1h = [
        "eth"
    ]
    metrics_1h = [
        "IssTotNtv"
    ]
    append_asset_metrics(assets_1h, metrics_1h, "1h", dest)

    # 1d metrics
    assets_1d = ["1inch", "aave", "ada", "ae_eth", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxx", "bal", "bat",
                 "bnb", "bnb_eth", "btm_eth", "busd", "comp", "cro", "crv", "cvc", "dai", "dgx", "dot", "drgn", "elf",
                 "eng", "eos_eth", "etc", "ethos", "eurc_eth", "fdusd_eth", "ftt", "fun", "fxc", "gno", "gnt", "gusd",
                 "hbtc", "hedg", "ht", "husd", "icp", "icx_eth", "kcs", "knc", "ldo", "lend", "leo_eth", "link", "loom",
                 "lpt", "lrc_eth", "lsk", "maid", "mana", "matic_eth", "mco", "mkr", "nas_eth", "nxm", "omg", "pax",
                 "paxg", "pay", "perp", "poly", "powr", "ppt", "pyusd_eth", "qash", "qnt", "qtum_eth", "ren", "renbtc",
                 "rep", "rev_eth", "snt", "snx", "srm", "sushi", "swrv", "trx_eth", "tusd_trx", "uma", "uni", "usdc",
                 "usdc_avaxc", "usdc_eth", "usdc_trx", "usdd_eth", "usdk", "usdt", "usdt_avaxc", "usdt_eth",
                 "usdt_omni", "vet_eth", "wbtc", "weth", "wnxm", "wtc", "xaut", "xlm", "xrp", "xtz", "yfi", "zil_eth",
                 "zrx"]
    metrics_1d = ["AdrPresCnt", "AdrRegCnt", "SplyAdrPres", "SplyAdrReg"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["sol"]
    metrics_1d = ["DelegatorRate", "SlotCnt", "SlotIntMean", "SlotLatest"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae", "ae_eth", "aion", "aion_eth", "alcx", "algo", "alpha", "alusd", "ant",
                 "ape", "audio", "audio_eth", "avaxc", "avaxp", "avaxx", "badger", "bal", "band_eth", "bat", "bch",
                 "bnb", "bnb_eth", "bnt", "bsv", "btc", "btg", "btm", "btm_eth", "buidl_eth", "busd", "cbat",
                 "cbbtc_base.eth", "cbbtc_eth", "ccomp", "cdai", "cel", "cennz", "ceth", "comp", "cro", "crv",
                 "crvusd_eth", "ctxc", "cuni", "cusdc", "cusdt", "cvc", "cwbtc", "czrx", "dai", "dai.e_base.eth",
                 "dash", "dcr", "dgb", "dgx", "doge", "dola.e_base.eth", "dot", "dpi", "drgn", "elf", "eng", "eos",
                 "eos_eth", "esd", "etc", "eth", "ethos", "eurc_eth", "eurcv_eth", "fdusd_eth", "fei_eth", "frax_eth",
                 "ftm_eth", "ftt", "fun", "fxc", "fxc_eth", "glm", "gno", "gnt", "grin", "grt", "grt_eth", "gusd",
                 "hbtc", "hedg", "ht", "husd", "icp", "icx", "icx_eth", "inst", "kcs", "kin1", "knc", "ldo", "lend",
                 "leo_eth", "link", "looks", "loom", "lpt", "lrc", "lrc_eth", "lsk", "ltc", "lusd_eth", "maid", "mana",
                 "matic_eth", "mco", "mkr", "mtl_metal", "nas", "nas_eth", "neo", "nftx", "nxm", "ogn", "okb", "omg",
                 "op_op.eth", "paid", "pax", "paxg", "pay", "perp", "pivx", "pol_eth", "poly", "powr", "ppt",
                 "pyusd_eth", "qash", "qnt", "qtum", "qtum_eth", "rad", "rad_eth", "radar", "rai_eth", "ren", "renbtc",
                 "rep", "rev_eth", "rhoc", "rsr", "sai", "salt", "shib", "shib_eth", "snt", "snx", "srm", "srn", "stmx",
                 "storj", "sushi", "swrv", "toke", "toke_eth", "trx_eth", "tusd", "tusd_trx", "ubt", "uma", "uni",
                 "usdc", "usdc.e_op.eth", "usdc_avaxc", "usdc_base.eth", "usdc_eth", "usdc_op.eth", "usdc_trx",
                 "usdd_eth", "usde_eth", "usdk", "usdt", "usdt.e_op.eth", "usdt_avaxc", "usdt_eth", "usdt_omni",
                 "usdt_trx", "veri", "vet", "vet_eth", "vtc", "wbtc", "weth", "wluna", "wnxm", "wsteth",
                 "wsteth.e_base.eth", "wsteth.e_op.eth", "wtc", "wust", "xaut", "xlm", "xmr", "xrp", "xsushi", "xtz",
                 "xvg", "yfi", "zec", "zil", "zil_eth", "zrx"]
    metrics_1d = ["SplyFound", "SplyLost"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae_eth", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxx", "bal", "bat",
                 "bnb", "bnb_eth", "bsv", "btc", "btg", "btm_eth", "busd", "comp", "cro", "crv", "cvc", "dai", "dgx",
                 "doge", "dot", "drgn", "elf", "eng", "eos_eth", "etc", "ethos", "eurc_eth", "fdusd_eth", "ftt", "fun",
                 "fxc", "gno", "gnt", "gusd", "hbtc", "hedg", "ht", "husd", "icp", "icx_eth", "kcs", "knc", "ldo",
                 "lend", "leo_eth", "link", "loom", "lpt", "lrc_eth", "lsk", "maid", "mana", "matic_eth", "mco", "mkr",
                 "nas_eth", "nxm", "omg", "pax", "paxg", "pay", "perp", "poly", "powr", "ppt", "pyusd_eth", "qash",
                 "qnt", "qtum_eth", "ren", "renbtc", "rep", "rev_eth", "snt", "snx", "srm", "sushi", "swrv", "trx_eth",
                 "tusd_trx", "uma", "uni", "usdc", "usdc_avaxc", "usdc_eth", "usdc_trx", "usdd_eth", "usdk",
                 "usdt_avaxc", "usdt_eth", "vet_eth", "wbtc", "weth", "wnxm", "wtc", "xaut", "xlm", "xrp", "xtz", "yfi",
                 "zec", "zil_eth", "zrx"]
    metrics_1d = ["SplyBot20PctUSD", "SplyTop20PctUSD"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["bch", "bsv", "btg"]
    metrics_1d = ["AdrBalForkCnt", "SplyFork"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["eth", "eth_cl", "sol"]
    metrics_1d = ["StakingRate", "ValidatorRate"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae", "ae_eth", "aion", "aion_eth", "alcx", "algo", "alpha", "alusd", "ant",
                 "ape", "audio", "audio_eth", "avaxc", "avaxp", "avaxx", "badger", "bal", "band_eth", "bat", "bch",
                 "bnb", "bnb_eth", "bnt", "bsv", "btc", "btg", "btm", "btm_eth", "buidl_eth", "busd", "cbat",
                 "cbbtc_base.eth", "cbbtc_eth", "ccomp", "cdai", "cel", "cennz", "ceth", "comp", "cro", "crv",
                 "crvusd_eth", "ctxc", "cuni", "cusdc", "cusdt", "cvc", "cwbtc", "czrx", "dai", "dai.e_base.eth",
                 "dash", "dcr", "dgb", "dgx", "doge", "dola.e_base.eth", "dot", "dpi", "drgn", "elf", "eng", "eos",
                 "eos_eth", "esd", "etc", "eth", "ethos", "eurc_eth", "eurcv_eth", "fdusd_eth", "fei_eth", "frax_eth",
                 "ftm_eth", "ftt", "fun", "fxc", "fxc_eth", "glm", "gno", "gnt", "grin", "grt", "grt_eth", "gusd",
                 "hbtc", "hedg", "ht", "husd", "icp", "icx", "icx_eth", "kcs", "kin1", "knc", "ldo", "lend", "leo_eth",
                 "link", "looks", "loom", "lpt", "lrc", "lrc_eth", "lsk", "ltc", "lusd_eth", "maid", "mana",
                 "matic_eth", "mco", "mkr", "mtl_metal", "nas", "nas_eth", "neo", "nftx", "nxm", "ogn", "okb", "omg",
                 "op_op.eth", "paid", "pax", "paxg", "pay", "perp", "pivx", "pol_eth", "poly", "powr", "ppt",
                 "pyusd_eth", "qash", "qnt", "qtum", "qtum_eth", "rad", "rad_eth", "radar", "rai_eth", "ren", "renbtc",
                 "rep", "rev_eth", "rhoc", "rsr", "sai", "salt", "shib", "shib_eth", "snt", "snx", "srm", "srn", "stmx",
                 "storj", "sushi", "swrv", "toke", "toke_eth", "trx_eth", "tusd", "tusd_trx", "ubt", "uma", "uni",
                 "usdc", "usdc.e_op.eth", "usdc_avaxc", "usdc_base.eth", "usdc_eth", "usdc_op.eth", "usdc_trx",
                 "usdd_eth", "usde_eth", "usdk", "usdt", "usdt.e_op.eth", "usdt_avaxc", "usdt_eth", "usdt_omni",
                 "usdt_trx", "veri", "vet", "vet_eth", "vtc", "wbtc", "weth", "wluna", "wnxm", "wsteth",
                 "wsteth.e_base.eth", "wsteth.e_op.eth", "wtc", "wust", "xaut", "xlm", "xmr", "xrp", "xsushi", "xtz",
                 "xvg", "yfi", "zec", "zil", "zil_eth", "zrx"]
    metrics_1d = ["SplyTeam"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae", "ae_eth", "aion", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxp",
                 "avaxx", "bal", "bat", "bch", "bnb", "bnb_eth", "bsv", "btc", "btg", "btm", "btm_eth", "busd", "cennz",
                 "comp", "cro", "crv", "ctxc", "cvc", "dai", "dash", "dcr", "dgb", "dgx", "doge", "dot", "drgn", "elf",
                 "eng", "eos", "eos_eth", "etc", "eth", "ethos", "eurc_eth", "eurcv_eth", "fdusd_eth", "ftt", "fun",
                 "fxc", "fxc_eth", "gno", "gnt", "grin", "gusd", "hbtc", "hedg", "ht", "husd", "icp", "icx", "icx_eth",
                 "kcs", "kin1", "knc", "ldo", "lend", "leo_eth", "link", "loom", "lpt", "lrc", "lrc_eth", "lsk", "ltc",
                 "maid", "mana", "matic_eth", "mco", "mkr", "mtl_metal", "nas", "nas_eth", "neo", "nxm", "omg", "pax",
                 "paxg", "pay", "perp", "pivx", "poly", "powr", "ppt", "pyusd_eth", "qash", "qnt", "qtum", "qtum_eth",
                 "ren", "renbtc", "rep", "rev_eth", "rhoc", "sai", "salt", "snt", "snx", "srm", "srn", "sushi", "swrv",
                 "trx_eth", "tusd", "tusd_trx", "uma", "uni", "usdc", "usdc_avaxc", "usdc_eth", "usdc_trx", "usdd_eth",
                 "usdk", "usdt", "usdt_avaxc", "usdt_eth", "usdt_omni", "usdt_trx", "veri", "vet", "vet_eth", "vtc",
                 "wbtc", "weth", "wnxm", "wtc", "xaut", "xlm", "xmr", "xrp", "xtz", "xvg", "yfi", "zec", "zil",
                 "zil_eth", "zrx"]
    metrics_1d = ["SplyRes"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae", "ae_eth", "aion", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxx",
                 "bal", "bat", "bch", "bnb", "bnb_eth", "bsv", "btc", "btg", "btm", "btm_eth", "busd", "cbbtc_base.eth",
                 "cbbtc_eth", "cennz", "comp", "cro", "crv", "ctxc", "cvc", "dai", "dai.e_base.eth", "dash", "dcr",
                 "dgb", "dgx", "doge", "dola.e_base.eth", "dot", "drgn", "elf", "eng", "eos_eth", "etc", "eth", "ethos",
                 "eurc_eth", "eurcv_eth", "fdusd_eth", "ftt", "fun", "fxc", "fxc_eth", "gno", "gnt", "gusd", "hbtc",
                 "hedg", "ht", "husd", "icn", "icp", "icx", "icx_eth", "kcs", "kin1", "knc", "ldo", "lend", "leo_eth",
                 "link", "loom", "lpt", "lrc", "lrc_eth", "lsk", "ltc", "maid", "mana", "matic_eth", "mco", "mkr",
                 "mtl_metal", "nas", "nas_eth", "nxm", "omg", "op_op.eth", "pax", "paxg", "pay", "perp", "pivx", "poly",
                 "powr", "ppt", "pyusd_eth", "qash", "qnt", "qtum", "qtum_eth", "ren", "renbtc", "rep", "rev_eth",
                 "rhoc", "sai", "salt", "snt", "snx", "srm", "srn", "sushi", "swrv", "trx_eth", "tusd", "tusd_trx",
                 "uma", "uni", "usdc", "usdc.e_op.eth", "usdc_avaxc", "usdc_base.eth", "usdc_eth", "usdc_op.eth",
                 "usdc_trx", "usdd_eth", "usdk", "usdt", "usdt.e_op.eth", "usdt_avaxc", "usdt_eth", "usdt_omni",
                 "usdt_trx", "veri", "vet", "vet_eth", "vtc", "wbtc", "weth", "wnxm", "wsteth.e_base.eth",
                 "wsteth.e_op.eth", "wtc", "xaut", "xlm", "xrp", "xtz", "xvg", "yfi", "zec", "zil", "zil_eth", "zrx"]
    metrics_1d = ["AdrCnt"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae", "aion", "alcx", "algo", "alpha", "ant", "ape", "api3", "atom", "audio",
                 "avax", "badger", "bal", "bat", "bch", "bit", "bnb", "bnt", "boden_sol", "bome_sol", "bonk_sol", "bsv",
                 "btc", "btg", "btm", "busd", "cel", "comp", "cro", "crv", "cvc", "cvx", "cwbtc", "dai", "dar", "dash",
                 "dcr", "dgb", "doge", "dot", "drgn", "elf", "enj", "ens", "eos", "etc", "eth", "eth_sepolia", "fil",
                 "flow", "flow_evm", "ftt", "fun", "fxs", "gala", "gas", "glm", "gno", "gnt", "grin", "grt", "gusd",
                 "hbtc", "ht", "icp", "icx", "imx", "kcs", "knc", "ldo", "lend", "link", "loom", "lpt", "lrc", "lsk",
                 "ltc", "luna", "lusd", "mana", "mkr", "mnt", "mpl", "mtl_metal", "nas", "neo", "nexo", "nftx", "nmr",
                 "nxm", "ogn", "okb", "omg", "osmo", "pax", "paxg", "pay", "perp", "poly", "powr", "pyusd_eth", "qash",
                 "qnt", "qtum", "rad", "ren", "renbtc", "rep", "rook", "rsr", "samo_sol", "sand", "shib", "skl", "sol",
                 "slerf_sol", "snt", "snx", "spell", "srm", "stmx", "storj", "stsol_sol", "sui", "sushi", "swrv",
                 "toke", "trx", "tusd", "uma", "uni", "usdc", "usdt", "ust", "uxd_sol", "vet", "w_sol", "waves", "wbtc",
                 "wen_sol", "weth", "wif_sol", "wnxm", "wsteth", "wtc", "xaut", "xem", "xlm", "xmr", "xrp", "xtz",
                 "xvg", "yfi", "zec", "zil", "zrx"]
    metrics_1d = ["SplyEstNtv"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae_eth", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxx", "bal", "bat",
                 "bch", "bnb", "bnb_eth", "bnt", "bsv", "btc", "btg", "btm_eth", "busd", "cel", "cennz", "comp", "cro",
                 "crv", "cvc", "dai", "dash", "dcr", "dgb", "dgx", "doge", "dot", "drgn", "elf", "eng", "eos_eth",
                 "etc", "eth", "eth_cl", "ethos", "eurc_eth", "eurcv_eth", "fdusd_eth", "ftt", "fun", "fxc", "fxc_eth",
                 "glm", "gno", "gnt", "grt", "grt_eth", "gusd", "hbtc", "hedg", "ht", "husd", "icp", "icx_eth", "kcs",
                 "knc", "ldo", "lend", "leo_eth", "link", "loom", "lpt", "lrc_eth", "lsk", "ltc", "lusd_eth", "maid",
                 "mana", "matic_eth", "mco", "mkr", "mtl_metal", "nas_eth", "nxm", "okb", "omg", "pax", "paxg", "pay",
                 "perp", "poly", "powr", "ppt", "pyusd_eth", "qash", "qnt", "qtum_eth", "rai_eth", "ren", "renbtc",
                 "rep", "rev_eth", "sai", "snt", "snx", "srm", "srn", "sushi", "swrv", "trx_eth", "tusd_trx", "uma",
                 "uni", "usdc", "usdc_avaxc", "usdc_eth", "usdc_trx", "usdd_eth", "usdk", "usdt", "usdt_avaxc",
                 "usdt_eth", "usdt_omni", "usdt_trx", "vet_eth", "vtc", "wbtc", "weth", "wluna", "wnxm", "wtc", "wust",
                 "xaut", "xlm", "xrp", "xtz", "xvg", "yfi", "zec", "zil_eth", "zrx"]
    metrics_1d = ["SplyTop20PctNtv"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["1inch", "aave", "ada", "ae_eth", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxx", "bal", "bat",
                 "bch", "bnb", "bnb_eth", "bnt", "bsv", "btc", "btg", "btm_eth", "busd", "cel", "cennz", "comp", "cro",
                 "crv", "cvc", "dai", "dash", "dcr", "dgb", "dgx", "doge", "dot", "drgn", "elf", "eng", "eos_eth",
                 "etc", "eth", "eth_cl", "ethos", "eurc_eth", "eurcv_eth", "fdusd_eth", "ftt", "fun", "fxc", "fxc_eth",
                 "glm", "gno", "gnt", "grt", "grt_eth", "gusd", "hbtc", "hedg", "ht", "husd", "icp", "icx_eth", "kcs",
                 "knc", "ldo", "lend", "leo_eth", "link", "loom", "lpt", "lrc_eth", "lsk", "ltc", "lusd_eth", "maid",
                 "mana", "matic_eth", "mco", "mkr", "mtl_metal", "nas_eth", "nxm", "okb", "omg", "pax", "paxg", "pay",
                 "perp", "poly", "powr", "ppt", "pyusd_eth", "qash", "qnt", "qtum_eth", "rai_eth", "ren", "renbtc",
                 "rep", "rev_eth", "sai", "snt", "snx", "srm", "srn", "sushi", "swrv", "trx_eth", "tusd_trx", "uma",
                 "uni", "usdc", "usdc_avaxc", "usdc_trx", "usdd_eth", "usdk", "usdt", "usdt_avaxc", "usdt_eth",
                 "usdt_omni", "usdt_trx", "vet_eth", "vtc", "wbtc", "weth", "wluna", "wnxm", "wtc", "wust", "xaut",
                 "xlm", "xrp", "xtz", "xvg", "yfi", "zec", "zil_eth", "zrx"]
    metrics_1d = ["SplyBot20PctNtv"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["bsv", "btc", "btg", "doge", "etc", "xmr", "zec"]
    metrics_1d = ["HashRib30d60d"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["eth_sepolia"]
    metrics_1d = ["CapMrktEstUSD"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["kcs"]
    metrics_1d = ["PriceUSD"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["usdt_eth"]
    metrics_1d = ["ContBalCnt", "ContCnt"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    assets_1d = ["btg", "ltc", "vtc"]
    metrics_1d = ["RevHashNtv", "RevHashUSD"]
    append_asset_metrics(assets_1d, metrics_1d, "1d", dest)

    # chain monitor internal metrics
    assets_1m = ["bch", "ltc", "bsv", "doge"]
    metrics_1m = ["mining_reward_mean", "mining_reward_spread"]
    append_asset_metrics(assets_1m, metrics_1m, "1m", dest)

    assets_1b = ["bch", "busd", "dash", "husd", "ltc", "pax", "paxg", "usdc", "usdt_omni",
                 "usdt_eth", "usdt_trx", "wbtc", "xrp", "zec"]
    metrics_1b = ["block_count_at_tip", "block_difficulty", "block_difficulty_change",
                  "block_count_empty_6b", "block_fees", "block_count_without_segwit_6b",
                  "block_count_by_same_miner_6b", "block_count_by_unknown_miners_6b",
                  "block_hashrate_mean_1d", "block_feerate_min", "block_feerate_max",
                  "block_feerate_mean", "block_feerate_median", "block_fee_min", "block_fee_max",
                  "block_fee_mean", "block_fee_median", "block_size", "block_tx_count"]
    append_asset_metrics(assets_1b, metrics_1b, "1b", dest)

    assets_1m = ["bch", "dash", "ltc", "xrp", "zec"]
    metrics_1m = ["time_inter_block", "time_since_last_block"]
    append_asset_metrics(assets_1m, metrics_1m, "1m", dest)

    assets_1b = ["usdc", "usdt_eth", "pax", "busd", "aave", "sushi", "usdk", "husd", "wbtc", "renbtc", "xaut", "paxg"]
    metrics_1b = ["sc_admin_key_change", "sc_proxy_implementation_update_count",
                  "sc_mint_assets", "sc_burn_assets",
                  "sc_volume_mint_assets", "sc_volume_burn_assets"]
    append_asset_metrics(assets_1b, metrics_1b, "1b", dest)
