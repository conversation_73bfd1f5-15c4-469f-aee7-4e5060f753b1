from ams_package_generators.ube2 import atlas_v2_internal_assets, atlas_v2_released_assets, atlas_v2_labs_assets

# Internal assets (not yet released for clients)
blockchain_job_internal_assets = sorted(atlas_v2_internal_assets)

# Separately entitled packages via ams-ui (notice overlap with released/all assets)
blockchain_job_async_only_solana = ["sol"]
blockchain_job_async_only_eth_cl = ["eth_cl"]
blockchain_job_sync_assets = atlas_v2_released_assets + atlas_v2_labs_assets
blockchain_job_released_assets = sorted(blockchain_job_async_only_solana + blockchain_job_async_only_eth_cl + blockchain_job_sync_assets)
blockchain_job_all_assets = sorted(blockchain_job_internal_assets + blockchain_job_released_assets)

# Validations (ensure no duplicates)
assert len(set(blockchain_job_internal_assets)) == len(blockchain_job_internal_assets)
assert len(set(blockchain_job_released_assets)) == len(blockchain_job_released_assets)
assert len(set(blockchain_job_internal_assets) & set(blockchain_job_released_assets)) == 0

def package_script():
    return [
        "{'asset': '%s'}" % ','.join(blockchain_job_all_assets)
    ]
