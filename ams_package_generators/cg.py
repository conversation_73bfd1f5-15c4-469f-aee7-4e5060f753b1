import json

def package_type():
    return "composite"

def find_cg_assets():
    assets = []
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        metadata = currency['metadata']
        if metadata.get("ckgo_id"):
            asset = currency['cm_ticker']
            assets.append(asset)
    return assets

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    # manually add datonomy assets which obtain supply data from sources alternative to CoinGecko
    market_cap_assets_1d = find_cg_assets() + ["loom", "dar", "t", "render"]
    market_cap_metrics_1d = ["CapMrktEstUSD"]
    append_asset_metrics(market_cap_assets_1d, market_cap_metrics_1d, "1d", dest)
