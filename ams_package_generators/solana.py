def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    all_solana_assets = [
        "sol",
        "bnsol_sol",
        "boden_sol",
        "bome_sol",
        "bonk_sol",
        "cbbtc_sol",
        "eurc_sol",
        "hnt_sol",
        "jitosol_sol",
        "jto_sol",
        "jup_sol",
        "jupsol_sol",
        "knk_sol",
        "me_sol",
        "melania_sol",
        "mnde_sol",
        "mplx_sol",
        "msol_sol",
        "orca_sol",
        "pyth_sol",
        "pyusd_sol",
        "ray_sol",
        "render_sol",
        "samo_sol",
        "sbr_sol",
        "slerf_sol",
        "slnd_sol",
        "stsol_sol",
        "tremp_sol",
        "trump_sol",
        "usdc_sol",
        "usde.e_sol",
        "usds.e_sol",
        "usdt_sol",
        "usdy_sol",
        "uxd_sol",
        "w_sol",
        "wen_sol",
        "wif_sol",
        "wsol_sol"
    ]

    solana_metrics_computable_for_all_solana_assets = [
        "AdrActCnt",
        "AdrActRecCnt",
        "AdrActSentCnt",
        "AssetCompletionTime",
        "AssetEODCompletionTime",
        "IssTotNtv",
        "IssTotUSD",
        "MevAdrActCnt",
        "MevTfrValAbUSD100MCnt",
        "MevTfrValAbUSD100MNtv",
        "MevTfrValAbUSD100MUSD",
        "MevTfrValAbUSD100kCnt",
        "MevTfrValAbUSD100kNtv",
        "MevTfrValAbUSD100kUSD",
        "MevTfrValAbUSD10MCnt",
        "MevTfrValAbUSD10MNtv",
        "MevTfrValAbUSD10MUSD",
        "MevTfrValAbUSD1MCnt",
        "MevTfrValAbUSD1MNtv",
        "MevTfrValAbUSD1MUSD",
        "MevTfrValBelUSD100Cnt",
        "MevTfrValBelUSD100Ntv",
        "MevTfrValBelUSD100USD",
        "MevTfrValBelUSD10kCnt",
        "MevTfrValBelUSD10kNtv",
        "MevTfrValBelUSD10kUSD",
        "MevTfrValBelUSD1kCnt",
        "MevTfrValBelUSD1kNtv",
        "MevTfrValBelUSD1kUSD",
        "MevTfrValBelUSD500Cnt",
        "MevTfrValBelUSD500Ntv",
        "MevTfrValBelUSD500USD",
        "MevTfrValNtv",
        "MevTfrValUSD",
        "MevTxCnt",
        "MevWalActCnt",
        "PriceBTC",
        "PriceUSD",
        "ROI1yr",
        "ROI30d",
        "SplyBurntNtv",
        "SplyBurntUSD",
        "TxCnt",
        "TxCntSec",
        "TxTfrCnt",
        "TxTfrValAbUSD100kCnt",
        "TxTfrValAbUSD100kNtv",
        "TxTfrValAbUSD100kUSD",
        "TxTfrValAbUSD100MCnt",
        "TxTfrValAbUSD100MNtv",
        "TxTfrValAbUSD100MUSD",
        "TxTfrValAbUSD10MCnt",
        "TxTfrValAbUSD10MNtv",
        "TxTfrValAbUSD10MUSD",
        "TxTfrValAbUSD1MCnt",
        "TxTfrValAbUSD1MNtv",
        "TxTfrValAbUSD1MUSD",
        "TxTfrValAdjNtv",
        "TxTfrValAdjUSD",
        'TxTfrValBelUSD5Cnt',
        'TxTfrValBelUSD5Ntv',
        'TxTfrValBelUSD5USD',
        'TxTfrValBelUSD10Cnt',
        'TxTfrValBelUSD10Ntv',
        'TxTfrValBelUSD10USD',
        'TxTfrValBelUSD20Cnt',
        'TxTfrValBelUSD20Ntv',
        'TxTfrValBelUSD20USD',
        'TxTfrValBelUSD50Cnt',
        'TxTfrValBelUSD50Ntv',
        'TxTfrValBelUSD50USD',
        "TxTfrValBelUSD100Cnt",
        "TxTfrValBelUSD100Ntv",
        "TxTfrValBelUSD100USD",
        "TxTfrValBelUSD10kCnt",
        "TxTfrValBelUSD10kNtv",
        "TxTfrValBelUSD10kUSD",
        "TxTfrValBelUSD1kCnt",
        "TxTfrValBelUSD1kNtv",
        "TxTfrValBelUSD1kUSD",
        "TxTfrValBelUSD500Cnt",
        "TxTfrValBelUSD500Ntv",
        "TxTfrValBelUSD500USD",
        "TxTfrValMeanNtv",
        "TxTfrValMeanUSD",
        "TxTfrValMedNtv",
        "TxTfrValMedUSD",
        "TxTfrValNtv",
        "TxTfrValRecNtv",
        "TxTfrValRecTop001PctNtv",
        "TxTfrValRecTop01PctNtv",
        "TxTfrValRecTop100KNtv",
        "TxTfrValRecTop100Ntv",
        "TxTfrValRecTop10KNtv",
        "TxTfrValRecTop10Ntv",
        "TxTfrValRecTop10PctNtv",
        "TxTfrValRecTop1KNtv",
        "TxTfrValRecTop1PctNtv",
        "TxTfrValSentNtv",
        "TxTfrValSentTop001PctNtv",
        "TxTfrValSentTop01PctNtv",
        "TxTfrValSentTop100KNtv",
        "TxTfrValSentTop100Ntv",
        "TxTfrValSentTop10KNtv",
        "TxTfrValSentTop10Ntv",
        "TxTfrValSentTop10PctNtv",
        "TxTfrValSentTop1KNtv",
        "TxTfrValSentTop1PctNtv",
        "TxTfrValUSD",
        "TxTfrValSentUSD",
        "TxTfrValRecUSD",
        "TxTfrValRecTop001PctUSD",
        "TxTfrValRecTop01PctUSD",
        "TxTfrValRecTop100KUSD",
        "TxTfrValRecTop100USD",
        "TxTfrValRecTop10KUSD",
        "TxTfrValRecTop10USD",
        "TxTfrValRecTop10PctUSD",
        "TxTfrValRecTop1KUSD",
        "TxTfrValRecTop1PctUSD",
        "TxTfrValSentTop001PctUSD",
        "TxTfrValSentTop01PctUSD",
        "TxTfrValSentTop100KUSD",
        "TxTfrValSentTop100USD",
        "TxTfrValSentTop10KUSD",
        "TxTfrValSentTop10USD",
        "TxTfrValSentTop10PctUSD",
        "TxTfrValSentTop1KUSD",
        "TxTfrValSentTop1PctUSD",
        "VtyDayRet180d",
        "VtyDayRet30d",
        "VtyDayRet60d",
        "WalActCnt"
    ]
    append_asset_metrics(all_solana_assets, solana_metrics_computable_for_all_solana_assets, "1d", dest)

    only_sol_asset = ["sol"]
    metrics_applicable_only_to_sol_asset = [
        "BlkCnt",
        "BlkHgt",
        "BlkIntMean",
        "DelegatorAPRNominal",
        "DelegatorAPYNominal",
        "DelegatorAPYReal",
        "DelegatorAddCnt1d",
        "DelegatorCnt",
        "DelegatorRemCnt1d",
        "FeeMeanNtv",
        "FeeMeanUSD",
        "FeeMedNtv",
        "FeeMedUSD",
        "MevBlkCnt",
        "FeeMevTotNtv",
        "FeeMevTotUSD",
        "FeeMevMeanNtv",
        "FeeMevMeanUSD",
        "FeeMevMedNtv",
        "FeeMevMedUSD",
        "FeeMevRevPct",
        "FeePrioMeanNtv",
        "FeePrioMeanUSD",
        "FeePrioMedNtv",
        "FeePrioMedUSD",
        "FeePrioTotMevNtv",
        "FeePrioTotMevUSD",
        "FeePrioMeanMevNtv",
        "FeePrioMeanMevUSD",
        "FeePrioMedMevNtv",
        "FeePrioMedMevUSD",
        "FeePrioTotNonvoteNtv",
        "FeePrioTotNonvoteUSD",
        "FeePrioTotNtv",
        "FeePrioTotUSD",
        "FeePrioTotVoteNtv",
        "FeePrioTotVoteUSD",
        "FeeRevPct",
        "FeeStorTotNtv",
        "FeeStorTotUSD",
        "FeeTotNonvoteNtv",
        "FeeTotNonvoteUSD",
        "FeeTotNtv",
        "FeeTotUSD",
        "FeeTotVoteNtv",
        "FeeTotVoteUSD",
        "IssContNtv",
        "IssContPctDay",
        "IssContPctAnn",
        "IssContPctDay",
        "IssContUSD",
        "RevDelegatorNtv",
        "RevDelegatorUSD",
        "RevNtv",
        "RevUSD",
        "RevValidatorNtv",
        "RevValidatorUSD",
        "SplyActStkedNtv",
        "SplyActStkedUSD",
        "SplyDelegatorActStkedNtv",
        "SplyDelegatorActStkedUSD",
        "SplyStkedNtv",
        "SplyStkedUSD",
        "SplyTotStkedNtv",
        "SplyTotStkedUSD",
        "SplyValidatorActStkedNtv",
        "SplyValidatorActStkedUSD",
        "SplyValidatorStkedNtv",
        "SplyValidatorStkedUSD",
        "SplyDelegatorStkedNtv",
        "SplyDelegatorStkedUSD",
        "StakerAddCnt1d",
        "StakerCnt",
        "StakerRemCnt1d",
        "StakingAPRNominal",
        "StakingAPYNominal",
        "StakingAPYReal",
        "TxCntNonvote",
        "TxCntVote",
        "TxContCallSuccCnt",
        "TxFailCnt",
        "TxFailCntNonvote",
        "TxFailCntVote",
        "TxTfrFTCnt",
        "TxTfrNFTCnt",
        "TxTfrTknCnt",
        "TxTfrValContCallNtv",
        "TxTfrValContCallUSD",
        "TxTknCnt",
        "ValidatorAPRNominal",
        "ValidatorAPYNominal",
        "ValidatorAPYReal",
        "ValidatorAddCnt1d",
        "ValidatorCnt",
        "ValidatorRemCnt1d"
    ]
    
    solana_supply_metrics_from_airflow = [
        "AdrBal1in100KCnt",
        "AdrBal1in100MCnt",
        "AdrBal1in10BCnt",
        "AdrBal1in10KCnt",
        "AdrBal1in10MCnt",
        "AdrBal1in1BCnt",
        "AdrBal1in1KCnt",
        "AdrBal1in1MCnt",
        "AdrBalCnt",
        "AdrBalNtv0.001Cnt",
        "AdrBalNtv0.01Cnt",
        "AdrBalNtv0.1Cnt",
        "AdrBalNtv100Cnt",
        "AdrBalNtv100KCnt",
        "AdrBalNtv10Cnt",
        "AdrBalNtv10KCnt",
        "AdrBalNtv1Cnt",
        "AdrBalNtv1KCnt",
        "AdrBalNtv1MCnt",
        "AdrBalUSD100Cnt",
        "AdrBalUSD100KCnt",
        "AdrBalUSD10Cnt",
        "AdrBalUSD10KCnt",
        "AdrBalUSD10MCnt",
        "AdrBalUSD1Cnt",
        "AdrBalUSD1KCnt",
        "AdrBalUSD1MCnt",
        "AdrCnt",
        "SplyAdrBal1in100K",
        "SplyAdrBal1in100M",
        "SplyAdrBal1in10B",
        "SplyAdrBal1in10K",
        "SplyAdrBal1in10M",
        "SplyAdrBal1in1B",
        "SplyAdrBal1in1K",
        "SplyAdrBal1in1M",
        "SplyAdrBalNtv0.001",
        "SplyAdrBalNtv0.01",
        "SplyAdrBalNtv0.1",
        "SplyAdrBalNtv1",
        "SplyAdrBalNtv10",
        "SplyAdrBalNtv100",
        "SplyAdrBalNtv100K",
        "SplyAdrBalNtv10K",
        "SplyAdrBalNtv1K",
        "SplyAdrBalNtv1M",
        "SplyAdrBalUSD1",
        "SplyAdrBalUSD10",
        "SplyAdrBalUSD100",
        "SplyAdrBalUSD100K",
        "SplyAdrBalUSD10K",
        "SplyAdrBalUSD10M",
        "SplyAdrBalUSD1K",
        "SplyAdrBalUSD1M",
        "SplyAdrTop100",
        "SplyAdrTop10Pct",
        "SplyAdrTop1Pct",
        "SplyBot20PctNtv",
        "SplyTop20PctNtv",
        # These four metrics are not computed in Airflow, but are computable from the metrics above
        "NDF",
        "SER",
        "SplyBot20PctUSD",
        "SplyTop20PctUSD"
    ]

    append_asset_metrics(only_sol_asset, metrics_applicable_only_to_sol_asset, "1d", dest)
    append_asset_metrics(only_sol_asset, solana_supply_metrics_from_airflow, "1d", dest)

    solana_assets_without_supply_metric = []
    solana_assets_with_supply_metric = all_solana_assets.copy()
    solana_assets_with_supply_metric = [asset for asset in solana_assets_with_supply_metric if
                                        asset not in solana_assets_without_supply_metric]
    solana_metrics_dependent_on_supply = [
        "CapMrktCurUSD",
        "InfPct",
        "InfPct180dAvg",
        "InfPct1yAvg",
        "InfPct30dAvg",
        "InfPct90dAvg",
        "InfPctAnn",
        "InfPctAnn180dAvg",
        "InfPctAnn1yAvg",
        "InfPctAnn30dAvg",
        "InfPctAnn90dAvg",
        "NVTAdj",
        "NVTAdj90",
        "SplyCur",
        "VelCur1yr",
        "VelCurAdj1yr"
    ]
    append_asset_metrics(solana_assets_with_supply_metric, solana_metrics_dependent_on_supply, "1d", dest)
