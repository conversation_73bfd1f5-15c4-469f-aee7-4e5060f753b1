import json

def package_type():
    return "composite"

def package_script():
    return ["reference_rates_ge_1s"]

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    metrics_200ms = ["ReferenceRate", "ReferenceRateUSD", "ReferenceRateEUR", "ReferenceRateBTC", "ReferenceRateETH"]
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        asset = currency['cm_ticker']
        metadata = currency['metadata']
        if metadata["hasRTRR"]:
            append_asset_metrics([asset], metrics_200ms, "200ms", dest)
