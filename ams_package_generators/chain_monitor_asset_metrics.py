def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    chain_monitor_mempool_assets_1m = ["btc"]
    chain_monitor_mempool_metrics_1m = ["mempool_count", "mempool_fee", "mempool_output_value",
                                        "mempool_vsize", "mempool_feerate_mean", "mempool_feerate_median",
                                        "mempool_fee_mean", "mempool_fee_median",
                                        "mempool_next_block_inclusion_approx_feerate_min",
                                        "mempool_next_block_approx_feerate_min", "mempool_next_block_approx_feerate_max",
                                        "mempool_next_block_approx_feerate_mean",
                                        "mempool_next_block_approx_feerate_median",
                                        "mempool_vsize_entered_1m", "mempool_vsize_left_1m",
                                        "mempool_count_entered_1m", "mempool_fee_entered_1m", "mempool_fee_mean_entered_1m",
                                        "mempool_output_value_entered_1m", "mempool_size", "mempool_size_entered_1m",
                                        "mempool_size_left_1m"]
    append_asset_metrics(chain_monitor_mempool_assets_1m, chain_monitor_mempool_metrics_1m, '1m', dest)

    chain_monitor_mining_assets_1m = ["btc"]
    chain_monitor_mining_metrics_1m = ["mining_reward_mean", "mining_reward_spread"]
    append_asset_metrics(chain_monitor_mining_assets_1m, chain_monitor_mining_metrics_1m, '1m', dest)

    chain_monitor_mined_assets_1b = ["btc", "eth"]
    chain_monitor_mined_metrics_1b = ["block_count_at_tip", "block_difficulty", "block_difficulty_change",
                                      "block_count_empty_6b", "block_fees", "block_count_without_segwit_6b",
                                      "block_count_by_same_miner_6b", "block_count_by_unknown_miners_6b",
                                      "block_hashrate_mean_1d", "block_feerate_min", "block_feerate_max",
                                      "block_feerate_mean", "block_feerate_median", "block_fee_min", "block_fee_max",
                                      "block_fee_mean", "block_fee_median", "block_size", "block_tx_count"]
    append_asset_metrics(chain_monitor_mined_assets_1b, chain_monitor_mined_metrics_1b, '1b', dest)

    chain_monitor_mined_assets_1m = ["btc", "eth"]
    chain_monitor_mined_metrics_1m = ["time_inter_block", "time_since_last_block"]
    append_asset_metrics(chain_monitor_mined_assets_1m, chain_monitor_mined_metrics_1m, '1m', dest)

    chain_monitor_confirmation_metrics_assets_1m = ["btc", "eth", "usdc", "usdt_eth", "pax", "busd", "aave", "sushi",
                                                    "usdk", "husd", "wbtc", "renbtc", "xaut", "paxg"]
    chain_monitor_confirmation_metrics_1m = ["confirmation_suggestion_min"]
    append_asset_metrics(chain_monitor_confirmation_metrics_assets_1m, chain_monitor_confirmation_metrics_1m, '1m', dest)

    chain_monitor_slashing_metrics_1d = ['IssFullParticipation', 'PenaltyNtv', 'SlashedNtv', 'StkPartRateMean']
    append_asset_metrics(["eth_cl"], chain_monitor_slashing_metrics_1d, '1d', dest)

    chain_monitor_slashing_metrics_1m = ['SlashPropEvCnt', 'SlashAttEvCnt', 'SlashEvCnt', 'SlashAttSurrEvCnt', 'SlashAttDblEvCnt', 'AttestCnt', 'AttestEpochCnt', 'AttestValCnt', 'AttestEpochValCnt', 'AttestValLateCnt', 'EpochCurr', 'EpochFinal', 'EpochJust']
    append_asset_metrics(["eth_cl"], chain_monitor_slashing_metrics_1m, '1m', dest)

    chain_monitor_eth_metrics_1b = ["block_count_consecutive_empty", "block_missed_slots", "block_base_fee",
                                    "block_priority_fee"]
    append_asset_metrics(["eth"], chain_monitor_eth_metrics_1b, '1b', dest)

    chain_monitor_block_reorg_assets_1b = ["btc", "bch", "dash", "eth", "ltc", "zec"]
    chain_monitor_block_reorg_metrics_1b = ["block_reorg_depth"]
    append_asset_metrics(chain_monitor_block_reorg_assets_1b, chain_monitor_block_reorg_metrics_1b, '1b', dest)
