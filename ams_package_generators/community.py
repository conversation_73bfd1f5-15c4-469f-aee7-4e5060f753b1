def package_type():
    return "composite"

def package_script():
    return [
        "reference_rates_ge_1s",
        "principal_prices_ge_1s",
        "market_data_base {'market_type': 'spot,future,option', 'exchange': 'not(cme,erisx,dydx)', 'time_restriction': 'null'}",
        "cmbi_indexes_community",
        "pair_candles",
        "ube2 {'asset': 'btc,eth,ltc,dash,zec'}",
        "time_bound {'resource_name': 'ube2', 'start_boundary': '-P30D', 'end_boundary': 'PT0S'}"
    ]

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    default_assets = ["1inch", "aave", "ada", "ae_eth", "aion_eth", "algo", "alpha", "ant", "avaxc", "avaxp", "avaxx",
                      "bal", "bat", "bch", "bnb", "bnb_eth", "bsv", "btc", "btg", "btm_eth", "buidl_eth", "busd",
                      "comp", "cro", "crv", "crvusd_eth", "cvc", "dai", "dash", "dcr", "dgb", "doge", "dot", "drgn",
                      "elf", "eos", "eos_eth", "etc", "eth", "eurc_eth", "eurcv_eth", "fdusd_eth", "flow", "flow_evm",
                      "flow_native", "frax_eth", "ftt", "fun", "fxc_eth", "gas", "gno", "gnt", "grin", "gusd", "hbtc",
                      "hedg", "ht", "husd", "icp", "icx_eth", "kcs", "knc", "ldo", "lend", "leo_eos", "leo_eth", "link",
                      "loom", "lpt", "lrc_eth", "ltc", "lusd_eth", "maid", "mana", "matic_eth", "mkr", "nas_eth", "neo",
                      "nxm", "omg", "pax", "paxg", "pay", "perp", "pol_eth", "poly", "powr", "ppt", "pyusd_eth", "qash",
                      "qnt", "qtum_eth", "ren", "renbtc", "rep", "rev_eth", "sai", "sdai_eth", "shib_eth", "snt", "snx",
                      "srm", "steth_lido", "susde_eth", "sushi", "swrv", "trx", "trx_eth", "tusd", "tusd_eth",
                      "tusd_trx", "uma", "uni", "usdc", "usdc_avaxc", "usdc_eth", "usdc_avaxc", "usdc_trx", "usdd_eth",
                      "usde_eth", "usdk", "usdm_eth", "usdt", "usdt_avaxc", "usdt_eth", "usdt_omni", "usdt_eth",
                      "usdt_trx", "vet_eth", "vtc", "wbtc", "weth", "wnxm", "wtc", "xaut", "xem", "xlm", "xmr", "xrp",
                      "xtz", "xvg", "yfi", "zec", "zil_eth", "zrx"]

    group_metrics = ['AssetCompletionTime', 'AssetEODCompletionTime', 'TxCnt', 'TxCntSec']
    append_asset_metrics(default_assets, group_metrics, '1d', dest)

    group1_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth',
                     'etc', 'eth', 'eurc_eth', 'eurcv_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'fxc_eth', 'gno',
                     'gnt', 'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth',
                     'link', 'loom', 'lpt', 'lrc_eth', 'ltc', 'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo',
                     'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash',
                     'qnt', 'qtum_eth', 'ren', 'renbtc', 'rev_eth', 'sai', 'sdai_eth', 'shib_eth', 'snt', 'snx',
                     'susde_eth', 'sushi', 'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni', 'usdc', 'usdc_avaxc',
                     'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt', 'usdt_avaxc',
                     'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut',
                     'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx']
    group1_metrics = ['NVTAdjFF']
    append_asset_metrics(group1_assets, group1_metrics, '1d', dest)

    group2_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht',
                     'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'lrc_eth', 'ltc',
                     'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay',
                     'perp', 'pol_eth', 'poly', 'powr', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth', 'ren', 'renbtc',
                     'rev_eth', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdk', 'usdm_eth', 'usdt', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc',
                     'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx']
    group2_metrics = ['NVTAdjFF90']
    append_asset_metrics(group2_assets, group2_metrics, '1d', dest)

    group3_assets = ['1inch', 'aave', 'ada', 'ae_eth', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal',
                     'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro',
                     'crv', 'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht',
                     'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'lrc_eth', 'ltc',
                     'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay',
                     'perp', 'pol_eth', 'poly', 'powr', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth', 'ren', 'renbtc',
                     'rev_eth', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth',
                     'tusd_trx', 'uma', 'uni', 'usdc', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth',
                     'usdm_eth', 'usdt', 'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vet_eth', 'vtc', 'wbtc',
                     'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zil_eth', 'zrx']
    group3_metrics = ['CapMVRVFF']
    append_asset_metrics(group3_assets, group3_metrics, '1d', dest)

    group4_assets = ['1inch', 'aave', 'ada', 'aion_eth', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal', 'bat', 'bch',
                     'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'btm_eth', 'buidl_eth', 'busd', 'comp', 'cro', 'crv',
                     'crvusd_eth', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth', 'etc',
                     'eth', 'eurc_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'gno', 'gnt', 'grin', 'gusd', 'hbtc',
                     'hedg', 'ht', 'husd', 'icp', 'icx_eth', 'knc', 'ldo', 'lend', 'leo_eth', 'link', 'loom', 'lpt',
                     'lrc_eth', 'ltc', 'lusd_eth', 'mana', 'matic_eth', 'mkr', 'nas_eth', 'neo', 'nxm', 'omg', 'pax',
                     'paxg', 'pay', 'perp', 'pol_eth', 'poly', 'powr', 'ppt', 'pyusd_eth', 'qash', 'qnt', 'qtum_eth',
                     'ren', 'renbtc', 'rev_eth', 'sai', 'sdai_eth', 'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi',
                     'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni', 'usdc', 'usdc_avaxc', 'usdc_eth', 'usdc_trx',
                     'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt', 'usdt_avaxc', 'usdt_eth', 'usdt_omni',
                     'usdt_trx', 'vet_eth', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xmr', 'xrp', 'xtz',
                     'xvg', 'yfi', 'zec', 'zil_eth', 'zrx']
    group4_metrics = ['CapMrktFFUSD']
    append_asset_metrics(group4_assets, group4_metrics, '1d', dest)

    group5_assets = ['1inch', 'aave', 'ada', 'algo', 'alpha', 'ant', 'avaxc', 'avaxp', 'bal', 'bat', 'bch', 'bnb',
                     'bnb_eth', 'bsv', 'btc', 'btg', 'buidl_eth', 'busd', 'comp', 'cro', 'crv', 'crvusd_eth', 'cvc',
                     'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'drgn', 'elf', 'eos_eth', 'etc', 'eth', 'eurc_eth',
                     'eurcv_eth', 'fdusd_eth', 'frax_eth', 'ftt', 'fun', 'fxc_eth', 'gno', 'gnt', 'gusd', 'hbtc',
                     'hedg', 'ht', 'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'ltc', 'lusd_eth',
                     'mana', 'matic_eth', 'mkr', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'pol_eth', 'poly',
                     'powr', 'ppt', 'pyusd_eth', 'qash', 'qnt', 'ren', 'renbtc', 'rev_eth', 'sai', 'sdai_eth',
                     'shib_eth', 'snt', 'snx', 'susde_eth', 'sushi', 'trx_eth', 'tusd_eth', 'tusd_trx', 'uma', 'uni',
                     'usdc', 'usdc_avaxc', 'usdc_eth', 'usdc_trx', 'usdd_eth', 'usde_eth', 'usdk', 'usdm_eth', 'usdt',
                     'usdt_avaxc', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut',
                     'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zrx']
    group5_metrics = ['SplyFF']
    append_asset_metrics(group5_assets, group5_metrics, '1d', dest)

    group6_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                     'eos', 'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo', 'trx', 'vtc',
                     'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group6_metrics = ['BlkCnt']
    append_asset_metrics(group6_assets, group6_metrics, '1d', dest)

    group7_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                     'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo', 'vtc', 'xem', 'xlm',
                     'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group7_metrics = ['FeeMeanUSD', 'FeeTotUSD']
    append_asset_metrics(group7_assets, group7_metrics, '1d', dest)

    group8_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                     'etc', 'eth', 'flow', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'vtc', 'xem', 'xlm', 'xmr',
                     'xrp', 'xtz', 'xvg', 'zec']
    group8_metrics = ['FeeTotNtv']
    append_asset_metrics(group8_assets, group8_metrics, '1d', dest)

    group9_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                     'etc', 'eth', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'neo', 'vtc', 'xem', 'xlm', 'xmr',
                     'xrp', 'xtz', 'xvg', 'zec']
    group9_metrics = ['FeeMedUSD']
    append_asset_metrics(group9_assets, group9_metrics, '1d', dest)

    group10_assets = ['ada', 'algo', 'avaxc', 'avaxp', 'avaxx', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb',
                      'doge', 'etc', 'eth', 'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'vtc', 'xem', 'xlm', 'xmr',
                      'xrp', 'xtz', 'xvg', 'zec']
    group10_metrics = ['FeeMedNtv']
    append_asset_metrics(group10_assets, group10_metrics, '1d', dest)

    group11_assets = ['ada', 'algo', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow',
                      'flow_evm', 'flow_native', 'grin', 'icp', 'ltc', 'vtc', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg',
                      'zec']
    group11_metrics = ['FeeMeanNtv']
    append_asset_metrics(group11_assets, group11_metrics, '1d', dest)

    group12_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'eth_cl', 'flow',
                      'flow_native', 'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group12_metrics = ['IssContNtv']
    append_asset_metrics(group12_assets, group12_metrics, '1d', dest)

    group13_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow',
                      'flow_native', 'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group13_metrics = ['IssContPctAnn', 'IssContPctDay']
    append_asset_metrics(group13_assets, group13_metrics, '1d', dest)

    group14_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow_native',
                      'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group14_metrics = ['IssContUSD']
    append_asset_metrics(group14_assets, group14_metrics, '1d', dest)

    group15_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'neo',
                      'vtc', 'xmr', 'xvg', 'zec']
    group15_metrics = ['BlkSizeMeanByte']
    append_asset_metrics(group15_assets, group15_metrics, '1d', dest)

    group16_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'flow_native', 'grin',
                      'ltc', 'vtc', 'xmr', 'xtz', 'xvg', 'zec']
    group16_metrics = ['RevNtv', 'RevUSD']
    append_asset_metrics(group16_assets, group16_metrics, '1d', dest)

    group17_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc', 'vtc',
                      'xmr', 'xtz', 'xvg', 'zec']
    group17_metrics = ['RevAllTimeUSD']
    append_asset_metrics(group17_assets, group17_metrics, '1d', dest)

    group18_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc', 'vtc',
                      'xmr', 'xvg', 'zec']
    group18_metrics = ['DiffLast', 'DiffMean']
    append_asset_metrics(group18_assets, group18_metrics, '1d', dest)

    group19_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'vtc', 'xmr',
                      'xvg', 'zec']
    group19_metrics = ['FeeByteMeanNtv']
    append_asset_metrics(group19_assets, group19_metrics, '1d', dest)

    group20_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'doge', 'etc', 'eth', 'ltc', 'vtc', 'xmr', 'zec']
    group20_metrics = ['HashRate', 'RevHashNtv', 'RevHashUSD']
    append_asset_metrics(group20_assets, group20_metrics, '1d', dest)

    group21_assets = ['bch', 'bsv', 'btc', 'btg', 'doge', 'etc', 'eth', 'grin', 'ltc', 'neo', 'xlm', 'xmr', 'xrp',
                      'xtz', 'zec']
    group21_metrics = ['SplyExpFut10yr']
    append_asset_metrics(group21_assets, group21_metrics, '1d', dest)

    group22_assets = ['btc']
    group22_metrics = ['HashRate30d', 'SplyMiner0HopAllNtv', 'SplyMiner0HopAllUSD', 'SplyMiner1HopAllNtv',
                       'SplyMiner1HopAllUSD']
    append_asset_metrics(group22_assets, group22_metrics, '1d', dest)

    group23_assets = ['btc', 'etc', 'eth', 'xmr']
    group23_metrics = ['RevHashRateNtv', 'RevHashRateUSD']
    append_asset_metrics(group23_assets, group23_metrics, '1d', dest)

    group24_assets = ['btc', 'eth']
    group24_metrics = ['FlowInExNtv', 'FlowInExUSD', 'FlowOutExNtv', 'FlowOutExUSD', 'FlowTfrFromExCnt']
    append_asset_metrics(group24_assets, group24_metrics, '1d', dest)

    group25_assets = ['btc', 'ltc']
    group25_metrics = ['BlkWghtMean', 'BlkWghtTot']
    append_asset_metrics(group25_assets, group25_metrics, '1d', dest)

    group26_assets = ['etc', 'eth']
    group26_metrics = ['GasLmtBlk', 'GasLmtBlkMean', 'GasLmtTx', 'GasLmtTxMean']
    append_asset_metrics(group26_assets, group26_metrics, '1d', dest)

    group27_assets = ['etc', 'eth', 'flow_evm']
    group27_metrics = ['GasUsedTx', 'GasUsedTxMean']
    append_asset_metrics(group27_assets, group27_metrics, '1d', dest)

    group28_exclude_assets = sorted(set(default_assets) - {'avaxx', 'eos', 'flow', 'flow_evm', 'flow_native', 'fxc_eth',
                                                           'grin', 'kcs', 'leo_eos', 'steth_lido', 'trx', 'xem', 'xmr'})
    group28_metrics = ['CapAct1yrUSD', 'CapMVRVCur', 'CapRealUSD']
    append_asset_metrics(group28_exclude_assets, group28_metrics, '1d', dest)

    group29_exclude_assets = sorted(set(default_assets) - {'avaxx', 'eos', 'flow', 'flow_evm', 'flow_native', 'fxc_eth',
                                                           'grin', 'kcs', 'steth_lido', 'trx', 'xmr'})
    group29_metrics = ['NVTAdj90']
    append_asset_metrics(group29_exclude_assets, group29_metrics, '1d', dest)

    group30_exclude_assets = sorted(set(default_assets) - {'avaxx', 'eos', 'flow', 'flow_evm', 'flow_native', 'grin',
                                                           'leo_eos', 'steth_lido', 'trx', 'xem', 'xmr'})
    group30_metrics = ['SplyAct10yr', 'SplyAct180d', 'SplyAct1d', 'SplyAct1yr', 'SplyAct2yr', 'SplyAct30d',
                       'SplyAct3yr', 'SplyAct4yr', 'SplyAct5yr', 'SplyAct7d', 'SplyAct90d', 'SplyActEver',
                       'SplyActPct1yr']
    append_asset_metrics(group30_exclude_assets, group30_metrics, '1d', dest)

    group31_exclude_assets = sorted(
        set(default_assets) - {'avaxx', 'eos', 'flow', 'flow_evm', 'flow_native', 'grin', 'trx', 'xem', 'xmr'})
    group31_metrics = ['VelCur1yr']
    append_asset_metrics(group31_exclude_assets, group31_metrics, '1d', dest)

    group32_exclude_assets = sorted(
        set(default_assets) - {'dcr', 'eos', 'flow', 'flow_evm', 'fxc_eth', 'kcs', 'steth_lido', 'xrp'})
    group32_metrics = ['IssTotUSD']
    append_asset_metrics(group32_exclude_assets, group32_metrics, '1d', dest)

    group33_exclude_assets = sorted(set(default_assets) - {'dcr', 'eos', 'flow_evm', 'steth_lido', 'xrp'})
    group33_metrics = ['IssTotNtv']
    append_asset_metrics(group33_exclude_assets, group33_metrics, '1d', dest)

    group34_exclude_assets = sorted(set(default_assets) - {'eos', 'flow', 'flow_evm', 'flow_native', 'fxc_eth', 'grin',
                                                           'kcs', 'leo_eos', 'steth_lido', 'trx', 'xem', 'xmr'})
    group34_metrics = ['AdrBalUSD100Cnt', 'AdrBalUSD100KCnt', 'AdrBalUSD10Cnt', 'AdrBalUSD10KCnt', 'AdrBalUSD10MCnt',
                       'AdrBalUSD1Cnt', 'AdrBalUSD1KCnt', 'AdrBalUSD1MCnt', 'SplyAdrBalUSD1', 'SplyAdrBalUSD10',
                       'SplyAdrBalUSD100', 'SplyAdrBalUSD100K', 'SplyAdrBalUSD10K', 'SplyAdrBalUSD10M',
                       'SplyAdrBalUSD1K', 'SplyAdrBalUSD1M']
    append_asset_metrics(group34_exclude_assets, group34_metrics, '1d', dest)

    group35_exclude_assets = sorted(set(default_assets) - {'eos', 'flow', 'flow_evm', 'flow_native', 'grin', 'leo_eos',
                                                           'steth_lido', 'trx', 'xem', 'xmr'})
    group35_metrics = ['AdrBal1in100KCnt', 'AdrBal1in100MCnt', 'AdrBal1in10BCnt', 'AdrBal1in10KCnt', 'AdrBal1in10MCnt',
                       'AdrBal1in1BCnt', 'AdrBal1in1KCnt', 'AdrBal1in1MCnt', 'AdrBalCnt', 'AdrBalNtv0.001Cnt',
                       'AdrBalNtv0.01Cnt', 'AdrBalNtv0.1Cnt', 'AdrBalNtv100Cnt', 'AdrBalNtv100KCnt', 'AdrBalNtv10Cnt',
                       'AdrBalNtv10KCnt', 'AdrBalNtv1Cnt', 'AdrBalNtv1KCnt', 'AdrBalNtv1MCnt', 'NDF', 'SER',
                       'SplyAdrBal1in100K', 'SplyAdrBal1in100M', 'SplyAdrBal1in10B', 'SplyAdrBal1in10K',
                       'SplyAdrBal1in10M', 'SplyAdrBal1in1B', 'SplyAdrBal1in1K', 'SplyAdrBal1in1M',
                       'SplyAdrBalNtv0.001', 'SplyAdrBalNtv0.01', 'SplyAdrBalNtv0.1', 'SplyAdrBalNtv1',
                       'SplyAdrBalNtv10', 'SplyAdrBalNtv100', 'SplyAdrBalNtv100K', 'SplyAdrBalNtv10K',
                       'SplyAdrBalNtv1K', 'SplyAdrBalNtv1M', 'SplyAdrTop100', 'SplyAdrTop10Pct', 'SplyAdrTop1Pct']
    append_asset_metrics(group35_exclude_assets, group35_metrics, '1d', dest)

    group36_exclude_assets = sorted(
        set(default_assets) - {'eos', 'flow', 'flow_evm', 'flow_native', 'grin', 'trx', 'xmr'})
    group36_metrics = ['NVTAdj']
    append_asset_metrics(group36_exclude_assets, group36_metrics, '1d', dest)

    group37_exclude_assets = sorted(set(default_assets) - {'eos', 'flow_evm'})
    group37_metrics = ['SplyCur']
    append_asset_metrics(group37_exclude_assets, group37_metrics, '1d', dest)

    group38_exclude_assets = sorted(
        set(default_assets) - {'eos', 'flow_evm', 'fxc_eth', 'kcs', 'steth_lido', 'vet'})
    group38_metrics = ['CapMrktCurUSD']
    append_asset_metrics(group38_exclude_assets, group38_metrics, '1d', dest)

    group39_exclude_assets = sorted(set(default_assets) - {'eurcv_eth', 'fxc_eth', 'steth_lido'})
    group39_metrics = ['PriceUSD']
    append_asset_metrics(group39_exclude_assets, group39_metrics, '1d', dest)

    group40_exclude_assets = sorted(set(default_assets) - {'flow', 'flow_evm'})
    group40_metrics = ['TxTfrCnt']
    append_asset_metrics(group40_exclude_assets, group40_metrics, '1d', dest)

    group41_exclude_assets = sorted(
        set(default_assets) - {'flow', 'flow_evm', 'fxc_eth', 'grin', 'kcs', 'steth_lido', 'xmr'})
    group41_metrics = ['TxTfrValAdjUSD', 'TxTfrValMeanUSD', 'TxTfrValMedUSD']
    append_asset_metrics(group41_exclude_assets, group41_metrics, '1d', dest)

    group42_exclude_assets = sorted(set(default_assets) - {'flow', 'flow_evm', 'grin', 'xmr'})
    group42_metrics = ['AdrActCnt', 'TxTfrValAdjNtv', 'TxTfrValMeanNtv', 'TxTfrValMedNtv']
    append_asset_metrics(group42_exclude_assets, group42_metrics, '1d', dest)

    group43_exclude_assets = sorted(set(default_assets) - {'fxc_eth', 'kcs', 'steth_lido', 'vet'})
    group43_metrics = ['PriceBTC', 'ROI1yr', 'ROI30d', 'VtyDayRet180d', 'VtyDayRet30d']
    append_asset_metrics(group43_exclude_assets, group43_metrics, '1d', dest)
