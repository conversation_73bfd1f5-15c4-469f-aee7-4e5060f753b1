def package_type():
    return "composite"

def package_script():
    research_metrics = [
        "volume_reported_spot_usd_1d",
        "volume_trusted_spot_usd_1d",
        "volume_reported_future_usd_1d",
        "open_interest_reported_future_usd",
        "AdrActCnt",
        'AdrBalUSD100Cnt', 'AdrBalUSD100KCnt', 'AdrBalUSD10Cnt', 'AdrBalUSD10KCnt', 'AdrBalUSD10MCnt',
        'AdrBalUSD1Cnt', 'AdrBalUSD1KCnt', 'AdrBalUSD1MCnt',
        "SplyCur",
        "SplyFF",
        "IssTotNtv",
        "SplyActPct1yr",
        "SplyAct30d",
        "SplyAdrBalNtv0.01",
        "RevUSD",
        "FeeTotUSD",
        "GasUsedTx",
        "HashRate",
        "TxTfrValAdjNtv",
        "FlowInExUSD",
        "FlowOutExUSD",
        "FlowTfrToExCnt",
        "FlowTfrFromExCnt",
        "CapMrktCurUSD",
        "CapMrktFFUSD",
        "CapMVRVCur",
        "ReferenceRateUSD"
    ]

    return [
        "eap",
        "- asset_metrics {'asset', 'frequency': 'not(1d)', 'metric': 'not(%s)'}" % ','.join(research_metrics)
    ]
