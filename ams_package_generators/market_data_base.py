import json
from typing import List

def package_script():
    """
    Generate market_data_base.json package with spot/derivatives and defi_spot exchanges.

    Returns:
       Dictionary containing the market_data_base package content

    Raises:
       FileNotFoundError: If the exchange file cannot be found
       json.JSONDecodeError: If the exchange file is not valid JSON
    """
    non_defi_exchanges = get_non_defi_exchanges("exchange.json")
    defi_exchanges = get_defi_exchanges("exchange.json")

    non_defi_exchange_list = ','.join(non_defi_exchanges)
    defi_exchange_list = ','.join(defi_exchanges)

    return [
        f"{{'market_type': 'spot,future,option', 'exchange': '{non_defi_exchange_list}', 'time_restriction': 'null,:now-1h'}}",
        f"{{'market_type': 'defi_spot', 'exchange': '{defi_exchange_list}', 'time_restriction': 'null,:now-1h'}}"
    ]

def normalize_exchange_name(exchange_name: str) -> str:
    """
    Normalize an exchange name by converting to lowercase and replacing hyphens with underscores.

    Args:
        exchange_name: The exchange name to normalize

    Returns:
        Normalized exchange name
    """
    return exchange_name.lower().replace('-', '_')

def get_non_defi_exchanges(exchange_file_path) -> List[str]:
    """
    Extract non-DeFi exchange names (where defi is false or not present) from the exchange.json file.

    Args:
        exchange_file_path: Path to the exchange.json file

    Returns:
        List of non-DeFi exchange names in lowercase with hyphens replaced by underscores

    Raises:
        FileNotFoundError: If the exchange file cannot be found
        json.JSONDecodeError: If the exchange file is not valid JSON
    """
    non_defi_exchanges = []
    with open(exchange_file_path, 'r') as file:
        data = json.load(file)

    for exchange in data:
        if 'name' in exchange and not exchange.get('defi', False):
            exchange_name = normalize_exchange_name(exchange['name'])
            non_defi_exchanges.append(exchange_name)

    return sorted(non_defi_exchanges)

def get_defi_exchanges(exchange_file_path) -> List[str]:
    """
    Extract DeFi exchange names (where defi=true) from the exchange.json file.

    Args:
        exchange_file_path: Path to the exchange.json file

    Returns:
        List of DeFi exchange names in lowercase with hyphens replaced by underscores

    Raises:
        FileNotFoundError: If the exchange file cannot be found
        json.JSONDecodeError: If the exchange file is not valid JSON
    """
    defi_exchanges = []
    with open(exchange_file_path, 'r') as file:
        data = json.load(file)

    for exchange in data:
        if 'name' in exchange and exchange.get('defi') is True:
            exchange_name = normalize_exchange_name(exchange['name'])
            defi_exchanges.append(exchange_name)

    return sorted(defi_exchanges)
