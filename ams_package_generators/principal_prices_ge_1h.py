import json

def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    am = dest
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        asset = currency['cm_ticker']
        metadata = currency['metadata']
        if metadata.get("has_principal_market_price"):
            am[(asset, "1h")].add("principal_market_price_usd")
            am[(asset, "1d")].add("principal_market_price_usd")
            am[(asset, "1h")].add("principal_market_usd")
            am[(asset, "1d")].add("principal_market_usd")
