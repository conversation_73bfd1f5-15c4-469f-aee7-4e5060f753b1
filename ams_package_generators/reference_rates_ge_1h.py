import json

def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    am = dest
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        asset = currency['cm_ticker']
        metadata = currency['metadata']
        if metadata["hasReferenceRate"]:
            am[(asset, "1h")].add("ReferenceRate")
            am[(asset, "1d")].add("ReferenceRate")
            am[(asset, "1h")].add("ReferenceRateUSD")
            am[(asset, "1d")].add("ReferenceRateUSD")
            am[(asset, "1h")].add("ReferenceRateEUR")
            am[(asset, "1d")].add("ReferenceRateEUR")
            am[(asset, "1h")].add("ReferenceRateBTC")
            am[(asset, "1d")].add("ReferenceRateBTC")
            am[(asset, "1h")].add("ReferenceRateETH")
            am[(asset, "1d")].add("ReferenceRateETH")
