def package_type():
    return "composite"

def compute_and_append_asset_metrics(dest):
    from ams_package_generators.asset_metrics import append_asset_metrics

    group1_assets = ["1inch", "aave", "ada", "alpha", "ant", "bal", "bat", "bch", "bnb", "bnb_eth", "bsv", "btc", "btg",
                     "busd", "comp", "cro", "crv", "cvc", "dai", "dash", "dcr", "dgb", "doge", "dot", "drgn", "elf",
                     "eos", "eos_eth", "etc", "eth", "ftt", "fun", "fxc_eth", "gas", "gno", "gnt", "grin", "gusd",
                     "hbtc", "hedg", "ht", "husd", "kcs", "knc", "lend", "leo_eth", "leo_eos", "link", "loom", "ltc",
                     "maid", "mana", "mkr", "neo", "nxm", "omg", "pax", "paxg", "pay", "poly", "powr", "ppt", "qash",
                     "ren", "renbtc", "rep", "sai", "snt", "snx", "srm", "sushi", "swrv", "trx", "trx_eth", "tusd",
                     "uma", "uni", "usdc", "usdk", "usdt", "usdt_omni", "usdt_eth", "usdt_trx", "vtc", "wbtc", "weth",
                     "wnxm", "wtc", "xaut", "xem", "xlm", "xmr", "xrp", "xtz", "xvg", "yfi", "zec", "zrx"]
    group1_metrics = ["AssetCompletionTime", "AssetEODCompletionTime", "TxCnt", "TxCntSec", "TxTfrCnt"]
    append_asset_metrics(group1_assets, group1_metrics, "1d", dest)

    # Include Groups
    group_include1_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'eos', 'etc', 'eth',
                             'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group_include1_metrics = ['BlkCnt', 'BlkHgt', 'BlkIntMean']
    append_asset_metrics(group_include1_assets, group_include1_metrics, "1d", dest)

    group_include2_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc',
                             'neo', 'vtc', 'xmr', 'xvg', 'zec']
    group_include2_metrics = ['BlkSizeByte', 'BlkSizeMeanByte', 'TxMeanByte', 'TxTfrMeanByte']
    append_asset_metrics(group_include2_assets, group_include2_metrics, "1d", dest)

    group_include3_assets = ['etc', 'eth']
    group_include3_metrics = ['BlkUncCnt', 'BlkUncRevPct', 'BlkUncRwd', 'BlkUncRwdUSD', 'ContERC20Cnt', 'ContERC721Cnt',
                              'GasLmtBlk', 'GasLmtBlkMean', 'GasLmtTx', 'GasLmtTxMean', 'GasUsedTx', 'GasUsedTxMean',
                              'TxERC20Cnt', 'TxERC721Cnt', 'TxTfrERC20Cnt', 'TxTfrERC721Cnt', 'TxTfrTknCnt', 'TxTknCnt']
    append_asset_metrics(group_include3_assets, group_include3_metrics, "1d", dest)

    group_include4_assets = ['btc', 'ltc']
    group_include4_metrics = ['BlkWghtMean', 'BlkWghtTot', 'FeeWghtMeanNtv']
    append_asset_metrics(group_include4_assets, group_include4_metrics, "1d", dest)

    group_include5_assets = ['bch', 'bsv', 'btc', 'btg', 'doge', 'etc', 'eth', 'grin', 'ltc', 'neo', 'xlm', 'xmr',
                             'xrp', 'xtz', 'zec']
    group_include5_metrics = ['CapFutExp10yrUSD', 'SplyExpFut10yr']
    append_asset_metrics(group_include5_assets, group_include5_metrics, "1d", dest)

    group_include6_assets = ['aave', 'ada', 'algo', 'alpha', 'ant', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc',
                             'btg', 'busd', 'comp', 'cro', 'crv', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot',
                             'drgn', 'elf', 'eos_eth', 'etc', 'eth', 'ftt', 'fun', 'gno', 'gnt', 'grin', 'gusd', 'hbtc',
                             'hedg', 'ht', 'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'ltc',
                             'mana', 'mkr', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'poly', 'powr', 'ppt',
                             'qash', 'qnt', 'ren', 'renbtc', 'rev_eth', 'sai', 'snt', 'trx_eth', 'uma', 'uni', 'usdc',
                             'usdk', 'usdt', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc',
                             'xaut', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zrx']
    group_include6_metrics = ['CapMrktFFUSD']
    append_asset_metrics(group_include6_assets, group_include6_metrics, "1d", dest)

    group_include7_assets = ['aave', 'ada', 'algo', 'alpha', 'ant', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc',
                             'btg', 'busd', 'comp', 'cro', 'crv', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge', 'dot',
                             'drgn', 'elf', 'etc', 'eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht', 'husd',
                             'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'ltc', 'mana', 'mkr', 'neo', 'nxm',
                             'omg', 'pax', 'paxg', 'pay', 'perp', 'poly', 'powr', 'qash', 'qnt', 'ren', 'renbtc',
                             'rev_eth', 'snt', 'trx_eth', 'uma', 'uni', 'usdc', 'usdt', 'usdt_eth', 'usdt_omni',
                             'usdt_trx', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg',
                             'yfi', 'zec', 'zrx']
    group_include7_metrics = ['CapMVRVFF']
    append_asset_metrics(group_include7_assets, group_include7_metrics, "1d", dest)

    group_include8_assets = ['etc', 'eth', 'xtz']
    group_include8_metrics = ['ContBalCnt', 'ContCnt', 'TxContCallCnt', 'TxContCallSuccCnt', 'TxContCreatCnt',
                              'TxContDestCnt']
    append_asset_metrics(group_include8_assets, group_include8_metrics, "1d", dest)

    group_include9_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc',
                             'vtc', 'xmr', 'xvg', 'zec']
    group_include9_metrics = ['DiffMean']
    append_asset_metrics(group_include9_assets, group_include9_metrics, "1d", dest)

    group_include10_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc', 'vtc',
                              'xmr', 'xvg', 'zec']
    group_include10_metrics = ['FeeByteMeanNtv']
    append_asset_metrics(group_include10_assets, group_include10_metrics, "1d", dest)

    group_include11_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin', 'ltc',
                              'vtc', 'xmr', 'xtz', 'xvg', 'zec']
    group_include11_metrics = ['FeeRevPct', 'RevAllTimeUSD', 'RevNtv', 'RevUSD']
    append_asset_metrics(group_include11_assets, group_include11_metrics, "1d", dest)

    group_include12_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin',
                              'ltc', 'vtc', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group_include12_metrics = ['FeeMeanNtv', 'FeeMedNtv', 'FeeTotNtv']
    append_asset_metrics(group_include12_assets, group_include12_metrics, "1d", dest)

    group_include13_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin',
                              'ltc', 'neo', 'vtc', 'xem', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'zec']
    group_include13_metrics = ['FeeMeanUSD', 'FeeMedUSD', 'FeeTotUSD']
    append_asset_metrics(group_include13_assets, group_include13_metrics, "1d", dest)

    group_include14_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'eth_cl',
                              'grin', 'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group_include14_metrics = ['IssContNtv']
    append_asset_metrics(group_include14_assets, group_include14_metrics, "1d", dest)

    group_include15_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'grin',
                              'ltc', 'neo', 'trx', 'vtc', 'xem', 'xlm', 'xmr', 'xtz', 'xvg', 'zec']
    group_include15_metrics = ['IssContPctAnn', 'IssContPctDay', 'IssContUSD']
    append_asset_metrics(group_include15_assets, group_include15_metrics, "1d", dest)

    group_include16_assets = ['aave', 'ada', 'algo', 'alpha', 'ant', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv',
                              'btc', 'btg', 'busd', 'comp', 'cro', 'crv', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge',
                              'dot', 'drgn', 'elf', 'eos_eth', 'etc', 'eth', 'ftt', 'fun', 'fxc_eth', 'gno', 'gnt',
                              'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom',
                              'lpt', 'ltc', 'mana', 'mkr', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'poly',
                              'powr', 'ppt', 'qash', 'qnt', 'ren', 'renbtc', 'rev_eth', 'sai', 'snt', 'trx_eth', 'uma',
                              'uni', 'usdc', 'usdk', 'usdt', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vtc', 'wbtc', 'weth',
                              'wnxm', 'wtc', 'xaut', 'xlm', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zrx']
    group_include16_metrics = ['NVTAdjFF']
    append_asset_metrics(group_include16_assets, group_include16_metrics, "1d", dest)

    group_include17_assets = ['aave', 'ada', 'algo', 'alpha', 'ant', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv',
                              'btc', 'btg', 'busd', 'comp', 'cro', 'crv', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge',
                              'dot', 'drgn', 'elf', 'etc', 'eth', 'ftt', 'fun', 'gno', 'gnt', 'gusd', 'hbtc', 'ht',
                              'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link', 'loom', 'lpt', 'ltc', 'mana', 'mkr',
                              'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp', 'poly', 'powr', 'qash', 'qnt', 'ren',
                              'renbtc', 'rev_eth', 'snt', 'trx_eth', 'uma', 'uni', 'usdc', 'usdk', 'usdt', 'usdt_eth',
                              'usdt_omni', 'usdt_trx', 'vtc', 'wbtc', 'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xrp',
                              'xtz', 'xvg', 'yfi', 'zec', 'zrx']
    group_include17_metrics = ['NVTAdjFF90']
    append_asset_metrics(group_include17_assets, group_include17_metrics, "1d", dest)

    group_include18_assets = ['btc', 'etc', 'eth', 'xmr']
    group_include18_metrics = ['PuellMulCont', 'PuellMulRev', 'PuellMulTot']
    append_asset_metrics(group_include18_assets, group_include18_metrics, "1d", dest)

    group_include19_assets = ['bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'ltc', 'vtc', 'xvg', 'zec']
    group_include19_metrics = ['SOPR', 'SOPROut', 'SplyRvv180d', 'SplyRvv1yr', 'SplyRvv2yr', 'SplyRvv30d', 'SplyRvv3yr',
                               'SplyRvv4yr', 'SplyRvv5yr', 'SplyRvv7d', 'SplyRvv90d', 'TxTfrValDayDst',
                               'TxTfrValDayDstMean']
    append_asset_metrics(group_include19_assets, group_include19_metrics, "1d", dest)

    group_include20_assets = ['aave', 'ada', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv', 'btc', 'btg', 'busd', 'comp',
                              'cro', 'crv', 'dash', 'dcr', 'dgb', 'doge', 'dot', 'etc', 'eth', 'ftt', 'gno', 'grin',
                              'ht', 'husd', 'link', 'ltc', 'mkr', 'neo', 'pax', 'paxg', 'ren', 'trx_eth', 'uma', 'uni',
                              'usdc', 'usdt', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'xaut', 'xlm', 'xmr', 'xrp', 'xtz',
                              'yfi', 'zec', 'zrx']
    group_include20_metrics = ['SplyExpFut10yrCMBI']
    append_asset_metrics(group_include20_assets, group_include20_metrics, "1d", dest)

    group_include21_assets = ['aave', 'ada', 'algo', 'alpha', 'ant', 'bal', 'bat', 'bch', 'bnb', 'bnb_eth', 'bsv',
                              'btc', 'btg', 'busd', 'comp', 'cro', 'crv', 'cvc', 'dai', 'dash', 'dcr', 'dgb', 'doge',
                              'dot', 'drgn', 'elf', 'eos_eth', 'etc', 'eth', 'ftt', 'fun', 'fxc_eth', 'gno', 'gnt',
                              'grin', 'gusd', 'hbtc', 'hedg', 'ht', 'husd', 'icp', 'knc', 'lend', 'leo_eth', 'link',
                              'loom', 'lpt', 'ltc', 'mana', 'mkr', 'neo', 'nxm', 'omg', 'pax', 'paxg', 'pay', 'perp',
                              'poly', 'powr', 'ppt', 'qash', 'qnt', 'ren', 'renbtc', 'rev_eth', 'sai', 'snt', 'trx_eth',
                              'uma', 'uni', 'usdc', 'usdk', 'usdt', 'usdt_eth', 'usdt_omni', 'usdt_trx', 'vtc', 'wbtc',
                              'weth', 'wnxm', 'wtc', 'xaut', 'xlm', 'xmr', 'xrp', 'xtz', 'xvg', 'yfi', 'zec', 'zrx']
    group_include21_metrics = ['SplyFF']
    append_asset_metrics(group_include21_assets, group_include21_metrics, "1d", dest)

    group_include22_assets = ['zec']
    group_include22_metrics = ['SplyShld', 'TxShldCnt', 'TxShldFullCnt']
    append_asset_metrics(group_include22_assets, group_include22_metrics, "1d", dest)

    group_include23_assets = ['btc']
    group_include23_metrics = ['FlowInBMXNtv', 'FlowInBMXUSD', 'FlowInHUONtv', 'FlowInHUOUSD', 'FlowNetBMXNtv',
                               'FlowNetBMXUSD', 'FlowOutBMXNtv', 'FlowOutBMXUSD', 'FlowOutHUONtv', 'FlowOutHUOUSD',
                               'FlowTfrInBMXCnt', 'FlowTfrInHUOCnt', 'FlowTfrOutBMXCnt', 'FlowTfrOutHUOCnt',
                               'SplyBMXNtv', 'SplyBMXUSD', 'SplyUTXOLoss', 'SplyUTXOProf', 'UTXOAgeMean', 'UTXOAgeMed',
                               'UTXOAgeValMean', 'UTXODay', 'UTXOLossCnt', 'UTXOLossUnrealUSD', 'UTXOProfCnt', 'UTXOProfUnrealUSD'
                               'FlowTfrInARKCnt', 'FlowTfrOutARKCnt', 'FlowInARKNtv', 'FlowNetARKNtv', 'FlowOutARKNtv',
                               'SplyARKNtv', 'FlowInARKUSD', 'FlowNetARKUSD', 'FlowOutARKUSD', 'SplyARKUSD', 'FlowTfrInBLKCnt',
                               'FlowTfrOutBLKCnt', 'FlowInBLKNtv', 'FlowNetBLKNtv', 'FlowOutBLKNtv', 'SplyBLKNtv', 'FlowInBLKUSD',
                               'FlowNetBLKUSD', 'FlowOutBLKUSD', 'SplyBLKUSD', 'FlowTfrInBWSCnt', 'FlowTfrOutBWSCnt', 'FlowInBWSNtv',
                               'FlowNetBWSNtv', 'FlowOutBWSNtv', 'SplyBWSNtv', 'FlowInBWSUSD', 'FlowNetBWSUSD', 'FlowOutBWSUSD', 'SplyBWSUSD',
                               'FlowTfrInGSCCnt', 'FlowTfrOutGSCCnt', 'FlowInGSCNtv', 'FlowNetGSCNtv', 'FlowOutGSCNtv', 'SplyGSCNtv',
                               'FlowInGSCUSD', 'FlowNetGSCUSD', 'FlowOutGSCUSD', 'SplyGSCUSD', 'FlowTfrInINVCnt', 'FlowTfrOutINVCnt',
                               'FlowInINVNtv', 'FlowNetINVNtv', 'FlowOutINVNtv', 'SplyINVNtv', 'FlowInINVUSD', 'FlowNetINVUSD',
                               'FlowOutINVUSD', 'SplyINVUSD', 'FlowTfrInTMPCnt', 'FlowTfrOutTMPCnt', 'FlowInTMPNtv', 'FlowNetTMPNtv',
                               'FlowOutTMPNtv', 'SplyTMPNtv', 'FlowInTMPUSD', 'FlowNetTMPUSD', 'FlowOutTMPUSD', 'SplyTMPUSD',
                               'FlowTfrInVANCnt', 'FlowTfrOutVANCnt', 'FlowInVANNtv', 'FlowNetVANNtv', 'FlowOutVANNtv', 'SplyVANNtv',
                               'FlowInVANUSD', 'FlowNetVANUSD', 'FlowOutVANUSD', 'SplyVANUSD', 'FlowTfrInVLKCnt', 'FlowTfrOutVLKCnt',
                               'FlowInVLKNtv', 'FlowNetVLKNtv', 'FlowOutVLKNtv', 'SplyVLKNtv', 'FlowInVLKUSD', 'FlowNetVLKUSD',
                               'FlowOutVLKUSD', 'SplyVLKUSD', 'FlowTfrInWDTCnt', 'FlowTfrOutWDTCnt', 'FlowInWDTNtv', 'FlowNetWDTNtv',
                               'FlowOutWDTNtv', 'SplyWDTNtv', 'FlowInWDTUSD', 'FlowNetWDTUSD', 'FlowOutWDTUSD', 'SplyWDTUSD',
                               'FlowTfrFromEtfCnt', 'FlowTfrToEtfCnt', 'TxEtfCnt', 'FlowInEtfNtv', 'FlowOutEtfNtv', 'SplyEtfNtv',
                               'FlowInEtfUSD', 'FlowOutEtfUSD', 'SplyEtfUSD', 'FlowTfrFromEtfInclCnt', 'FlowTfrToEtfInclCnt', 'FlowInEtfInclNtv',
                               'FlowOutEtfInclNtv', 'FlowInEtfInclUSD', 'FlowOutEtfInclUSD']
    append_asset_metrics(group_include23_assets, group_include23_metrics, "1d", dest)

    group_include24_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'etc', 'eth', 'ltc',
                              'neo', 'vtc', 'xvg', 'zec']
    group_include24_metrics = ['TxTfrValAdjByte']
    append_asset_metrics(group_include24_assets, group_include24_metrics, "1d", dest)

    group_include25_assets = ['btc', 'eth']
    group_include25_metrics = ['FlowInBFXNtv', 'FlowInBFXUSD', 'FlowInBITNtv', 'FlowInBITUSD', 'FlowInBNBNtv',
                               'FlowInBNBUSD', 'FlowInBSPNtv', 'FlowInBSPUSD', 'FlowInBTXNtv', 'FlowInBTXUSD',
                               'FlowInCRONtv', 'FlowInCROUSD', 'FlowInDERNtv', 'FlowInDERUSD', 'FlowInExInclNtv',
                               'FlowInExInclUSD', 'FlowInExNtv', 'FlowInExUSD', 'FlowInGEMNtv', 'FlowInGEMUSD',
                               'FlowInGIONtv', 'FlowInGIOUSD', 'FlowInHBTNtv', 'FlowInHBTUSD', 'FlowInKCNNtv',
                               'FlowInKCNUSD', 'FlowInKORNtv', 'FlowInKORUSD', 'FlowInKRKNtv', 'FlowInKRKUSD',
                               'FlowInMXCNtv', 'FlowInMXCUSD', 'FlowInNBXNtv', 'FlowInNBXUSD', 'FlowInOKXNtv',
                               'FlowInOKXUSD', 'FlowInPOLNtv', 'FlowInPOLUSD', 'FlowInSBGNtv', 'FlowInSBGUSD',
                               'FlowNetBFXNtv', 'FlowNetBFXUSD', 'FlowNetBITNtv', 'FlowNetBITUSD', 'FlowNetBNBNtv',
                               'FlowNetBNBUSD', 'FlowNetBSPNtv', 'FlowNetBSPUSD', 'FlowNetBTXNtv', 'FlowNetBTXUSD',
                               'FlowNetCRONtv', 'FlowNetCROUSD', 'FlowNetDERNtv', 'FlowNetDERUSD', 'FlowNetGEMNtv',
                               'FlowNetGEMUSD', 'FlowNetGIONtv', 'FlowNetGIOUSD', 'FlowNetHBTNtv', 'FlowNetHBTUSD',
                               'FlowNetHUONtv', 'FlowNetHUOUSD', 'FlowNetKCNNtv', 'FlowNetKCNUSD', 'FlowNetKORNtv',
                               'FlowNetKORUSD', 'FlowNetKRKNtv', 'FlowNetKRKUSD', 'FlowNetMXCNtv', 'FlowNetMXCUSD',
                               'FlowNetNBXNtv', 'FlowNetNBXUSD', 'FlowNetOKXNtv', 'FlowNetOKXUSD', 'FlowNetPOLNtv',
                               'FlowNetPOLUSD', 'FlowNetSBGNtv', 'FlowNetSBGUSD', 'FlowOutBFXNtv', 'FlowOutBFXUSD',
                               'FlowOutBITNtv', 'FlowOutBITUSD', 'FlowOutBNBNtv', 'FlowOutBNBUSD', 'FlowOutBSPNtv',
                               'FlowOutBSPUSD', 'FlowOutBTXNtv', 'FlowOutBTXUSD', 'FlowOutCRONtv', 'FlowOutCROUSD',
                               'FlowOutDERNtv', 'FlowOutDERUSD', 'FlowOutExInclNtv', 'FlowOutExInclUSD', 'FlowOutExNtv',
                               'FlowOutExUSD', 'FlowOutGEMNtv', 'FlowOutGEMUSD', 'FlowOutGIONtv', 'FlowOutGIOUSD',
                               'FlowOutHBTNtv', 'FlowOutHBTUSD', 'FlowOutKCNNtv', 'FlowOutKCNUSD', 'FlowOutKORNtv',
                               'FlowOutKORUSD', 'FlowOutKRKNtv', 'FlowOutKRKUSD', 'FlowOutMXCNtv', 'FlowOutMXCUSD',
                               'FlowOutNBXNtv', 'FlowOutNBXUSD', 'FlowOutOKXNtv', 'FlowOutOKXUSD', 'FlowOutPOLNtv',
                               'FlowOutPOLUSD', 'FlowOutSBGNtv', 'FlowOutSBGUSD', 'FlowTfrFromExCnt',
                               'FlowTfrFromExInclCnt', 'FlowTfrInBFXCnt', 'FlowTfrInBITCnt', 'FlowTfrInBNBCnt',
                               'FlowTfrInBSPCnt', 'FlowTfrInBTXCnt', 'FlowTfrInCROCnt', 'FlowTfrInDERCnt',
                               'FlowTfrInGEMCnt', 'FlowTfrInGIOCnt', 'FlowTfrInHBTCnt', 'FlowTfrInKCNCnt',
                               'FlowTfrInKORCnt', 'FlowTfrInKRKCnt', 'FlowTfrInMXCCnt', 'FlowTfrInNBXCnt',
                               'FlowTfrInOKXCnt', 'FlowTfrInPOLCnt', 'FlowTfrInSBGCnt', 'FlowTfrOutBFXCnt',
                               'FlowTfrOutBITCnt', 'FlowTfrOutBNBCnt', 'FlowTfrOutBSPCnt', 'FlowTfrOutBTXCnt',
                               'FlowTfrOutCROCnt', 'FlowTfrOutDERCnt', 'FlowTfrOutGEMCnt', 'FlowTfrOutGIOCnt',
                               'FlowTfrOutHBTCnt', 'FlowTfrOutKCNCnt', 'FlowTfrOutKORCnt', 'FlowTfrOutKRKCnt',
                               'FlowTfrOutMXCCnt', 'FlowTfrOutNBXCnt', 'FlowTfrOutOKXCnt', 'FlowTfrOutPOLCnt',
                               'FlowTfrOutSBGCnt', 'FlowTfrToExCnt', 'FlowTfrToExInclCnt', 'SplyBFXNtv', 'SplyBFXUSD',
                               'SplyBITNtv', 'SplyBITUSD', 'SplyBNBNtv', 'SplyBNBUSD', 'SplyBSPNtv', 'SplyBSPUSD',
                               'SplyBTXNtv', 'SplyBTXUSD', 'SplyCRONtv', 'SplyCROUSD', 'SplyDERNtv', 'SplyDERUSD',
                               'SplyExNtv', 'SplyExUSD', 'SplyGEMNtv', 'SplyGEMUSD', 'SplyGIONtv', 'SplyGIOUSD',
                               'SplyHBTNtv', 'SplyHBTUSD', 'SplyHUONtv', 'SplyHUOUSD', 'SplyKCNNtv', 'SplyKCNUSD',
                               'SplyKORNtv', 'SplyKORUSD', 'SplyKRKNtv', 'SplyKRKUSD', 'SplyMXCNtv', 'SplyMXCUSD',
                               'SplyNBXNtv', 'SplyNBXUSD', 'SplyOKXNtv', 'SplyOKXUSD', 'SplyPOLNtv', 'SplyPOLUSD',
                               'SplySBGNtv', 'SplySBGUSD', 'TxExCnt', 'FlowInCBSNtv', 'FlowInCBSUSD',
                               'FlowMinerIn1HopAllCBSNtv', 'FlowMinerIn1HopAllCBSUSD', 'FlowMinerOut1HopAllCBSNtv',
                               'FlowMinerOut1HopAllCBSUSD', 'FlowNetCBSNtv', 'FlowNetCBSUSD',
                               'FlowOutCBSNtv', 'FlowOutCBSUSD', 'FlowTfrInCBSCnt', 'FlowTfrOutCBSCnt', 'SplyCBSNtv',
                               'SplyCBSUSD']
    append_asset_metrics(group_include25_assets, group_include25_metrics, "1d", dest)

    group_include26_assets = ['bch', 'bsv', 'btc', 'btg', 'doge', 'ltc', 'xvg', 'zec']
    group_include26_metrics = ['TxOpRetCnt']
    append_asset_metrics(group_include26_assets, group_include26_metrics, "1d", dest)

    group_include27_assets = ['ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge', 'gas', 'grin', 'ltc',
                              'neo', 'vtc', 'xvg', 'zec']
    group_include27_metrics = ['UTXOCnt']
    append_asset_metrics(group_include27_assets, group_include27_metrics, "1d", dest)

    # Exclude Groups
    default_assets = group1_assets
    group28_assets = sorted(set(default_assets) - {'ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                                                   'dot', 'eos', 'fxc_eth', 'gas', 'grin', 'leo_eos', 'ltc', 'maid',
                                                   'neo', 'sai', 'trx', 'usdt', 'usdt_omni', 'usdt_trx', 'vtc', 'xem',
                                                   'xlm', 'xmr', 'xrp', 'xvg', 'zec'})
    group28_metrics = ['SplyContUSD', 'TxTfrValContCallUSD']
    append_asset_metrics(group28_assets, group28_metrics, '1d', dest)

    group29_assets = sorted(set(default_assets) - {'ada', 'bch', 'bsv', 'btc', 'btg', 'dash', 'dcr', 'dgb', 'doge',
                                                   'dot', 'eos', 'gas', 'grin', 'leo_eos', 'ltc', 'maid', 'neo', 'trx',
                                                   'usdt', 'usdt_omni', 'usdt_trx', 'vtc', 'xem', 'xlm', 'xmr', 'xrp',
                                                   'xvg', 'zec'})
    group29_metrics = ['SplyContNtv', 'TxContCnt', 'TxTfrValContCallNtv']
    append_asset_metrics(group29_assets, group29_metrics, '1d', dest)

    group30_assets = sorted(set(default_assets) - {'dcr', 'eos', 'fxc_eth', 'kcs', 'xrp'})
    group30_metrics = ['IssTotUSD']
    append_asset_metrics(group30_assets, group30_metrics, '1d', dest)

    group31_assets = sorted(set(default_assets) - {'dcr', 'eos', 'xrp'})
    group31_metrics = ['IssTotNtv']
    append_asset_metrics(group31_assets, group31_metrics, '1d', dest)

    group32_assets = sorted(set(default_assets) - {'eos'})
    group32_metrics = ['SplyCur']
    append_asset_metrics(group32_assets, group32_metrics, '1d', dest)

    group33_assets = sorted(
        set(default_assets) - {'eos', 'fxc_eth', 'grin', 'kcs', 'leo_eos', 'trx', 'xem', 'xmr'})
    group33_metrics = ['AdrBalUSD100Cnt', 'AdrBalUSD100KCnt', 'AdrBalUSD10Cnt', 'AdrBalUSD10KCnt', 'AdrBalUSD10MCnt',
                       'AdrBalUSD1Cnt', 'AdrBalUSD1KCnt', 'AdrBalUSD1MCnt', 'CapAct1yrUSD', 'CapMVRVCur', 'CapRealUSD',
                       'RVTAdj', 'RVTAdj90', 'SplyAdrBalUSD1', 'SplyAdrBalUSD10', 'SplyAdrBalUSD100',
                       'SplyAdrBalUSD100K', 'SplyAdrBalUSD10K', 'SplyAdrBalUSD10M', 'SplyAdrBalUSD1K',
                       'SplyAdrBalUSD1M']
    append_asset_metrics(group33_assets, group33_metrics, '1d', dest)

    group34_assets = sorted(set(default_assets) - {'eos', 'fxc_eth', 'grin', 'kcs', 'steth_lido', 'trx', 'xmr'})
    group34_metrics = ['NVTAdj90']
    append_asset_metrics(group34_assets, group34_metrics, '1d', dest)

    group35_assets = sorted(set(default_assets) - {'eos', 'fxc_eth', 'kcs', 'vet'})
    group35_metrics = ['CapMrktCurUSD']
    append_asset_metrics(group35_assets, group35_metrics, '1d', dest)

    group36_assets = sorted(set(default_assets) - {'eos', 'grin', 'leo_eos', 'trx', 'xem', 'xmr'})
    group36_metrics = ['AdrBal1in100KCnt', 'AdrBal1in100MCnt', 'AdrBal1in10BCnt', 'AdrBal1in10KCnt', 'AdrBal1in10MCnt',
                       'AdrBal1in1BCnt', 'AdrBal1in1KCnt', 'AdrBal1in1MCnt', 'AdrBalCnt', 'AdrBalNtv0.001Cnt',
                       'AdrBalNtv0.01Cnt', 'AdrBalNtv0.1Cnt', 'AdrBalNtv100Cnt', 'AdrBalNtv100KCnt', 'AdrBalNtv10Cnt',
                       'AdrBalNtv10KCnt', 'AdrBalNtv1Cnt', 'AdrBalNtv1KCnt', 'AdrBalNtv1MCnt', 'NDF', 'SER',
                       'SplyAct10yr', 'SplyAct180d', 'SplyAct1d', 'SplyAct1yr', 'SplyAct2yr', 'SplyAct30d',
                       'SplyAct3yr', 'SplyAct4yr', 'SplyAct5yr', 'SplyAct7d', 'SplyAct90d', 'SplyActEver',
                       'SplyActPct1yr', 'SplyAdrBal1in100K', 'SplyAdrBal1in100M', 'SplyAdrBal1in10B',
                       'SplyAdrBal1in10K', 'SplyAdrBal1in10M', 'SplyAdrBal1in1B', 'SplyAdrBal1in1K', 'SplyAdrBal1in1M',
                       'SplyAdrBalNtv0.001', 'SplyAdrBalNtv0.01', 'SplyAdrBalNtv0.1', 'SplyAdrBalNtv1',
                       'SplyAdrBalNtv10', 'SplyAdrBalNtv100', 'SplyAdrBalNtv100K', 'SplyAdrBalNtv10K',
                       'SplyAdrBalNtv1K', 'SplyAdrBalNtv1M', 'SplyAdrTop100', 'SplyAdrTop10Pct', 'SplyAdrTop1Pct',
                       'VelAct1yr', 'VelActAdj1yr']
    append_asset_metrics(group36_assets, group36_metrics, '1d', dest)

    group37_assets = sorted(set(default_assets) - {'eos', 'grin', 'trx', 'xem', 'xmr'})
    group37_metrics = ['AdrActRecCnt', 'AdrActSentCnt', 'VelCur1yr', 'VelCurAdj1yr']
    append_asset_metrics(group37_assets, group37_metrics, '1d', dest)

    group38_assets = sorted(set(default_assets) - {'eos', 'grin', 'trx', 'xmr'})
    group38_metrics = ['NVTAdj']
    append_asset_metrics(group38_assets, group38_metrics, '1d', dest)

    group39_assets = sorted(set(default_assets) - {'fxc_eth', 'grin', 'kcs', 'xmr'})
    group39_metrics = ['TxTfrValAdjUSD', 'TxTfrValMeanUSD', 'TxTfrValMedUSD', 'TxTfrValUSD']
    append_asset_metrics(group39_assets, group39_metrics, '1d', dest)

    group40_assets = sorted(set(default_assets) - {'fxc_eth', 'kcs', 'vet'})
    group40_metrics = ['PriceBTC', 'PriceUSD', 'ROI1yr', 'ROI30d', 'VtyDayRet180d', 'VtyDayRet30d', 'VtyDayRet60d']
    append_asset_metrics(group40_assets, group40_metrics, '1d', dest)

    group41_assets = sorted(set(default_assets) - {'grin', 'xmr'})
    group41_metrics = ['AdrActCnt', 'TxTfrValAdjNtv', 'TxTfrValMeanNtv', 'TxTfrValMedNtv', 'TxTfrValNtv']
    append_asset_metrics(group41_assets, group41_metrics, '1d', dest)
