def package_script():
    udm_mainchain_assets = ["btc", "bch", "dash", "eth", "ltc", "xrp", "zec"]
    udm_mainchain_assets.sort()
    return [
        "{'asset': '%s'}" % ','.join(udm_mainchain_assets),
    ]

def package_discovery_script():
    chain_monitor_asset_chains_catalog_assets = ["btc", "eth"]
    return [
        "script  - script {'asset': 'not(%s)'}" % ','.join(chain_monitor_asset_chains_catalog_assets)
    ]
