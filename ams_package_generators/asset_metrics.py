import json
import pkgutil
import sys
from collections import defaultdict

asset_metrics_package_map = {}

def package_script():
    global asset_metrics_package_map
    asset_metrics_package_map = build_asset_metrics_regular_package_map()
    return serialize_asset_metrics(asset_metrics_package_map)

def build_asset_metrics_regular_package_map():
    print('Building asset_metrics regular package map...')
    result = defaultdict(set)

    # import asset metrics from all defined packages
    import ams_package_generators
    for importer, modname, ispkg in pkgutil.iter_modules(ams_package_generators.__path__):
        submodule = sys.modules["ams_package_generators." + modname]
        package_type = submodule.package_type() if hasattr(submodule, 'package_type') else "regular"
        if package_type != "regular":
            if hasattr(submodule, "compute_and_append_asset_metrics"):
                submodule.compute_and_append_asset_metrics(result)

    # add reference rates for asset metrics && sort sets of metrics
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        asset = currency['cm_ticker']

        # add market metrics to the regular package for all assets
        result[(asset, "1h")].add("open_interest_reported_future_coin_margined_usd")
        result[(asset, "1h")].add("open_interest_reported_future_nonperpetual_usd")
        result[(asset, "1h")].add("open_interest_reported_future_perpetual_usd")
        result[(asset, "1h")].add("open_interest_reported_future_tether_margined_usd")
        result[(asset, "1h")].add("open_interest_reported_future_usd")
        result[(asset, "1h")].add("open_interest_reported_option_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_tether_margined_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_usdc_margined_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_coin_margined_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_call_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_put_market_value_usd")
        result[(asset, "1h")].add("open_interest_reported_option_notional_usd")
        result[(asset, "1h")].add("open_interest_reported_option_tether_margined_notional_usd")
        result[(asset, "1h")].add("open_interest_reported_option_usdc_margined_notional_usd")
        result[(asset, "1h")].add("open_interest_reported_option_coin_margined_notional_usd")
        result[(asset, "1h")].add("open_interest_reported_option_call_notional_usd")
        result[(asset, "1h")].add("open_interest_reported_option_put_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_future_coin_margined_usd")
        result[(asset, "1d")].add("open_interest_reported_future_nonperpetual_usd")
        result[(asset, "1d")].add("open_interest_reported_future_perpetual_usd")
        result[(asset, "1d")].add("open_interest_reported_future_tether_margined_usd")
        result[(asset, "1d")].add("open_interest_reported_future_usd")
        result[(asset, "1d")].add("open_interest_reported_option_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_tether_margined_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_usdc_margined_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_coin_margined_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_call_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_put_market_value_usd")
        result[(asset, "1d")].add("open_interest_reported_option_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_option_tether_margined_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_option_usdc_margined_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_option_coin_margined_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_option_call_notional_usd")
        result[(asset, "1d")].add("open_interest_reported_option_put_notional_usd")
        result[(asset, "1d")].add("volume_reported_spot_usd_1d")
        result[(asset, "1h")].add("volume_reported_spot_usd_1h")
        result[(asset, "1d")].add("volume_trusted_spot_usd_1d")
        result[(asset, "1h")].add("volume_trusted_spot_usd_1h")
        result[(asset, "1d")].add("volume_reported_future_coin_margined_usd_1d")
        result[(asset, "1h")].add("volume_reported_future_coin_margined_usd_1h")
        result[(asset, "1d")].add("volume_reported_future_nonperpetual_usd_1d")
        result[(asset, "1h")].add("volume_reported_future_nonperpetual_usd_1h")
        result[(asset, "1d")].add("volume_reported_future_perpetual_usd_1d")
        result[(asset, "1h")].add("volume_reported_future_perpetual_usd_1h")
        result[(asset, "1d")].add("volume_reported_future_tether_margined_usd_1d")
        result[(asset, "1h")].add("volume_reported_future_tether_margined_usd_1h")
        result[(asset, "1d")].add("volume_reported_future_usd_1d")
        result[(asset, "1h")].add("volume_reported_future_usd_1h")
        result[(asset, "1h")].add("volume_reported_option_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_tether_margined_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_tether_margined_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_usdc_margined_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_usdc_margined_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_coin_margined_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_coin_margined_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_call_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_call_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_put_market_value_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_put_market_value_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_notional_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_tether_margined_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_tether_margined_notional_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_usdc_margined_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_usdc_margined_notional_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_coin_margined_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_coin_margined_notional_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_call_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_call_notional_usd_1d")
        result[(asset, "1h")].add("volume_reported_option_put_notional_usd_1h")
        result[(asset, "1d")].add("volume_reported_option_put_notional_usd_1d")

        # add 1h implied volatility metrics to all assets
        result[(asset, "1h")].add("volatility_implied_atm_1d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_2d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_3d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_7d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_14d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_21d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_30d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_60d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_90d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_120d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_180d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_270d_expiration")
        result[(asset, "1h")].add("volatility_implied_atm_1y_expiration")

        # add liquidations metrics
        for frequency in ["5m", "1h", "1d"]:
            for metric in ["liquidations_reported_future_buy_units_",
                           "liquidations_reported_future_buy_usd_",
                           "liquidations_reported_future_sell_units_",
                           "liquidations_reported_future_sell_usd_"]:
                result[(asset, frequency)].add(f"{metric}{frequency}")

        for frequency in ["10m", "1h", "1d"]:
            for metric in ["volatility_realized_usd_rolling_24h", "volatility_realized_usd_rolling_7d",
                           "volatility_realized_usd_rolling_30d"]:
                result[(asset, frequency)].add(metric)

        for frequency in ["1h", "1d"]:
            for metric in ["futures_aggregate_funding_rate_usd_margin_8h_period",
                           "futures_aggregate_funding_rate_usd_margin_1d_period",
                           "futures_aggregate_funding_rate_usd_margin_30d_period",
                           "futures_aggregate_funding_rate_usd_margin_1y_period",
                           "futures_aggregate_funding_rate_coin_margin_8h_period",
                           "futures_aggregate_funding_rate_coin_margin_1d_period",
                           "futures_aggregate_funding_rate_coin_margin_30d_period",
                           "futures_aggregate_funding_rate_coin_margin_1y_period",
                           "futures_aggregate_funding_rate_all_margin_8h_period",
                           "futures_aggregate_funding_rate_all_margin_1d_period",
                           "futures_aggregate_funding_rate_all_margin_30d_period",
                           "futures_aggregate_funding_rate_all_margin_1y_period",
                           "futures_cumulative_funding_rate_usd_margin_rolling_1d",
                           "futures_cumulative_funding_rate_usd_margin_rolling_7d",
                           "futures_cumulative_funding_rate_usd_margin_rolling_30d",
                           "futures_cumulative_funding_rate_coin_margin_rolling_1d",
                           "futures_cumulative_funding_rate_coin_margin_rolling_7d",
                           "futures_cumulative_funding_rate_coin_margin_rolling_30d",
                           "futures_cumulative_funding_rate_all_margin_rolling_1d",
                           "futures_cumulative_funding_rate_all_margin_rolling_7d",
                           "futures_cumulative_funding_rate_all_margin_rolling_30d"]:
                result[(asset, frequency)].add(metric)

    for (asset, frequency), metrics in result.items():
        result[(asset, frequency)] = sorted(metrics)
    # sort by keys
    return dict(sorted(result.items()))

def build_asset_metrics_exclusion_map():
    print('Building asset_metrics exclusion map...')
    result = defaultdict(set)

    # import asset metrics from the internal package
    submodule = sys.modules["ams_package_generators.internal_asset_metrics"]
    submodule.compute_and_append_asset_metrics(result)

    for (asset, frequency), metrics in result.items():
        result[(asset, frequency)] = sorted(metrics)
    # sort by keys
    return dict(sorted(result.items()))

def package_discovery_script():
    # build package_discovery_script script
    excluded_asset_metrics_map = build_asset_metrics_exclusion_map()
    from ams_data_generator import join_lines
    discovery_script = join_lines(["script"] + serialize_asset_metrics(excluded_asset_metrics_map), prefix_for_non_first_lines='- script ')
    return discovery_script

def calc_composite_asset_metrics_package_script(package_name):
    am = defaultdict(set)
    import ams_package_generators
    package_generator = getattr(ams_package_generators, package_name.lower())
    if package_generator is None:
        raise ValueError("Unsupported package name: " + package_name)
    package_generator.compute_and_append_asset_metrics(am)

    # sort by keys
    am = dict(sorted(am.items()))

    if len(am) != 0:
        for (asset, frequency), metrics in am.items():
            # check that we have the same metrics in the god-mode asset_metrics package
            all_metrics = asset_metrics_package_map[(asset, frequency)]
            for metric_to_find in metrics:
                if metric_to_find not in all_metrics:
                    raise ValueError(f"The metric {metric_to_find}, asset {asset}, frequency {frequency} weren't found in the asset_metrics package.")

    return serialize_asset_metrics(am, composite=True)

def serialize_asset_metrics(asset_metrics_map, composite: bool = False):
    """
    Optimize the serialization of asset_metrics_map by grouping similar metrics,
    frequencies, and assets to reduce the number of lines.

    Args:
        asset_metrics_map: Dictionary mapping (asset, frequency) to sets of metrics
        composite: Boolean flag indicating whether the output is for a composite package

    Returns:
        List of serialized strings with optimized grouping
    """
    # Step 1: Group by metrics - find assets and frequencies that share the same set of metrics
    metrics_to_asset_freq = {}
    for (asset, freq), metrics in asset_metrics_map.items():
        # Convert metrics to frozenset for dictionary key
        metrics_key = frozenset(metrics)
        if metrics_key not in metrics_to_asset_freq:
            metrics_to_asset_freq[metrics_key] = {}

        # Group by asset
        if asset not in metrics_to_asset_freq[metrics_key]:
            metrics_to_asset_freq[metrics_key][asset] = set()

        metrics_to_asset_freq[metrics_key][asset].add(freq)

    # Step 2: Group by frequencies - find assets that share the same metrics and frequencies
    combined_groups = []
    for metrics, asset_to_freqs in metrics_to_asset_freq.items():
        # Group assets by their frequency sets
        freq_sets_to_assets = {}
        for asset, freqs in asset_to_freqs.items():
            freq_key = frozenset(freqs)
            if freq_key not in freq_sets_to_assets:
                freq_sets_to_assets[freq_key] = set()
            freq_sets_to_assets[freq_key].add(asset)

        # Add each group to our final result
        for freqs, assets in freq_sets_to_assets.items():
            combined_groups.append({
                'metrics': metrics,
                'frequencies': freqs,
                'assets': assets
            })

    # Step 3: Generate the optimized serialized lines
    asset_metrics_script_lines = []
    for group in combined_groups:
        metrics_str = ', '.join(sorted(group['metrics']))
        freqs_str = ', '.join(sorted(group['frequencies']))
        assets_str = ', '.join(sorted(group['assets']))

        if composite:
            asset_metrics_script_lines.append(
                f"asset_metrics {{'metric': '{metrics_str}', 'frequency': '{freqs_str}', 'asset': '{assets_str}'}}"
            )
        else:
            asset_metrics_script_lines.append(
                f"{{'metric': '{metrics_str}', 'frequency': '{freqs_str}', 'asset': '{assets_str}'}}"
            )

    return asset_metrics_script_lines

def append_asset_metrics(assets, metrics, frequency, dest):
    for asset in assets:
        dest[(asset, frequency)].update(metrics)
