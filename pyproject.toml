[project]
name = "octopus"
version = "1.0.0"
description = "Monorepo for the FHs related applications"
authors = []
dependencies = [
    "authlib==1.4.0",
    "bitstring==4.2.3",
    "boto3==1.35.86",
    "boto3-stubs==1.35.8",
    "ciso8601==2.3.2",
    "colorama==0.4.6",
    "confluent-kafka==2.7.0",
    "cython==3.0.11",
    "expiringdict==1.2.2",
    "fastapi==0.115.6",
    "freezegun==1.5.1",
    "google-cloud-pubsub==2.27.1",
    "grafanalib==0.7.1",
    "grpc-google-iam-v1==0.13.1",
    "jinja2==3.1.2",
    "kubernetes==31.0.0",
    "kubernetes-stubs==22.6.0.post1",
    "lxml==5.3.0",
    "lz4==4.3.3",
    "minio==7.2.13",
    "mypy==1.14.0",
    "openpyxl==3.1.5",
    "openpyxl-stubs==0.1.25",
    "orjson==3.10.12",
    "pgcli==4.1.0",
    "prometheus-client==0.21.1",
    "psycopg2==2.9.10",
    "pyftpclient==0.1.15",
    "pytest==8.3.4",
    "pytz==2024.2",
    "requests==2.32.3",
    "ruff==0.8.5",
    "simplewebsocketserver==0.1.2",
    "slack-bolt==1.22.0",
    "slack-sdk==3.34.0",
    "sortedcontainers==2.4.0",
    "testcontainers==4.9.0",
    "types-attrs==19.1.0",
    "types-colorama==0.4.15.20240311",
    "types-protobuf==5.29.1.20241207",
    "types-psycopg2==2.9.21.20241019",
    "types-python-dateutil==2.9.0.20241206",
    "types-pytz==2024.2.0.20241221",
    "types-pyyaml==6.0.12.20241221",
    "types-requests==2.32.0.20241016",
    "uvicorn==0.34.0",
    "websocket-client==1.8.0",
]
requires-python = ">=3.13"

[project.optional-dependencies]
dev = [
    "ansible==11.1.0",
    "pre-commit==4.0.1",
    "tabulate==0.9.0",
]

[tool.ruff]
line-length = 129
indent-width = 4
include = ["*.py"]
exclude = ["feed_protocol_pb2.py", "*_pb2.py", "*_pb2.pyi"]
preview = true

[tool.ruff.lint]
extend-select = [
    "E231",  # missing whitespace after ','
    "E501",  # line too long
]

[tool.ruff.lint.per-file-ignores]
"test/**.py" = ["E501"]  # ignore `line too long` errors for tests

[tool.ruff.format]
indent-style = "space"
quote-style = "double"
