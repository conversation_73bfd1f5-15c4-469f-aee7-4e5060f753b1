[{"name": "Coin Metrics", "id": 0}, {"name": "CoinMarketCap", "id": 1}, {"name": "Bitfinex", "id": 2}, {"name": "Fixer.io", "id": 3, "keys": [{"id": "API", "description": "", "value": "fcce368a056eef2071d5c2e741d67ba5"}]}, {"name": "Binance", "id": 4, "keys": [{"id": "API", "description": "Grants non-trading access to Binance API", "value": "kxg6QcIrcq1vmbv8HFJUeetdivVPXofbMgVKbZDHSfdn8sCmYbzPcB2xRilFAUSC"}]}]