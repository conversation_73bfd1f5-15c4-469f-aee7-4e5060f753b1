WS scraper (trades and books)
==================================

1. Run it for at least 10min and observe no errors
2. Compare output of the WS scraper with the HTTP scraper
3. Try to subscribe to invalid channels
4. Ensure test example contains all types of messages (like, trades, book, heartbeat, subscription confirmation, etc)
5. Try the scraper on the single channel and on many channels
6. Try to interrupt the scraper by disconnection from the internet
7. Try to interrupt the scraper with the ctrl+c. This must ensure that the stop/disconnect works properly
8. Ensure there are no public methods/variables left in the code that are not used from the outside of where they were declared
