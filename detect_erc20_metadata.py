#!/usr/bin/python3

import argparse
import json
import logging
# pip install psycopg2-binary
import psycopg2
import sys

# In Mac: CFLAGS=-I/usr/local/Cellar/python@3.10/3.10.6_1/Frameworks/Python.framework/Headers/ pip install web3
from web3 import Web3
from collections import namedtuple


logging.basicConfig(format='%(asctime)s - %(levelname)s: %(message)s', level=logging.INFO)

parser = argparse.ArgumentParser(description="Detects metadata for ERC-20 assets for addition to currency.json")

parser.add_argument(
    "--contract",
    required=True,
    help="Address of the ETH contract for the ERC-20 asset. Ex: a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48"
)

parser.add_argument(
    "--web3-url",
    default="http://localhost:8545",
    help="URL for a Web3 API." # https://web3py.readthedocs.io/en/stable/web3.eth.html
)

parser.add_argument(
    "--eth-exporter-postgres-url",
    required=True,
    help="URL pointing to postgres-factory-staging database. Format is postgresql://postgres@hostname:port/database" # https://gitlab.com/coinmetrics/exporters/ethereum-exporter
)

parser.add_argument(
    "--factory-postgres-url",
    required=True,
    help="URL pointing to postgres-factory-staging database. Format is postgresql://postgres@hostname:port/database"
)

parser.add_argument(
    "--currency-json-path",
    default="currency.json",
    help="Path to resources' currency.json file"
)

erc20_metadata_abi = """[
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [
      {
        "name": "",
        "type": "uint256"
      }
    ],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "name",
    "outputs": [
      {
        "name": "",
        "type": "string"
      }
    ],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "symbol",
    "outputs": [
      {
        "name": "",
        "type": "string"
      }
    ],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  }
]"""

OnChainMetadata = namedtuple("OnChainMetadata", ["decimals", "name", "symbol"])


def get_onchain_metadata(web3, contract):
    contract = web3.eth.contract(address=Web3.toChecksumAddress(contract), abi=erc20_metadata_abi)
    decimals = contract.functions.decimals().call()
    name = contract.functions.name().call()
    symbol = contract.functions.symbol().call()

    return OnChainMetadata(decimals, name, symbol)


def get_genesis_timestamp(postgres_url, contract):
    with psycopg2.connect(postgres_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT creation_block_time FROM contracts WHERE asset = 'eth' AND contract = %s", (contract,))
            return cursor.fetchall()[0][0]


def get_genesis_block_height(postgres_url, contract, genesis_timestamp):
    with psycopg2.connect(postgres_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT MIN(b.height)
                FROM eth.actions a
                JOIN eth.blocks b
                ON a.block_hash = b.hash
                WHERE a.to = '\\x{contract}'
                AND a.type = 3 -- contract creation
                AND b.miner_time = %s
            """, (genesis_timestamp,))

            return cursor.fetchone()[0]


def get_currency_info(contract, web3, factory_postgres_url, eth_exporter_postgres_url):
    logging.info("Getting on-chain metadata for %s", contract)
    onchain_metadata = get_onchain_metadata(web3, contract)

    logging.info("Determining genesis timestamp for %s", contract)
    genesis_timestamp = get_genesis_timestamp(factory_postgres_url, contract)

    logging.info("Determining genesis block height for %s", contract)
    genesis_block_height = get_genesis_block_height(eth_exporter_postgres_url, contract, genesis_timestamp)

    return {
        "id": -1,
        "cm_ticker": onchain_metadata.symbol.lower(),
        "name": onchain_metadata.name,
        "metadata": {
          "hasReferenceRate": False,
          "hasRTRR": False,
          "exchangeRateRegime": "floating",
          "firstBlockHeight": genesis_block_height,
          "decimals": onchain_metadata.decimals,
          "genesis": str(genesis_timestamp.date()),
          "type": "erc20",
          "parent": "eth",
          "contract": contract
        }
    }


def get_existing_currencies(currency_file):
    with open(currency_file) as f:
        return json.load(f)


if __name__ == "__main__":
    args = parser.parse_args()

    contract = args.contract.lower()
    if contract.startswith("0x"):
        contract = contract[2:]

    web3 = Web3(Web3.HTTPProvider(args.web3_url))
    if not web3.isConnected():
        logging.warning("Could not connect to Web3 API")
        sys.exit(1)

    # We start by building a first version of the currency.json entry for the token
    currency_info = get_currency_info(contract, web3, args.factory_postgres_url, args.eth_exporter_postgres_url)

    # Then we need to figure out if an entry exists with the same cm_ticker?
    # if so, we'll use that entry's id otherwise we use the next available one
    existing_currencies = get_existing_currencies(args.currency_json_path)
    currency_id = -1
    for currency in existing_currencies:
        if currency["cm_ticker"] == currency_info["cm_ticker"]:
            currency_info["id"] = currency["id"]
            currency_info["metadata"]["hasReferenceRate"] = currency["metadata"]["hasReferenceRate"]
            currency_info["metadata"]["hasRTRR"] = currency["metadata"]["hasRTRR"]
            currency_info["metadata"]["exchangeRateRegime"] = currency["metadata"]["exchangeRateRegime"]
            if "ckgo_id" in currency["metadata"]:
                currency_info["metadata"]["ckgo_id"] = currency["metadata"]["ckgo_id"]

            # We also preserve the existing name
            currency_info["name"] = currency["name"]

            # We add the metadata originally contained in the currency and that we didn't modify
            for property in currency["metadata"]:
              if property not in currency_info["metadata"]:
                currency_info["metadata"][property] = currency["metadata"][property]

            logging.info("Please replace")
            print(json.dumps(currency, indent=2))

            logging.info("with:")
            print(json.dumps(currency_info, indent=2))

            sys.exit(1)

    # If we didn't find an existing currency with this id, we take the next one available
    currency_info["id"] = existing_currencies[-1]["id"] + 1
    logging.info("Please add this at the end of currency.json:")
    print(json.dumps(currency_info, indent=2))
