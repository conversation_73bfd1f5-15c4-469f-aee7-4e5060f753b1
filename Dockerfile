FROM ubuntu:24.04

RUN apt-get update && apt-get install -y software-properties-common gcc-13 apt-transport-https && add-apt-repository -y ppa:deadsnakes/ppa && apt-get update
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y python3.13-venv python3-cryptography lsb-release libssl-dev build-essential  \
    make git wget protobuf-compiler cargo curl libffi-dev libbz2-dev libxslt-dev libxml2-dev libpq-dev python3.13-dev ca-certificates cron rclone && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.13 1

RUN update-ca-certificates

COPY src/kafka_eater/bash/init.sh /usr/local/bin/init.sh
RUN chmod +x /usr/local/bin/init.sh

COPY src/kafka_eater/bash/upload.sh /usr/local/bin/upload.sh
RUN chmod +x /usr/local/bin/upload.sh

COPY src/kafka_eater/bash/cleanup.sh /usr/local/bin/cleanup.sh
RUN chmod +x /usr/local/bin/cleanup.sh

# install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# create venv
RUN uv venv .venv --python=python3.13
# use venv by default
ENV PATH="/.venv/bin:$PATH"

# install kubectl
RUN curl -LO https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl  \
    && chmod +x ./kubectl && mv ./kubectl /usr/bin/kubectl

# install requirements
COPY pyproject.toml uv.lock ./
RUN uv pip compile pyproject.toml --group dev --all-extras -o requirements.txt
RUN uv pip install setuptools==75.6.0 wheel==0.45.1 && uv pip install --no-cache-dir -r requirements.txt


COPY . .

CMD ["python"]
