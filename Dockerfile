FROM ubuntu:24.04

RUN apt-get update && apt-get install -y software-properties-common gcc-13 apt-transport-https && add-apt-repository -y ppa:deadsnakes/ppa && apt-get update
# Installing rclone for the Kafka eater to upload files to S3
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y python3.13-venv python3-cryptography lsb-release libssl-dev build-essential  \
    make git wget protobuf-compiler cargo curl libffi-dev libbz2-dev libxslt-dev libxml2-dev libpq-dev python3.13-dev rclone && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.13 1

# install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# create venv
RUN uv venv .venv --python=python3.13
# use venv by default
ENV PATH="/.venv/bin:$PATH"

# install kubectl
RUN curl -LO https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl  \
    && chmod +x ./kubectl && mv ./kubectl /usr/bin/kubectl

# install requirements
COPY requirements.txt requirements.txt
RUN uv pip install setuptools==75.6.0 wheel==0.45.1 && uv pip install --no-cache-dir -r requirements.txt

# compile Cython extension
COPY setup.py setup.py
COPY src/octopus/cdata.pyx src/octopus/cdata.pyx
RUN uv run python setup.py build_ext --inplace --verbose

COPY . .

CMD ["python"]
