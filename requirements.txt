# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via starlette
argon2-cffi==23.1.0
    # via minio
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
attrs==25.3.0
    # via grafanalib
authlib==1.4.0
    # via octopus (pyproject.toml)
bcrypt==4.3.0
    # via paramiko
bitarray==2.9.3
    # via bitstring
bitstring==4.2.3
    # via octopus (pyproject.toml)
boto3==1.35.86
    # via octopus (pyproject.toml)
boto3-stubs==1.35.8
    # via octopus (pyproject.toml)
botocore==1.35.99
    # via
    #   boto3
    #   s3transfer
botocore-stubs==1.37.24
    # via boto3-stubs
cachetools==5.5.2
    # via google-auth
certifi==2025.1.31
    # via
    #   kubernetes
    #   minio
    #   requests
cffi==1.17.1
    # via
    #   argon2-cffi-bindings
    #   cryptography
    #   pynacl
charset-normalizer==3.4.1
    # via requests
ciso8601==2.3.2
    # via octopus (pyproject.toml)
cli-helpers==2.4.0
    # via pgcli
click==8.1.8
    # via
    #   pgcli
    #   pgspecial
    #   uvicorn
colorama==0.4.6
    # via octopus (pyproject.toml)
configobj==5.0.9
    # via
    #   cli-helpers
    #   pgcli
confluent-kafka==2.7.0
    # via octopus (pyproject.toml)
cryptography==44.0.2
    # via
    #   authlib
    #   paramiko
cython==3.0.11
    # via octopus (pyproject.toml)
deprecated==1.2.18
    # via
    #   opentelemetry-api
    #   opentelemetry-semantic-conventions
docker==7.1.0
    # via testcontainers
durationpy==0.9
    # via kubernetes
et-xmlfile==2.0.0
    # via openpyxl
expiringdict==1.2.2
    # via octopus (pyproject.toml)
fastapi==0.115.6
    # via octopus (pyproject.toml)
freezegun==1.5.1
    # via octopus (pyproject.toml)
google-api-core==2.24.2
    # via google-cloud-pubsub
google-auth==2.38.0
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   kubernetes
google-cloud-pubsub==2.27.1
    # via octopus (pyproject.toml)
googleapis-common-protos==1.69.2
    # via
    #   google-api-core
    #   grpc-google-iam-v1
    #   grpcio-status
grafanalib==0.7.1
    # via octopus (pyproject.toml)
grpc-google-iam-v1==0.13.1
    # via
    #   octopus (pyproject.toml)
    #   google-cloud-pubsub
grpcio==1.71.0
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   google-api-core
    #   google-cloud-pubsub
h11==0.14.0
    # via uvicorn
idna==3.10
    # via
    #   anyio
    #   requests
importlib-metadata==8.6.1
    # via opentelemetry-api
iniconfig==2.1.0
    # via pytest
jinja2==3.1.2
    # via octopus (pyproject.toml)
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
kubernetes==31.0.0
    # via octopus (pyproject.toml)
kubernetes-stubs==22.6.0.post1
    # via octopus (pyproject.toml)
lxml==5.3.0
    # via octopus (pyproject.toml)
lz4==4.3.3
    # via octopus (pyproject.toml)
markupsafe==3.0.2
    # via jinja2
minio==7.2.13
    # via octopus (pyproject.toml)
mypy==1.14.0
    # via
    #   octopus (pyproject.toml)
    #   openpyxl-stubs
mypy-extensions==1.0.0
    # via mypy
oauthlib==3.2.2
    # via
    #   kubernetes
    #   requests-oauthlib
openpyxl==3.1.5
    # via
    #   octopus (pyproject.toml)
    #   openpyxl-stubs
openpyxl-stubs==0.1.25
    # via octopus (pyproject.toml)
opentelemetry-api==1.31.1
    # via
    #   google-cloud-pubsub
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-sdk==1.31.1
    # via google-cloud-pubsub
opentelemetry-semantic-conventions==0.52b1
    # via opentelemetry-sdk
orjson==3.10.12
    # via octopus (pyproject.toml)
os-utils==0.1.6
    # via pyftpclient
packaging==24.2
    # via pytest
paramiko==3.5.1
    # via pyftpclient
pgcli==4.1.0
    # via octopus (pyproject.toml)
pgspecial==2.1.3
    # via pgcli
pluggy==1.5.0
    # via pytest
prometheus-client==0.21.1
    # via octopus (pyproject.toml)
prompt-toolkit==3.0.50
    # via pgcli
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-pubsub
protobuf==5.29.4
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
psycopg==3.2.6
    # via
    #   pgcli
    #   pgspecial
psycopg2==2.9.10
    # via octopus (pyproject.toml)
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodome==3.22.0
    # via minio
pydantic==2.11.1
    # via fastapi
pydantic-core==2.33.0
    # via pydantic
pyftpclient==0.1.15
    # via octopus (pyproject.toml)
pygments==2.19.1
    # via
    #   cli-helpers
    #   pgcli
pynacl==1.5.0
    # via paramiko
pytest==8.3.4
    # via octopus (pyproject.toml)
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   freezegun
    #   kubernetes
python-dotenv==1.1.0
    # via testcontainers
pytz==2024.2
    # via octopus (pyproject.toml)
pyyaml==6.0.2
    # via kubernetes
requests==2.32.3
    # via
    #   octopus (pyproject.toml)
    #   docker
    #   google-api-core
    #   kubernetes
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via kubernetes
rsa==4.9
    # via google-auth
ruff==0.8.5
    # via octopus (pyproject.toml)
s3transfer==0.10.4
    # via boto3
setproctitle==1.3.5
    # via pgcli
simplewebsocketserver==0.1.2
    # via octopus (pyproject.toml)
six==1.17.0
    # via
    #   kubernetes
    #   python-dateutil
slack-bolt==1.22.0
    # via octopus (pyproject.toml)
slack-sdk==3.34.0
    # via
    #   octopus (pyproject.toml)
    #   slack-bolt
sniffio==1.3.1
    # via anyio
sortedcontainers==2.4.0
    # via octopus (pyproject.toml)
sqlparse==0.5.3
    # via
    #   pgcli
    #   pgspecial
starlette==0.41.3
    # via fastapi
tabulate==0.9.0
    # via cli-helpers
testcontainers==4.9.0
    # via octopus (pyproject.toml)
types-attrs==19.1.0
    # via octopus (pyproject.toml)
types-awscrt==0.24.2
    # via botocore-stubs
types-colorama==0.4.15.20240311
    # via octopus (pyproject.toml)
types-protobuf==5.29.1.20241207
    # via octopus (pyproject.toml)
types-psycopg2==2.9.21.20241019
    # via octopus (pyproject.toml)
types-python-dateutil==2.9.0.20241206
    # via octopus (pyproject.toml)
types-pytz==2024.2.0.20241221
    # via octopus (pyproject.toml)
types-pyyaml==6.0.12.20241221
    # via octopus (pyproject.toml)
types-requests==2.32.0.20241016
    # via octopus (pyproject.toml)
types-s3transfer==0.11.4
    # via boto3-stubs
typing-extensions==4.13.0
    # via
    #   fastapi
    #   minio
    #   mypy
    #   openpyxl-stubs
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
    #   testcontainers
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
urllib3==2.3.0
    # via
    #   botocore
    #   docker
    #   kubernetes
    #   minio
    #   requests
    #   testcontainers
    #   types-requests
uvicorn==0.34.0
    # via octopus (pyproject.toml)
wcwidth==0.2.13
    # via
    #   prompt-toolkit
    #   tabulate
websocket-client==1.8.0
    # via
    #   octopus (pyproject.toml)
    #   kubernetes
wrapt==1.17.2
    # via
    #   deprecated
    #   testcontainers
zipp==3.21.0
    # via importlib-metadata
