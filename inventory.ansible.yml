all:
  hosts:
    wpwebsite:
      ansible_host: **************
      become: yes
    sentinel-3:
      ansible_host: ***************
      become: yes
    main-01:
      ansible_host: *************
      become: yes
    scrapers-production-1:
      ansible_host: *************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    scrapers-production-2:
      ansible_host: *************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    feed-handlers-prod-1:
      ansible_host: **************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    feed-handlers-prod-2:
      ansible_host: **************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books-prod-1:
      ansible_host: *************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books-prod-2:
      ansible_host: ************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books2-prod-1:
      ansible_host: **************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books2-prod-2:
      ansible_host: **************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books3-prod-1:
      ansible_host: ***********
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books3-prod-2:
      ansible_host: *************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books4-prod-1:
      ansible_host: ***************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    fh-books4-prod-2:
      ansible_host: *************
      become: yes
      ansible_python_interpreter: /usr/bin/python3
    datavis-backend-stage-0:
      ansible_host: **************
      become: yes
    datavis-backend-prod-0:
      ansible_host: **************
      become: yes
    consul-grafana-host:
      ansible_host: ************
      ansible_user: oleksandrb
