# ---------- base template ----------
.deploy-kafka-eater:
  image: registry.gitlab.com/coinmetrics/ops/cicd-tools:0.2.0-stable
  variables:
    RELEASE: md-factory-kafka-eater       # helm release name
    NAMESPACE: md-factory                 # k8s namespace
    HELM_VALUES: ""                       # e.g. "-f values-cdev1.yaml -f values-cdev1-coinbase.yaml"
    SECRET_VALUES_PATH: ""                # e.g. "secrets://secret-values-cdev1.yaml"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  dependencies: [ ]
  script:
    # SOPS key for helm-sops
    - export SOPS_AGE_KEY="$(kubectl get secret -n coinmetrics coinmetrics --template={{.data.secret}} | base64 -d)"
    - echo "SOPS_AGE_KEY loaded"
    - cd ./src/kafka_eater/helm
    - echo "Performing dry run..."
    - helm upgrade "$RELEASE" chart --debug --dry-run --install -n "$NAMESPACE" --wait $HELM_VALUES ${SECRET_VALUES_PATH:+-f "$SECRET_VALUES_PATH"} --set image.tag="$CI_COMMIT_SHA"
    - echo "Dry run complete. Deploying..."
    - helm upgrade "$RELEASE" chart --debug --install -n "$NAMESPACE" --wait $HELM_VALUES ${SECRET_VALUES_PATH:+-f "$SECRET_VALUES_PATH"} --set image.tag="$CI_COMMIT_SHA"

# ---------- environment jobs ----------
kafka-eater:cdev1:
  extends: .deploy-kafka-eater
  stage: deploy:staging
  variables:
    HELM_VALUES: "-f values-common.yaml -f values-cdev1.yaml"
    SECRET_VALUES_PATH: "secrets://secret-values-cdev1.yaml"
  tags:
    - env-cdev1
    - rt-containerd

kafka-eater:cp1:
  extends: .deploy-kafka-eater
  stage: deploy:production-1
  variables:
    HELM_VALUES: "-f values-common.yaml -f values-cp1.yaml"
    SECRET_VALUES_PATH: "secrets://secret-values-cp1.yaml"
  tags:
    - env-cp1
    - rt-containerd
