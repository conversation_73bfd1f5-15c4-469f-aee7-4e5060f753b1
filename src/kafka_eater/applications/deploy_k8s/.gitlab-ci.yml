kafka-eater-cdev1:
  image: registry.gitlab.com/coinmetrics/ops/cicd-tools:0.2.0-stable
  stage: deploy:staging
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  dependencies: [ ]
  script:
    - export SOPS_AGE_KEY=$(kubectl get secret -n coinmetrics coinmetrics --template={{.data.secret}} | base64 -d)
    - echo "SOPS_AGE_KEY=$SOPS_AGE_KEY"
    - cd ./src/kafka_eater/helm
    # dry run
    - helm upgrade kafka-eater chart --debug --dry-run --install -n md-factory --wait -f values-cdev1.yaml -f values-cdev1-coinbase.yaml -f secrets://secret-values-cdev1.yaml --set image.tag=$CI_COMMIT_SHA
    # actual deploy
    - helm upgrade kafka-eater chart --debug --install -n md-factory --wait -f values-cdev1.yaml -f values-cdev1-coinbase.yaml -f secrets://secret-values-cdev1.yaml --set image.tag=$CI_COMMIT_SHA

  tags:
    - env-cdev1
    - rt-containerd
