from argparse import ArgumentParser, Namespace
from collections.abc import Mapping
from time import sleep
from typing import Dict, List, Optional

from confluent_kafka import Consumer, KafkaException, Message, TopicPartition

from src.kafka_eater.data_saver import DataSaver
from src.kafka_eater.exchange_handler import ExchangeHandler
from src.kafka_eater.message_processor import MessageProcessor
from src.resources.exchange import glib_exchange
from src.utils.application import Application, application_arguments_parser
from src.utils.diagnostics import Diagnostics, IGaugeMetric
from src.utils.kafka import kafka_servers_arguments
from src.utils.types import JsonValue


class KafkaEater:
    _KAFKA_POLL_MAX_RECORDS_DEFAULT = 200000
    _KAFKA_MAX_PARTITION_FETCH_BYTES = 20 * 1024 * 1024
    _KAFKA_TIMEOUT_MS = 0

    _TOPIC_TEMPLATE = "{kafka_topic_prefix}_{exchange_id}"

    def __init__(self) -> None:
        self.args = self.parse_cli()
        self._print_input_arguments()
        self.glib_exchange_instance = glib_exchange()
        self.message_processor = MessageProcessor()

        self.exchange_id_exchange_handler_dict: Dict[int, ExchangeHandler[int]] = {}
        self.kafka_consumers = self._init_kafka_consumers()

        self.data_saver: Optional[DataSaver] = None
        self.diagnostics_variables: Dict[str, Dict[str, IGaugeMetric]] = {}

    def _init_kafka_consumers(self) -> Dict[str, Dict[str, Consumer]]:
        consumers = {}
        print("Initializing Kafka consumers...")

        for exchange_name, exchange_id in self.get_exchange_info_from_args().items():
            topic = self._TOPIC_TEMPLATE.format(kafka_topic_prefix=self.args.kafka_topic_prefix, exchange_id=exchange_id)
            print(f"  Setting up consumers for exchange '{exchange_name}' (ID: {exchange_id}) on topic '{topic}'")

            # Create consumer 1
            print(f"  Creating consumer_1 for {self.args.kafka_servers[0]}")
            consumer_1 = Consumer({
                "bootstrap.servers": self.args.kafka_servers[0],
                "group.id": f"kafka_eater_{self.prepare_exchange_name(exchange_name)}_1",
                "max.partition.fetch.bytes": self.args.kafka_max_partition_fetch_bytes,
                "auto.offset.reset": "latest",
                "enable.auto.commit": False,
            })

            # Create consumer 2
            print(f"  Creating consumer_2 for {self.args.kafka_servers[1]}")
            consumer_2 = Consumer({
                "bootstrap.servers": self.args.kafka_servers[1],
                "group.id": f"kafka_eater_{self.prepare_exchange_name(exchange_name)}_2",
                "max.partition.fetch.bytes": self.args.kafka_max_partition_fetch_bytes,
                "auto.offset.reset": "latest",
                "enable.auto.commit": False,
            })

            consumers[exchange_name] = {
                "consumer_1": consumer_1,
                "consumer_2": consumer_2,
                "topic_prefix": self.args.kafka_topic_prefix,
            }

            # Subscribe to topics
            print(f"  Subscribing consumer_1 to topic '{topic}'")
            consumer_1.subscribe([topic])
            print(f"  Subscribing consumer_2 to topic '{topic}'")
            consumer_2.subscribe([topic])

            print(f"✓ Successfully initialized consumers for exchange '{exchange_name}'")

        print(f"✓ All Kafka consumers initialized successfully! Total exchanges: {len(consumers)}")
        return consumers

    def _init_diagnostics_variables(self, app: Application) -> Dict[str, Dict[str, IGaugeMetric]]:
        diagnostics = {}
        for exchange_name in self.kafka_consumers.keys():
            handled_exchange_name = self.prepare_exchange_name(exchange_name)
            diagnostics[exchange_name] = {
                "lag_1": app.diagnostics.gauge(f"kafka_eater_lag_1_{handled_exchange_name}"),
                "lag_2": app.diagnostics.gauge(f"kafka_eater_lag_2_{handled_exchange_name}"),
                "records_consumed_1": app.diagnostics.gauge(f"kafka_eater_records_consumed_1_{handled_exchange_name}"),
                "records_consumed_2": app.diagnostics.gauge(f"kafka_eater_records_consumed_2_{handled_exchange_name}"),
            }
        return diagnostics

    def run(self) -> None:
        exchanges = self.get_exchange_info_from_args()
        exchange_ids_to_handle = list(exchanges.values())
        if set(self.message_processor.exchange_id_key_translator_dict.keys()) < set(exchange_ids_to_handle):
            raise ValueError(
                f"There is no info in self.message_processor.exchange_id_key_translator_dict about KeyTranslators "
                f"of exchanges with ids: "
                f"{set(exchange_ids_to_handle) - set(self.message_processor.exchange_id_key_translator_dict.keys())}"
            )
        with Application(
            prometheus=self.args.prometheus,
            log_level=self.args.log_level,
            log_debug_tags=self.args.log_debug_tags,
        ) as app:
            self.data_saver = DataSaver(self.args.output_path, app.diagnostics)
            self.diagnostics_variables = self._init_diagnostics_variables(app)

            app.diagnostics.info("🚀 Starting Kafka consumption loop...")
            app.diagnostics.info(f"Monitoring {len(self.kafka_consumers)} exchanges")
            app.diagnostics.info(f"Output path: {self.args.output_path}")
            app.diagnostics.info(f"Pause between polls: {self.args.pause or 0} seconds")

            while True:
                for exchange_name in self.kafka_consumers.keys():
                    exchange_id = exchanges[exchange_name]
                    consumed_data_1 = self.poll_consumer(self.kafka_consumers[exchange_name]["consumer_1"], app.diagnostics, exchange_name)
                    consumed_data_2 = self.poll_consumer(self.kafka_consumers[exchange_name]["consumer_2"], app.diagnostics, exchange_name)
                    self.get_and_send_diagnostics_metrics(consumed_data_1, consumed_data_2, exchange_name, app.diagnostics)
                    processed_data_1 = self.message_processor.process(
                        raw_data=consumed_data_1,
                        exchange_ids_to_handle=exchange_ids_to_handle,
                        idx=1,
                        market_type=self.args.market_type,
                    )
                    processed_data_2 = self.message_processor.process(
                        raw_data=consumed_data_2,
                        exchange_ids_to_handle=exchange_ids_to_handle,
                        idx=2,
                        market_type=self.args.market_type,
                    )
                    # pay attention, 'processed_data_1' will be changed as well
                    merged_data = self.merge_data(processed_data_1, processed_data_2)
                    if exchange_id in self.exchange_id_exchange_handler_dict:
                        exchange_handler = self.exchange_id_exchange_handler_dict[exchange_id]
                    else:
                        exchange_handler = ExchangeHandler(
                            self.message_processor.exchange_id_key_translator_dict[exchange_id],
                            self.prepare_exchange_name(exchange_name),
                            app.diagnostics,
                        )
                        self.exchange_id_exchange_handler_dict[exchange_id] = exchange_handler
                    if merged_data:
                        if diff_topics := set(merged_data.keys()) - {exchange_id}:
                            topic = self._TOPIC_TEMPLATE.format(
                                kafka_topic_prefix=self.args.kafka_topic_prefix, exchange_id=exchange_id
                            )
                            app.diagnostics.error(
                                ValueError(f"In topic '{topic}' found quotes of different topics: {diff_topics}")
                            )
                        processed_data = exchange_handler.process(merged_data[exchange_id])
                        if processed_data:
                            self.data_saver.save(processed_data, exchange_name)
                        if consumed_data_1:
                            last1 = list(consumed_data_1.values())[0][-1]
                            self.kafka_consumers[exchange_name]["consumer_1"].commit(message=last1, asynchronous=False)
                        if consumed_data_2:
                            last2 = list(consumed_data_2.values())[0][-1]
                            self.kafka_consumers[exchange_name]["consumer_2"].commit(message=last2, asynchronous=False)
                self.make_pause()

    def poll_consumer(self, consumer: Consumer, diagnostics: Diagnostics, exchange_name: str = "") -> Dict[TopicPartition, List[Message]]:
        try:
            msg = consumer.poll(timeout=self.args.kafka_timeout_ms / 1000.0)
            if msg is None:
                return {}
            if msg.error():
                raise KafkaException(msg.error())

            # Log successful message consumption (but not too frequently to avoid spam)
            if hasattr(self, '_message_count'):
                self._message_count += 1
            else:
                self._message_count = 1

            # Log every 10000th message to show activity without spamming
            if self._message_count % 10000 == 0:
                msg_count = self._message_count
                exchange_info = f" exchange '{exchange_name}'" if exchange_name else ""

                # Try to extract exchange_time from the message payload
                exchange_time_info = ""
                try:
                    import json
                    payload = json.loads(msg.value().decode('utf-8'))
                    if 'exchange_time' in payload:
                        exchange_time_info = f" exchange_time '{payload['exchange_time']}'"
                except Exception:
                    # If we can't parse the message, just skip the exchange_time info
                    pass

                diagnostics.info(f"📨 Consumed {msg_count} messages from{exchange_info} topic '{msg.topic()}' partition {msg.partition()}{exchange_time_info}")

            return {TopicPartition(msg.topic(), msg.partition()): [msg]}
        except KafkaException as e:
            diagnostics.error(e, "Kafka error:")
            return {}

    def parse_cli(self) -> Namespace:
        arg_parser = ArgumentParser()
        application_arguments_parser(arg_parser)
        arg_parser.add_argument(
            "exchanges", type=str, help="List of exchanges to store separated by commas. E.g.: 'HitBTC,Coinbase'"
        )
        arg_parser.add_argument("kafka_servers", type=kafka_servers_arguments, help="Kafka server1+server2 to store")
        arg_parser.add_argument("kafka_topic_prefix", type=str, help="Kafka's topic prefix")
        arg_parser.add_argument("output_path", type=str, help="Path to basic directory where KafkaEater stores files.")
        arg_parser.add_argument("--pause", type=int, help="Length of pause between polls in seconds")
        arg_parser.add_argument(
            "--kafka-max-records",
            type=int,
            default=self._KAFKA_POLL_MAX_RECORDS_DEFAULT,
            help="Max records to consumed from kafka in one poll",
        )
        arg_parser.add_argument(
            "--kafka-max-partition-fetch-bytes",
            type=int,
            default=self._KAFKA_MAX_PARTITION_FETCH_BYTES,
            help="Max bytes to consume from kafka in one poll",
        )
        arg_parser.add_argument(
            "--kafka-timeout-ms",
            type=int,
            default=self._KAFKA_TIMEOUT_MS,
            help="Milliseconds spent waiting in poll if data is not available in the buffer",
        )
        arg_parser.add_argument(
            "--market-type",
            type=str,
            default=None,
            help="If set ('spot' or 'futures') KafkaEater will be handling messages of this market type only",
        )
        return arg_parser.parse_args()

    def get_exchange_info_from_args(self) -> Dict[str, int]:
        exchange_names = self.args.exchanges.split(",")
        return {name: self.glib_exchange_instance.exchange_id_by_name(name) for name in exchange_names}

    def merge_data(self, data_1: JsonValue, data_2: JsonValue) -> JsonValue:
        # merge 'data_2' into 'data_1' dict, for performance reasons it mutates 'data_1'
        for key, value in data_2.items():
            if isinstance(value, Mapping):
                data_1[key] = self.merge_data(data_1.get(key, {}), value)
            else:
                data_1[key] = value
        return data_1

    def get_and_send_diagnostics_metrics(
        self,
        raw_data_1: Dict[TopicPartition, List[Message]],
        raw_data_2: Dict[TopicPartition, List[Message]],
        exchange_name: str,
        app_diagnostics: Diagnostics,
    ) -> None:
        try:
            if raw_data_1:
                end_offset = self.kafka_consumers[exchange_name]["consumer_1"].get_watermark_offsets(list(raw_data_1.keys())[0])[
                    1
                ]
                self.diagnostics_variables[exchange_name]["lag_1"].set(end_offset - list(raw_data_1.values())[0][-1].offset())
                self.diagnostics_variables[exchange_name]["records_consumed_1"].set(
                    len(list(raw_data_1.values())[0]) if len(raw_data_1.values()) else 0
                )
            if raw_data_2:
                end_offset = self.kafka_consumers[exchange_name]["consumer_2"].get_watermark_offsets(list(raw_data_2.keys())[0])[
                    1
                ]
                self.diagnostics_variables[exchange_name]["lag_2"].set(end_offset - list(raw_data_2.values())[0][-1].offset())
                self.diagnostics_variables[exchange_name]["records_consumed_2"].set(
                    len(list(raw_data_2.values())[0]) if len(raw_data_2.values()) else 0
                )
        except Exception as exc:
            app_diagnostics.error(exc, "Something is wrong with 'end_offset' extractor")

    def make_pause(self) -> None:
        sleep(self.args.pause or 0)

    def prepare_exchange_name(self, exchange_name: str) -> str:
        name = exchange_name.replace(".", "_")
        return f"{name}_{self.args.market_type}" if self.args.market_type else name

    def _print_input_arguments(self) -> None:
        """Print all input arguments as plain text for debugging purposes."""
        print("=" * 60)
        print("KAFKA EATER - RAW INPUT ARGUMENTS")
        print("=" * 60)
        print(f"Raw args: {self.args}")
        print("=" * 60)


if __name__ == "__main__":
    kafka_eater = KafkaEater()
    kafka_eater.run()
