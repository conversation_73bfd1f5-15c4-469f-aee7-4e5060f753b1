import gzip
import os.path
from collections import defaultdict
from concurrent.futures import ALL_COMPLETED, ThreadPoolExecutor, wait
from os import makedirs
from time import sleep
from typing import DefaultDict, Dict, List

import orjson

from src.utils.diagnostics import Diagnostics
from src.utils.timeutil import dt_from_us
from src.utils.types import JsonValue


class DataSaver:
    SLEEPING_PERIOD_IN_CASE_OF_ERROR = 5
    FILE_NAME_TEMPLATE = "{}.json.gz"

    def __init__(self, path_to_base_dir: str, diagnostics: Diagnostics):
        self.path_to_base_dir = path_to_base_dir
        self.diagnostics = diagnostics
        self.thread_pool_executor = ThreadPoolExecutor()
        makedirs(self.path_to_base_dir, exist_ok=True)

    def save(self, data: Dict[str, List[JsonValue]], exchange_name: str) -> None:
        futures = []
        output_dict: DefaultDict[str, DefaultDict[str, List[JsonValue]]] = defaultdict(lambda: defaultdict(list))
        for symbol_raw, symbol_data_raw in data.items():
            for elem in symbol_data_raw:
                dt = dt_from_us(elem["exchange_time"])
                output_dict[elem["market_type"]][dt.strftime("%Y-%m-%d")].append(elem)

        for market_type, market_type_data in output_dict.items():
            for day, day_data in market_type_data.items():
                file_name = self.FILE_NAME_TEMPLATE.format(day)
                day_data.sort(key=lambda quote: quote["exchange_time"])
                if market_type == "spot":
                    folder_name = "market-quotes-spot"
                elif market_type == "futures":
                    folder_name = "market-quotes-future"
                else:
                    folder_name = "market-quotes-unknown"
                path_to_dir = os.path.join(self.path_to_base_dir, folder_name, exchange_name.lower())
                while True:
                    try:
                        makedirs(path_to_dir, exist_ok=True)
                        path_to_file = os.path.join(path_to_dir, file_name)
                        futures.append(self.thread_pool_executor.submit(self._save_data_to_file, path_to_file, day_data))
                    except (OSError, FileExistsError) as exc:
                        sleep(self.SLEEPING_PERIOD_IN_CASE_OF_ERROR)
                        self.diagnostics.error(exc)
                    else:
                        break
        wait(futures, timeout=180, return_when=ALL_COMPLETED)

    @staticmethod
    def _save_data_to_file(path_to_file: str, data: List[JsonValue]) -> None:
        # open the raw file so we can fsync AFTER gzip closes (writes the trailer)
        with open(path_to_file, "ab") as f:
            # _COMPRESS_LEVEL_FAST = 1
            # _COMPRESS_LEVEL_TRADEOFF = 6
            # _COMPRESS_LEVEL_BEST = 9
            with gzip.GzipFile(fileobj=f, mode="ab", compresslevel=3) as gz:
                gz.writelines(orjson.dumps(v, option=orjson.OPT_APPEND_NEWLINE) for v in data)
                gz.flush()  # push compressed bytes into f (not final)
            # flush() = Python/zlib → kernel; fsync() = kernel → disk.
            f.flush()  # gzip is now closed; CRC/footer are in the file
            os.fsync(f.fileno())  # make the bytes durable

    @staticmethod
    def _sanitize_symbol(symbol: str) -> str:
        return symbol.replace("/", "")
