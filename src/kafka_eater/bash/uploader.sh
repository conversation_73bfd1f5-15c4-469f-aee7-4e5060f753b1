#!/usr/bin/env bash
set -Eeuo pipefail

# ---- Config (minimal env) ----
: "${S3_ENDPOINT_URL:?S3_ENDPOINT_URL is required}"   # e.g. https://mini-minio-cdev1.cnmtrcs.io
: "${AWS_ACCESS_KEY_ID:?AWS_ACCESS_KEY_ID is required}"
: "${AWS_SECRET_ACCESS_KEY:?AWS_SECRET_ACCESS_KEY is required}"
: "${AWS_REGION:=us-east-1}"

export TZ=UTC

SRC_SPOT="/opt/kafka_eater/market-quotes-spot"
SRC_FUTURE="/opt/kafka_eater/market-quotes-future"
DST_SPOT="market-quotes-spot"
DST_FUTURES="market-quotes-futures"

# Fixed tuning (no extra envs)
TRANSFERS=16
CHECKERS=32
UPLOAD_CONCURRENCY=8
CHUNK_SIZE=64M
STATS=30s

LOG() { printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

# ---- Prepare local dirs (safe if already exist) ----
mkdir -p "$SRC_SPOT" "$SRC_FUTURE"

# ---- Configure rclone remote (idempotent) ----
rclone config create minio s3 \
  provider Minio \
  endpoint "$S3_ENDPOINT_URL" \
  env_auth true \
  --non-interactive || true

rclone version

# ---- Write the actual copy runner (called by cron) ----
cat >/usr/local/bin/uploader-copy.sh <<'EOSH'
#!/usr/bin/env bash
set -Eeuo pipefail
export TZ=UTC

SRC_SPOT="/opt/kafka_eater/market-quotes-spot"
SRC_FUTURE="/opt/kafka_eater/market-quotes-future"
DST_SPOT="market-quotes-spot"
DST_FUTURES="market-quotes-futures"

TRANSFERS=16
CHECKERS=32
UPLOAD_CONCURRENCY=8
CHUNK_SIZE=64M
STATS=30s

log(){ printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

copy_one() {
  local src="$1" dst="$2"
  # Ensure directory exists (created on startup, but harmless to check)
  mkdir -p "$src"
  if find "$src" -type f -print -quit | grep -q .; then
    log "Copy start: $src -> minio:$dst"
    rclone copy "$src" "minio:$dst" \
      --s3-no-check-bucket \
      --transfers "$TRANSFERS" \
      --checkers "$CHECKERS" \
      --s3-upload-concurrency "$UPLOAD_CONCURRENCY" \
      --s3-chunk-size "$CHUNK_SIZE" \
      --fast-list \
      --progress \
      --stats "$STATS" \
      -vv
    log "Copy OK: $src -> minio:$dst"
  else
    log "Skip (empty): $src"
  fi
}

copy_one "$SRC_FUTURE" "$DST_FUTURES" || log "WARN: futures copy failed"
copy_one "$SRC_SPOT"   "$DST_SPOT"    || log "WARN: spot copy failed"
EOSH
chmod +x /usr/local/bin/uploader-copy.sh

# ---- Install a daily cron (01:00 UTC) and run crond in foreground ----
mkdir -p /etc/crontabs
printf '0 1 * * * /usr/local/bin/uploader-copy.sh >>/proc/1/fd/1 2>&1\n' >/etc/crontabs/root

LOG "Cron installed: 01:00 UTC daily"
# Use busybox crond if available; fall back to Debian/Ubuntu cron
if command -v crond >/dev/null 2>&1; then
  exec crond -f -l 8 -L /dev/stdout
elif command -v busybox >/dev/null 2>&1; then
  exec busybox crond -f -l 8 -L /dev/stdout
else
  # Last resort: try 'cron' (Ubuntu) in foreground
  exec cron -f
fi
