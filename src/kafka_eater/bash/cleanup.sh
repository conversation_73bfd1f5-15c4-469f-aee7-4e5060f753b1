#!/usr/bin/env bash

# Why we delete asynchronously (not via `rclone move`):
# - Safety: keep a short local cache so failed/partial uploads can be retried.
# - Late data: files are gzip-appendable; late quotes may arrive and need re-upload.
# - Idempotency: `copy/sync` with --min-age avoids racing with writers; re-runs are safe.
# - Observability: readiness/external freshness checks can confirm remote state before pruning.
# - Resilience: tolerates MinIO issues and pod restarts without losing source data.
# - Ops: retention (3d/10d) is handled here, separate from upload throughput tuning.

set -Eeuo pipefail
export TZ=UTC

log(){ printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

log "Starting cleanup..."

: "${DAYS_TO_KEEP:?DAYS_TO_KEEP is required}"

log "DAYS_TO_KEEP: $DAYS_TO_KEEP"

CUTOFF="$(date -u -d "${DAYS_TO_KEEP} days ago" +%F)"

log "CUTOFF: $CUTOFF"

prune_root() {
  local ROOT="$1"
  [ -d "$ROOT" ] || return 0

  for EXCH in "$ROOT"/*; do
    [ -d "$EXCH" ] || continue
    # match YYYY-MM-DD.json.gz, delete if filename date < CUTOFF
    find "$EXCH" \
      -maxdepth 1 -type f -regextype posix-extended \
      -regex '.*/[0-9]{4}-[0-9]{2}-[0-9]{2}\.json\.gz$' -print \
    | awk -v cutoff="$CUTOFF" -F'/' '{ fn=$NF; sub(/\.json\.gz$/,"",fn); if (fn < cutoff) print $0 }' \
    | xargs -r rm -v
  done
}

prune_root "/opt/kafka_eater/market-quotes-future"
prune_root "/opt/kafka_eater/market-quotes-spot"

log "Finished cleanup."
