#!/usr/bin/env bash
set -Eeuo pipefail
export TZ=UTC

SRC_SPOT="/opt/kafka_eater/market-quotes-spot"
SRC_FUTURE="/opt/kafka_eater/market-quotes-future"
DST_SPOT="market-quotes-spot"
DST_FUTURES="market-quotes-futures"

TRANSFERS=16
CHECKERS=32
UPLOAD_CONCURRENCY=8
CHUNK_SIZE=64M
STATS=30s

log(){ printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

copy_one() {
  local src="$1" dst="$2"
  # Ensure directory exists (created on startup, but harmless to check)
  mkdir -p "$src"
  if find "$src" -type f -print -quit | grep -q .; then
    log "Copy start: $src -> minio:$dst"
    rclone copy "$src" "minio:$dst" \
      --s3-no-check-bucket \
      --transfers "$TRANSFERS" \
      --checkers "$CHECKERS" \
      --s3-upload-concurrency "$UPLOAD_CONCURRENCY" \
      --s3-chunk-size "$CHUNK_SIZE" \
      --fast-list \
      --progress \
      --stats "$STATS" \
      -vv
    log "Copy OK: $src -> minio:$dst"
  else
    log "Skip (empty): $src"
  fi
}

copy_one "$SRC_FUTURE" "$DST_FUTURES" || log "WARN: futures copy failed"
copy_one "$SRC_SPOT"   "$DST_SPOT"    || log "WARN: spot copy failed"
