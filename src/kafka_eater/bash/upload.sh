#!/usr/bin/env bash
set -Eeuo pipefail
export TZ=UTC

log(){ printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

log "Starting upload..."

SRC_SPOT="/opt/kafka_eater/market-quotes-spot"
SRC_FUTURE="/opt/kafka_eater/market-quotes-future"

mkdir -p "$SRC_SPOT" "$SRC_FUTURE"

DST_SPOT="market-quotes-spot"
DST_FUTURES="market-quotes-futures"

# Max number of files uploaded in parallel. Higher = more throughput (up to your disk, network, and server limits) but more load.
TRANSFERS=1
# Number of parallel “check” workers that do metadata/listing/existence checks on the remote. Helps keep the pipeline full when latency is non-trivial.
CHECKERS=8
# Per-file multipart concurrency: how many parts of a single large object are uploaded simultaneously. Great for big files over high-bandwidth links. Note this multiplies per-file memory/connection usage.
UPLOAD_CONCURRENCY=6
# Size of each multipart chunk for S3 uploads. Bigger chunks = fewer parts & better throughput, but more memory per concurrent part. Your setting balances speed with resource use.
CHUNK_SIZE=64M
# Prints aggregate stats (speed, ETA, errors) every 30 seconds. Good cadence for long-running jobs.
STATS=30s
# Skips files younger than 30 minutes. This creates a “stable window” so partially-written files (e.g., still being produced by Kafka consumers) aren’t picked up until they’ve aged past 30 minutes.
STABLE_WINDOW="30m"
# In-memory read-ahead buffer per active transfer; bounds extra RAM use on top of multipart part buffers—raise for throughput, lower to save memory.
BUFFER_SIZE=16M

copy_one() {
  local src="$1" dst="$2"
  if find "$src" -type f -print -quit | grep -q .; then
    log "Copy start: $src -> minio:$dst"
    rclone copy "$src" "minio:$dst" \
      --s3-no-check-bucket \
      --transfers "$TRANSFERS" \
      --checkers "$CHECKERS" \
      --s3-upload-concurrency "$UPLOAD_CONCURRENCY" \
      --s3-chunk-size "$CHUNK_SIZE" \
      --buffer-size "$BUFFER_SIZE" \
      --fast-list \
      --progress \
      --stats "$STATS" \
      --min-age "$STABLE_WINDOW" \
      -vv
    log "Copy OK: $src -> minio:$dst"
  else
    log "Skip (empty): $src"
  fi
}

LOCKDIR=/tmp/upload.lock
if mkdir "$LOCKDIR" 2>/dev/null; then
  trap 'rmdir "$LOCKDIR"' EXIT
else
  log "another copy is running; skipping"
  exit 1
fi

copy_one "$SRC_FUTURE" "$DST_FUTURES" || log "WARN: futures copy failed"
copy_one "$SRC_SPOT"   "$DST_SPOT"    || log "WARN: spot copy failed"

log "Finished upload."
