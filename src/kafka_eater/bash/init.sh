#!/usr/bin/env bash
set -Eeuo pipefail
export TZ=UTC

log() { printf '[%s] %s\n' "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" "$*"; }

log "Starting initialization..."

: "${S3_ENDPOINT_URL:?S3_ENDPOINT_URL is required}"

log "S3_ENDPOINT_URL: $S3_ENDPOINT_URL"

: "${AWS_ACCESS_KEY_ID:?AWS_ACCESS_KEY_ID is required}"

: "${AWS_SECRET_ACCESS_KEY:?AWS_SECRET_ACCESS_KEY is required}"

: "${AWS_REGION:?AWS_REGION is required}"

log "AWS_REGION: $AWS_REGION"

: "${DAYS_TO_KEEP:?DAYS_TO_KEEP is required}"

log "DAYS_TO_KEEP: $DAYS_TO_KEEP"

log "Printing rclone version..."

rclone version

log "Creating rclone remote 'minio' at $S3_ENDPOINT_URL (region=$AWS_REGION)..."
rclone config create minio s3 \
  provider Minio \
  endpoint "$S3_ENDPOINT_URL" \
  access_key_id "$AWS_ACCESS_KEY_ID" \
  secret_access_key "$AWS_SECRET_ACCESS_KEY" \
  region "$AWS_REGION" \
  --non-interactive

log "Ensuring rclone remote 'minio' exists..."
rclone config show minio | grep endpoint

log "Running initial sync..."
/usr/local/bin/upload.sh 2>&1 && /usr/local/bin/cleanup.sh 2>&1 && date +\%s > /tmp/last_sync_ok

log "Installing cron schedule..."
cat >/etc/cron.d/sync <<CRON
DAYS_TO_KEEP=${DAYS_TO_KEEP}
45 * * * * root /usr/local/bin/upload.sh >>/proc/1/fd/1 2>&1 && /usr/local/bin/cleanup.sh >>/proc/1/fd/1 2>&1 && date +\%s > /tmp/last_sync_ok
CRON
chmod 600 /etc/cron.d/sync

log "Starting cron..."
exec cron -f
