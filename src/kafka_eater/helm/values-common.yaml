---
persistence:
  storageClassName: "zfs"

tolerations:
  - key: "coinmetrics.io/zfs-only"
    operator: "Exists"
    effect: "NoExecute"

kafkaEater:
  defaults:
    kafkaServers: "kafka-trades-1.kafka.svc+kafka-trades-2.kafka.svc"
    kafkaTopicPrefix: "quotes"
    outputPath: "/opt/kafka_eater"

  containers:
    - containerName: binance
      create: true
      exchange: "Binance"
      kafkaTimeoutMs: "500"
      kafkaMaxRecords: "100000"
      kafkaMaxPartitionFetchBytes: "104857600"
      resources:
        requests:
          cpu: "0.1"
          memory: "5Gi"
        limits:
          memory: "5Gi"

    - containerName: coinbase
      create: true
      exchange: "Coinbase"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: binance-us
      create: true
      exchange: "Binance.US"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: hitbtc
      create: true
      exchange: "HitBTC"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: kraken
      create: true
      exchange: "Kraken"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: okex
      create: true
      exchange: "OKEx"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: huobi-spot
      create: true
      exchange: "Huobi"
      marketType: "spot"
      kafkaTimeoutMs: "500"
      kafkaMaxRecords: "100000"
      kafkaMaxPartitionFetchBytes: "104857600"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: huobi-futures
      create: true
      exchange: "Huobi"
      marketType: "futures"
      pause: "1"
      kafkaTimeoutMs: "500"
      kafkaMaxRecords: "100000"
      kafkaMaxPartitionFetchBytes: "104857600"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: gemini
      create: true
      exchange: "Gemini"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: deribit
      create: true
      exchange: "Deribit"
      pause: "1"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

    - containerName: bybit
      create: true
      exchange: "Bybit"
      resources:
        requests:
          cpu: "0.1"
          memory: "2Gi"
        limits:
          memory: "2Gi"

sync:
  create: true
