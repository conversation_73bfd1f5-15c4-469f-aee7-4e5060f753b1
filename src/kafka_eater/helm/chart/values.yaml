---
image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  pullPolicy: IfNotPresent
  tag: ""

nameOverride: ""
fullnameOverride: ""

replicaCount: "1"

persistence:
  storageClassName: ""
  size: ""
tolerations: []
nodeAffinity: []

kafkaEater:
  # List of exchanges to store separated by commas. E.g.: 'HitBTC,Coinbase'
  exchanges: ""
  # Kafka server1+server2 to store
  kafkaServers: ""
  # Kafka's topic prefix
  kafkaTopicPrefix: ""
  # Path to basic directory where KafkaEater stores files.
  outputPath: "/opt/kafka_eater"
  # Length of pause between polls in seconds
  pause: ""
  # Max records to consumed from kafka in one poll
  kafkaMaxRecords: ""
  # Max bytes to consume from kafka in one poll
  kafkaMaxPartitionFetchBytes: ""
  # Milliseconds spent waiting in poll if data is not available in the buffer
  kafkaTimeoutMs: ""
  # If set ('spot' or 'futures') KafkaEater will be handling messages of this market type only
  marketType: ""
  resources:
    requests:
      cpu: "0.1"
      memory: "1Gi"
    limits:
      memory: "1Gi"

uploader:
  create: false
  s3EndpointUrl: ""
  awsAccessKeyId: ""
  awsSecretAccessKey: ""
  awsRegion: ""
  inputPath: "/opt/kafka_eater"
  resources:
    requests:
      cpu: "0.1"
      memory: "0.25Gi"
    limits:
      memory: "0.25Gi"

cleanup:
  create: false
  daysToKeep: 5
  inputPath: "/opt/kafka_eater"
  resources:
    requests:
      cpu: "0.1"
      memory: "0.25Gi"
    limits:
      memory: "0.25Gi"
