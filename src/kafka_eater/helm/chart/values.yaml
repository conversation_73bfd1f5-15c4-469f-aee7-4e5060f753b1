---
image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  pullPolicy: IfNotPresent
  tag: ""

nameOverride: "md-factory"
fullnameOverride: ""

replicaCount: 1

persistence:
  storageClassName: ""
  size: ""
tolerations: []
nodeAffinity: []

kafkaEater:
  # Global defaults for all kafka eater containers
  defaults:
    kafkaServers: ""
    kafkaTopicPrefix: "quotes"
    outputPath: "/opt/kafka_eater"
    kafkaMaxRecords: "10000"
    kafkaMaxPartitionFetchBytes: "10485760"
    kafkaTimeoutMs: "200"
    resources:
      requests:
        cpu: "0.1"
        memory: "1Gi"
      limits:
        memory: "1Gi"

  # Individual exchange configurations
  containers: []

sync:
  create: false
  s3EndpointUrl: ""
  awsAccessKeyId: ""
  awsSecretAccessKey: ""
  awsRegion: ""
  # rclone copy - Copy files from source to dest, skipping already copied.
  # rclone sync - Make source and dest identical, modifying destination only.
  daysToKeep: 3
  inputPath: "/opt/kafka_eater"
  resources:
    requests:
      cpu: "0.1"
      memory: "1Gi"
    limits:
      memory: "1Gi"
