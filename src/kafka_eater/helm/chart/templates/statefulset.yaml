apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "kafka-eater.fullname" . }}
  labels:
    {{- include "kafka-eater.labels" . | nindent 4 }}
    app.kubernetes.io/component: kafka-eater
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "kafka-eater.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: kafka-eater
  serviceName: kafka-eater
  template:
    metadata:
      labels:
        {{- include "kafka-eater.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: kafka-eater
    spec:
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- with .Values.nodeAffinity }}
        nodeAffinity:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        {{- range $index, $container := .Values.kafkaEater.containers }}
        {{- if $container.create }}
        - name: kafka-eater-{{ $container.containerName }}
          command: ["python", "-m", "src.kafka_eater.applications.kafka_eater"]
          args:
            - {{ $container.exchange | default $container.containerName | quote }}
            - {{ $container.kafkaServers | default $.Values.kafkaEater.defaults.kafkaServers | quote }}
            - {{ $container.kafkaTopicPrefix | default $.Values.kafkaEater.defaults.kafkaTopicPrefix | quote }}
            - {{ $container.outputPath | default $.Values.kafkaEater.defaults.outputPath | quote }}
            - "--prometheus"
            - "[::]:{{ add 8000 $index }}"
            - "--readiness-port"
            - "{{ add 8082 $index }}"
            {{- with $container.pause }}
            - "--pause={{ . }}"
            {{- end }}
            {{- with ($container.kafkaTimeoutMs | default $.Values.kafkaEater.defaults.kafkaTimeoutMs) }}
            - "--kafka-timeout-ms={{ . }}"
            {{- end }}
            {{- with ($container.kafkaMaxRecords | default $.Values.kafkaEater.defaults.kafkaMaxRecords) }}
            - "--kafka-max-records={{ . }}"
            {{- end }}
            {{- with ($container.kafkaMaxPartitionFetchBytes | default $.Values.kafkaEater.defaults.kafkaMaxPartitionFetchBytes) }}
            - "--kafka-max-partition-fetch-bytes={{ . }}"
            {{- end }}
            {{- with $container.marketType }}
            - "--market-type={{ . }}"
            {{- end }}
          image: "{{ $.Values.image.repository }}:{{ $.Values.image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          ports:
            - containerPort: {{ add 8000 $index }}
              name: http{{ $index }}
              protocol: TCP
          {{- with ($container.resources | default $.Values.kafkaEater.defaults.resources) }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: "{{ $container.outputPath | default $.Values.kafkaEater.defaults.outputPath }}"
        {{- end }}
        {{- end }}

        {{- if .Values.uploader.create }}
        - name: uploader
          command: ["/bin/sh", "-c"]
          args:
            - |
              echo "Starting uploader..."
              echo "S3_ENDPOINT_URL: $S3_ENDPOINT_URL"
              echo "AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID"
              echo "AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY"
              echo "S3_ENDPOINT_URL: $AWS_REGION"
              echo "Done."
              tail -f /dev/null
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: S3_ENDPOINT_URL
              value: "{{ .Values.uploader.s3EndpointUrl }}"
            - name: AWS_REGION
              value: "{{ .Values.uploader.awsRegion }}"
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "kafka-eater.fullname" . }}
                  key: awsAccessKeyId
                  optional: false
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "kafka-eater.fullname" . }}
                  key: awsSecretAccessKey
                  optional: false
          {{- with .Values.uploader.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: "{{ .Values.uploader.inputPath }}"
        {{- end }}

        {{- if .Values.cleanup.create }}
        - name: cleanup
          command: ["/bin/sh", "-c"]
          args:
            - |
              echo "Starting cleanup..."
              echo "Days to keep: {{ .Values.cleanup.daysToKeep }}"
              echo "Done."
              tail -f /dev/null
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: DAYS_TO_KEEP
              value: "{{ .Values.cleanup.daysToKeep }}"
          {{- with .Values.cleanup.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: "{{ .Values.cleanup.inputPath }}"
        {{- end }}
      terminationGracePeriodSeconds: 10
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: state
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: {{ .Values.persistence.size }}
        storageClassName: {{ .Values.persistence.storageClassName }}
        volumeMode: Filesystem
