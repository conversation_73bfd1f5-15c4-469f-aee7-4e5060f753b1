---
persistence:
  storageClassName: "zfs"
  size: "500Gi"

tolerations:
  - key: "coinmetrics.io/zfs-only"
    operator: "Exists"
    effect: "NoExecute"

kafkaEater:
  defaults:
    kafkaServers: "kafka-trades-1.kafka.svc+kafka-trades-2.kafka.svc"
    kafkaTopicPrefix: "quotes"
    outputPath: "/opt/kafka_eater"

  containers:
    - containerName: binance
      create: true
      exchange: "Binance"
      kafkaTimeoutMs: "600"
      kafkaMaxRecords: "500000"
      kafkaMaxPartitionFetchBytes: "104857600"
      resources:
        requests:
          cpu: "0.1"
          memory: "5Gi"
        limits:
          memory: "5Gi"

    - containerName: coinbase
      create: true
      exchange: "Coinbase"
      pause: "10"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: binance-us
      create: true
      exchange: "Binance.US"
      pause: "35"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: hitbtc
      create: true
      exchange: "HitBTC"
      pause: "20"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: kraken
      create: true
      exchange: "Kraken"
      pause: "35"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: okex
      create: true
      exchange: "OKEx"
      kafkaTimeoutMs: "500"
      resources:
        requests:
          cpu: "0.1"
          memory: "3Gi"
        limits:
          memory: "3Gi"

    - containerName: huobi-spot
      create: true
      exchange: "Huobi"
      marketType: "spot"
      kafkaTimeoutMs: "700"
      kafkaMaxRecords: "800000"
      kafkaMaxPartitionFetchBytes: "254857600"
      resources:
        requests:
          cpu: "0.1"
          memory: "6Gi"
        limits:
          memory: "6Gi"

    - containerName: huobi-futures
      create: true
      exchange: "Huobi"
      marketType: "futures"
      pause: "5"
      kafkaTimeoutMs: "500"
      resources:
        requests:
          cpu: "0.1"
          memory: "6Gi"
        limits:
          memory: "6Gi"

    - containerName: gemini
      create: true
      exchange: "Gemini"
      pause: "90"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: deribit
      create: true
      exchange: "Deribit"
      pause: "100"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

    - containerName: bybit
      create: true
      exchange: "Bybit"
      resources:
        requests:
          cpu: "0.1"
          memory: "1Gi"
        limits:
          memory: "1Gi"

uploader:
  create: true
  s3EndpointUrl: "https://mini-minio-cdev1.cnmtrcs.io"
  awsRegion: "us-east-1"

cleanup:
  create: true
