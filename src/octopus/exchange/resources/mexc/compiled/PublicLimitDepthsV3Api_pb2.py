# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: PublicLimitDepthsV3Api.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cPublicLimitDepthsV3Api.proto\"\x90\x01\n\x16PublicLimitDepthsV3Api\x12(\n\x04\x61sks\x18\x01 \x03(\x0b\x32\x1a.PublicLimitDepthV3ApiItem\x12(\n\x04\x62ids\x18\x02 \x03(\x0b\x32\x1a.PublicLimitDepthV3ApiItem\x12\x11\n\teventType\x18\x03 \x01(\t\x12\x0f\n\x07version\x18\x04 \x01(\t\"<\n\x19PublicLimitDepthV3ApiItem\x12\r\n\x05price\x18\x01 \x01(\t\x12\x10\n\x08quantity\x18\x02 \x01(\tB?\n\x1c\x63om.mxc.push.common.protobufB\x1bPublicLimitDepthsV3ApiProtoH\x01P\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'PublicLimitDepthsV3Api_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.mxc.push.common.protobufB\033PublicLimitDepthsV3ApiProtoH\001P\001'
  _PUBLICLIMITDEPTHSV3API._serialized_start=33
  _PUBLICLIMITDEPTHSV3API._serialized_end=177
  _PUBLICLIMITDEPTHV3APIITEM._serialized_start=179
  _PUBLICLIMITDEPTHV3APIITEM._serialized_end=239
# @@protoc_insertion_point(module_scope)
