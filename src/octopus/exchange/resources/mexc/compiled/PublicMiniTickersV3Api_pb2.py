# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: PublicMiniTickersV3Api.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from src.octopus.exchange.resources.mexc.compiled import PublicMiniTickerV3Api_pb2 as PublicMiniTickerV3Api__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cPublicMiniTickersV3Api.proto\x1a\x1bPublicMiniTickerV3Api.proto\"?\n\x16PublicMiniTickersV3Api\x12%\n\x05items\x18\x01 \x03(\x0b\x32\x16.PublicMiniTickerV3ApiB?\n\x1c\x63om.mxc.push.common.protobufB\x1bPublicMiniTickersV3ApiProtoH\x01P\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'PublicMiniTickersV3Api_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.mxc.push.common.protobufB\033PublicMiniTickersV3ApiProtoH\001P\001'
  _PUBLICMINITICKERSV3API._serialized_start=61
  _PUBLICMINITICKERSV3API._serialized_end=124
# @@protoc_insertion_point(module_scope)
