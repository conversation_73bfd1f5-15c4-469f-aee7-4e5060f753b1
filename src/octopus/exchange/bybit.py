import csv
import gzip
import re
import threading
import urllib.parse
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from enum import StrEnum
from functools import partial
from http import HTTPStatus
from operator import attrgetter
from pathlib import Path
from typing import Callable, Dict, Iterable, List, Optional

import ciso8601
from requests import Response

from src.octopus.data import (
    BookData,
    BookType,
    ExchangeInstrument,
    FundingRateData,
    FuturesContractData,
    FuturesTickerData,
    Instrument,
    LiquidationData,
    MarketType,
    OpenInterestData,
    OptionContractData,
    OptionTickerData,
    OptionType,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.entry_history import TraversalBase, TraversalResult
from src.octopus.exceptions import InconsistentResponse, MarketError, NoLongerSupportedError, UnsupportedMarketType
from src.octopus.exchange.api import (
    BookStreamParams,
    ExchangeMarkets,
    HistoryMarker,
    IClientFactory,
    IHistoryTraversal,
    Instruments,
    LiquidationStreamParams,
    TradeStreamParams,
    TraversalMethod,
)
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.exchange.cryptocom import NoDataReconnectStream
from src.octopus.stream_components.book import BookComponent
from src.octopus.stream_components.error import ErrorHandlingComponent
from src.octopus.stream_components.ping import PingComponent
from src.octopus.stream_components.subscription import SubscriptionComponent, SubscriptionManagerBase, SubscriptionUpdate
from src.octopus.stream_components.trade import LiquidationComponent, TradeComponent
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.utils.cache import Cache
from src.utils.diagnostics import Diagnostics
from src.utils.execution import IRunnable, MultiRunnable
from src.utils.http import IHttpClient, get_json
from src.utils.stream import ComponentProcessor, JsonTranslator, Stream, StreamParams
from src.utils.timeutil import (
    EARLIEST_ALLOWED_DATE,
    dt_from_any,
    dt_from_any_aware,
    dt_from_ms,
    dt_from_ms_aware,
    dt_from_s,
    open_interest_timestamp,
    truncate_to_minute,
)
from src.utils.trade_id import _make_trade_id
from src.utils.types import JsonValue

"""
Bybit API Documentation: https://bybit-exchange.github.io/docs/spot/#t-introduction
"""

EXCHANGE_NAME = "Bybit"
EXCHANGE_BIRTHDAY = datetime(2018, 11, 14)
TRADE_HISTORY_URL = "https://public.bybit.com/trading"
THROTTLE_ROWS_PER_MIN = 500_000
_TRADE_ID_CHANGE_TS = int(datetime(year=2022, month=4, day=17, tzinfo=timezone.utc).timestamp() * 1000.0)  # TS in MS
_TRADE_ID_CHANGE_TS_2 = int(datetime(year=2022, month=9, day=2, tzinfo=timezone.utc).timestamp() * 1000.0)  # TS in MS
NON_PERPETUAL_SYMBOL_RE = re.compile(r"(\w+-\d{2}\w{3}\d{2})")
MAXIMUM_NO_DATA_TOLERANCE = timedelta(minutes=6)
MAX_MARKETS_PER_REQUEST = 1000


class BybitFuturesType(StrEnum):
    LINEAR = "linear"
    INVERSE = "inverse"


def _make_spot_trade_id(
    time_ms: int, price: Decimal, amount: Decimal, is_buy: Optional[bool], exchange_id: Optional[int]
) -> int:
    if time_ms < _TRADE_ID_CHANGE_TS:
        return time_ms

    if time_ms < _TRADE_ID_CHANGE_TS_2:
        return _make_trade_id(str(time_ms), price, amount, is_buy)

    if not exchange_id:
        raise NoLongerSupportedError("Exchange provided no 'trade_id'.")

    return exchange_id


class BybitHttpApi(ExchangeHttpApiBase):
    _BASE_URL = "https://api.bybit.com"
    SPOT_MARKETS_URL = "https://api.bybit.com/v5/market/instruments-info?category=spot"
    SPOT_TRADES_URL = "https://api.bybit.com/v5/market/recent-trade?category=spot"
    BOOKS_URL = "https://api.bybit.com/v5/market/orderbook"
    FUTURES_MARKETS_LINEAR_URL = "https://api.bybit.com/v5/market/instruments-info?category=linear"
    FUTURES_MARKETS_INVERSE_URL = "https://api.bybit.com/v5/market/instruments-info?category=inverse"
    OPTION_MARKETS_URL = "https://api.bybit.com/v5/market/instruments-info?category=option&baseCoin={}"
    TICKERS_URL_V5 = "https://api.bybit.com/v5/market/tickers"
    FUTURES_TICKER_CACHE_TTL = 15
    INSTRUMENT_SUSPENSION_PERIOD = timedelta(hours=1)
    KNOWN_METADATA_LISTING_DATE: Dict[str, datetime] = {
        "USTCUSDT": ciso8601.parse_datetime("2022-05-25T13:55:18.034000+0000"),
        "LUNCUSDC": ciso8601.parse_datetime("2022-05-25T13:54:39.451000+0000"),
    }
    OPTION_BASE_COINS = ["BTC", "ETH", "SOL"]
    _OPTION_OPEN_INTEREST_CACHE_TTL = 55

    _TICKER_CACHE_TTL = 35

    def __init__(self, exchange_name: str, ticker_translator: ExchangeTickerTranslator, diagnostics: Diagnostics):
        super().__init__(exchange_name, ticker_translator, diagnostics)
        self._futures_historical_delay = 0
        self._futures_ticker_cache = Cache[Dict[str, FuturesTickerData]](ttl=self.FUTURES_TICKER_CACHE_TTL)
        self._futures_ticker_cache_lock = threading.Lock()
        self._option_settle_price_key: str = ""
        self._option_settle_price_value: Optional[float] = None
        self._ticker_cache = Cache[Dict[str, OptionTickerData]](ttl=self._TICKER_CACHE_TTL)
        self._ticker_cache_lock = threading.Lock()
        self._option_open_interest_cache = Cache[Dict[str, OpenInterestData]](ttl=self._OPTION_OPEN_INTEREST_CACHE_TTL)
        self._option_open_interest_lock = threading.Lock()

    @staticmethod
    def update_paginated_markets_url(url: str, limit: int, cursor: Optional[str] = None) -> str:
        url_parts = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(url_parts.query)
        query_params["limit"] = [str(limit)]
        if cursor:
            query_params["cursor"] = [cursor]
        else:
            query_params.pop("cursor", None)
        updated_query = urllib.parse.urlencode(query_params, doseq=True)
        return str(urllib.parse.urlunparse(url_parts._replace(query=updated_query)))

    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        result = []
        for market_info in self._get_spot_markets(client):
            symbol = market_info["symbol"]
            base = market_info["baseCoin"].lower()
            quote = market_info["quoteCoin"].lower()
            result.append(ExchangeInstrument(MarketType.SPOT, symbol, base, quote))
        return self.translator.exchange_to_coinmetrics(result)

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for market_info in self._get_spot_markets(client):
            base = market_info["baseCoin"].lower()
            quote = market_info["quoteCoin"].lower()
            symbol = market_info["symbol"]
            lot_size_filter = market_info["lotSizeFilter"]
            price_filter = market_info["priceFilter"]

            listing_date = self.KNOWN_METADATA_LISTING_DATE.get(symbol, SpotContractData.DEFAULT_LISTING_DATE)
            contract_data = SpotContractData(
                symbol=symbol,
                base_id=self.translator.to_cm_id(base),
                quote_id=self.translator.to_cm_id(quote),
                base_name=self.translator.translate(base),
                quote_name=self.translator.translate(quote),
                native_base_name=market_info["baseCoin"],
                native_quote_name=market_info["quoteCoin"],
                listing_date=listing_date,
                end_date=None,
                is_current=True,
                amount_increment=Decimal(lot_size_filter["basePrecision"]),
                amount_size_min=Decimal(lot_size_filter["minOrderQty"]),
                order_size_min=Decimal(lot_size_filter["minOrderAmt"]),
                amount_size_max=Decimal(lot_size_filter["maxOrderQty"]),
                price_increment=Decimal(price_filter["tickSize"]),
                status="online" if market_info["status"] == "1" else "offline",
            )
            result.append(contract_data)
        return result

    def _get_spot_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        # Spot does not support pagination, so limit, cursor are invalid.
        # https://bybit-exchange.github.io/docs/v5/market/instrument
        return list(get_json(client.get(self.SPOT_MARKETS_URL, timeout=15))["result"]["list"])

    def futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        markets = [
            Instrument.futures(
                row["symbol"], metadata={"funding_interval": row["fundingInterval"], "type": BybitFuturesType.LINEAR}
            )
            for row in self._get_futures_linear_markets(client)
        ]
        markets.extend([
            Instrument.futures(
                row["symbol"], metadata={"funding_interval": row["fundingInterval"], "type": BybitFuturesType.INVERSE}
            )
            for row in self._get_futures_inverse_markets(client)
        ])
        return ExchangeMarkets(markets)

    def futures_markets_metadata(self, client: IHttpClient) -> List[FuturesContractData]:
        result: List[FuturesContractData] = []

        for row in self._get_futures_markets(client):
            symbol = row["symbol"]
            base = row["baseCoin"].lower()
            quote = row["quoteCoin"].lower()
            expiry_date = None
            if row.get("deliveryTime") and row["deliveryTime"] != "0":
                expiry_date = dt_from_any(row["deliveryTime"])
            elif re.match(NON_PERPETUAL_SYMBOL_RE, symbol):
                expiry_date_str = symbol.split("-")[1]
                expiry_date = datetime.strptime(f"{expiry_date_str} 08", "%d%b%y %H")

            if base.startswith("10000"):
                base = base.replace("10000", "")
                contract_size = Decimal(10000)
            elif "1000" in base:
                # Special case any "xxxx1000" and "1000xxxx" symbols
                base = base.replace("1000", "")
                contract_size = Decimal(1000)
            else:
                contract_size = Decimal(1)

            base_id = self.translator.to_cm_id(base)
            quote_id = self.translator.to_cm_id(quote)
            if row["settleCoin"].lower() == "usdc":
                size_asset_name, size_asset_id = base, base_id
                margin_asset_name, margin_asset_id = "usdc", self.translator.to_cm_id("usdc")
            elif quote == "usdt":
                size_asset_name, size_asset_id = base, base_id
                margin_asset_name, margin_asset_id = quote, quote_id
            else:
                size_asset_name, size_asset_id = quote, quote_id
                margin_asset_name, margin_asset_id = base, base_id

            contract_data = FuturesContractData(
                symbol=symbol,
                underlying_base_id=base_id,
                underlying_base_name=self.translator.translate(base),
                underlying_quote_id=quote_id,
                underlying_quote_name=self.translator.translate(quote),
                underlying_native_base_name=row["baseCoin"],
                underlying_native_quote_name=row["quoteCoin"],
                size_asset_id=size_asset_id,
                size_asset_name=self.translator.translate(size_asset_name),
                margin_asset_id=margin_asset_id,
                margin_asset_name=self.translator.translate(margin_asset_name),
                listing_date=self._get_first_date_from_html(symbol, client),
                expiry_date=expiry_date,
                contract_size=contract_size,
                tick_size=Decimal(row["priceFilter"]["tickSize"]),
                amount_increment=Decimal(row["lotSizeFilter"]["qtyStep"]),
                amount_size_min=Decimal(row["lotSizeFilter"]["minOrderQty"]),
                amount_size_max=Decimal(row["lotSizeFilter"]["maxOrderQty"]),
                price_increment=Decimal("0.1") ** int(row["priceScale"]),
                price_size_min=Decimal(row["priceFilter"]["minPrice"]),
                price_size_max=Decimal(row["priceFilter"]["maxPrice"]),
            )
            result.append(contract_data)
        return result

    def option_markets(self, client: IHttpClient) -> ExchangeMarkets:
        markets = [Instrument.option(row["symbol"]) for row in self._get_option_markets(client) if row["status"] == "Trading"]
        return ExchangeMarkets(markets)

    def _get_futures_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        markets = self._get_futures_linear_markets(client)
        markets.extend(self._get_futures_inverse_markets(client))
        return markets

    def get_paginated_markets(self, client: IHttpClient, url: str) -> List[Dict[str, JsonValue]]:
        markets: List[Dict[str, JsonValue]] = []
        url = self.update_paginated_markets_url(url, MAX_MARKETS_PER_REQUEST)
        result = None
        while result is None or len(result.get("nextPageCursor", "")) > 0:
            result = get_json(client.get(url=url, timeout=15))["result"]
            markets.extend(result["list"])
            cursor = result.get("nextPageCursor")
            url = self.update_paginated_markets_url(url, MAX_MARKETS_PER_REQUEST, cursor)
        return markets

    def _get_option_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        markets = []
        for base_coin in self.OPTION_BASE_COINS:
            markets.extend(self.get_paginated_markets(client, self.OPTION_MARKETS_URL.format(base_coin)))
        return markets

    def _get_futures_linear_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return self.get_paginated_markets(client, self.FUTURES_MARKETS_LINEAR_URL)

    def _get_futures_inverse_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return self.get_paginated_markets(client, self.FUTURES_MARKETS_INVERSE_URL)

    def historical_futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        return self.futures_markets(client)

    def futures_markets_perpetual(self, client: IHttpClient) -> ExchangeMarkets:
        return self.futures_markets(client)

    @staticmethod
    def parse_option_strike_price(symbol: str) -> Decimal:
        # parse strike price from symbol e.g. BTC-26SEP25-120000-C
        return Decimal(symbol.split("-")[2])

    def option_settle_price(self, http_client: IHttpClient, symbol: str) -> Optional[float]:
        parts = symbol.split("-")  # expecting BTC-27MAR21-55000-C
        cache_key = f"{parts[0]}-{parts[1]}"
        if self._option_settle_price_key != cache_key:
            settle_price = self._get_option_settle_price(http_client, symbol)
            self._option_settle_price_key = cache_key
            self._option_settle_price_value = settle_price

        return self._option_settle_price_value

    def _get_option_settle_price(self, http_client: IHttpClient, symbol: str) -> Optional[float]:
        url = f"{self._BASE_URL}/v5/market/delivery-price?category=option&symbol={symbol}"
        data = get_json(http_client.get(url))["result"]["list"]
        for datum in data:
            if datum["symbol"] == symbol:
                return float(datum["deliveryPrice"])
        return None

    def option_metadata(self, client: IHttpClient) -> List[OptionContractData]:
        res = []
        for market in self._get_option_markets(client):
            quote = market["quoteCoin"].lower()
            quote_id = self.translator.to_cm_id(quote)
            base = market["baseCoin"].lower()
            base_id = self.translator.to_cm_id(base)
            tick_size = Decimal(market["priceFilter"]["tickSize"])
            res.append(
                OptionContractData(
                    strike=self.parse_option_strike_price(market["symbol"]),
                    size=Decimal("1"),
                    is_european=True,
                    symbol=market["symbol"],
                    underlying_base_id=base_id,
                    underlying_quote_id=quote_id,
                    size_asset_id=base_id,
                    margin_asset_id=quote_id,
                    underlying_base_name=base,
                    underlying_quote_name=quote,
                    size_asset_name=base,
                    margin_asset_name=quote,
                    expiry_date=dt_from_any(market["deliveryTime"]),
                    option_type=OptionType.CALL if market["optionsType"] == "Call" else OptionType.PUT,
                    listing_date=dt_from_any(market["launchTime"]),
                    amount_increment=Decimal(market["lotSizeFilter"]["qtyStep"]),
                    amount_size_min=Decimal(market["lotSizeFilter"]["minOrderQty"]),
                    amount_size_max=Decimal(market["lotSizeFilter"]["maxOrderQty"]),
                    price_size_max=Decimal(market["priceFilter"]["maxPrice"]),
                    price_size_min=Decimal(market["priceFilter"]["minPrice"]),
                    tick_size=tick_size,
                    status="online" if market["status"].lower() == "trading" else "offline",
                    price_increment=tick_size,
                    price_asset_name=quote,
                    price_asset_id=quote_id,
                )
            )
        return res

    def option_ticker(self, client: IHttpClient, instrument: Instrument) -> OptionTickerData:
        with self._ticker_cache_lock:
            results = self._ticker_cache.get()
            if not results:
                results = self._get_all_option_tickers(client)
                self._ticker_cache.add(results)
            return results[instrument.symbol]

    def _get_all_option_tickers(self, client: IHttpClient) -> Dict[str, OptionTickerData]:
        results: Dict[str, OptionTickerData] = {}
        for base_coin in self.OPTION_BASE_COINS:
            dt = datetime.now(timezone.utc)
            for ticker in get_json(
                client.get(f"https://api.bybit.com/v5/market/tickers?category=option&baseCoin={base_coin.upper()}")
            )["result"]["list"]:
                results[ticker["symbol"]] = OptionTickerData(
                    time=truncate_to_minute(dt),
                    exchange_time=dt,
                    price_last=Decimal(ticker["lastPrice"]),
                    price_bid=Decimal(ticker["bid1Price"]),
                    price_ask=Decimal(ticker["ask1Price"]),
                    price_mark=Decimal(ticker["markPrice"]),
                    price_index=Decimal(ticker["indexPrice"]),
                    amount_bid=Decimal(ticker["bid1Size"]),
                    amount_ask=Decimal(ticker["ask1Size"]),
                    index_name="",
                    implied_vol_trade=None,
                    implied_vol_bid=Decimal(ticker["bid1Iv"]),
                    implied_vol_ask=Decimal(ticker["ask1Iv"]),
                    implied_vol_mark=Decimal(ticker["markIv"]),
                    greek_delta=Decimal(ticker["delta"]),
                    greek_gamma=Decimal(ticker["gamma"]),
                    greek_theta=Decimal(ticker["theta"]),
                    greek_vega=Decimal(ticker["vega"]),
                    greek_rho=None,
                    estimated_settlement_price=(
                        Decimal(ticker["predictedDeliveryPrice"]) if ticker["predictedDeliveryPrice"] != "0" else None
                    ),
                )
        return results

    def option_open_interest(self, client: IHttpClient, instrument: Instrument) -> OpenInterestData:
        with self._option_open_interest_lock:
            results = self._option_open_interest_cache.get()
            if not results:
                results = self._get_all_option_open_interests(client)
                self._option_open_interest_cache.add(results)
            return results[instrument.symbol]

    def _get_all_option_open_interests(self, client: IHttpClient) -> Dict[str, OpenInterestData]:
        result: Dict[str, OpenInterestData] = {}
        for base_coin in self.OPTION_BASE_COINS:
            dt = datetime.now(timezone.utc)
            for open_interest in get_json(client.get(f"{self.TICKERS_URL_V5}?category=option&baseCoin={base_coin.upper()}"))[
                "result"
            ]["list"]:
                result[open_interest["symbol"]] = OpenInterestData(
                    contract_count=Decimal(open_interest["openInterest"]),
                    contract_value_usd=(Decimal(open_interest["openInterest"]) * Decimal(open_interest["underlyingPrice"])),
                    time=truncate_to_minute(dt),
                )
        return result

    def _get_first_date_from_html(self, symbol: str, client: IHttpClient) -> datetime:
        if trading_dates := _get_trade_dates(symbol, client, EXCHANGE_BIRTHDAY):
            return trading_dates[0]

        self.diagnostics.info(f"{symbol}: Trading data not available: using exchange birthday: {EXCHANGE_BIRTHDAY}")
        return EXCHANGE_BIRTHDAY

    def last_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        if instrument.market_type == MarketType.SPOT:
            return sorted(self._spot_trades(client, instrument), key=lambda t: t.time)
        elif instrument.market_type == MarketType.FUTURES:
            return self._last_futures_trades(client, instrument)
        else:
            raise UnsupportedMarketType(f"Unsupported MarketType {instrument.market_type.name}")

    def _spot_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        response = get_json(client.get(f"{self.SPOT_TRADES_URL}&symbol={instrument.symbol}", timeout=15))
        if response["retCode"] != 0:
            error_message = response["retMsg"]
            raise MarketError(f"Cannot get spot trades for symbol {instrument.symbol}. Return message: {error_message}")

        try:
            return [self._get_spot_trade_from_http_message(trade) for trade in response["result"]["list"]]
        except NoLongerSupportedError:
            return []

    @staticmethod
    def _get_spot_trade_from_http_message(trade: JsonValue) -> TradeData:
        time_ms = int(trade["time"])
        amount = Decimal(trade["size"])
        price = Decimal(trade["price"])
        is_buy = trade["side"] == "Buy"

        return TradeData(
            trade_id=_make_spot_trade_id(time_ms, price, amount, is_buy, exchange_id=trade["execId"]),
            amount=amount,
            price=price,
            is_buy=is_buy,
            time=dt_from_ms(time_ms),
        )

    def _last_futures_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        trading_dates = _get_trade_dates(instrument.symbol, client, EXCHANGE_BIRTHDAY)
        if not trading_dates:
            return []

        # we are returning fake row because this function is only used for calculating lag
        # and parsing historical files is very expensive
        return [TradeData(trade_id=0, amount=Decimal(0), price=Decimal(0), is_buy=True, time=trading_dates[-1])]

    @staticmethod
    def trade_history_traversal_method() -> TraversalMethod:
        return TraversalMethod.TIME

    def trade_history_traversal(self, client: IHttpClient, instrument: Instrument) -> IHistoryTraversal[TradeData]:
        return _TradeHistoryTraversal(instrument, client, self)

    def trades_since(self, instrument: Instrument, marker: Optional[HistoryMarker], client: IHttpClient) -> List[TradeData]:
        after_date = marker.time if marker is not None else EXCHANGE_BIRTHDAY
        trade_dates = _get_trade_dates(instrument.symbol, client, after_date)
        if not trade_dates:
            return []
        next_trade_date = trade_dates[0]
        converted_rows = list(_iter_trade_rows_csv(instrument.symbol, next_trade_date, client))
        converted_rows.sort(key=attrgetter("time"))
        self.diagnostics.info(f"Uploading {instrument.symbol} {next_trade_date}: {len(converted_rows)} trades")
        return converted_rows

    def futures_open_interest(self, client: IHttpClient, instrument: Instrument) -> Optional[OpenInterestData]:
        response = get_json(
            client.get(f"{self.TICKERS_URL_V5}?category={instrument.metadata['type']}&symbol={instrument.symbol}")
        )
        try:
            return OpenInterestData(
                contract_count=response["result"]["list"][0]["openInterest"],
                contract_value_usd=response["result"]["list"][0]["openInterestValue"],
                time=open_interest_timestamp(),
            )
        except Exception:
            print("failed to parse response from bybit open interest endpoint", response)

    def futures_ticker(self, client: IHttpClient, instrument: Instrument) -> FuturesTickerData:
        with self._futures_ticker_cache_lock:
            results = self._futures_ticker_cache.get()
            if not results:
                results = self._get_all_futures_ticker(client)
                self._futures_ticker_cache.add(results)
            return results[instrument.symbol]

    def _get_all_futures_ticker(self, client: IHttpClient) -> Dict[str, FuturesTickerData]:
        futures = self._get_futures_ticker_by_url(client, f"{self.TICKERS_URL_V5}?category=linear")
        futures.update(self._get_futures_ticker_by_url(client, f"{self.TICKERS_URL_V5}?category=inverse"))
        return futures

    def _get_futures_ticker_by_url(self, client: IHttpClient, url: str) -> Dict[str, FuturesTickerData]:
        response = get_json(client.get(url))
        results: Dict[str, FuturesTickerData] = {}
        for result in response["result"]["list"]:
            ftd = FuturesTickerData(
                exchange_time=dt_from_any_aware(response["time"]),
                price_mark=Decimal(result["markPrice"]),
                price_index=Decimal(result["indexPrice"]),
                estimated_settlement_price=self._get_optional_decimal(result, "predictedDeliveryPrice"),
                estimated_funding_rate=self._get_optional_decimal(result, "fundingRate"),
                estimated_funding_rate_time=self._get_optional_time(result, "nextFundingTime"),
            )
            results[result["symbol"]] = ftd
        return results

    def _get_optional_decimal(self, result: JsonValue, field_name: str) -> Optional[Decimal]:
        """Handle bybit returns of '' for missing decimal values"""
        field_value = result.get(field_name, "")
        return None if field_value == "" else Decimal(field_value)

    def _get_optional_time(self, result: JsonValue, field_name: str) -> Optional[datetime]:
        """Handle bybit returns of '0' for missing time values"""
        field_value = result.get(field_name, "0")
        return None if field_value == "0" else dt_from_any_aware(field_value)

    def last_funding_rates(self, client: IHttpClient, instrument: Instrument) -> List[FundingRateData]:
        result = get_json(
            client.get(f"{self.TICKERS_URL_V5}?category={instrument.metadata['type']}&symbol={instrument.symbol}")
        )["result"]
        funding_interval = timedelta(minutes=instrument.metadata["funding_interval"])
        return [
            FundingRateData(
                funding_rate=Decimal(rate["fundingRate"]) if rate["fundingRate"] else Decimal(0),
                funding_interval=funding_interval,
                funding_rate_period=funding_interval,
                time=(dt_from_any(rate["nextFundingTime"]) if rate["nextFundingTime"] != "0" else EARLIEST_ALLOWED_DATE),
            )
            for rate in result["list"]
        ]

    def funding_rate_history_traversal_method(self) -> "TraversalMethod":
        return TraversalMethod.TIME

    def funding_rate_traversal(self, client: IHttpClient, instrument: Instrument) -> IHistoryTraversal[FundingRateData]:
        return _FundingRateTraversal(instrument, client, self)

    def funding_rates_since(
        self, instrument: Instrument, marker: Optional[HistoryMarker], client: IHttpClient
    ) -> List[FundingRateData]:
        # it was decided that it will be enough for our purpose just to take last 200 historical entries
        funding_rates_json = get_json(
            client.get(
                (
                    f"{self._BASE_URL}/v5/market/funding/history?category={instrument.metadata['type']}&"
                    f"symbol={instrument.symbol}"
                ),
                timeout=20,
            )
        )["result"]["list"]
        funding_rates = [
            _parse_historical_funding_rate(funding_rate, instrument.metadata["funding_interval"])
            for funding_rate in funding_rates_json
        ]
        return sorted(funding_rates, key=lambda x: x.time)

    def book(self, client: IHttpClient, instrument: Instrument, depth: int, poll_max_book: bool) -> BookData:
        if instrument.market_type == MarketType.SPOT:
            return self._spot_book(client, instrument, depth)
        elif instrument.market_type == MarketType.FUTURES:
            return self._futures_book(client, instrument)
        else:
            raise UnsupportedMarketType(f"Unsupported MarketType {instrument.market_type.name}")

    def _spot_book(self, client: IHttpClient, instrument: Instrument, depth: int) -> BookData:
        endpoint = f"{self.BOOKS_URL}?category=spot&symbol={instrument.symbol}&limit={depth}"
        response = get_json(client.get(endpoint))
        return_code, return_message = response["retCode"], response["retMsg"]
        if return_code != 0:
            raise MarketError(
                f"Cannot get books for symbol {instrument.symbol}. "
                f"Exchange return the following error: {return_code} {return_message}."
            )

        result = response["result"]
        if result is not None and isinstance(result, dict):
            bids = [PriceLevel(price=Decimal(r[0]), amount=Decimal(r[1])) for r in result["b"]]
            asks = [PriceLevel(price=Decimal(r[0]), amount=Decimal(r[1])) for r in result["a"]]
        else:
            bids = []
            asks = []

        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=None,
            exchange_time=dt_from_ms_aware(result["ts"]),
            bids=bids,
            asks=asks,
        )

    def _futures_book(self, client: IHttpClient, instrument: Instrument) -> BookData:
        category = "linear" if instrument.metadata["type"] == BybitFuturesType.LINEAR else "inverse"
        endpoint = f"{self.BOOKS_URL}?category={category}&symbol={instrument.symbol}&limit=200"
        response = get_json(client.get(endpoint))
        if response["retCode"] != 0:
            raise MarketError(
                f"Cannot get books for symbol {instrument.symbol}. Exchange return the following "
                f"error: {response['retCode']} {response['retMsg']}."
            )
        if response["result"]["s"] != instrument.symbol:
            raise InconsistentResponse(f"Unexpected response for {instrument.symbol}: {response}")
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=response["result"]["u"],
            exchange_time=dt_from_any_aware(response["result"]["ts"]),
            bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in response["result"]["b"]],
            asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in response["result"]["a"]],
        )

    def check_rate_limited(self, response: Response) -> float:
        """Wait a minute after receiving 403 code from Cloudflare"""
        return 60 if response.status_code == HTTPStatus.UNAUTHORIZED else 0


def _get_trade_dates(symbol: str, client: IHttpClient, after: datetime) -> List[datetime]:
    response = client.get(TRADE_HISTORY_URL)
    if f'href="{symbol}/"' in response.body:
        trading_dates_html = client.get(f"{TRADE_HISTORY_URL}/{symbol}/").body
        trading_dates = sorted(re.findall(rf'href="{symbol}(\d\d\d\d-\d\d-\d\d)\.csv\.gz', trading_dates_html))
        all_dates = [ciso8601.parse_datetime_as_naive(row) for row in trading_dates]
        return sorted([row for row in all_dates if row >= after])
    return []


def _iter_trade_rows_csv(symbol: str, trade_date: datetime, client: IHttpClient, batch_size: int = 10000) -> Iterable[TradeData]:
    url = f"{TRADE_HISTORY_URL}/{symbol}/{symbol}{trade_date.date()}.csv.gz"

    cache_file_path = _download_and_cache_csv(url, client)
    rows_processed, state_file_path = _read_processing_state(cache_file_path)

    with gzip.open(cache_file_path, "rt", encoding="utf-8", newline="") as text_stream:
        reader = csv.DictReader(text_stream)

        for i, row in enumerate(reader):
            if i > rows_processed:
                yield _parse_trade_from_csv(row)
            if i >= rows_processed + batch_size:
                _store_processing_state(state_file_path, rows_processed + batch_size)
                break
        else:
            # clean up files if we have processed all rows
            cache_file_path.unlink()
            state_file_path.unlink(missing_ok=True)


def _download_and_cache_csv(url: str, client: IHttpClient) -> Path:
    cache_dir = Path() / "bybit_trade_cache"
    cache_dir.mkdir(exist_ok=True)

    cache_filename = url.split("/")[-1]  # Extract filename from URL
    cache_file_path = cache_dir / cache_filename

    if cache_file_path.exists():
        return cache_file_path

    try:
        raw_trade_data = client.get(url, raw=True).raw_body
        temp_file_path = cache_file_path.with_suffix(".tmp")
        with open(temp_file_path, "wb") as f:
            f.write(raw_trade_data)
        temp_file_path.rename(cache_file_path)
    except Exception as e:
        temp_file_path = cache_file_path.with_suffix(".tmp")
        if temp_file_path.exists():
            temp_file_path.unlink()
        raise e

    return cache_file_path


def _read_processing_state(data_file_path: Path) -> tuple[int, Path]:
    state_file_path = data_file_path.with_suffix(data_file_path.suffix + ".state")

    if not state_file_path.exists():
        return -1, state_file_path

    try:
        with open(state_file_path, "r") as f:
            return int(f.read().strip()), state_file_path
    except (ValueError, IOError):
        return -1, state_file_path


def _store_processing_state(state_file_path: Path, new_state: int) -> None:
    try:
        temp_state_path = state_file_path.with_suffix(".tmp")
        with open(temp_state_path, "w") as f:
            f.write(str(new_state))
        temp_state_path.rename(state_file_path)
    except Exception:
        temp_state_path = state_file_path.with_suffix(".tmp")
        if temp_state_path.exists():
            temp_state_path.unlink()


def _parse_trade_from_csv(row: dict) -> TradeData:
    return TradeData(
        trade_id=int(row["trdMatchID"].replace("-", ""), base=16),
        amount=Decimal(row["size"]),
        price=Decimal(row["price"]),
        is_buy=row["side"] == "Buy",
        time=dt_from_s(int(Decimal(row["timestamp"]))),
    )


def _parse_historical_funding_rate(funding_rate: JsonValue, interval_minutes: int) -> FundingRateData:
    funding_interval = timedelta(minutes=interval_minutes)
    return FundingRateData(
        funding_rate=Decimal(funding_rate["fundingRate"]),
        funding_interval=funding_interval,
        funding_rate_period=funding_interval,
        time=dt_from_any(funding_rate["fundingRateTimestamp"]),
    )


class _TradeHistoryTraversal(TraversalBase[BybitHttpApi, TradeData]):
    def first_entries(self) -> TraversalResult[TradeData]:
        return self._get_traversal_results(None)

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[TradeData]:
        return self._get_traversal_results(reference)

    def _get_traversal_results(self, marker: Optional[HistoryMarker]) -> TraversalResult[TradeData]:
        trades = self._api.trades_since(self._instrument, marker, self._client)
        marker = HistoryMarker(trades[-1].trade_id, trades[-1].time) if trades else None
        return TraversalResult(trades, last_entry_marker=marker)


class _FundingRateTraversal(TraversalBase[BybitHttpApi, FundingRateData]):
    def first_entries(self) -> TraversalResult[FundingRateData]:
        return self._get_traversal_results(None)

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[FundingRateData]:
        return self._get_traversal_results(reference)

    def _get_traversal_results(self, marker: Optional[HistoryMarker]) -> TraversalResult[FundingRateData]:
        rates = self._api.funding_rates_since(self._instrument, marker, self._client)
        marker = HistoryMarker(0, rates[-1].time) if rates else None
        return TraversalResult(rates, last_entry_marker=marker)


def _validate_update_book_quote_message(message: JsonValue) -> bool:
    if isinstance(message, dict) and "topic" in message and "data" in message and isinstance(data := message["data"], dict):
        return data["b"] or data["a"]
    return False


class BybitWebSocketApi(ExchangeStreamingApiBase):
    _V5_SPOT_URL = "wss://stream.bybit.com/v5/public/spot"
    _V5_FUTURES_LINEAR = "wss://stream.bybit.com/v5/public/linear"
    _V5_FUTURES_INVERSE = "wss://stream.bybit.com/v5/public/inverse"
    _SPOT_BOOK_MAX_DEPTH = 50
    _FUTURES_BOOK_MAX_DEPTH = 500
    BOOK_WS_CONNECTION_TIMEOUT = 15
    MAX_MARKETS_PER_REQUEST = 1000

    _previous_quote: Dict[str, Dict[str, PriceLevel]] = {}

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"{instrument.symbol}")
        subscription_manager = _TradeSpotSubscriptionManager(translator)
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            self._get_ping_component(),
            SubscriptionComponent(instruments, subscription_manager, params.scraped_market_count),
            TradeComponent(
                exchange_name=self.exchange_name,
                extract_instrument=partial(self._extract_instrument_from_spot_trade_message, translator),
                extract_trades=self._get_spot_trade_from_streaming_message,
                on_trades=params.on_trades,
            ),
            break_after_first_message_recognition=True,
        )
        return Stream(factory.websocket(self._V5_SPOT_URL), processor, JsonTranslator(), params.diagnostics)

    def futures_trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"publicTrade.{instrument.symbol}")

        futures_linear = [i for i in instruments if i.metadata["type"] == BybitFuturesType.LINEAR]
        futures_inverse = [i for i in instruments if i.metadata["type"] == BybitFuturesType.INVERSE]
        streams: List[IRunnable] = []
        subscription = _TradeSubscriptionManagerV5(translator)

        if futures_linear:
            streams.append(self._futures_trades_common(self._V5_FUTURES_LINEAR, futures_linear, subscription, params, factory))

        if futures_inverse:
            streams.append(self._futures_trades_common(self._V5_FUTURES_INVERSE, futures_inverse, subscription, params, factory))
        return MultiRunnable(streams)

    @staticmethod
    def _futures_trades_common(
        url: str,
        instruments: List[Instrument],
        subscription: SubscriptionManagerBase,
        params: TradeStreamParams,
        factory: IClientFactory,
    ) -> IRunnable:
        def extract_instrument(message: JsonValue) -> Optional[Instrument]:
            if isinstance(message, dict) and "topic" in message:
                return subscription.translator.to_instrument(message["topic"])
            return None

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            PingComponent(
                lambda _: {"op": "ping"}, lambda msg: isinstance(msg, dict) and msg.get("ret_msg") == "pong", interval=30.0
            ),
            SubscriptionComponent(instruments, subscription, params.scraped_market_count, batch_subscription=True),
            TradeComponent(EXCHANGE_NAME, extract_instrument, _futures_trades_from_message, params.on_trades),
            break_after_first_message_recognition=True,
        )

        return Stream(factory.websocket(url), processor, JsonTranslator(), params.diagnostics)

    def liquidations(self, factory: IClientFactory, instruments: Instruments, params: LiquidationStreamParams) -> IRunnable:
        futures = [i for i in instruments if i.metadata["type"] == BybitFuturesType.LINEAR]
        futures_inverse = [i for i in instruments if i.metadata["type"] == BybitFuturesType.INVERSE]

        streams: List[IRunnable] = []

        if futures:
            futures_stream = self._liquidations_common(self._V5_FUTURES_LINEAR, futures, params, factory)
            streams.append(futures_stream)

        if futures_inverse:
            futures_inverse_stream = self._liquidations_common(self._V5_FUTURES_INVERSE, futures_inverse, params, factory)
            streams.append(futures_inverse_stream)

        return MultiRunnable(streams)

    @staticmethod
    def _liquidations_common(
        url: str, instruments: List[Instrument], params: LiquidationStreamParams, factory: IClientFactory
    ) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"liquidation.{instrument.symbol}")
        subscription = _LiquidationSubscriptionManager(translator)

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            PingComponent(
                lambda _: {"op": "ping"}, lambda msg: isinstance(msg, dict) and msg.get("ret_msg") == "pong", interval=30.0
            ),
            SubscriptionComponent(instruments, subscription, params.scraped_market_count, batch_subscription=True),
            LiquidationComponent(
                EXCHANGE_NAME, subscription.extract_instrument, _liquidation_from_message, params.on_liquidations
            ),
        )

        return Stream(factory.websocket(url), processor, JsonTranslator(), params.diagnostics)

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(
            instruments, lambda instrument: f"orderbook.{self._SPOT_BOOK_MAX_DEPTH}.{instrument.symbol.upper()}"
        )
        subscription_manager = _BookSubscriptionManagerV5(translator, self._SPOT_BOOK_MAX_DEPTH)
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            self._get_ping_component(),
            SubscriptionComponent(instruments, subscription_manager, params.scraped_market_count, batch_subscription=True),
            BookComponent(
                self.exchange_name,
                partial(self._extract_instrument_from_quote_and_book_message, translator),
                self._get_book_from_message_v5,
                params,
            ),
            break_after_first_message_recognition=True,
        )
        return NoDataReconnectStream(
            client=factory.websocket(self._V5_SPOT_URL),
            processor=processor,
            translator=JsonTranslator(),
            diagnostics=params.diagnostics,
            params=StreamParams(connect_timeout=self.BOOK_WS_CONNECTION_TIMEOUT),
            validate_update_message=_validate_update_book_quote_message,
            no_data_reconnect_period=MAXIMUM_NO_DATA_TOLERANCE,
        )

    def futures_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(
            instruments, lambda instrument: f"orderbook.{self._FUTURES_BOOK_MAX_DEPTH}.{instrument.symbol.upper()}"
        )
        extract_instrument = partial(self._extract_instrument_from_quote_and_book_message, translator)
        streams: List[IRunnable] = []

        futures_linear = [i for i in instruments if i.metadata["type"] == BybitFuturesType.LINEAR]
        futures_inverse = [i for i in instruments if i.metadata["type"] == BybitFuturesType.INVERSE]
        if futures_linear:
            streams.append(
                self._get_futures_books_stream(
                    self._V5_FUTURES_LINEAR,
                    futures_linear,
                    _BookSubscriptionManagerV5(translator, self._FUTURES_BOOK_MAX_DEPTH),
                    extract_instrument,
                    self._get_book_from_message_v5,
                    params,
                    factory,
                )
            )
        if futures_inverse:
            streams.append(
                self._get_futures_books_stream(
                    self._V5_FUTURES_INVERSE,
                    futures_inverse,
                    _BookSubscriptionManagerV5(translator, self._FUTURES_BOOK_MAX_DEPTH),
                    extract_instrument,
                    self._get_book_from_message_v5,
                    params,
                    factory,
                )
            )
        return MultiRunnable(streams)

    def quotes(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"orderbook.1.{instrument.symbol}")
        subscription_manager = _QuoteSubscriptionManager(translator)
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            self._get_ping_component(),
            SubscriptionComponent(instruments, subscription_manager, params.scraped_market_count, batch_subscription=True),
            BookComponent(
                self.exchange_name,
                partial(self._extract_instrument_from_quote_and_book_message, translator),
                self._get_quote_from_message,
                params,
            ),
            break_after_first_message_recognition=True,
        )
        return NoDataReconnectStream(
            client=factory.websocket(self._V5_SPOT_URL),
            processor=processor,
            translator=JsonTranslator(),
            diagnostics=params.diagnostics,
            params=StreamParams(connect_timeout=self.BOOK_WS_CONNECTION_TIMEOUT),
            validate_update_message=_validate_update_book_quote_message,
            no_data_reconnect_period=MAXIMUM_NO_DATA_TOLERANCE,
        )

    def futures_quotes(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"orderbook.1.{instrument.symbol.upper()}")
        extract_instrument = partial(self._extract_instrument_from_quote_and_book_message, translator)
        streams: List[IRunnable] = []
        futures_linear: List[Instrument] = []
        futures_inverse: List[Instrument] = []

        for instrument in instruments:
            if instrument.metadata["type"] == BybitFuturesType.LINEAR:
                futures_linear.append(instrument)
            elif instrument.metadata["type"] == BybitFuturesType.INVERSE:
                futures_inverse.append(instrument)
            else:
                self._client._diagnostics.warning(f"Unknown instrument type: {instrument}")  # type: ignore
        if futures_linear:
            streams.append(
                self._get_futures_books_stream(
                    self._V5_FUTURES_LINEAR,
                    futures_linear,
                    _QuoteSubscriptionManager(translator),
                    extract_instrument,
                    self._get_quote_from_message,
                    params,
                    factory,
                )
            )
        if futures_inverse:
            streams.append(
                self._get_futures_books_stream(
                    self._V5_FUTURES_INVERSE,
                    futures_inverse,
                    _QuoteSubscriptionManager(translator),
                    extract_instrument,
                    self._get_quote_from_message,
                    params,
                    factory,
                )
            )
        return MultiRunnable(streams)

    def _get_futures_books_stream(
        self,
        url: str,
        instruments: List[Instrument],
        subscription: SubscriptionManagerBase,
        get_instrument_from_message: Callable[[JsonValue], Optional[Instrument]],
        get_book_from_message: Callable[[JsonValue], BookData],
        params: BookStreamParams,
        factory: IClientFactory,
    ) -> IRunnable:
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            self._get_ping_component(),
            SubscriptionComponent(instruments, subscription, params.scraped_market_count, batch_subscription=True),
            BookComponent(
                self.exchange_name,
                get_instrument_from_message,
                get_book_from_message,
                params,
            ),
            break_after_first_message_recognition=True,
        )

        return NoDataReconnectStream(
            client=factory.websocket(url),
            processor=processor,
            translator=JsonTranslator(),
            diagnostics=params.diagnostics,
            params=StreamParams(connect_timeout=self.BOOK_WS_CONNECTION_TIMEOUT),
            validate_update_message=_validate_update_book_quote_message,
            no_data_reconnect_period=MAXIMUM_NO_DATA_TOLERANCE,
        )

    @staticmethod
    def _get_ping_component() -> PingComponent[JsonValue]:
        return PingComponent(
            lambda _: {"op": "ping"},
            lambda msg: isinstance(msg, dict) and (msg.get("ret_msg") == "pong" or msg.get("pong")),
            interval=30.0,
        )

    @staticmethod
    def _extract_instrument_from_spot_trade_message(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "topic" in message:
            return translator.to_instrument(message["topic"].replace("publicTrade.", ""))
        return None

    @staticmethod
    def _extract_instrument_from_quote_and_book_message(
        translator: ChannelTranslator, message: JsonValue
    ) -> Optional[Instrument]:
        if isinstance(message, dict) and "topic" in message:
            return translator.to_instrument(message["topic"])
        return None

    @staticmethod
    def _get_spot_trade_from_streaming_message(message: JsonValue) -> List[TradeData]:
        trades: List[TradeData] = []
        for trade in message["data"]:
            time_ms = trade["T"]
            amount = Decimal(trade["v"])
            price = Decimal(trade["p"])
            is_buy = trade["S"] == "Buy"

            trades.append(
                TradeData(
                    trade_id=_make_spot_trade_id(time_ms, price, amount, is_buy, exchange_id=int(trade["i"])),
                    amount=amount,
                    price=price,
                    is_buy=is_buy,
                    time=dt_from_ms(time_ms),
                )
            )

        return trades

    @staticmethod
    def _get_spot_book_from_message(message: JsonValue) -> BookData:
        _data = message["data"]
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=None,
            exchange_time=dt_from_ms_aware(_data["t"]),
            bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in _data["b"]],
            asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in _data["a"]],
        )

    @staticmethod
    def _get_book_from_message_v5(message: JsonValue) -> BookData:
        book_type = BookType.FULL if message["type"] == "snapshot" else BookType.DELTA
        data = message["data"]
        return BookData(
            book_type=book_type,
            exchange_sequence_id=data.get("u"),
            exchange_time=dt_from_any_aware(int(message["ts"])),
            bids=BookData.sort_bids([PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in data["b"]]),
            asks=BookData.sort_asks([PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in data["a"]]),
        )

    def _get_quote_from_message(self, message: JsonValue) -> BookData:
        data = message["data"]
        instrument_str = data["s"]
        if message["type"] == "snapshot":
            self._previous_quote[instrument_str] = {}
        sorted_bids = sorted(data["b"], key=lambda bid: bid[1], reverse=True)
        sorted_asks = sorted(data["a"], key=lambda ask: ask[1], reverse=True)
        if data["b"]:
            bids = [PriceLevel(price=Decimal(sorted_bids[0][0]), amount=Decimal(sorted_bids[0][1]))]
            self._previous_quote[instrument_str]["b"] = bids[0]
        else:
            bids = [self._previous_quote[instrument_str]["b"]] if self._previous_quote[instrument_str].get("b") else []
        if data["a"]:
            asks = [PriceLevel(price=Decimal(sorted_asks[0][0]), amount=Decimal(sorted_asks[0][1]))]
            self._previous_quote[instrument_str]["a"] = asks[0]
        else:
            asks = [self._previous_quote[instrument_str]["a"]] if self._previous_quote[instrument_str].get("a") else []
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=data["u"],
            exchange_time=dt_from_ms_aware(message["ts"]),
            bids=bids,
            asks=asks,
        )


def _is_error_message(message: JsonValue) -> bool:
    return isinstance(message, dict) and message.get("code", "0") != "0" or message.get("success") is False


def _is_usdt_perp(symbol: str) -> bool:
    return symbol.endswith("USDT")


class _TradeSubscriptionManager(SubscriptionManagerBase):
    _instruments: List[Instrument] = []

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments = instruments
        return {"op": "subscribe", "args": ["trade"]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and "success" in msg and msg["success"]:
            return SubscriptionUpdate(self._instruments)
        else:
            return None


class _TradeUSDTSubscriptionManager(SubscriptionManagerBase):
    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        return {"op": "subscribe", "args": [f"trade.{i.symbol}" for i in instruments]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if (
            isinstance(msg, dict)
            and "success" in msg
            and msg["success"]
            and "request" in msg
            and "args" in msg["request"]
            and isinstance(msg["request"]["args"], list)
        ):
            return SubscriptionUpdate([self.translator.to_instrument(arg) for arg in msg["request"]["args"]])
        else:
            return None


class _TradeSubscriptionManagerV5(SubscriptionManagerBase):
    _instruments: List[Instrument] = []

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments = instruments
        return {"op": "subscribe", "args": [f"publicTrade.{i.symbol}" for i in instruments]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and "success" in msg and msg["success"]:
            return SubscriptionUpdate(self._instruments)
        else:
            return None


class _SpotSubscriptionManager(SubscriptionManagerBase):
    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and msg.get("event") == "sub" and msg.get("msg") == "Success":
            instrument = self.extract_instrument(msg)
            if instrument:
                return SubscriptionUpdate([instrument])
        return None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "params" in message:
            return self.translator.to_instrument(message["params"]["symbol"])
        return None


class _TradeSpotSubscriptionManager(SubscriptionManagerBase):
    def create_subscription_message(self, instrument: Instrument) -> JsonValue:
        return {"req_id": instrument.symbol, "op": "subscribe", "args": [f"publicTrade.{instrument.symbol}"]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and msg.get("op") == "subscribe" and msg.get("success"):
            if instrument := self.translator.to_instrument(msg["req_id"]):
                return SubscriptionUpdate([instrument])
        return None


class _LiquidationSubscriptionManager(SubscriptionManagerBase):
    _instruments: List[Instrument] = []

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments = instruments
        return {"op": "subscribe", "args": [f"liquidation.{instrument.symbol}" for instrument in instruments]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and msg.get("success") is True and msg.get("op") == "subscribe":
            return SubscriptionUpdate(self._instruments)
        return None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "topic" in message and "data" in message:
            return self.translator.to_instrument(message["topic"])
        return None


class _BookSubscriptionManagerV5(SubscriptionManagerBase):
    _instruments: List[Instrument] = []

    def __init__(self, channel_translator: ChannelTranslator, depth: int):
        super().__init__(channel_translator)
        self.depth = depth

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments = instruments
        return {"op": "subscribe", "args": [f"orderbook.{self.depth}.{i.symbol}" for i in instruments]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and msg.get("success"):
            return SubscriptionUpdate(self._instruments)
        return None


class _QuoteSubscriptionManager(_SpotSubscriptionManager):
    _instruments: List[Instrument]

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._instruments = instruments
        return {"op": "subscribe", "args": [f"orderbook.1.{i.symbol.upper()}" for i in instruments]}

    def update_subscriptions(self, msg: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(msg, dict) and msg.get("op") == "subscribe" and msg.get("success"):
            return SubscriptionUpdate(self._instruments)
        return None


def _futures_trades_from_message(message: JsonValue) -> List[TradeData]:
    return [
        TradeData(
            trade_id=int(trade["i"].replace("-", ""), base=16),
            amount=Decimal(trade["v"]),
            price=Decimal(trade["p"]),
            is_buy=trade["S"] == "Buy",
            time=dt_from_any(trade["T"]),
        )
        for trade in message["data"]
    ]


def _liquidation_from_message(message: JsonValue) -> List[LiquidationData]:
    liquidation = message["data"]
    return [
        LiquidationData(
            liquidation_id=int(f"{liquidation['updatedTime']}{liquidation['size']}{liquidation['price']}".replace(".", "")),
            amount=Decimal(liquidation["size"]),
            price=Decimal(liquidation["price"]),
            # Bybit reports this data backwards to our representation [per Jon]
            is_buy=liquidation["side"] == "Sell",
            is_order=True,
            time=dt_from_any_aware(int(liquidation["updatedTime"])),
        )
    ]
