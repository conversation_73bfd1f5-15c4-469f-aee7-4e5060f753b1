import time
import zlib
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Callable, Dict, Iterable, List, Optional, Tuple, Any

import ciso8601
from sortedcontainers import SortedDict  # type: ignore

from src.octopus.data import (
    BookData,
    BookType,
    FundingRateData,
    FuturesContractData,
    HistoryMarker,
    Instrument,
    LiquidationData,
    Market,
    MarketType,
    MutableBookData,
    OpenInterestData,
    PriceLevel,
    SpotContractData,
    Trade,
    TradeData,
)
from src.octopus.entry_history import TraversalBase
from src.octopus.exceptions import (
    ChecksumFailed,
    ConnectionConfirmationFailure,
    ExchangeHeartbeatTimeout,
    ExchangeHttpError,
    InstrumentNotFound,
    UnexpectedMessageFormat,
)
from src.octopus.exchange.api import (
    BookStreamParams,
    ExchangeInstrument,
    ExchangeMarkets,
    IClientFactory,
    IHistoryTraversal,
    Instruments,
    LiquidationStreamParams,
    TradeCallback,
    TradeStreamParams,
    TraversalMethod,
    TraversalResult,
)
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.stream_components.book import BookComponent
from src.octopus.stream_components.error import ErrorHandlingComponent
from src.octopus.stream_components.subscription import SubscriptionComponent, SubscriptionManagerBase, SubscriptionUpdate
from src.octopus.stream_components.trade import LiquidationComponent, TradeComponent
from src.octopus.stream_components.types import ExtractInstrument
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.octopus.utils import compress_price_levels, get_trade_history_marker
from src.resources.exchange import glib_exchange
from src.utils.diagnostics import Diagnostics, IGaugeMetric
from src.utils.execution import IRunnable
from src.utils.http import IHttpClient, get_json
from src.utils.stream import P_T, ComponentProcessor, IStreamApi, IStreamProcessor, JsonTranslator, Stream, StreamParams
from src.utils.timeutil import (
    dt_from_any,
    dt_from_ms,
    dt_from_ms_aware,
    dt_from_us,
    dt_from_us_aware,
    dt_to_us,
    open_interest_timestamp,
)
from src.utils.trade_id import TradeId
from src.utils.types import JsonValue
from src.websocket.data import WebSocketParams, WebSocketPingParams

EXCHANGE_NAME = "Kraken"

MAX_TRADES_PER_REQUEST = 1000

BOOKS_DEPTH = 100
QUOTES_BOOK_DEPTH = 10

EXCHANGE_TRADE_ID_USAGE_START_DATE = datetime(year=2024, month=1, day=12, hour=10, minute=30)
COMPLEX_TRADE_ID_USAGE_START_DATE = datetime(year=2024, month=1, day=18, hour=12, minute=30)


def _parse_spot_trade_data(trade: JsonValue, symbol: str, id_generator: TradeId) -> TradeData:
    if datetime.utcnow() > COMPLEX_TRADE_ID_USAGE_START_DATE:
        trade_id = id_generator.make_trade_id(
            instrument=symbol,
            timestamp=str(Decimal(trade[2])).replace(".", ""),
            price=Decimal(trade[0]),
            amount=Decimal(trade[1]),
            is_buy=trade[3] == "b",
        )
    elif datetime.utcnow() > EXCHANGE_TRADE_ID_USAGE_START_DATE:
        trade_id = trade[6]
    else:
        trade_id = int(1000000 * Decimal(trade[2]))

    return TradeData(
        trade_id=trade_id,
        amount=Decimal(trade[1]),
        price=Decimal(trade[0]),
        is_buy=trade[3] == "b",
        time=dt_from_us(int(1000000 * Decimal(trade[2]))),
    )


def _parse_ws_futures_trade_data(trade: JsonValue) -> TradeData:
    return TradeData(
        trade_id=int(trade["uid"].replace("-", ""), base=16),
        amount=Decimal(trade["qty"]),
        price=Decimal(trade["price"]),
        is_buy=trade["side"] == "buy",
        time=dt_from_ms(int(trade["time"])),
    )


def _parse_http_liqudiations_data(trade: JsonValue) -> LiquidationData:
    return LiquidationData(
        liquidation_id=int(trade["uid"].replace("-", ""), base=16),
        amount=Decimal(trade["size"]),
        price=Decimal(trade["price"]),
        is_buy=trade["side"] == "buy",
        is_order=False,
        time=ciso8601.parse_datetime_as_naive(trade["time"]),
    )


def _parse_http_funding_rates_data(funding_rate: JsonValue) -> FundingRateData:
    return FundingRateData(
        funding_rate=Decimal(funding_rate["relativeFundingRate"]),
        funding_rate_period=timedelta(hours=1),
        funding_interval=timedelta(hours=4),
        time=ciso8601.parse_datetime_as_naive(funding_rate["timestamp"]),
    )


def _parse_ws_liqudiations_data(trade: JsonValue) -> LiquidationData:
    return LiquidationData(
        liquidation_id=int(trade["uid"].replace("-", ""), base=16),
        amount=Decimal(trade["qty"]),
        price=Decimal(trade["price"]),
        is_buy=trade["side"] == "buy",
        is_order=False,
        time=dt_from_ms(int(trade["time"])),
    )


@dataclass(frozen=True)
class TradesRequestResult:
    trades: List[TradeData]
    duplicate_timestamps: List[int]


def ensure_sort_order(array: List[Any], is_good: Callable[[Any, Any], bool]) -> List[Any]:
    if not array:
        return []
    return array if is_good(array[0], array[-1]) else array[::-1]


class KrakenHttpApi(ExchangeHttpApiBase):
    _SPOT_ROOT_URL = "https://api.kraken.com/0/public"
    _FUTURES_ROOT_URL = "https://futures.kraken.com/derivatives/api/v3"
    _FUTURES_ROOT_URL_V4 = "https://futures.kraken.com/derivatives/api/v4"

    def __init__(
        self,
        exchange_name: str,
        ticker_translator: ExchangeTickerTranslator,
        diagnostics: Diagnostics,
        skip_trade_strategy: str = "",
    ) -> None:
        super().__init__(exchange_name, ticker_translator, diagnostics)
        self._trades_last_datetime: Dict[str, Optional[datetime]] = {}
        self._trades_last_id: Dict[str, Optional[int]] = {}
        self._trade_id_generator = TradeId(max_len=10_000, max_age_seconds=5 * 60)
        self._skip_trades = skip_trade_strategy != "no-skip-trades"

    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        result = []
        for symbol, market_info in self._get_spot_markets(client).items():
            if symbol.find(".") >= 0:
                continue

            base = market_info["base"].lower()
            quote = market_info["quote"].lower()
            alt_symbol = market_info["wsname"]
            result.append(ExchangeInstrument(MarketType.SPOT, symbol, base, quote, alt_symbol))

        return self.translator.exchange_to_coinmetrics(result)

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for symbol, market_info in self._get_spot_markets(client).items():
            if symbol.find(".") >= 0:
                continue

            base = market_info["base"].lower()
            quote = market_info["quote"].lower()

            contract_data = SpotContractData(
                symbol=symbol,
                base_id=self.translator.to_cm_id(base),
                quote_id=self.translator.to_cm_id(quote),
                base_name=self.translator.translate(base),
                quote_name=self.translator.translate(quote),
                native_base_name=market_info["base"],
                native_quote_name=market_info["quote"],
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
            )
            result.append(contract_data)
        return result

    def _get_spot_markets(self, client: IHttpClient) -> Dict[str, Dict[str, JsonValue]]:
        return self._spot_request(client, f"{self._SPOT_ROOT_URL}/AssetPairs")["result"]  # type: ignore

    def futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        return ExchangeMarkets([
            Instrument.futures(market_info["symbol"].upper())
            for market_info in self._get_futures_markets(client)
            if market_info["type"] in ["futures_inverse", "flexible_futures"]
        ])

    def futures_markets_metadata(self, client: IHttpClient) -> List[FuturesContractData]:
        underlying_pairs: Dict[str, Tuple[str, str]] = {}
        underlying_pairs_raw: Dict[str, Tuple[str, str]] = {}
        for ticker_info in self._get_futures_tickers(client):
            if "pair" not in ticker_info:
                continue

            symbol = ticker_info["symbol"].upper()
            base_quote = ticker_info["pair"].split(":")

            if len(base_quote) != 2:
                raise UnexpectedMessageFormat(f"expected XXX:YYY, got {base_quote}")
            else:
                base = base_quote[0].lower() if base_quote[0] != "XBT" else "btc"
                quote = base_quote[1].lower() if base_quote[1] != "XBT" else "btc"
                underlying_pairs[symbol] = (base, quote)
                underlying_pairs_raw[symbol] = base_quote

        result: List[FuturesContractData] = []
        for market_info in self._get_futures_markets(client):
            symbol = market_info["symbol"].upper()
            underlying_pair = underlying_pairs.get(symbol)
            if not underlying_pair:
                continue

            base, quote = underlying_pair
            if market_info["type"] == "flexible_futures":
                size_asset = base
                margin_asset = "usd"
                margin_asset_id = self.translator.to_cm_id(margin_asset)
            else:
                size_asset = "usd"
                margin_asset = base
                margin_asset_id = self.translator.to_cm_id(margin_asset)

            base_id = self.translator.to_cm_id(base)
            expiry_date_str = market_info.get("lastTradingTime")
            contract_data = FuturesContractData(
                symbol=symbol,
                underlying_base_id=base_id,
                underlying_quote_id=self.translator.to_cm_id(quote),
                size_asset_id=self.translator.to_cm_id(size_asset),
                margin_asset_id=margin_asset_id,
                underlying_base_name=self.translator.translate(base),
                underlying_quote_name=self.translator.translate(quote),
                size_asset_name=self.translator.translate(size_asset),
                margin_asset_name=self.translator.translate(margin_asset),
                underlying_native_base_name=underlying_pairs_raw[symbol][0],
                underlying_native_quote_name=underlying_pairs_raw[symbol][1],
                listing_date=None,
                expiry_date=ciso8601.parse_datetime_as_naive(expiry_date_str) if expiry_date_str else None,
                contract_size=Decimal(market_info["contractSize"]),
                tick_size=Decimal(market_info["tickSize"]),
            )
            result.append(contract_data)

        return result

    def _get_futures_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return list(self._futures_request(client, f"{self._FUTURES_ROOT_URL}/instruments")["instruments"])

    def _get_futures_tickers(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return list(self._futures_request(client, f"{self._FUTURES_ROOT_URL}/tickers")["tickers"])

    def futures_markets_perpetual(self, client: IHttpClient) -> ExchangeMarkets:
        return ExchangeMarkets([
            instrument
            for instrument in self.futures_markets(client).recognized
            if instrument.symbol.startswith("PI") or instrument.symbol.startswith("PF")
        ])

    def historical_futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        return self.futures_markets(client)

    @staticmethod
    def trade_history_traversal_method() -> TraversalMethod:
        return TraversalMethod.TIME

    def trade_history_traversal(self, client: IHttpClient, instrument: Instrument) -> IHistoryTraversal[TradeData]:
        return _TradeHistoryTraversal(instrument, client, self)

    def book(self, client: IHttpClient, instrument: Instrument, depth: int, poll_max_book: bool) -> BookData:
        if poll_max_book:
            depth = min(depth, 500)
        if instrument.market_type == MarketType.SPOT:
            response = self._spot_request(client, f"{self._SPOT_ROOT_URL}/Depth?pair={instrument.symbol}&count={depth}")
            return BookData(
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=None,
                bids=[
                    PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1]))
                    for bid in response["result"][instrument.symbol]["bids"]
                ],
                asks=[
                    PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1]))
                    for ask in response["result"][instrument.symbol]["asks"]
                ],
            )
        else:
            response = self._futures_request(client, f"{self._FUTURES_ROOT_URL}/orderbook/?symbol={instrument.symbol}")
            asks = ensure_sort_order(response["orderBook"]["asks"], is_good=lambda first, last: first[0] < last[0])
            bids = ensure_sort_order(response["orderBook"]["bids"], is_good=lambda first, last: first[0] > last[0])
            return BookData(
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=ciso8601.parse_datetime(response["serverTime"]),
                bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in bids[:depth]],
                asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in asks[:depth]],
            )

    def futures_open_interest(self, client: IHttpClient, instrument: Instrument) -> Optional[OpenInterestData]:
        for ticker_info in self._get_futures_tickers(client):
            if ticker_info["symbol"].upper() == instrument.symbol:
                value = Decimal(ticker_info["openInterest"])
                return OpenInterestData(value, value, open_interest_timestamp())

        raise InstrumentNotFound(instrument.symbol)

    def last_liquidations(self, client: IHttpClient, instrument: Instrument) -> List[LiquidationData]:
        raw_trades = self._raw_futures_trades_since(client, instrument)
        return [_parse_http_liqudiations_data(trade_data) for trade_data in raw_trades if trade_data["type"] == "liquidation"]

    def last_funding_rates(self, client: IHttpClient, instrument: Instrument) -> List[FundingRateData]:
        assert instrument.symbol.startswith("PI") or instrument.symbol.startswith("PF"), (
            f"Detected attempt to request funding rates for non perpetual instrument: {instrument.symbol}"
        )

        url = f"{self._FUTURES_ROOT_URL_V4}/historicalfundingrates?symbol={instrument.symbol}"
        funding_rates_raw = get_json(client.get(url))["rates"]
        funding_rates_raw.sort(key=lambda x: x["timestamp"])
        # we are taking last 7 days only
        return [_parse_http_funding_rates_data(funding_rate) for funding_rate in funding_rates_raw[-168:]]

    """ Exchange specific interface """

    def last_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        if instrument.market_type == MarketType.SPOT:
            return self._last_spot_trades(client, instrument)
        else:
            raise NotImplementedError()

    def _last_spot_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        if datetime.utcnow() > COMPLEX_TRADE_ID_USAGE_START_DATE:
            return []

        if instrument.symbol not in self._trades_last_datetime:
            last_trades, _ = self.trades_since(client=client, instrument=instrument, last_trade_time=None)
            self._trades_last_datetime[instrument.symbol] = last_trades[-1].time if len(last_trades) > 0 else None
            return last_trades

        last_trade_datetime = self._trades_last_datetime.get(instrument.symbol)
        new_trades, _ = self.trades_since(client=client, instrument=instrument, last_trade_time=last_trade_datetime)
        # if we cannot keep up, we switch to collecting last trades and rely on historical FH to fill the gaps
        if self._skip_trades and len(new_trades) == MAX_TRADES_PER_REQUEST:
            new_trades.extend(self.trades_since(client=client, instrument=instrument, last_trade_time=None)[0])
        trade_history_marker = get_trade_history_marker(new_trades)
        if trade_history_marker:
            self._trades_last_datetime[instrument.symbol] = trade_history_marker.time
        return new_trades

    def trades_since(
        self, client: IHttpClient, instrument: Instrument, last_trade_time: Optional[datetime]
    ) -> Tuple[List[TradeData], HistoryMarker]:
        # Trades endpoint does not support floating point number in `since` param.
        # That means that max that we can collect is the `limit`(=1000) trades each second.
        # So we lose data if we use this endpoint
        url = f"{self._SPOT_ROOT_URL}/Trades?pair={instrument.symbol}"
        if last_trade_time:
            url = f"{url}&since={dt_to_us(last_trade_time) / 1000000}"
        response = get_json(client.get(url))
        raw_trades = response["result"][instrument.symbol]

        last_trade_id = self._trades_last_id.get(instrument.symbol) or 0
        trades = [
            _parse_spot_trade_data(trade, instrument.symbol, self._trade_id_generator)
            for trade in raw_trades
            if trade[6] > last_trade_id
        ]

        history_marker = None
        if raw_trades:
            self._trades_last_id[instrument.symbol] = raw_trades[-1][6]
            last_raw_trade_time = dt_from_us(int(1000000 * Decimal(raw_trades[-1][2])))
            if last_trade_time != last_raw_trade_time:
                last_trade_time = last_raw_trade_time
            else:
                last_trade_time = last_raw_trade_time + timedelta(seconds=1)
            history_marker = HistoryMarker(id=raw_trades[-1][6], time=last_trade_time)

        return trades, history_marker

    def _raw_futures_trades_since(
        self, client: IHttpClient, instrument: Instrument, since: Optional[datetime] = None
    ) -> JsonValue:
        url = "{}/history?symbol={}{}".format(
            self._FUTURES_ROOT_URL, instrument.symbol, "&since={}".format(dt_to_us(since) * 1000) if since is not None else ""
        )

        return self._futures_request(client, url)["history"]

    def _spot_request(self, client: IHttpClient, url: str) -> JsonValue:
        response = get_json(client.get(url, timeout=15))
        if len(response["error"]) > 0:
            raise ExchangeHttpError(response["error"])
        return response

    def _futures_request(self, client: IHttpClient, url: str) -> Dict[str, JsonValue]:
        response = get_json(client.get(url, timeout=15))
        if not isinstance(response, dict) or response.get("result") != "success":
            raise ExchangeHttpError(response["error"])
        return response


class _TradeHistoryTraversal(TraversalBase[KrakenHttpApi, TradeData]):
    def first_entries(self) -> TraversalResult[TradeData]:
        trades, history_marker = self._api.trades_since(self._client, self._instrument, datetime(1970, 1, 1))
        return TraversalResult(trades, last_entry_marker=history_marker)

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[TradeData]:
        trades, history_marker = self._api.trades_since(self._client, self._instrument, reference.time)
        return TraversalResult(trades, last_entry_marker=history_marker)


class KrakenWebSocketApi(ExchangeStreamingApiBase):
    _SPOT_URL = "wss://ws.kraken.com"
    _SPOT_URL_V2 = "wss://ws.kraken.com/v2"
    _FUTURE_URL = "wss://futures.kraken.com/ws/v1"

    _Common = Tuple[ComponentProcessor[JsonValue], ChannelTranslator, ExtractInstrument[JsonValue]]

    def __init__(self, exchange_name: str, ticker_translator: ExchangeTickerTranslator, client: IHttpClient):
        super().__init__(exchange_name, ticker_translator, client)
        self._trade_id_generator = TradeId(max_len=10_000, max_age_seconds=5 * 60)

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        processor, _, extract_instrument = self._get_spot_common(
            instruments,
            "trade",
            params.scraped_market_count,
        )
        processor.install(
            TradeComponent(EXCHANGE_NAME, extract_instrument, self._get_spot_trades_from_message, params.on_trades)
        )
        return Stream(
            client=factory.websocket(self._SPOT_URL_V2),
            processor=processor,
            translator=JsonTranslator(),
            diagnostics=params.diagnostics,
            params=StreamParams(connect_timeout=5),
        )

    def futures_trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)

        return Stream(
            factory.websocket(self._FUTURE_URL, WebSocketParams(ping=WebSocketPingParams(ping_interval=59))),
            ComponentProcessor(
                ErrorHandlingComponent(_is_error_message),
                _FuturesHeartbeat(),
                _FuturesSystemStatus(),
                SubscriptionComponent(
                    instruments,
                    _FuturesSubscriptionManager(translator, "trade"),
                    params.scraped_market_count,
                    batch_subscription=True,
                ),
                TradeComponent(
                    EXCHANGE_NAME,
                    lambda msg: translator.to_instrument(msg["product_id"]) if "product_id" in msg else None,
                    _futures_trades_from_message,
                    params.on_trades,
                ),
            ),
            JsonTranslator(),
            params.diagnostics,
        )

    def liquidations(self, factory: IClientFactory, instruments: Instruments, params: LiquidationStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)

        return Stream(
            factory.websocket(self._FUTURE_URL, WebSocketParams(ping=WebSocketPingParams(ping_interval=59))),
            ComponentProcessor(
                ErrorHandlingComponent(_is_error_message),
                _FuturesHeartbeat(),
                _FuturesSystemStatus(),
                SubscriptionComponent(
                    instruments,
                    _FuturesSubscriptionManager(translator, "trade"),
                    params.scraped_market_count,
                    batch_subscription=True,
                ),
                LiquidationComponent(
                    EXCHANGE_NAME,
                    lambda msg: translator.to_instrument(msg["product_id"]) if "product_id" in msg else None,
                    _liquidations_from_message,
                    params.on_liquidations,
                ),
            ),
            JsonTranslator(),
            params.diagnostics,
        )

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        processor, _, extract_instrument = self._get_spot_common(
            instruments,
            "book",
            params.scraped_market_count,
        )
        processor.install(
            BookComponent(
                exchange_name=EXCHANGE_NAME,
                extract_instrument=extract_instrument,
                extract_book=self._get_spot_book_from_message,
                stream_params=params,
                max_orders_to_keep=params.depth,
            )
        )
        return Stream(factory.websocket(self._SPOT_URL_V2), processor, JsonTranslator(), params.diagnostics)

    def futures_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)
        return Stream(
            factory.websocket(self._FUTURE_URL),
            ComponentProcessor(
                ErrorHandlingComponent(_is_error_message),
                _FuturesHeartbeat(),
                _FuturesSystemStatus(),
                SubscriptionComponent(
                    instruments,
                    _FuturesSubscriptionManager(translator, "book"),
                    params.scraped_market_count,
                    batch_subscription=True,
                ),
                BookComponent(
                    EXCHANGE_NAME,
                    lambda msg: translator.to_instrument(msg["product_id"]) if "product_id" in msg else None,
                    self._future_book_from_message,
                    params,
                ),
            ),
            JsonTranslator(),
            params.diagnostics,
        )

    def quotes(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        processor, _, extract_instrument = self._get_spot_common(instruments, "ticker", params.scraped_market_count)
        processor.install(
            BookComponent(
                exchange_name=EXCHANGE_NAME,
                extract_instrument=extract_instrument,
                extract_book=self._get_spot_quote_from_message,
                stream_params=params,
            )
        )
        return Stream(factory.websocket(self._SPOT_URL_V2), processor, JsonTranslator(), params.diagnostics)

    def futures_quotes(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)
        return Stream(
            factory.websocket(self._FUTURE_URL, WebSocketParams(ping=WebSocketPingParams(ping_interval=59))),
            ComponentProcessor(
                ErrorHandlingComponent(_is_error_message),
                _FuturesHeartbeat(),
                _FuturesSystemStatus(),
                SubscriptionComponent(
                    instruments,
                    _FuturesSubscriptionManager(translator, "quote"),
                    params.scraped_market_count,
                    batch_subscription=True,
                ),
                BookComponent(
                    EXCHANGE_NAME,
                    lambda msg: translator.to_instrument(msg["product_id"]) if "product_id" in msg else None,
                    self._future_book_from_message,
                    params,
                ),
            ),
            JsonTranslator(),
            params.diagnostics,
        )

    def _get_common(
        self, instruments: Iterable[Instrument], data_type: str, scraped_market_count: IGaugeMetric
    ) -> "KrakenWebSocketApi._Common":
        translator = ChannelTranslator(instruments, lambda instrument: instrument.alt_symbol)

        def extract_instrument(message: JsonValue) -> Optional[Instrument]:
            if not isinstance(message, list) or (len(message) < 4 or len(message) > 6):
                return None
            else:
                return translator.to_instrument(message[-1])

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            _Heartbeat(),
            _SystemStatus(),
            SubscriptionComponent(
                instruments, _SubscriptionManager(translator, data_type), scraped_market_count, batch_subscription=True
            ),
        )

        return processor, translator, extract_instrument

    def _get_spot_common(
        self, instruments: Iterable[Instrument], channel_name: str, scraped_market_count: IGaugeMetric
    ) -> "KrakenWebSocketApi._Common":
        def channel_from_instrument(instrument: Instrument) -> str:
            base, quote = instrument.alt_symbol.split("/")
            if base == "XBT":
                base = "BTC"
            elif base == "XDG":
                base = "DOGE"
            if quote == "XBT":
                quote = "BTC"
            elif quote == "XDG":
                quote = "DOGE"
            return f"{base}/{quote}"

        translator = ChannelTranslator(instruments, channel_from_instrument)

        def extract_instrument(message: JsonValue) -> Optional[Instrument]:
            if not isinstance(message, dict) or message.get("data") is None:
                return None
            else:
                if len(message.get("data", [])) > 0 and message.get("data")[0].get("symbol") is not None:
                    return translator.to_instrument(message.get("data", [])[0].get("symbol"))

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            _HeartbeatV2(),
            _SystemStatus(),
            SubscriptionComponent(
                instruments,
                _SubscriptionManagerV2(translator, channel_name),
                scraped_market_count,
                batch_subscription=True,
            ),
        )
        return processor, translator, extract_instrument

    def _trades_from_message(self, message: JsonValue, symbol: str) -> List[TradeData]:
        if datetime.utcnow() <= COMPLEX_TRADE_ID_USAGE_START_DATE:
            return []
        return [_parse_spot_trade_data(trade, symbol, self._trade_id_generator) for trade in message[1]]

    @staticmethod
    def _get_spot_trades_from_message(message: JsonValue) -> List[TradeData]:
        return [
            TradeData(
                trade_id=trade["trade_id"],
                amount=Decimal(trade["qty"]),
                price=Decimal(trade["price"]),
                is_buy=trade["side"] == "buy",
                time=dt_from_any(trade["timestamp"]),
            )
            for trade in message["data"]
        ]

    @staticmethod
    def _get_spot_book_from_message(message: JsonValue) -> BookData:
        book_type = BookType.FULL if message["type"] == "snapshot" else BookType.DELTA
        bids = [
            PriceLevel(price=Decimal(bid["price"]), amount=Decimal(bid["qty"])) for bid in message["data"][0].get("bids", [])
        ]
        asks = [
            PriceLevel(price=Decimal(ask["price"]), amount=Decimal(ask["qty"])) for ask in message["data"][0].get("asks", [])
        ]
        return BookData(
            book_type=book_type,
            exchange_sequence_id=None,
            exchange_time=None,
            bids=compress_price_levels(BookData.sort_bids(bids)),
            asks=compress_price_levels(BookData.sort_asks(asks)),
        )

    def _get_spot_quote_from_message(self, message: JsonValue) -> BookData:
        bids, asks = [], []
        if len(message.get("data", [])) > 0:
            if message["data"][0]["bid"]:
                bids = [PriceLevel(price=Decimal(message["data"][0]["bid"]), amount=Decimal(message["data"][0]["bid_qty"]))]
            if message["data"][0]["ask"]:
                asks = [PriceLevel(price=Decimal(message["data"][0]["ask"]), amount=Decimal(message["data"][0]["ask_qty"]))]
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=None,
            exchange_time=datetime.now(tz=timezone.utc),
            bids=bids,
            asks=asks,
        )

    @staticmethod
    def _future_book_from_message(message: JsonValue) -> BookData:
        if message["feed"] == "book_snapshot":
            return BookData(
                book_type=BookType.FULL,
                exchange_sequence_id=message["seq"],
                exchange_time=dt_from_ms_aware(int(message["timestamp"])),
                bids=[PriceLevel(price=Decimal(elem["price"]), amount=Decimal(elem["qty"])) for elem in message["bids"]],
                asks=[PriceLevel(price=Decimal(elem["price"]), amount=Decimal(elem["qty"])) for elem in message["asks"]],
            )
        elif message["feed"] == "book":
            price_level = PriceLevel(price=Decimal(message["price"]), amount=Decimal(message["qty"]))
            if message["side"] == "sell":
                bids = []
                asks = [price_level]
            else:
                bids = [price_level]
                asks = []
            return BookData(
                book_type=BookType.DELTA,
                exchange_sequence_id=message["seq"],
                exchange_time=dt_from_ms_aware(int(message["timestamp"])),
                asks=asks,
                bids=bids,
            )
        else:
            raise ValueError(f"Unknown type of message: {message}")


@dataclass(frozen=True)
class KrakenBookData(BookData):
    checksum: Optional[str]


class KrakenMutableBookData(MutableBookData):
    def __init__(self, data: Optional[KrakenBookData] = None):
        self.checksum: Optional[str] = data.checksum if data else None
        super(KrakenMutableBookData, self).__init__(data)

    def apply_delta(self, delta: KrakenBookData) -> None:  # type: ignore
        def update_side(state: "SortedDict[Decimal, PriceLevel]", delta: List[PriceLevel]) -> None:
            for el in delta:
                if el.amount == 0:
                    if el.price in state:
                        del state[el.price]
                    # Tolerate missing zero levels
                else:
                    state[el.price] = el

        update_side(self._bids, delta.bids)
        update_side(self._asks, delta.asks)
        self.checksum = delta.checksum
        self.validate()

    def validate(self) -> None:
        super().validate()
        self._validate_checksum()

    def _validate_checksum(self) -> None:
        if not self.checksum:
            return

        asks = list(self._asks.values()[:10])
        bids = list(self._bids.values()[:10])
        if not asks and not bids:
            return

        # Kraken does not declare floating point precision for price and amount
        if len(asks) > 0:
            price_precision = -asks[0].price.as_tuple().exponent
            amount_precision = -asks[0].amount.as_tuple().exponent
        else:
            price_precision = -bids[0].price.as_tuple().exponent
            amount_precision = -bids[0].amount.as_tuple().exponent

        def prepare_price_str(value: PriceLevel) -> str:
            price_format_str = f"{{:.{price_precision}f}}"
            amount_format_str = f"{{:.{amount_precision}f}}"
            price_str = price_format_str.format(value.price).replace(".", "").lstrip("0")
            amount_str = amount_format_str.format(value.amount).replace(".", "").lstrip("0")
            return f"{price_str}{amount_str}"

        parts = [prepare_price_str(ask) for ask in asks]
        parts.extend([prepare_price_str(bid) for bid in bids])

        calculated_sum = zlib.crc32(bytes("".join(parts), encoding="utf-8"))

        if str(calculated_sum) != self.checksum:
            bids_str = "\n".join([str(bid) for bid in bids])
            asks_str = "\n".join([str(ask) for ask in reversed(asks)])

            raise ChecksumFailed(
                f"Checksum failed for:\nasks\n{asks_str}\nbids\n{bids_str}\nExpected: {self.checksum}. Got: {calculated_sum}."
            )


def _is_error_message(msg: JsonValue) -> bool:
    return isinstance(msg, dict) and (msg.get("status") == "error" or msg.get("event") == "error" or msg.get("event") == "alert")


def _futures_trades_from_message(message: JsonValue) -> List[TradeData]:
    if "trades" in message:
        return [_parse_ws_futures_trade_data(trade) for trade in message["trades"]]
    else:
        return [_parse_ws_futures_trade_data(message)]


def _liquidations_from_message(message: JsonValue) -> List[LiquidationData]:
    if "trades" in message:
        return [_parse_ws_liqudiations_data(trade) for trade in message["trades"] if trade["type"] == "liquidation"]
    else:
        if message["type"] == "liquidation":
            return [_parse_ws_liqudiations_data(message)]
        return []


def _book_from_message(message: JsonValue) -> KrakenBookData:
    data = message[1]
    if isinstance(message[2], dict):
        data.update(message[2])

    def process_side(key: str, book_timestamp: Optional[datetime]) -> Tuple[List[PriceLevel], Optional[datetime]]:
        result: Dict[Decimal, Decimal] = {}
        snapshot_key = key + "s"
        entries = data[snapshot_key] if snapshot_key in data else data.get(key, [])

        for entry in entries:
            price = Decimal(entry[0])
            amount = Decimal(entry[1])
            time = dt_from_us_aware(int(1000000 * Decimal(entry[2])))

            if book_timestamp is None or time > book_timestamp:
                book_timestamp = time

            result[price] = amount

        return [PriceLevel(price=price, amount=amount) for price, amount in result.items()], book_timestamp

    bids, book_timestamp = process_side("b", None)
    asks, book_timestamp = process_side("a", book_timestamp)

    checksum = data.get("c")
    book_type = BookType.FULL if "bs" in data and "as" in data else BookType.DELTA
    return KrakenBookData(
        book_type=book_type,
        exchange_sequence_id=None,
        exchange_time=book_timestamp,
        bids=BookData.sort_bids(bids) if book_type == BookType.DELTA else bids,
        asks=BookData.sort_asks(asks) if book_type == BookType.DELTA else asks,
        checksum=checksum,
    )


class _SubscriptionManager(SubscriptionManagerBase):
    def __init__(self, translator: ChannelTranslator, subscription_name: str):
        super().__init__(translator)

        if subscription_name not in {"trade", "book", "quote"}:
            raise ValueError("subscription name should be either `trade`, `book`, or `quote`")
        else:
            self._subscription_name = subscription_name

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        if self._subscription_name == "book":
            subscription = {"name": self._subscription_name, "depth": BOOKS_DEPTH}
        elif self._subscription_name == "quote":
            subscription = {"name": "book", "depth": QUOTES_BOOK_DEPTH}
        else:
            subscription = {"name": self._subscription_name}

        return {
            "event": "subscribe",
            "pair": [self.translator.to_channel(market) for market in instruments],
            "subscription": subscription,
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if not isinstance(message, dict) or not isinstance(message.get("subscription"), dict):
            return None

        if message["subscription"].get("name") != self._subscription_name:
            # 'quote' handler subscribes to 'book' channel
            if not (message["subscription"].get("name") == "book" and self._subscription_name == "quote"):
                return None

        if message.get("event") != "subscriptionStatus":
            return None

        instrument = self.translator.to_instrument(message["pair"])
        status = message.get("status")

        if status == "subscribed":
            return SubscriptionUpdate(positive_confirmations=[instrument])
        elif status == "error":
            return SubscriptionUpdate(negative_confirmations=[instrument])
        else:
            return None


class _SubscriptionManagerV2(SubscriptionManagerBase):
    _BOOKS_DEPTH = 1000

    def __init__(self, translator: ChannelTranslator, channel: str):
        super().__init__(translator)

        if channel not in ["trade", "book", "ticker"]:
            raise ValueError("subscription can only be `trade` or `book`")
        else:
            self._channel = channel

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        if self._channel == "trade":
            return {
                "method": "subscribe",
                "params": {
                    "channel": self._channel,
                    "symbol": [self.translator.to_channel(market) for market in instruments],
                    "snapshot": False,
                },
            }
        elif self._channel == "book":
            return {
                "method": "subscribe",
                "params": {
                    "channel": self._channel,
                    "symbol": [self.translator.to_channel(market) for market in instruments],
                    "depth": self._BOOKS_DEPTH,
                    "snapshot": True,
                },
            }
        elif self._channel == "ticker":
            return {
                "method": "subscribe",
                "params": {
                    "channel": self._channel,
                    "symbol": [self.translator.to_channel(market) for market in instruments],
                    "event_trigger": "bbo",
                    "snapshot": True,
                },
            }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if not isinstance(message, dict) or message.get("method") != "subscribe":
            return None
        instrument = self.translator.to_instrument(message["result"]["symbol"])
        return SubscriptionUpdate(positive_confirmations=[instrument])


class _FuturesSubscriptionManager(SubscriptionManagerBase):
    def __init__(self, translator: ChannelTranslator, subscription_name: str):
        super().__init__(translator)
        self._subscription_name = subscription_name

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        feed = self._subscription_name if self._subscription_name != "quote" else "book"
        return {"event": "subscribe", "feed": feed, "product_ids": [instrument.symbol for instrument in instruments]}

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if not isinstance(message, dict) or "product_ids" not in message:
            return None

        event = message.get("event")
        instrument = self.translator.to_instrument(message["product_ids"][0])

        if event == "subscribed":
            return SubscriptionUpdate(positive_confirmations=[instrument])
        elif event in ["subscribed_failed", "alert", "error"]:
            return SubscriptionUpdate(negative_confirmations=[instrument])
        else:
            return None


class _SystemStatus(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        if not isinstance(message, dict):
            return False

        event = message.get("event")

        if event == "systemStatus":
            status = message["status"]

            if status == "maintenance":
                stream.diagnostics.info("exchange feed is under maintenance")
                stream.reconnect()
            elif status != "online":
                stream.diagnostics.error_ns(ConnectionConfirmationFailure(f"unknown system status: {status}"))
                stream.reconnect()

            return True

        elif event == "subscriptionStatus":
            if message.get("status") == "unsubscribed":
                stream.diagnostics.info("exchange forcefully unsubscribed us from channel")
                stream.reconnect()
                return True

        if message.get("channel") == "status":
            return True

        return False


class _FuturesSystemStatus(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        if isinstance(message, dict) and message.get("event") == "info":
            stream.diagnostics.info("feed version: {}".format(message.get("version")))
            return True
        else:
            return False


class _Heartbeat(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        return isinstance(message, dict) and message.get("event") == "heartbeat"


class _HeartbeatV2(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        return isinstance(message, dict) and message.get("channel") == "heartbeat"


class _FuturesHeartbeat(IStreamProcessor[JsonValue]):
    TIMEOUT = 15.0

    def on_open(self, stream: IStreamApi[JsonValue]) -> None:
        self._message_last_received_at = time.time()
        stream.send({"event": "subscribe", "feed": "heartbeat"})

    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        self._message_last_received_at = time.time()

        if not isinstance(message, dict):
            return False

        if "event" not in message and message.get("feed") == "heartbeat":
            return True

        if message.get("event") == "subscribed" and message.get("feed") == "heartbeat":
            stream.diagnostics.info("subscribed to heartbeat")
            return True
        else:
            return False

    def on_tick(self, time_passed: float, stream: IStreamApi[JsonValue]) -> None:
        if time.time() - self._message_last_received_at > _FuturesHeartbeat.TIMEOUT:
            stream.diagnostics.error(ExchangeHeartbeatTimeout())
            stream.reconnect(gracefully=False)


class KrakenTradeComponent(IStreamProcessor[P_T]):
    def __init__(
        self,
        exchange_name: str,
        extract_instrument: ExtractInstrument[P_T],
        extract_trades: Callable[[P_T, str], List[TradeData]],
        on_trades: TradeCallback,
    ):
        self._exchange_id = glib_exchange().exchange_id_by_name(exchange_name)
        self._extract_instrument = extract_instrument
        self._extract_trades = extract_trades
        self._on_trades = on_trades

    def on_message(self, message: P_T, stream: IStreamApi[P_T]) -> bool:
        if (instrument := self._extract_instrument(message)) is None:
            return False

        market = Market(self._exchange_id, instrument)
        trades = [Trade(market, trade_data) for trade_data in self._extract_trades(message, instrument.symbol)]

        try:
            if len(trades) > 0:
                self._on_trades(trades)
        except Exception as e:
            stream.diagnostics.error(e, "failure in on_trades")

        return True
