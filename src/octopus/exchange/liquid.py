import json
from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Union

from authlib.jose import jwt  # type: ignore

from src.octopus.data import BookData, BookType, HistoryMarker, Instrument, MarketType, PriceLevel, SpotContractData, TradeData
from src.octopus.entry_history import TraversalBase
from src.octopus.exceptions import ConnectionAuthorizationFailure, ConnectionConfirmationFailure
from src.octopus.exchange.api import (
    BookStreamParams,
    ExchangeInstrument,
    ExchangeMarkets,
    IClientFactory,
    IHistoryTraversal,
    Instruments,
    TradeStreamParams,
    TraversalMethod,
    TraversalResult,
)
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.stream_components.book import BookComponent
from src.octopus.stream_components.error import ErrorHandlingComponent
from src.octopus.stream_components.subscription import SubscriptionComponent, SubscriptionManagerBase, SubscriptionUpdate
from src.octopus.stream_components.trade import TradeComponent
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.octopus.utils import get_trade_history_marker, trim_last_timestamp_entries, trim_last_timestamp_entries_if_recent
from src.utils.execution import IRunnable
from src.utils.http import IHttpClient, get_json
from src.utils.stream import ComponentProcessor, IStreamApi, IStreamProcessor, JsonTranslator, Stream
from src.utils.timeutil import dt_from_us_aware, dt_to_ms, dt_to_s
from src.utils.types import JsonValue

EXCHANGE_NAME = "Liquid"
EXCHANGE_BIRTHDAY = datetime(2009, 1, 1)

MAX_TRADES_PER_REQUEST = 1000


def _parse_trade_data(trade: JsonValue) -> TradeData:
    return TradeData(
        int(trade["id"]),
        abs(Decimal(trade["quantity"])),
        abs(Decimal(trade["price"])),
        trade["taker_side"] == "buy",
        datetime.utcfromtimestamp(int(trade["created_at"])),
    )


class LiquidHttpApi(ExchangeHttpApiBase):
    _SPOT_ROOT_URL = "https://api.liquid.com"

    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        result = []
        for market_info in self._get_spot_markets(client):
            symbol = str(market_info["currency_pair_code"])
            alt_symbol = str(market_info["id"])
            base = market_info["base_currency"].lower()
            quote = market_info["quoted_currency"].lower()
            result.append(ExchangeInstrument(MarketType.SPOT, symbol, base, quote, alt_symbol))

        return self.translator.exchange_to_coinmetrics(result)

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for market_info in self._get_spot_markets(client):
            symbol = str(market_info["currency_pair_code"])
            base = market_info["base_currency"].lower()
            quote = market_info["quoted_currency"].lower()

            contract_data = SpotContractData(
                symbol=symbol,
                base_id=self.translator.to_cm_id(base),
                quote_id=self.translator.to_cm_id(quote),
                base_name=self.translator.translate(base),
                quote_name=self.translator.translate(quote),
                listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                end_date=None,
                is_current=True,
                taker_fee=Decimal(str(market_info["taker_fee"])),
                maker_fee=Decimal(str(market_info["maker_fee"])),
                price_increment=Decimal(str(market_info["tick_size"])),
                status="online" if not market_info["disabled"] else "offline",
                margin_trading_enabled=True if market_info["margin_enabled"] else False,
            )
            result.append(contract_data)
        return result

    def _get_spot_markets(self, client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return self._request(client, f"{self._SPOT_ROOT_URL}/products")  # type: ignore

    def last_trades(self, client: IHttpClient, instrument: Instrument) -> List[TradeData]:
        url = f"{self._SPOT_ROOT_URL}/executions?product_id={instrument.alt_symbol}&limit={MAX_TRADES_PER_REQUEST}"
        return [_parse_trade_data(trade) for trade in reversed(self._request(client, url)["models"])]

    @staticmethod
    def trade_history_traversal_method() -> TraversalMethod:
        return TraversalMethod.TIME

    def trade_history_traversal(self, client: IHttpClient, instrument: Instrument) -> IHistoryTraversal[TradeData]:
        return _TradeHistoryTraversal(instrument, client, self)

    def book(self, client: IHttpClient, instrument: Instrument, depth: int, poll_max_book: bool) -> BookData:
        response = self._request(client, f"{self._SPOT_ROOT_URL}/products/{instrument.alt_symbol}/price_levels?full=1")

        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=None,
            exchange_time=None,
            bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in response["buy_price_levels"][:depth]],
            asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in response["sell_price_levels"][:depth]],
        )

    """ Exchange specific interface """

    def trades_since(
        self, client: IHttpClient, instrument: Instrument, since: datetime, count: int = MAX_TRADES_PER_REQUEST
    ) -> List[TradeData]:
        assert 0 < count <= MAX_TRADES_PER_REQUEST, "count should be > 0 and <= {}".format(MAX_TRADES_PER_REQUEST)

        url = "{}/executions?product_id={}&timestamp={}&limit={}".format(
            self._SPOT_ROOT_URL, instrument.alt_symbol, dt_to_s(since), count
        )

        return [_parse_trade_data(trade) for trade in self._request(client, url)]

    def _request(self, client: IHttpClient, url: str) -> JsonValue:
        return get_json(client.get(url, headers={"X-Quoine-API-Version": "2"}, timeout=15))


class _TradeHistoryTraversal(TraversalBase[LiquidHttpApi, TradeData]):
    def first_entries(self) -> TraversalResult[TradeData]:
        return self.next_entries(HistoryMarker(id=0, time=EXCHANGE_BIRTHDAY))

    def next_entries(self, reference: HistoryMarker) -> TraversalResult[TradeData]:
        timestamp = reference.time + timedelta(seconds=1)
        trades = self._api.trades_since(self._client, self._instrument, timestamp, MAX_TRADES_PER_REQUEST)

        if len(trades) == MAX_TRADES_PER_REQUEST:
            trimmed_trades = trim_last_timestamp_entries(trades, lambda t: t.time)
            if len(trimmed_trades) == 0:
                return TraversalResult(trades, True, get_trade_history_marker(trades))
            else:
                return TraversalResult(trimmed_trades, False, get_trade_history_marker(trimmed_trades))
        else:
            trimmed_trades = trim_last_timestamp_entries_if_recent(trades, lambda t: t.time)
            return TraversalResult(trimmed_trades, False, get_trade_history_marker(trimmed_trades))


class LiquidWebSocketApi(ExchangeStreamingApiBase):
    _BASE_URL = "wss://tap.liquid.com:443/app/PythonClient?client=Pysher&version=0.6.0&protocol=6"

    def __init__(
        self,
        exchange_name: str,
        ticker_translator: ExchangeTickerTranslator,
        client: IHttpClient,
        token: str = "",
        secret: str = "",
    ):
        super().__init__(exchange_name, ticker_translator, client)
        self._token = token
        self._secret = secret

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        client = factory.websocket(self._BASE_URL)

        authentication = _Authentication(self._token, self._secret)

        subscription = _SubscriptionManager(
            ChannelTranslator(instruments, lambda instrument: instrument.symbol.lower()), "executions_cash"
        )

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            authentication,
            SubscriptionComponent(
                instruments=instruments,
                manager=subscription,
                gauge=params.scraped_market_count,
                timeout=30.0,
                reconnect_on_timeout=False,
                retry_interval=600,
                batch_subscription=False,
                can_subscribe=authentication.is_authenticated,
            ),
            TradeComponent(EXCHANGE_NAME, subscription.extract_instrument, _trades_from_message, params.on_trades),
        )

        return Stream(client, processor, _MessageTranslator(), params.diagnostics)

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        client = factory.websocket(self._BASE_URL)
        authentication = _Authentication(self._token, self._secret)
        subscription = _SubscriptionManager(
            ChannelTranslator(instruments, lambda instrument: instrument.symbol.lower()), "price_ladders_cash"
        )
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            authentication,
            SubscriptionComponent(
                instruments=instruments,
                manager=subscription,
                gauge=params.scraped_market_count,
                timeout=30.0,
                reconnect_on_timeout=False,
                retry_interval=600,
                batch_subscription=False,
                can_subscribe=authentication.is_authenticated,
            ),
            BookComponent(EXCHANGE_NAME, subscription.extract_instrument, self._get_book_from_message, params),
        )
        return Stream(client, processor, _MessageTranslator(), params.diagnostics)

    @staticmethod
    def _get_book_from_message(message: JsonValue) -> Optional[BookData]:
        if data := message.get("data"):
            timestamp_string = data["timestamp"].replace(".", "")[:16]
            if len(timestamp_string) < 16:
                timestamp_string += "0" * (16 - len(timestamp_string))
            return BookData(
                book_type=BookType.FULL,
                exchange_sequence_id=None,
                exchange_time=dt_from_us_aware(int(timestamp_string)),
                bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in data["bids"]],
                asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in data["asks"]],
            )
        return None


class _MessageTranslator(JsonTranslator):
    def decode(self, message: Union[str, bytes]) -> JsonValue:
        d_message = json.loads(message, parse_float=Decimal)

        if not _is_error_message(d_message):
            if "data" in d_message and isinstance(d_message["data"], str):
                d_message["data"] = json.loads(d_message["data"], parse_float=Decimal)

        return d_message


def _is_error_message(message: JsonValue) -> bool:
    return isinstance(message, dict) and "error" in message["event"]


def _trades_from_message(message: JsonValue) -> List[TradeData]:
    return [_parse_trade_data(message["data"])] if "id" in message["data"] else []


class _SubscriptionManager(SubscriptionManagerBase):
    def __init__(self, channel_translator: ChannelTranslator, channel: str):
        super().__init__(channel_translator)
        self.channel = channel

    def create_subscription_message(self, instrument: Instrument) -> JsonValue:
        return {"event": "pusher:subscribe", "data": {"channel": f"{self.channel}_{self.translator.to_channel(instrument)}"}}

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        instrument = self.extract_instrument(message)
        return SubscriptionUpdate([instrument]) if instrument is not None else None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if (
            isinstance(message, dict)
            and "channel" in message
            and (message.get("data") or message.get("event") == "pusher_internal:subscription_succeeded")
        ):
            return self.translator.to_instrument(message["channel"].split("_")[-1])
        else:
            return None


class _Authentication(IStreamProcessor[JsonValue]):
    def __init__(self, token: str, secret: str) -> None:
        self._token = token
        self._secret = secret

    def on_open(self, stream: IStreamApi[JsonValue]) -> None:
        self._connection_confirmed = False
        self._authentication_confirmed = False

    def on_tick(self, time_passed: float, stream: IStreamApi[JsonValue]) -> None:
        if time_passed > 15.0:
            if not self._connection_confirmed:
                stream.diagnostics.error_ns(ConnectionConfirmationFailure())
                stream.reconnect()
            if not self._authentication_confirmed:
                stream.diagnostics.error_ns(ConnectionAuthorizationFailure("auth request timeout"))
                stream.reconnect()

    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        if not isinstance(message, dict):
            return False

        event = message.get("event")

        if event == "pusher:connection_established":
            stream.diagnostics.info("received connection confirmation")
            self._connection_confirmed = True
            self._authenticate(stream)
            return True

        elif event == "quoine:auth_success":
            stream.diagnostics.info("authentication successful")
            self._authentication_confirmed = True
            return True

        elif event == "quoine:auth_failure":
            stream.diagnostics.error_ns(ConnectionAuthorizationFailure())
            return True
        elif event == "pusher_internal:subscription_succeeded":
            stream.diagnostics.info("subscription successful")
            return True
        else:
            return False

    def is_authenticated(self) -> bool:
        return self._authentication_confirmed

    def _authenticate(self, stream: IStreamApi[JsonValue]) -> None:
        header = {"alg": "HS256"}
        payload = {"path": "/realtime", "nonce": dt_to_ms(datetime.utcnow()), "token_id": self._token}
        signature = jwt.encode(header, payload, self._secret).decode("ascii")

        auth_payload = {"path": "/realtime", "headers": {"X-Quoine-Auth": signature}}
        stream.send({"event": "quoine:auth_request", "data": auth_payload})
