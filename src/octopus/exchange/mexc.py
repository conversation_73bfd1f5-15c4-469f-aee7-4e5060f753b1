from collections import defaultdict
from dataclasses import dataclass
from decimal import Decimal
from functools import partial
from typing import Callable, Dict, Iterable, List, Optional

from src.octopus.data import (
    BookData,
    BookType,
    ExchangeInstrument,
    FuturesContractData,
    Instrument,
    MarketType,
    MutableBookDataTolerateMissingZeroLevels,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exchange.api import BookStreamParams, ExchangeMarkets, IClientFactory, Instruments, IRunnable, TradeStreamParams
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.exchange.resources.mexc.compiled import PushDataV3ApiWrapper_pb2
from src.octopus.stream_components.book import BookComponent, BookSnapshotComponentWithListOfUpdatesBase
from src.octopus.stream_components.ping import PingComponent
from src.octopus.stream_components.subscription import SubscriptionComponent, SubscriptionManagerBase, SubscriptionUpdate
from src.octopus.stream_components.trade import TradeComponent
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.utils.http import IHttpClient, get_json
from src.utils.stream import P_T, ComponentProcessor, IStreamApi, IStreamProcessor, JsonTranslator, Stream, ProtobufTranslator
from src.utils.timeutil import dt_from_ms_aware, dt_to_ms
from src.utils.trade_id import TradeId
from src.utils.types import JsonValue

"""
New MEXC API Documentation: https://mxcdevelop.github.io/apidocs/spot_v3_en/#introduction
MEXC API Documentation: https://mxcdevelop.github.io/APIDoc/
"""

EXCHANGE_NAME = "MEXC"


class MEXCHttpApi(ExchangeHttpApiBase):
    _SPOT_BASE_URL = "https://api.mexc.com"
    _FUTURES_BASE_URL = "https://contract.mexc.com"

    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        exchange_instruments = [
            ExchangeInstrument(
                market_type=MarketType.SPOT,
                symbol=market_info["symbol"],
                base=market_info["baseAsset"].lower(),
                quote=market_info["quoteAsset"].lower(),
            )
            for market_info in self._get_spot_markets(client)
        ]
        return self.translator.exchange_to_coinmetrics(exchange_instruments)

    def futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        exchange_instruments = [
            ExchangeInstrument(
                market_type=MarketType.FUTURES,
                symbol=market_info["symbol"],
                base=market_info["baseCoin"].lower(),
                quote=market_info["quoteCoin"].lower(),
            )
            for market_info in self._get_futures_markets(client)
        ]
        return self.translator.exchange_to_coinmetrics(exchange_instruments)

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for market_info in self._get_spot_markets(client):
            if "SPOT" in market_info["permissions"]:
                symbol = market_info["symbol"]
                base = market_info["baseAsset"].lower()
                quote = market_info["quoteAsset"].lower()
                contract_data = SpotContractData(
                    symbol=symbol,
                    base_id=self.translator.to_cm_id(base),
                    quote_id=self.translator.to_cm_id(quote),
                    base_name=self.translator.translate(base),
                    quote_name=self.translator.translate(quote),
                    native_base_name=market_info["baseAsset"],
                    native_quote_name=market_info["quoteAsset"],
                    listing_date=SpotContractData.DEFAULT_LISTING_DATE,
                    end_date=None,
                    is_current=True,
                    status="online" if market_info["status"] == "ENABLED" else "offline",
                    amount_increment=Decimal(str(market_info["baseSizePrecision"])),
                    amount_size_min=None,
                    amount_size_max=None,
                    price_increment=Decimal("0.1") ** int(market_info["quotePrecision"]),
                    price_size_min=None,
                    price_size_max=None,
                    order_size_min=None,
                    taker_fee=Decimal(str(market_info["takerCommission"])),
                    maker_fee=Decimal(str(market_info["makerCommission"])),
                    margin_trading_enabled=market_info["isMarginTradingAllowed"],
                )
                result.append(contract_data)
        return result

    @staticmethod
    def _futures_contract_size_asset_name(base_coin: str, settle_coin: str) -> str:
        #  https://gitlab.com/coinmetrics/feed-handlers/octopus/-/issues/1021#mexc-futures
        if settle_coin in ("usdt", "usdc"):
            return base_coin
        elif base_coin == settle_coin:
            return "usd"
        else:
            raise Exception("Cannot resolve contract size asset name")

    def futures_markets_metadata(self, client: IHttpClient) -> List[FuturesContractData]:
        result: List[FuturesContractData] = []
        for market_info in self._get_futures_markets(client):
            symbol = market_info["symbol"]
            base_coin = market_info["baseCoin"].replace("1000", "").lower()
            quote_coin = market_info["quoteCoin"].lower()
            settle_coin = market_info["settleCoin"].lower()
            size_asset_name = self._futures_contract_size_asset_name(base_coin=base_coin, settle_coin=settle_coin)

            # https://gitlab.com/coinmetrics/feed-handlers/octopus/-/issues/1021#mexc-futures
            tick_size = Decimal("0.1") ** int(market_info["priceScale"])
            contract_data = FuturesContractData(
                symbol=symbol,
                underlying_base_id=self.translator.to_cm_id(base_coin),
                underlying_quote_id=self.translator.to_cm_id(quote_coin),
                underlying_base_name=self.translator.translate(base_coin),
                underlying_quote_name=self.translator.translate(quote_coin),
                size_asset_name=size_asset_name,
                size_asset_id=self.translator.to_cm_id(size_asset_name),
                margin_asset_name=settle_coin,
                margin_asset_id=self.translator.to_cm_id(settle_coin),
                underlying_native_base_name=market_info["baseCoin"],
                underlying_native_quote_name=market_info["quoteCoin"],
                listing_date=None,
                expiry_date=None,
                contract_size=Decimal(str(market_info["contractSize"])),
                tick_size=tick_size,
                multiplier_size=None,
                multiplier_size_asset_id=None,
                status="online" if market_info["state"] == 0 else "offline",
                amount_increment=Decimal(str(market_info["volUnit"])),
                amount_size_min=Decimal(str(market_info["minVol"])),
                amount_size_max=Decimal(str(market_info["maxVol"])),
                price_increment=tick_size,  # TODO: consider fetching this separately?
                price_size_min=None,
                price_size_max=None,
                order_size_min=None,
                taker_fee=Decimal(str(market_info["takerFeeRate"])),
                maker_fee=Decimal(str(market_info["makerFeeRate"])),
                margin_trading_enabled=None,
            )
            result.append(contract_data)
        return result

    def _get_spot_markets(self, client: IHttpClient) -> JsonValue:
        return get_json(client.get(f"{self._SPOT_BASE_URL}/api/v3/exchangeInfo", timeout=15))["symbols"]

    def _get_futures_markets(self, client: IHttpClient) -> JsonValue:
        return get_json(client.get(f"{self._FUTURES_BASE_URL}/api/v1/contract/detail", timeout=15))["data"]


@dataclass(frozen=True)
class BookDataMEXC(BookData):
    exchange_sequence_id: int

    def is_next_sequence(self, other: "BookDataMEXC") -> Optional[bool]:  # type: ignore[override]
        return other.exchange_sequence_id > self.exchange_sequence_id


@dataclass(frozen=True)
class BookDataFuturesMEXC(BookData):
    exchange_sequence_id: int

    def is_next_sequence(self, other: "BookDataFuturesMEXC") -> Optional[bool]:  # type: ignore[override]
        return other.exchange_sequence_id > self.exchange_sequence_id


class SpotBookCacheComponent(BookComponent[P_T]):
    max_instrument_cache_length = 20

    def __init__(self, *args, get_book_sequence_id: Callable[[JsonValue], int], **kwargs):  # type: ignore
        super().__init__(*args, **kwargs)
        self._get_book_sequence_id = get_book_sequence_id

        self._cache: Dict[Instrument, Dict[int, JsonValue]] = defaultdict(dict)
        self._next_book_id: Dict[Instrument, int] = {}

    def on_open(self, stream: IStreamApi[P_T]) -> None:
        super().on_open(stream)
        self._cache = defaultdict(dict)
        self._next_book_id = {}

    def on_message(self, message: P_T, stream: IStreamApi[P_T]) -> bool:
        instrument = self._extract_instrument(message)
        if instrument is None:
            return False
        # run caching mechanism only when snapshot is gotten
        if self._books.get(instrument) is None or self._books[instrument]._last_book is None:
            return super().on_message(message, stream)
        message_id = self._get_book_sequence_id(message)
        if self._next_book_id.get(instrument) is None or message_id == self._next_book_id[instrument]:
            self._next_book_id[instrument] = message_id + 1
            return super().on_message(message, stream)
        self._cache[instrument][message_id] = message
        while self._next_book_id[instrument] in self._cache[instrument].keys():
            msg = self._cache[instrument].pop(self._next_book_id[instrument])
            self._next_book_id[instrument] = self._next_book_id[instrument] + 1
            super().on_message(msg, stream)
        if len(self._cache[instrument]) > self.max_instrument_cache_length:
            for msg_id in sorted(self._cache[instrument].keys()):
                msg = self._cache[instrument].pop(msg_id)
                self._next_book_id[instrument] = self._next_book_id[instrument] + 1
                super().on_message(msg, stream)
        return True


class BookSnapshotComponentMEXC(BookSnapshotComponentWithListOfUpdatesBase[P_T]):
    _extract_sequence_id: Callable[[P_T], int]

    def _handle_list_of_updates(  # type: ignore[override]
        self, stream: IStreamApi[P_T], snapshot: BookDataMEXC, instrument: Instrument
    ) -> None:
        self._list_of_updates[instrument].sort(key=lambda m: self._extract_sequence_id(m))
        for msg in self._list_of_updates[instrument]:
            if self._extract_sequence_id(msg) <= snapshot.exchange_sequence_id:
                continue
            self._book_component.on_message(msg, stream)
        self._list_of_updates = defaultdict(list)


class MEXCWebSocketApi(ExchangeStreamingApiBase):
    _SPOT_BASE_URL = "wss://wbs-api.mexc.com/ws"
    _FUTURES_BASE_URL = "wss://contract.mexc.com/edge"

    def __init__(self, exchange_name: str, ticker_translator: ExchangeTickerTranslator, client: IHttpClient):
        super().__init__(exchange_name, ticker_translator, client)
        self._trade_id = TradeId(max_len=10_000, max_age_seconds=5 * 60)

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        translator: ChannelTranslator = ChannelTranslator(instruments, self._get_spot_trade_channel_from_instrument)
        extract_instrument = partial(self._spot_trade_extract_instrument, translator)
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            PingComponent(
                construct_ping=lambda _: {"method": "PING"},
                extract_pong=lambda msg: isinstance(msg, dict) and msg.get("msg") == "PONG",
                interval=30.0,
            ),
            SubscriptionComponent(
                instruments=instruments,
                manager=_SpotTradesSubscriptionManager(channel_translator=translator),
                gauge=params.scraped_market_count,
                batch_subscription=False,
            ),
            TradeComponent(
                self.exchange_name,
                extract_instrument,
                self._extract_spot_trades,
                params.on_trades,
            ),
            break_after_first_message_recognition=True,
        )
        return Stream(
            client=factory.websocket(self._SPOT_BASE_URL),
            processor=processor,
            translator=ProtobufTranslator(protobuf_data_class=PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper),
            diagnostics=params.diagnostics,
        )

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, self._get_spot_book_channel_from_instrument)
        extract_instrument = partial(self._spot_book_extract_instrument, translator)
        book_component = SpotBookCacheComponent(  # type: ignore
            EXCHANGE_NAME,
            extract_instrument,
            self._get_spot_book_from_message,
            params,
            mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
            get_book_sequence_id=self._get_spot_book_sequence_id,
        )
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            SubscriptionComponent(
                instruments=instruments,
                manager=_SpotBooksSubscriptionManager(translator),
                gauge=params.scraped_market_count,
                batch_subscription=True,
            ),
            BookSnapshotComponentMEXC(
                extract_instrument,
                self._get_spot_book_sequence_id,
                lambda instrument: self._get_spot_book_snapshot(self.http_client, instrument),
                book_component,
                params.executor,
                max_concurrent_requests=4,
            ),
            book_component,
        )
        return Stream(
            client=factory.websocket(self._SPOT_BASE_URL),
            processor=processor,
            translator=ProtobufTranslator(protobuf_data_class=PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper),
            diagnostics=params.diagnostics,
        )

    def futures_trades(
        self,
        factory: IClientFactory,
        instruments: Instruments,
        params: TradeStreamParams,
    ) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)
        if len(instruments) > 1:
            raise Exception(
                "We cannot subscribe to multiple channels at once "
                "because there is no channel ID in the messages we get from MEXC exchange"
            )

        extract_instrument = partial(self._futures_trade_extract_instrument, translator)
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            SubscriptionComponent(
                instruments=instruments,
                manager=_FuturesTradesSubscriptionManager(translator),
                gauge=params.scraped_market_count,
                batch_subscription=False,
            ),
            TradeComponent(
                self.exchange_name,
                extract_instrument,
                self._extract_futures_trades,
                params.on_trades,
            ),
            PingComponent(
                construct_ping=lambda _: {"method": "ping"},
                extract_pong=lambda msg: isinstance(msg, dict) and msg.get("channel") == "pong",
                interval=30.0,
            ),
            InfoComponent(),
            break_after_first_message_recognition=True,
        )
        return Stream(
            factory.websocket(self._FUTURES_BASE_URL),
            processor,
            JsonTranslator(),
            params.diagnostics,
        )

    def futures_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        instrument_list = list(instruments)
        if len(instrument_list) > 1:
            raise Exception(
                "We cannot subscribe to multiple channels at once "
                "because there is no channel ID in the messages we get from MEXC exchange"
            )

        translator = ChannelTranslator(instruments, lambda instrument: instrument.symbol)
        extract_instrument = partial(self._futures_book_extract_instrument, translator)
        book_component = BookComponent(
            exchange_name=self.exchange_name,
            extract_instrument=extract_instrument,
            extract_book=self._get_futures_book_from_message,
            stream_params=params,
            mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
        )
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            SubscriptionComponent(
                instruments=instruments,
                manager=_FuturesBooksSubscriptionManager(translator),
                gauge=params.scraped_market_count,
            ),
            BookSnapshotComponentMEXC(
                extract_instrument,
                self._get_futures_book_sequence_id,
                lambda instrument: self._get_futures_book_snapshot(self.http_client, instrument),
                book_component,
                params.executor,
                max_concurrent_requests=4,
            ),
            book_component,
            PingComponent(
                lambda _: {"method": "ping"}, lambda msg: isinstance(msg, dict) and msg.get("channel") == "pong", interval=30.0
            ),
            InfoComponent(),
        )
        return Stream(factory.websocket(self._FUTURES_BASE_URL), processor, JsonTranslator(), params.diagnostics)

    @staticmethod
    def _get_spot_book_sequence_id(message: PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper) -> int:
        return int(message.publicAggreDepths.toVersion)

    @staticmethod
    def _get_spot_book_snapshot(client: IHttpClient, instrument: Instrument) -> BookDataMEXC:
        # Discard any bid/ask level with a 0 price or 0 amount
        def convert_levels(levels: List[JsonValue]) -> List[PriceLevel]:
            price_levels = [PriceLevel(price=Decimal(level[0]), amount=Decimal(level[1])) for level in levels]
            return [level for level in price_levels if level.price > 0 and level.amount > 0]

        response = client.get(f"https://api.mexc.com/api/v3/depth?symbol={instrument.symbol}&limit=5000")
        body = get_json(response)
        return BookDataMEXC(
            book_type=BookType.FULL,
            exchange_sequence_id=body["lastUpdateId"],
            exchange_time=None,
            bids=BookData.sort_bids(convert_levels(body.get("bids", []))),
            asks=BookData.sort_asks(convert_levels(body.get("asks", []))),
        )

    def _get_spot_book_from_message(self, message: PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper) -> BookDataMEXC:
        """
        Example:

            channel: "<EMAIL>@10ms@BTCUSDT"
            symbol: "BTCUSDT"
            sendTime: 1754578099052
            publicAggreDepths {
              asks {
                price: "116313.25"
                quantity: "0.82218618"
              }
              asks {
                price: "116312.97"
                quantity: "0"
              }
              asks {
                price: "116312.98"
                quantity: "0"
              }
              asks {
                price: "116311.41"
                quantity: "0.6996792"
              }
              eventType: "<EMAIL>@10ms"
              fromVersion: "43266376453"
              toVersion: "43266376456"
            }

        """
        return BookDataMEXC(
            book_type=BookType.DELTA,
            exchange_sequence_id=self._get_spot_book_sequence_id(message),
            exchange_time=dt_from_ms_aware(int(message.sendTime)),
            bids=BookData.sort_bids([
                PriceLevel(price=Decimal(bid.price), amount=Decimal(bid.quantity)) for bid in message.publicAggreDepths.bids
            ]),
            asks=BookData.sort_asks([
                PriceLevel(price=Decimal(ask.price), amount=Decimal(ask.quantity)) for ask in message.publicAggreDepths.asks
            ]),
        )

    @staticmethod
    def _get_spot_book_channel_from_instrument(instrument: Instrument) -> str:
        return f"<EMAIL>@10ms@{instrument.symbol}"

    @staticmethod
    def _spot_book_extract_instrument(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper) and message.symbol:
            return translator.to_instrument(message.channel)
        return None

    @staticmethod
    def _spot_trade_extract_instrument(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, PushDataV3ApiWrapper_pb2.PushDataV3ApiWrapper) and message.symbol:
            return translator.to_instrument(message.channel)
        return None

    @staticmethod
    def _futures_trade_extract_instrument(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "symbol" in message:
            return translator.to_instrument(message["symbol"])
        return None

    @staticmethod
    def _get_spot_trade_channel_from_instrument(instrument: Instrument) -> str:
        return f"<EMAIL>@10ms@{instrument.symbol}"

    @staticmethod
    def _get_futures_book_sequence_id(message: JsonValue) -> int:
        return int(message["data"]["version"])

    @staticmethod
    def _futures_book_extract_instrument(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "symbol" in message:
            return translator.to_instrument(message["symbol"])
        return None

    @staticmethod
    def _get_futures_book_from_message(message: JsonValue) -> BookData:
        return BookDataFuturesMEXC(
            book_type=BookType.DELTA,
            exchange_sequence_id=int(message["data"]["version"]),
            exchange_time=dt_from_ms_aware(int(message["ts"])),
            bids=BookData.sort_bids([
                PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1]), count=bid[2])
                for bid in message["data"].get("bids") or []
            ]),
            asks=BookData.sort_asks([
                PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1]), count=ask[2])
                for ask in message["data"].get("asks") or []
            ]),
        )

    @staticmethod
    def _is_buy_trade(trade_type: int) -> bool:
        if trade_type == 1:
            return True
        elif trade_type == 2:
            return False
        else:
            raise Exception(f"Unknown MEXC trade type '{trade_type}'")

    def _extract_spot_trades(self, message: JsonValue) -> List[TradeData]:
        """
        Example:

            channel: "<EMAIL>@10ms@1DOLLARUSDC"
            symbol: "1DOLLARUSDC"
            sendTime: 1754644186889
            publicAggreDeals {
              deals {
                price: "0.004479"
                quantity: "4417.22"
                tradeType: 1
                time: 1754644186880
              }
              eventType: "<EMAIL>@10ms"
            }
        """

        def _is_buy(trade_type: int) -> bool:
            if trade_type == 1:
                return True
            elif trade_type == 2:
                return False
            else:
                raise Exception(f"Unknown MEXC spot trade type '{trade_type}'")

        symbol = message.symbol
        res = []
        for item in message.publicAggreDeals.deals:
            trade_time = dt_from_ms_aware(item.time)
            price = Decimal(item.price)
            amount = abs(Decimal(item.quantity))
            is_buy = _is_buy(item.tradeType)
            trade_id = self._trade_id.make_trade_id(symbol, str(dt_to_ms(trade_time)), price, amount, is_buy)
            res.append(
                TradeData(
                    trade_id=trade_id,
                    amount=amount,
                    price=price,
                    time=trade_time,
                    is_buy=is_buy,
                )
            )
        return res

    def _extract_futures_trades(self, message: JsonValue) -> List[TradeData]:
        return [trade_data for trade_data in self._futures_trades_from_message(message)]

    def _futures_trades_from_message(self, message: JsonValue) -> Iterable[TradeData]:
        """Message example:
        {
            'channel': 'push.deal',
            'data': [{'M': 1, 'O': 3, 'T': 2, 'p': Decimal('1.0342'), 't': 1756115328624, 'v': 10}],
            'symbol': '1000000MOG_USDT',
            'ts': 1756115328624
        }
        """
        if isinstance(message, dict) and message.keys() >= {"data", "symbol", "channel"}:
            if message["channel"] == "push.deal":
                instrument = message["symbol"]
                for data in message["data"]:
                    trade_time = dt_from_ms_aware(data["t"])
                    price = Decimal(data["p"])
                    amount = abs(Decimal(data["v"]))
                    trade_id = self._trade_id.make_trade_id(
                        instrument,
                        str(dt_to_ms(trade_time)),
                        price,
                        amount,
                        self._is_buy_trade(data["T"]),
                    )
                    yield TradeData(
                        trade_id=trade_id,
                        amount=amount,
                        price=price,
                        time=trade_time,
                        is_buy=self._is_buy_trade(data["T"]),
                    )

    @staticmethod
    def _get_futures_book_snapshot(client: IHttpClient, instrument: Instrument) -> BookData:
        response = client.get(f"https://contract.mexc.com/api/v1/contract/depth/{instrument.symbol}")
        body = get_json(response)
        return BookDataFuturesMEXC(
            book_type=BookType.FULL,
            exchange_sequence_id=body["data"]["version"],
            exchange_time=dt_from_ms_aware(body["data"]["timestamp"]),
            bids=BookData.sort_bids([
                PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1]), count=bid[2]) for bid in body["data"].get("bids") or []
            ]),
            asks=BookData.sort_asks([
                PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1]), count=ask[2]) for ask in body["data"].get("asks") or []
            ]),
        )


class _SpotBooksSubscriptionManager(SubscriptionManagerBase):
    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        return {"method": "SUBSCRIPTION", "params": [self.translator.to_channel(instrument) for instrument in instruments]}

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        instrument = self.extract_instrument(message)
        return SubscriptionUpdate(positive_confirmations=[instrument]) if instrument is not None else None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "msg" in message:
            return self.translator.to_instrument(message["msg"])
        return None


class _SpotTradesSubscriptionManager(SubscriptionManagerBase):
    _instruments_to_confirm: List[Instrument]

    def reset(self) -> None:
        self._instruments_to_confirm = []

    def create_subscription_message(self, instrument: Instrument) -> JsonValue:
        self._instruments_to_confirm.append(instrument)
        return {"method": "SUBSCRIPTION", "params": [self.translator.to_channel(instrument)]}

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        raise NotImplementedError("No batch subscription for MEXC spot trades")

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        instrument = self.extract_instrument(message)
        if instrument:
            return SubscriptionUpdate(positive_confirmations=[instrument])
        return None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "msg" in message:
            return self.translator.to_instrument(message["msg"])
        return None


class _FuturesBooksSubscriptionManager(SubscriptionManagerBase):
    _instrument: Instrument

    def create_subscription_message(self, instrument: Instrument) -> JsonValue:
        self._instrument = instrument
        return {"method": "sub.depth", "param": {"symbol": self.translator.to_channel(instrument)}}

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        instrument = self.extract_instrument(message)
        return SubscriptionUpdate(positive_confirmations=[instrument]) if instrument is not None else None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and message.get("data") == "success":
            return self._instrument
        if isinstance(message, dict) and "symbol" in message:
            return self.translator.to_instrument(message["symbol"])
        return None


class _FuturesTradesSubscriptionManager(SubscriptionManagerBase):
    _instrument: Instrument

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        pass

    def create_subscription_message(self, instrument: Instrument) -> JsonValue:
        self._instrument = instrument
        return {
            "method": "sub.deal",
            "param": {"symbol": self.translator.to_channel(instrument)},
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        instrument = self.extract_instrument(message)
        return SubscriptionUpdate(positive_confirmations=[instrument]) if instrument is not None else None

    def extract_instrument(self, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and message.get("channel", "") == "rs.sub.deal" and message.get("data", "") == "success":
            return self._instrument
        return None


class InfoComponent(IStreamProcessor[P_T]):
    def on_message(self, message: P_T, stream: IStreamApi[P_T]) -> bool:
        return isinstance(message, dict) and "channel" in message and message["channel"] == "clientId"
