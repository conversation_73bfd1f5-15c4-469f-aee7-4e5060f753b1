import json
import random
import time
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from functools import partial
from http import HTTPStatus
from typing import Callable, DefaultDict, Dict, Iterator, List, Optional

import ciso8601
from requests import Response

from src.octopus.data import (
    BookData,
    BookType,
    FuturesContractData,
    Instrument,
    MarketType,
    MutableBookDataTolerateMissingZeroLevels,
    PriceLevel,
    SpotContractData,
    TradeData,
)
from src.octopus.exceptions import ConnectionConfirmationFailure, UnexpectedMessageFormat
from src.octopus.exchange.api import (
    BookStreamParams,
    ExchangeInstrument,
    ExchangeMarkets,
    IClientFactory,
    Instruments,
    TradeStreamParams,
)
from src.octopus.exchange.api_utils import ExchangeHttpApiBase, ExchangeStreamingApiBase
from src.octopus.stream_components.book import BookComponent, BookSnapshotComponentWithListOfUpdatesBase
from src.octopus.stream_components.error import ErrorHandlingComponent
from src.octopus.stream_components.ping import PingComponent
from src.octopus.stream_components.subscription import (
    ISubscriptionManager,
    SubscriptionComponent,
    SubscriptionManagerBase,
    SubscriptionUpdate,
)
from src.octopus.stream_components.trade import TradeComponent
from src.octopus.stream_components.types import ExtractInstrument
from src.octopus.translation import ChannelTranslator, ExchangeTickerTranslator
from src.octopus.utils import compress_price_levels
from src.utils.diagnostics import Diagnostics
from src.utils.execution import IBackgroundTaskExecutor, IRunnable
from src.utils.http import IHttpClient, get_json
from src.utils.stream import (
    P_T,
    ComponentProcessor,
    ConnectionEvent,
    IStreamApi,
    IStreamingConnection,
    IStreamProcessor,
    JsonTranslator,
    MessageReceived,
    Stream,
    StreamingClient,
)
from src.utils.timeutil import ProvideCurrentTime, dt_from_any, dt_from_any_aware, dt_from_ms_aware, dt_to_us
from src.utils.types import JsonValue
from src.websocket.api import WebSocketMessage

EXCHANGE_NAME = "KuCoin"


class KuCoinHttpApi(ExchangeHttpApiBase):
    KNOWN_METADATA_LISTING_DATE: Dict[str, datetime] = {
        "OXEN-BTC": ciso8601.parse_datetime("2021-01-07 10:12:45.634073+0000"),
        "OXEN-ETH": ciso8601.parse_datetime("2021-01-07 09:41:01.600896+0000"),
        "OXEN-USDT": ciso8601.parse_datetime("2021-01-07 09:50:25.283055+0000"),
    }

    def spot_markets(self, client: IHttpClient) -> ExchangeMarkets:
        result = []

        for market_info in self._get_spot_markets(client):
            symbol = market_info["name"]
            base = market_info["baseCurrency"].lower()
            quote = market_info["quoteCurrency"].lower()
            result.append(ExchangeInstrument(MarketType.SPOT, symbol, base, quote, alt_symbol=market_info["symbol"]))

        return self.translator.exchange_to_coinmetrics(result)

    def spot_markets_metadata(self, client: IHttpClient) -> List[SpotContractData]:
        result: List[SpotContractData] = []
        for market_info in self._get_spot_markets(client):
            symbol = market_info["name"]
            base = market_info["baseCurrency"].lower()
            quote = market_info["quoteCurrency"].lower()

            listing_date = self.KNOWN_METADATA_LISTING_DATE.get(symbol, SpotContractData.DEFAULT_LISTING_DATE)
            contract_data = SpotContractData(
                symbol=symbol,
                base_id=self.translator.to_cm_id(base),
                quote_id=self.translator.to_cm_id(quote),
                base_name=self.translator.translate(base),
                quote_name=self.translator.translate(quote),
                native_base_name=market_info["baseCurrency"],
                native_quote_name=market_info["quoteCurrency"],
                listing_date=listing_date,
                end_date=None,
                is_current=True,
                amount_increment=Decimal(market_info["baseIncrement"]),
                amount_size_min=Decimal(market_info["baseMinSize"]),
                amount_size_max=Decimal(market_info["baseMaxSize"]),
                price_increment=Decimal(market_info["priceIncrement"]),
                price_size_min=Decimal(market_info["priceIncrement"]),
                order_size_min=Decimal(market_info["quoteMinSize"]),
                margin_trading_enabled=market_info["isMarginEnabled"],
                status="online" if market_info["enableTrading"] else "offline",
            )
            result.append(contract_data)
        return result

    def futures_markets_metadata(self, client: IHttpClient) -> List[FuturesContractData]:
        result: List[FuturesContractData] = []
        for market_info in self._get_futures_markets(client):
            base_name = market_info["baseCurrency"].lower() if market_info["baseCurrency"] != "XBT" else "btc"
            quote_name = market_info["quoteCurrency"].lower() if market_info["quoteCurrency"] != "XBT" else "btc"
            margin_asset_name = market_info["settleCurrency"].lower() if market_info["settleCurrency"] != "XBT" else "btc"
            if market_info["settleCurrency"] in ("USDT", "USDC"):
                size_asset_name = base_name
            elif market_info["settleCurrency"] == market_info["baseCurrency"]:
                size_asset_name = "usd"
            else:
                self.diagnostics.warning(f"Instrument with unknown 'size_asset': {market_info}")
                continue
            result.append(
                FuturesContractData(
                    symbol=market_info["symbol"],
                    underlying_base_id=self.translator.to_cm_id(base_name),
                    underlying_quote_id=self.translator.to_cm_id(quote_name),
                    size_asset_id=self.translator.to_cm_id(size_asset_name),
                    margin_asset_id=self.translator.to_cm_id(margin_asset_name),
                    underlying_base_name=base_name,
                    underlying_quote_name=quote_name,
                    size_asset_name=size_asset_name,
                    margin_asset_name=margin_asset_name,
                    underlying_native_base_name=market_info["baseCurrency"],
                    underlying_native_quote_name=market_info["quoteCurrency"],
                    listing_date=dt_from_any_aware(market_info["firstOpenDate"]),
                    expiry_date=dt_from_any_aware(market_info["expireDate"]) if market_info["expireDate"] else None,
                    amount_size_max=Decimal(market_info["maxOrderQty"]),
                    price_size_max=Decimal(market_info["maxPrice"]),
                    amount_size_min=Decimal(market_info["lotSize"]),
                    amount_increment=Decimal(market_info["lotSize"]),
                    tick_size=Decimal(market_info["tickSize"]),
                    price_increment=Decimal(market_info["tickSize"]),
                    contract_size=abs(Decimal(market_info["multiplier"])),
                    maker_fee=Decimal(market_info["makerFeeRate"]),
                    taker_fee=Decimal(market_info["takerFeeRate"]),
                    status="online" if market_info["status"] == "Open" else "offline",
                )
            )
        return result

    @staticmethod
    def _get_spot_markets(client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return get_json(client.get(f"{KuCoinHttpApi._get_base_url()}/symbols"))["data"]  # type: ignore

    @staticmethod
    def _get_futures_markets(client: IHttpClient) -> List[Dict[str, JsonValue]]:
        return get_json(client.get(f"{KuCoinHttpApi._get_futures_base_url()}/contracts/active"))["data"]  # type: ignore

    def futures_markets(self, client: IHttpClient) -> ExchangeMarkets:
        result = []

        for market_info in self._get_futures_markets(client):
            symbol = market_info["symbol"]
            base = market_info["baseCurrency"].lower()
            quote = market_info["quoteCurrency"].lower()
            result.append(ExchangeInstrument(MarketType.FUTURES, symbol, base, quote, alt_symbol=market_info["symbol"]))

        return self.translator.exchange_to_coinmetrics(result)

    def book(self, client: IHttpClient, instrument: Instrument, depth: int, poll_max_book: bool) -> Optional[BookData]:
        if instrument.market_type == MarketType.SPOT:
            url = f"{self._get_http_book_snapshot_url()}?symbol={instrument.alt_symbol}"
        elif instrument.market_type == MarketType.FUTURES:
            url = f"{self._get_futures_base_url()}/level2/snapshot?symbol={instrument.alt_symbol}"
        else:
            raise ValueError(f"Unexpected 'market_type' of instrument: {instrument}")

        response = get_json(client.get(url))
        # handle {'code': '200000', 'data': {'time': 0, 'sequence': None, 'bids': None, 'asks': None}} response
        if (
            isinstance(response, dict)
            and isinstance(response["data"], dict)
            and not response["data"].get("time")
            and not response["data"].get("sequence")
            and not response["data"].get("bids")
            and not response["data"].get("asks")
        ):
            return None

        if (
            not isinstance(response, dict)
            or not isinstance(response["data"], dict)
            or not isinstance(response["data"]["bids"], list)
            or not isinstance(response["data"]["asks"], list)
        ):
            raise UnexpectedMessageFormat(f"expected dict with 'asks' and 'bids' list fields, got: {response}")

        data = response["data"]
        bids = [PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1]), count=None) for bid in data["bids"]]
        asks = [PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1]), count=None) for ask in data["asks"]]
        bids = compress_price_levels(bids, depth)
        asks = compress_price_levels(asks, depth)

        book_time = response["data"].get("time") or response["data"].get("ts")
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=int(data["sequence"]) if data["sequence"] else None,
            exchange_time=dt_from_any_aware(book_time) if book_time else None,
            bids=bids,
            asks=asks,
        )

    def check_rate_limited(self, response: Response) -> float:
        """From the KuCoin support email:
        There are three kinds of restrictions:
            code: 1015, the first is the cloudflare restriction based on ip - blocks for 30s
            code: 200002, the second is the restriction based on the user's userid mode - blocks for 10s
            code: 429000, the third is the restriction of the whole site traffic - no block
        """
        if response.status_code != HTTPStatus.TOO_MANY_REQUESTS:
            return 0

        inactivity_time_by_code = {"1015": 30, "200002": 10, "429000": 0}
        try:
            code = response.json().get("code")
        except ValueError:
            code = ""
        print(f"Rate limited with code: {code}")
        return inactivity_time_by_code.get(code) or 0

    @classmethod
    def _get_base_url(cls) -> str:
        return "https://api.kucoin.com/api/v1"

    @classmethod
    def _get_futures_base_url(cls) -> str:
        return "https://api-futures.kucoin.com/api/v1"

    @classmethod
    def _get_http_book_snapshot_url(cls) -> str:
        return f"{KuCoinHttpApi._get_base_url()}/market/orderbook/level2_100"

    @classmethod
    def _get_http_futures_book_snapshot_url(cls) -> str:
        return f"{KuCoinHttpApi._get_futures_base_url()}/level2/depth100"


@dataclass(frozen=True)
class BookDataKuCoinSpot(BookData):
    exchange_sequence_id: int
    exchange_sequence_end_id: int

    def is_next_sequence(self, other: "BookDataKuCoinSpot") -> Optional[bool]:  # type: ignore[override]
        return (
            self.exchange_sequence_end_id + 1 >= other.exchange_sequence_id
            and self.exchange_sequence_end_id < other.exchange_sequence_end_id
        )


class BookSnapshotComponentKuCoin(BookSnapshotComponentWithListOfUpdatesBase[P_T]):
    def _handle_list_of_updates(  # type: ignore[override]
        self, stream: IStreamApi[P_T], snapshot: BookDataKuCoinSpot, instrument: Instrument
    ) -> None:
        for msg in self._list_of_updates[instrument]:
            if msg["data"]["sequenceEnd"] <= snapshot.exchange_sequence_id:
                continue
            if msg["data"]["sequenceStart"] <= snapshot.exchange_sequence_id + 1 <= msg["data"]["sequenceEnd"]:
                msg["data"]["sequenceStart"] = snapshot.exchange_sequence_id + 1
            self._book_component.on_message(msg, stream)
        self._list_of_updates = defaultdict(list)


class FutureBookSnapshotComponentKuCoin(BookSnapshotComponentWithListOfUpdatesBase[P_T]):
    def _handle_list_of_updates(  # type: ignore[override]
        self, stream: IStreamApi[P_T], snapshot: BookDataKuCoinSpot, instrument: Instrument
    ) -> None:
        for msg in self._list_of_updates[instrument]:
            if msg["data"]["sequence"] <= snapshot.exchange_sequence_id:
                continue
            self._book_component.on_message(msg, stream)
        self._list_of_updates = defaultdict(list)


class WelcomeAckComponentKuCoin(IStreamProcessor[JsonValue]):
    def on_message(self, message: JsonValue, stream: IStreamApi[P_T]) -> bool:
        if message["type"] == "welcome" or message["type"] == "ack":
            return True
        return False


class PingPongKuCoin(IStreamProcessor[JsonValue]):
    lastPingTimeMs = time.time() * 1000
    ping_interval: int

    def __init__(
        self,
        ping_interval: int,
    ):
        self.ping_interval = ping_interval

    def on_message(self, message: JsonValue, stream: IStreamApi[P_T]) -> bool:
        if message["type"] == "pong":
            return True
        current_time = time.time() * 1000

        if current_time - self.lastPingTimeMs >= self.ping_interval:
            ping_message = f'{{"id":"ping-{current_time}","type":"ping"}}'
            self.lastPingTimeMs = current_time
            json_loads = json.loads(ping_message)
            stream.send(json_loads)
        return False


class BookMessageComponentKuCoin(BookSnapshotComponentWithListOfUpdatesBase[P_T]):
    def __init__(
        self,
        extract_instrument: ExtractInstrument[P_T],
        extract_sequence_id: Callable[[P_T], Optional[int]],
        obtain_snapshot: Callable[[Instrument], BookData],
        book_component: BookComponent[P_T],
        task_executor: IBackgroundTaskExecutor,
        max_concurrent_requests: int,
    ):
        super().__init__(
            extract_instrument,
            extract_sequence_id,
            obtain_snapshot,
            book_component,
            task_executor,
            max_concurrent_requests,
        )
        self._list_of_updates: DefaultDict[Instrument, List[JsonValue]] = defaultdict(list)


class KuCoinWebSocketApi(ExchangeStreamingApiBase):
    _IN_MEMORY_STORED_BOOK_DEPTH = 30000

    def __init__(
        self,
        exchange_name: str,
        ticker_translator: ExchangeTickerTranslator,
        client: IHttpClient,
        current_time_provider: ProvideCurrentTime = lambda: datetime.utcnow(),
    ):
        super().__init__(exchange_name, ticker_translator, client)
        self._current_time_provider = current_time_provider

    def trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        client = _websocket_client(factory, self.http_client, self._current_time_provider)

        translator = ChannelTranslator(instruments, lambda instrument: f"/market/match:{instrument.alt_symbol}")

        connection_confirmation = _ConnectionIdConfirmation()

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            PingComponent(
                lambda time_passed: {"type": "ping", "id": str(time_passed)},
                lambda msg: isinstance(msg, dict) and msg.get("type") == "pong",
                interval=10.0,
            ),
            connection_confirmation,
            SubscriptionComponent(
                instruments,
                _SubscriptionManager(instruments),
                params.scraped_market_count,
                batch_subscription=True,
                can_subscribe=connection_confirmation.is_confirmed,
            ),
            TradeComponent(
                self.exchange_name,
                partial(self._trades_extract_instrument, translator),
                _trades_from_message,
                params.on_trades,
            ),
        )

        return Stream(client, processor, JsonTranslator(), params.diagnostics)

    def futures_trades(self, factory: IClientFactory, instruments: Instruments, params: TradeStreamParams) -> IRunnable:
        client = _websocket_client(factory, self.http_client, self._current_time_provider)
        translator = ChannelTranslator(instruments, lambda instrument: f"/contractMarket/execution:{instrument.alt_symbol}")
        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            PingComponent(
                lambda time_passed: {"type": "ping", "id": str(time_passed)},
                lambda msg: isinstance(msg, dict) and msg.get("type") == "pong",
                interval=10.0,
            ),
            SubscriptionComponent(
                instruments,
                _FuturesTradesSubscriptionManager(instruments),
                params.scraped_market_count,
                batch_subscription=True,
            ),
            TradeComponent(
                self.exchange_name,
                partial(self._trades_extract_instrument, translator),
                self._get_future_trade_from_message,
                params.on_trades,
            ),
        )

        return Stream(client, processor, JsonTranslator(), params.diagnostics)

    def books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        translator = ChannelTranslator(instruments, lambda instrument: f"{instrument.alt_symbol.upper()}")
        book_component = BookComponent(
            exchange_name=self.exchange_name,
            extract_instrument=partial(self._extract_instrument, translator),
            extract_book=self._get_book_spot_from_message,
            stream_params=params,
            mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
        )

        extract_instrument = partial(self._extract_instrument, translator)
        snapshot_component = BookSnapshotComponentKuCoin(
            extract_instrument,
            self._get_book_first_sequence_id,
            lambda instrument: self._get_book_snapshot(self.http_client, instrument, self._IN_MEMORY_STORED_BOOK_DEPTH),
            book_component,
            params.executor,
            max_concurrent_requests=4,
        )

        subscription = BooksSubscriptionManager(
            instruments, ChannelTranslator(instruments, lambda instrument: ",".join([translator.to_channel(instrument)]))
        )

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            WelcomeAckComponentKuCoin(),
            PingPongKuCoin(ping_interval=50000),
            SubscriptionComponent(instruments, subscription, params.scraped_market_count, batch_subscription=True),
            book_component,
            snapshot_component,
        )
        return Stream(
            factory.websocket_with_url_provider(self.get_web_socket_connection_url_for_spot),
            processor,
            JsonTranslator(),
            params.diagnostics,
        )

    def futures_books(self, factory: IClientFactory, instruments: Instruments, params: BookStreamParams) -> IRunnable:
        url_provider: Callable[[], str] = self.get_web_socket_connection_url_for_future
        translator = ChannelTranslator(instruments, lambda instrument: f"{instrument.alt_symbol.upper()}")
        book_component = BookComponent(
            exchange_name=self.exchange_name,
            extract_instrument=partial(self._extract_instrument, translator),
            extract_book=self._get_book_future_from_message,
            stream_params=params,
            mutable_book_data_class=MutableBookDataTolerateMissingZeroLevels,
        )

        extract_instrument = partial(self._extract_instrument, translator)
        snapshot_component = FutureBookSnapshotComponentKuCoin(
            extract_instrument,
            self._get_futures_book_first_sequence_id,
            lambda instrument: self._get_book_snapshot(self.http_client, instrument, self._IN_MEMORY_STORED_BOOK_DEPTH),
            book_component,
            params.executor,
            max_concurrent_requests=4,
        )

        subscription = FuturesBooksSubscriptionManager(
            instruments, ChannelTranslator(instruments, lambda instrument: ",".join([translator.to_channel(instrument)]))
        )

        processor: ComponentProcessor[JsonValue] = ComponentProcessor(
            ErrorHandlingComponent(_is_error_message),
            WelcomeAckComponentKuCoin(),
            PingPongKuCoin(ping_interval=18000),
            SubscriptionComponent(instruments, subscription, params.scraped_market_count, batch_subscription=True),
            book_component,
            snapshot_component,
        )
        return Stream(
            factory.websocket_with_url_provider(url_provider),
            processor,
            JsonTranslator(),
            params.diagnostics,
        )

    def get_web_socket_connection_url_for_spot(self) -> str:
        return self.get_web_socket_connection_url_for_futures_and_spot(False)

    def get_web_socket_connection_url_for_future(self) -> str:
        return self.get_web_socket_connection_url_for_futures_and_spot(True)

    def get_web_socket_connection_url_for_futures_and_spot(self, future: bool) -> str:
        random.seed(datetime.now().timestamp())
        bullet_public_url = self.get_bullet_public_url(future)
        json_response = get_json(self._client.post(bullet_public_url))
        instance_servers = json_response["data"]["instanceServers"]
        selected_host = random.randint(0, len(instance_servers) - 1)
        return f"{instance_servers[selected_host]['endpoint']}?token={json_response['data']['token']}"

    def get_bullet_public_url(self, future: bool) -> str:
        if future:
            return f"{KuCoinHttpApi._get_futures_base_url()}/bullet-public"
        return f"{KuCoinHttpApi._get_base_url()}/bullet-public"

    @staticmethod
    def _get_book_spot_from_message(message: JsonValue) -> BookData:
        data = message["data"]
        bids = [PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in data["changes"]["bids"]]
        asks = [PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in data["changes"]["asks"]]
        return BookDataKuCoinSpot(
            book_type=BookType.DELTA,
            exchange_sequence_id=data["sequenceStart"],
            exchange_sequence_end_id=data["sequenceEnd"],
            exchange_time=dt_from_ms_aware(data["time"]),
            bids=BookData.sort_bids(bids),
            asks=BookData.sort_asks(asks),
        )

    @staticmethod
    def _get_book_future_from_message(message: JsonValue) -> BookData:
        # Message format
        # {
        #     'type': 'message',
        #     'topic': '/contractMarket/level2:DOGEUSDTM', 'subject': 'level2',
        #     'data':
        #         {
        #             'sequence': 1668994127365,
        #             'change': '0.08974,buy,1826',
        #             'timestamp': 1670926665088
        #         }
        # }
        data = message["data"]
        change = data["change"].split(",")
        price = [PriceLevel(price=Decimal(change[0]), amount=Decimal(change[2]))]
        if change[1] == "sell":
            asks = price
            bids = []
        else:
            asks = []
            bids = price
        return BookData(
            book_type=BookType.DELTA,
            exchange_sequence_id=data["sequence"],
            exchange_time=dt_from_ms_aware(data["timestamp"]),
            bids=bids,
            asks=asks,
        )

    @staticmethod
    def _extract_instrument(translator: ChannelTranslator, message: JsonValue) -> Optional[Instrument]:
        if isinstance(message, dict) and "topic" in message:
            # example topic: /market/level2:BTC-USDT
            return translator.to_instrument(message["topic"].split(":")[1])
        return None

    def _get_book_snapshot(self, client: IHttpClient, instrument: Instrument, depth: int) -> BookData:
        if instrument.market_type == MarketType.SPOT:
            base_url = KuCoinHttpApi._get_http_book_snapshot_url()
            body = get_json(client.get(f"{base_url}?symbol={instrument.alt_symbol.upper()}&limit={depth}"))
            return BookDataKuCoinSpot(
                book_type=BookType.FULL,
                exchange_sequence_id=int(body["data"]["sequence"]),
                exchange_sequence_end_id=int(body["data"]["sequence"]),
                exchange_time=dt_from_ms_aware(body["data"]["time"]),
                bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in body["data"]["bids"]],
                asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in body["data"]["asks"]],
            )
        base_url = KuCoinHttpApi._get_http_futures_book_snapshot_url()
        body = get_json(client.get(f"{base_url}?symbol={instrument.alt_symbol.upper()}&limit={depth}"))
        data = body["data"]
        return BookData(
            book_type=BookType.FULL,
            exchange_sequence_id=int(data["sequence"]),
            exchange_time=dt_from_any_aware(body["data"]["ts"]),
            bids=[PriceLevel(price=Decimal(bid[0]), amount=Decimal(bid[1])) for bid in data["bids"]],
            asks=[PriceLevel(price=Decimal(ask[0]), amount=Decimal(ask[1])) for ask in data["asks"]],
        )

    @staticmethod
    def _get_book_first_sequence_id(message: JsonValue) -> Optional[int]:
        if isinstance(message, dict) and "data" in message:
            return int(message["data"]["sequenceStart"])
        return None

    @staticmethod
    def _get_futures_book_first_sequence_id(message: JsonValue) -> Optional[int]:
        if isinstance(message, dict) and "data" in message:
            return int(message["data"]["sequence"])
        return None

    @staticmethod
    def _get_future_trade_from_message(message: JsonValue) -> List[TradeData]:
        data = message["data"]
        return [
            TradeData(
                trade_id=int(data["tradeId"], base=16),
                amount=Decimal(data["size"]),
                price=Decimal(data["price"]),
                is_buy=data["side"] == "buy",
                time=dt_from_any_aware(data["ts"]),
            )
        ]

    @staticmethod
    def _trades_extract_instrument(translator: ChannelTranslator, msg: JsonValue) -> Optional[Instrument]:
        if isinstance(msg, dict) and msg.get("type") == "message" and "topic" in msg:
            return translator.to_instrument(msg["topic"])
        return None


class _WebSocketConnection(IStreamingConnection[WebSocketMessage]):
    def __init__(self, connection: IStreamingConnection[WebSocketMessage], expected_connection_id: int):
        self._connection = connection
        self._expected_connection_id = expected_connection_id
        self._sent_expected_id = False

    def close(self, gracefully: bool = True) -> None:
        self._connection.close(gracefully)

    def send(self, message: WebSocketMessage) -> bool:
        return self._connection.send(message)

    def communicate(self, timeout: float) -> Iterator[ConnectionEvent[WebSocketMessage]]:
        if self._sent_expected_id:
            yield from self._connection.communicate(timeout)
        else:
            self._sent_expected_id = True
            yield MessageReceived(str(self._expected_connection_id))


def _websocket_client(
    factory: IClientFactory, http_client: IHttpClient, current_time_provider: ProvideCurrentTime
) -> StreamingClient[WebSocketMessage]:
    def connect(timeout: float, diagnostics: Diagnostics) -> IStreamingConnection[WebSocketMessage]:
        connection_id = dt_to_us(current_time_provider())

        connection_info = get_json(http_client.post("https://api.kucoin.com/api/v1/bullet-public"))
        token = connection_info["data"]["token"]
        candidate_servers = [s for s in connection_info["data"]["instanceServers"] if s["protocol"] == "websocket"]

        if len(candidate_servers) == 0:
            raise ConnectionError("no server supporting WS was provided by the exchange")
        else:
            server_url = random.choice(candidate_servers)["endpoint"]

        diagnostics.info(f"will connect to {server_url} using token `{token}`")

        url = "{}?token={}&connectId={}&acceptUserMessage=true".format(server_url, token, connection_id)
        connection = factory.websocket(url)(timeout, diagnostics)
        return _WebSocketConnection(connection, connection_id)

    return connect


def _is_error_message(message: JsonValue) -> bool:
    return isinstance(message, dict) and message.get("type") == "error"


def _trades_from_message(message: JsonValue) -> List[TradeData]:
    data = message["data"]

    return [
        TradeData(
            trade_id=int(data["tradeId"], base=16),
            amount=Decimal(data["size"]),
            price=Decimal(data["price"]),
            is_buy=data["side"] == "buy",
            time=dt_from_any(data["time"]),
        )
    ]


class _ConnectionIdConfirmation(IStreamProcessor[JsonValue]):
    def on_open(self, stream: IStreamApi[JsonValue]) -> None:
        self._confirmed = False
        self._expected_connection_id: Optional[str] = None

    def on_message(self, message: JsonValue, stream: IStreamApi[JsonValue]) -> bool:
        # synthetic message of our own making, sent by _WebSocketConnection before any actual message from exchange.
        if self._expected_connection_id is None and isinstance(message, int):
            self._expected_connection_id = str(message)
            return True

        if isinstance(message, dict) and message.get("type") == "welcome":
            connection_id = message.get("id", None)

            if connection_id is None or self._expected_connection_id != connection_id:
                stream.diagnostics.error(
                    ConnectionConfirmationFailure("expected id: {}, got: {}".format(self._expected_connection_id, connection_id))
                )
                stream.reconnect()
            else:
                stream.diagnostics.info("connection id `{}` confirmed".format(self._expected_connection_id))
                self._confirmed = True

            return True
        else:
            return False

    def on_tick(self, time_passed: float, stream: IStreamApi[JsonValue]) -> None:
        if time_passed > 15.0 and not self._confirmed:
            stream.diagnostics.error_ns(ConnectionConfirmationFailure("confirmation timeout"))
            stream.reconnect()

    def is_confirmed(self) -> bool:
        return self._confirmed


class _SubscriptionManager(ISubscriptionManager):
    def __init__(self, instruments: Instruments):
        self._instruments = list(instruments)

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        return {
            "id": "__BATCH_SUBSCRIPTION__",
            "type": "subscribe",
            "topic": "/market/match:{}".format(",".join([i.alt_symbol for i in instruments])),
            "privateChannel": False,
            "response": True,
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(message, dict) and message.get("id") == "__BATCH_SUBSCRIPTION__":
            if message.get("type") == "ack":
                return SubscriptionUpdate(positive_confirmations=self._instruments)
            else:
                return SubscriptionUpdate(negative_confirmations=self._instruments)
        else:
            return None


class _FuturesTradesSubscriptionManager(ISubscriptionManager):
    _subscription_message_id: str

    def __init__(self, instruments: Instruments):
        self._instruments = list(instruments)

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self._subscription_message_id = f"__BATCH_SUBSCRIPTION__{time.time()}"
        return {
            "id": f"{self._subscription_message_id}",
            "type": "subscribe",
            "topic": f"/contractMarket/execution:{','.join([instrument.alt_symbol for instrument in instruments])}",
            "response": True,
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(message, dict) and message.get("id") == self._subscription_message_id:
            if message.get("type") == "ack":
                return SubscriptionUpdate(positive_confirmations=self._instruments)
            else:
                return SubscriptionUpdate(negative_confirmations=self._instruments)
        else:
            return None


class BooksSubscriptionManager(SubscriptionManagerBase):
    subscription_message_id: str

    def __init__(self, instruments: Instruments, channel_translator: ChannelTranslator):
        super(BooksSubscriptionManager, self).__init__(channel_translator)
        self._instruments = list(instruments)

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self.subscription_message_id = f"__BATCH_SUBSCRIPTION__{time.time()}"
        return {
            "id": f"{self.subscription_message_id}",
            "type": "subscribe",
            "topic": f"/market/level2:{','.join([self.translator.to_channel(instrument) for instrument in instruments])}",
            "privateChannel": False,
            "response": True,
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(message, dict) and message.get("id") == self.subscription_message_id:
            if message.get("type") == "ack":
                return SubscriptionUpdate(positive_confirmations=self._instruments)
        return None


class FuturesBooksSubscriptionManager(SubscriptionManagerBase):
    subscription_message_id: str

    def __init__(self, instruments: Instruments, channel_translator: ChannelTranslator):
        super(FuturesBooksSubscriptionManager, self).__init__(channel_translator)
        self._instruments = list(instruments)

    def create_batch_subscription_message(self, instruments: List[Instrument]) -> JsonValue:
        self.subscription_message_id = f"__FUTURES_BATCH_SUBSCRIPTION__{time.time()}"
        instruments_string = ",".join([self.translator.to_channel(instrument) for instrument in instruments])
        return {
            "id": f"{self.subscription_message_id}",
            "type": "subscribe",
            "topic": f"/contractMarket/level2:{instruments_string}",
            "privateChannel": False,
            "response": True,
        }

    def update_subscriptions(self, message: JsonValue) -> Optional[SubscriptionUpdate]:
        if isinstance(message, dict) and message.get("id") == self.subscription_message_id:
            if message.get("type") == "ack":
                return SubscriptionUpdate(positive_confirmations=self._instruments)
        return None
