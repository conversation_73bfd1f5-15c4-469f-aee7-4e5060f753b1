from dataclasses import dataclass, field

from src.octopus.generators.deployment.data_loader_types.general import DataLoaderDeployment, DataLoaderEntrypointArgs
from src.octopus.inventory.types import DataType, DeploymentType


@dataclass
class Cdev1FundingRateDataLoaderDeployment(DataLoaderDeployment):
    data_type: DataType = DataType.FUNDING_RATE
    deployment: DeploymentType = DeploymentType.CDEV1
    entrypoint: str = "python -m src.octopus.applications.funding_rate_loader"
    entrypoint_args: DataLoaderEntrypointArgs = field(
        default_factory=lambda: DataLoaderEntrypointArgs(
            postgres_out_futures="generate",
        )
    )


@dataclass
class ProdFundingRateDataLoaderDeployment(DataLoaderDeployment):
    data_type: DataType = DataType.FUNDING_RATE
    deployment: DeploymentType = DeploymentType.PROD
    entrypoint: str = "python -m src.octopus.applications.funding_rate_loader"
    entrypoint_args: DataLoaderEntrypointArgs = field(
        default_factory=lambda: DataLoaderEntrypointArgs(
            postgres_out_futures="generate",
        )
    )


@dataclass
class Cp1FundingRateDataLoaderDeployment(DataLoaderDeployment):
    data_type: DataType = DataType.FUNDING_RATE
    deployment: DeploymentType = DeploymentType.CP1
    entrypoint: str = "python -m src.octopus.applications.funding_rate_loader"
    entrypoint_args: DataLoaderEntrypointArgs = field(
        default_factory=lambda: DataLoaderEntrypointArgs(
            postgres_out_futures="generate",
        )
    )


@dataclass
class Cp2FundingRateDataLoaderDeployment(DataLoaderDeployment):
    data_type: DataType = DataType.FUNDING_RATE
    deployment: DeploymentType = DeploymentType.CP2
    entrypoint: str = "python -m src.octopus.applications.funding_rate_loader"
    entrypoint_args: DataLoaderEntrypointArgs = field(
        default_factory=lambda: DataLoaderEntrypointArgs(
            postgres_out_futures="generate",
        )
    )
