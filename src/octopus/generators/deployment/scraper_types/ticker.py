from dataclasses import dataclass, field

from src.octopus.applications.proxies.enums import (
    Cp1HetznerEu,
    HetznerUs,
    SmartproxyHetzner,
    WebShareEu,
    Cp2HetznerEu,
    SmartproxyCP1,
    SmartproxyCP2,
)
from src.octopus.generators.deployment.scraper_types.general import ScraperDeployment, ScraperEntrypointArgs, ShipperDeployment
from src.octopus.inventory.types import DataType, DeploymentType, MarketType, Scraper


@dataclass
class TickerScraperEntrypointArgs(ScraperEntrypointArgs):
    def to_string(self, scraper: Scraper, data_type: DataType, deployment: DeploymentType) -> str:
        result_args = [
            super().to_string(scraper, data_type, deployment),
        ]
        return "\n".join([row for row in result_args if row])


@dataclass
class ProdTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.PROD
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.http_futures_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_exch_out="generate",
            proxy_groups=[
                [
                    SmartproxyHetzner.FUTURES_TICKER,
                    HetznerUs.FUTURES_TICKER,
                ]
            ],
        )
    )


@dataclass
class ProdHttpOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.PROD
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.http_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_out="generate",
            proxy_groups=[
                [
                    SmartproxyHetzner.OPTION_TICKER,
                    HetznerUs.OPTION_TICKER,
                ]
            ],
        )
    )


@dataclass
class ProdWsFutureTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.PROD
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.streaming_future_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_exch_out="generate",
            proxy_groups=[[SmartproxyHetzner.FUTURES_TICKER, HetznerUs.FUTURES_TICKER]],
        )
    )


@dataclass
class ProdWsOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.PROD
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.streaming_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_out="generate",
            proxy_groups=[
                [
                    SmartproxyHetzner.OPTION_TICKER,
                    HetznerUs.OPTION_TICKER,
                ]
            ],
        )
    )


@dataclass
class Cdev1TickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CDEV1
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.http_futures_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_exch_out="generate",
            proxy_groups=[[WebShareEu.FUTURES_TICKER]],
        )
    )


@dataclass
class Cdev1HttpOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CDEV1
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.http_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_out="generate",
            proxy_groups=[[WebShareEu.OPTION_TICKER]],
        )
    )


@dataclass
class Cdev1WsFutureTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CDEV1
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.streaming_future_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_exch_out="generate",
            proxy_groups=[[WebShareEu.FUTURES_TICKER]],
        )
    )


@dataclass
class Cdev1WsOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CDEV1
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.streaming_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_out="generate",
            proxy_groups=[[WebShareEu.OPTION_TICKER]],
        )
    )


@dataclass
class Cp1TickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP1
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.http_futures_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_exch_out="generate",
            proxy_groups=[
                [
                    Cp1HetznerEu.FUTURES_TICKER,
                    SmartproxyCP1.TICKER_F_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp1HttpOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP1
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.http_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_out="generate",
            proxy_groups=[
                [
                    Cp1HetznerEu.OPTION_TICKER,
                    SmartproxyCP1.TICKER_O_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp1WsFutureTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP1
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.streaming_future_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_exch_out="generate",
            proxy_groups=[
                [
                    Cp1HetznerEu.FUTURES_TICKER,
                    SmartproxyCP1.TICKER_F_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp1WsOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP1
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.streaming_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_out="generate",
            proxy_groups=[
                [
                    Cp1HetznerEu.OPTION_TICKER,
                    SmartproxyCP1.TICKER_O_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp2TickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP2
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.http_futures_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_exch_out="generate",
            proxy_groups=[
                [
                    Cp2HetznerEu.FUTURES_TICKER,
                    SmartproxyCP2.TICKER_F_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp2HttpOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP2
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.http_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            poll_interval=30,
            postgres_out="generate",
            proxy_groups=[
                [
                    Cp2HetznerEu.OPTION_TICKER,
                    SmartproxyCP2.TICKER_O_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp2WsFutureTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP2
    data_type: DataType = DataType.TICKER_F
    entrypoint: str = "python -m src.octopus.applications.streaming_future_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_exch_out="generate",
            proxy_groups=[
                [
                    Cp2HetznerEu.FUTURES_TICKER,
                    SmartproxyCP2.TICKER_F_REALTIME,
                ]
            ],
        )
    )


@dataclass
class Cp2WsOptionTickerScraperDeployment(ScraperDeployment):
    deployment: DeploymentType = DeploymentType.CP2
    data_type: DataType = DataType.TICKER_O
    entrypoint: str = "python -m src.octopus.applications.streaming_option_ticker_scraper"
    entrypoint_args: TickerScraperEntrypointArgs = field(
        default_factory=lambda: TickerScraperEntrypointArgs(
            postgres_out="generate",
            proxy_groups=[
                [
                    Cp2HetznerEu.OPTION_TICKER,
                    SmartproxyCP2.TICKER_O_REALTIME,
                ]
            ],
        )
    )


@dataclass
class TickerShipperFuturesDeployment(ShipperDeployment):
    data_type: DataType = DataType.TICKER_F
    market_type: MarketType = MarketType.FUTURES


@dataclass
class TickerShipperOptionDeployment(ShipperDeployment):
    data_type: DataType = DataType.TICKER_O
    market_type: MarketType = MarketType.OPTION
