import logging
import re
from copy import deepcopy
from decimal import Decimal
from os.path import basename, dirname, join
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import yaml
from jinja2 import Environment, FileSystemLoader
from kubernetes.utils import parse_quantity

from src.octopus.applications.configs.cpu_requests import CPU_REQUESTS_PER_POD
from src.octopus.applications.configs.mem_requests import MEM_REQUESTS_PER_POD
from src.octopus.arguments import get_proxies_total
from src.octopus.generators.deployment.data_loader_types.general import DataLoaderDeployment
from src.octopus.generators.deployment.db_stat_collector_types.general import DbStatCollectorDeployment
from src.octopus.generators.deployment.scraper_types.general import DeploymentType, ScraperDeployment
from src.octopus.generators.deployment.yaml_dumper import IndentedYamlDumper, YamlQuoted, yaml_quoted_presenter
from src.octopus.generators.utils import update_obj_attributes_recursively
from src.octopus.inventory.types import (
    CollectionMode,
    ConnectionMode,
    DataLoader,
    DataType,
    DbStatCollector,
    FHGroup,
    MarketType,
    Scraper,
)
from src.utils.http import ProxyParams

MAX_POD_NAME_LENGTH = 63 - 17  # 63 is the actual limit, 17 is the length of the hostname tacked onto the end
GENERATOR_TEMPLATE_DIR = join(dirname(__file__), "templates")
SINGLE_INSTANCE_DEPLOYMENTS = ["daily-totals", "historical-patch", "patch-job"]
SINGLE_STACK_DEPLOYMENTS = []
DEFAULT_CPU_REQUEST = "250m"
DEFAULT_MEM_REQUEST = "3Gi"


def _get_kube_namespace(deployment: DeploymentType) -> str:
    return "fh"


def _generate_affinity_rules(
    exchange_service: Optional[Tuple[str, Union[ScraperDeployment, DataLoaderDeployment]]] = None,
) -> str:
    yaml.add_representer(YamlQuoted, yaml_quoted_presenter)  # type: ignore
    match_expressions = [
        {
            "key": "coinmetrics.io/stack",
            "operator": "In",
            "values": [YamlQuoted("${FEED_HANDLER_INSTANCE}")],
        }
    ]
    affinity_rules = {
        "nodeAffinity": {
            "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": match_expressions}]}
        }
    }
    return f"\n{str(yaml.dump(affinity_rules, Dumper=IndentedYamlDumper))}"


def _generate_image_pull_secrets(deployment: DeploymentType) -> str:
    secret = {"type": "none"}
    image_pull_secrets = {"imagePullSecrets": secret}
    return f"{str(yaml.dump(image_pull_secrets, Dumper=IndentedYamlDumper))}"


def _generate_kubernetes_tag(deployment: DeploymentType) -> str:
    if deployment in [DeploymentType.CDEV1, DeploymentType.CP1]:
        return "kube-any-small"
    return "kubernetes"


def _to_pod_quantity(value: Decimal) -> str:
    return f"{value // 1024 // 1024}Mi"


def _generate_mem_request(deployment: DeploymentType, service: ScraperDeployment, pod_name: str) -> str:
    memory_limit_str = service.mem_limit.replace("g", "Gi").replace("m", "Mi")
    if deployment in {
        DeploymentType.CP1,
    }:
        mem_request_str = (
            service.mem_request
            if service.mem_request
            else MEM_REQUESTS_PER_POD[pod_name]
            if pod_name in MEM_REQUESTS_PER_POD
            else DEFAULT_MEM_REQUEST
        )
        memory_limit = parse_quantity(memory_limit_str)
        mem_request = parse_quantity(mem_request_str)
        return _to_pod_quantity(min(mem_request, memory_limit))
    return memory_limit_str if memory_limit_str else DEFAULT_MEM_REQUEST


def _generate_entrypoint(config: Scraper, data_type: DataType) -> str:
    base_entrypoint = "python -m src.octopus.applications"

    if config.connection_mode == ConnectionMode.HISTORY:
        if data_type in {DataType.FUNDING_RATE, DataType.LIQUIDATION, DataType.OPEN_INTEREST, DataType.TRADE}:
            if data_type == DataType.TRADE and config.market_type == MarketType.FUTURES and config.exchange_name == "Bybit":
                return f"{base_entrypoint}.bybit_history_trade_futures_scraper"
            return f"{base_entrypoint}.history_{data_type.m_name}_scraper"

    if config.connection_mode == ConnectionMode.HTTP:
        if data_type == DataType.TICKER_F:
            return f"{base_entrypoint}.http_futures_ticker_scraper"
        if data_type == DataType.TICKER_O:
            return f"{base_entrypoint}.http_option_ticker_scraper"
        return f"{base_entrypoint}.http_{data_type.m_name}_scraper"

    if config.connection_mode == ConnectionMode.STREAMING:
        if data_type == DataType.TICKER_F:
            return f"{base_entrypoint}.streaming_future_ticker_scraper"
        if data_type == DataType.TICKER_O:
            return f"{base_entrypoint}.streaming_option_ticker_scraper"

    if config.connection_mode == ConnectionMode.STREAMING:
        if data_type in {DataType.BOOK, DataType.LIQUIDATION, DataType.TRADE}:
            return f"{base_entrypoint}.streaming_{data_type.m_name}_scraper"

    if config.connection_mode == ConnectionMode.STREAMING:
        if data_type in {DataType.QUOTE}:
            return f"{base_entrypoint}.streaming_{data_type.m_name}_feed_handler"

    raise ValueError(f"Unsupported entrypoint type: {data_type}, {config.connection_mode.m_name}")


def generate_script(
    config: Scraper,
    data_type: DataType,
    collection_mode: CollectionMode,
    service: ScraperDeployment,
    deployment: DeploymentType,
) -> str:
    exchange_name = config.exchange_name.replace(" ", "_").replace(".", "").replace("-", "_").lower()
    pod_prefix = config.get_pod_prefix(data_type)

    if len(pod_prefix) > MAX_POD_NAME_LENGTH:
        raise ValueError(f"Pod name too long: {pod_prefix}")

    if not re.match(r"^(?!-)[-a-z\d]+(?<!-)$", pod_prefix):
        raise ValueError(f"Invalid pod name: {pod_prefix}")

    deployment_settings = deepcopy(config.deployment_settings)
    update_obj_attributes_recursively(service, deployment_settings, raise_error_on_missing_attributes=True)

    # Override settings for K8S configuration

    if config.connection_mode == ConnectionMode.HISTORY:
        service.entrypoint_args.postgres_out = None
        if data_type != DataType.TRADE:
            service.entrypoint_args.pos_postgres_out = _get_postgres_out(config.market_type, data_type, False)
        service.entrypoint_args.pos_postgres_exch_out = _get_postgres_out(config.market_type, data_type, True)
    elif service.entrypoint_args.postgres_out is not None:
        service.entrypoint_args.postgres_out = _get_postgres_out(config.market_type, data_type, False)

    if service.entrypoint_args.postgres_exch_out is not None:
        service.entrypoint_args.postgres_exch_out = _get_postgres_out(config.market_type, data_type, True)

    if data_type != DataType.METADATA:
        if service.entrypoint_args.postgres_out is not None:
            service.entrypoint_args.postgres_out += f"+{_get_local_postgres_name(service.deployment)}"
        if service.entrypoint_args.postgres_exch_out is not None:
            service.entrypoint_args.postgres_exch_out += f"+{_get_local_postgres_name(service.deployment)}"

    if service.entrypoint_args.kafka_out is not None and len(service.entrypoint_args.kafka_out) > 0:
        if data_type == DataType.BOOK:
            service.entrypoint_args.kafka_out_proto = ["kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"]
        else:
            service.entrypoint_args.kafka_out = ["kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"]
    if service.entrypoint_args.kafka_out_proto is not None and len(service.entrypoint_args.kafka_out_proto) > 0:
        if data_type == DataType.BOOK:
            service.entrypoint_args.kafka_out_proto = ["kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"]
        else:
            service.entrypoint_args.kafka_out_proto = ["kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"]

    if service.entrypoint_args.kafka_out_quotes is not None and len(service.entrypoint_args.kafka_out_quotes) > 0:
        service.entrypoint_args.kafka_out_quotes = ["kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"]
    if not service.entrypoint_args.has_output_channels():
        logging.warning(f"No output channels defined for following deployment config ({data_type}): {config}.")

    # Always set the Prometheus
    service.entrypoint_args.prometheus = "$CM_FEED_HANDLER_PROMETHEUS_HOST"

    if service.entrypoint_args.websocket_out is not None:
        service.entrypoint_args.websocket_out = []

    if service.entrypoint_args.proxies is not None or service.entrypoint_args.proxy_groups is not None:
        proxies = []
        if service.entrypoint_args.proxies:
            proxies = [ProxyParams.from_string(x) for x in service.entrypoint_args.proxies]

        proxy_groups = service.entrypoint_args.proxy_groups if service.entrypoint_args.proxy_groups else [[]]
        exclude_proxies = service.entrypoint_args.exclude_proxies if service.entrypoint_args.exclude_proxies else []
        total_proxies = [x.__repr__() for x in get_proxies_total(proxies, proxy_groups, exclude_proxies)]
        if total_proxies and len(total_proxies) == 1:
            logging.warning(f"The following config adds a single proxy to a FH: {config}. Refactor the config to remove a SPOF.")

    if service.entrypoint_args.cluster_machines:
        service.entrypoint_args.cluster_machines = [
            m.replace("scrapers-production-", "") for m in service.entrypoint_args.cluster_machines
        ]
        service.entrypoint_args.machine = service.entrypoint_args.machine.replace(
            "{{ inventory_hostname }}", "${FEED_HANDLER_INSTANCE}"
        )
    command = (
        f"{_generate_entrypoint(config, data_type)} "
        f"{config.exchange_name.split()[0]} "
        f"{service.entrypoint_args.to_string(config, data_type, deployment)}"
        f"\n--environment {service.environment}".replace(
            "--streaming-api-params {{ inventory_hostname }}", "--streaming-api-params local"
        ).replace("{{ inventory_hostname }}", "${DOLLAR}(HOSTNAME)")  # TODO: Can this just be ${FEED_HANDLER_INSTANCE} instead?
    )

    if data_type == DataType.METADATA:
        command = re.sub("--database .*:swt\n", "", command, count=1, flags=0)
        command = command.replace("--postgres-out", "--database")

    if service.entrypoint_args.market_range is not None:
        market_range = service.entrypoint_args.market_range.replace(":", "-")
    else:
        market_range = "all"

    exchange_components = exchange_name.split("_")
    if len(exchange_components) == 1:
        exchange_market = config.market_type.m_name
    else:
        exchange_market = f"{'-'.join(exchange_components[1:])}-{config.market_type.m_name}"

    cpu_limit = "# no limit" if service.cpu_limit == "0" else f'cpu: "{service.cpu_limit}"'
    pod_name = config.get_pod_prefix(data_type)
    cpu_request = (
        service.cpu_request
        if service.cpu_request
        else CPU_REQUESTS_PER_POD[pod_name]
        if pod_name in CPU_REQUESTS_PER_POD
        else DEFAULT_CPU_REQUEST
    )
    template_config = {
        "scraper_name": pod_prefix,
        "exchange_name": exchange_components[0],
        "exchange_type": data_type.m_name,
        "exchange_market": exchange_market,
        "exchange_collection_mode": collection_mode.m_name,
        "exchange_connection_mode": config.connection_mode.m_name,
        "market_range": market_range,
        "cpu_limit": cpu_limit,
        "cpu_request": cpu_request,
        "memory_limit": service.mem_limit.replace("g", "Gi").replace("m", "Mi"),
        "memory_request": _generate_mem_request(deployment, service, pod_name),
        "node_affinity": _generate_affinity_rules((exchange_name, service)),
        "image_pull_secrets": _generate_image_pull_secrets(deployment),
        "args": command.split(),
    }

    j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
    return str(j2loader.get_template("k8s_feed_handler_yaml_template.j2").render(template_config))


def generate_data_loader_script(
    data_loader: DataLoader,
    deployment: DataLoaderDeployment,
    data_type: DataType,
    deployment_type: DeploymentType,
) -> str:
    exchange_name = data_loader.exchange_name.split(" ")[0]
    pod_prefix = data_loader.get_container_prefix(data_type)

    if len(pod_prefix) > MAX_POD_NAME_LENGTH:
        raise ValueError(f"Pod name too long: {pod_prefix}")

    if not re.match(r"^(?!-)[-a-z\d]+(?<!-)$", pod_prefix):
        raise ValueError(f"Invalid pod name: {pod_prefix}")

    if deployment.entrypoint_args.postgres_out_spot is not None:
        deployment.entrypoint_args.postgres_out_spot = _get_full_postgres_out(
            market_type=MarketType.SPOT, data_type=data_type, deployment_type=deployment_type, is_sharded=False
        )
    if deployment.entrypoint_args.postgres_out_futures is not None:
        deployment.entrypoint_args.postgres_out_futures = _get_full_postgres_out(
            market_type=MarketType.FUTURES, data_type=data_type, deployment_type=deployment_type, is_sharded=False
        )
    if deployment.entrypoint_args.postgres_out_option is not None:
        deployment.entrypoint_args.postgres_out_option = _get_full_postgres_out(
            market_type=MarketType.OPTION, data_type=data_type, deployment_type=deployment_type, is_sharded=False
        )

    if deployment.entrypoint_args.postgres_exch_out_spot is not None:
        deployment.entrypoint_args.postgres_exch_out_spot = _get_full_postgres_out(
            market_type=MarketType.SPOT, data_type=data_type, deployment_type=deployment_type, is_sharded=True
        )
    if deployment.entrypoint_args.postgres_exch_out_futures is not None:
        deployment.entrypoint_args.postgres_exch_out_futures = _get_full_postgres_out(
            market_type=MarketType.FUTURES, data_type=data_type, deployment_type=deployment_type, is_sharded=True
        )
    if deployment.entrypoint_args.postgres_exch_out_option is not None:
        deployment.entrypoint_args.postgres_exch_out_option = _get_full_postgres_out(
            market_type=MarketType.OPTION, data_type=data_type, deployment_type=deployment_type, is_sharded=True
        )

    if deployment.entrypoint_args.kafka_in:
        if data_type == DataType.BOOK:
            deployment.entrypoint_args.kafka_in = "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
        else:
            deployment.entrypoint_args.kafka_in = "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"

    deployment_settings = deepcopy(data_loader.deployment_settings)
    update_obj_attributes_recursively(deployment, deployment_settings, raise_error_on_missing_attributes=True)

    # Always set the Prometheus
    deployment.entrypoint_args.prometheus = "$CM_FEED_HANDLER_PROMETHEUS_HOST"

    command = (
        f"python -m src.octopus.applications.{data_type.m_name}_loader "
        f"{exchange_name} "
        f"{deployment.entrypoint_args.to_string(data_type)}".replace(
            "{{ inventory_hostname }}", "${DOLLAR}(HOSTNAME)"
        )  # TODO: Can this just be ${FEED_HANDLER_INSTANCE} instead?
    )

    template_config = {
        "scraper_name": pod_prefix,
        "replicas_count": deployment.replicas,
        "exchange_name": exchange_name,
        "data_type": data_type.m_name,
        "cpu_limit": "# no limit" if deployment.cpu_limit == "0" else f'cpu: "{deployment.cpu_limit}"',
        "cpu_request": deployment.cpu_request,
        "memory_limit": deployment.mem_limit.replace("g", "Gi").replace("m", "Mi"),
        "node_affinity": _generate_affinity_rules((exchange_name, deployment)),
        "args": command.split(),
    }

    j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
    return str(j2loader.get_template("k8s_data_loader_yaml_template.j2").render(template_config))


def generate_db_stat_collector_script(
    db_stat_collector: DbStatCollector,
    deployment: DbStatCollectorDeployment,
    data_type: DataType,
    deployment_type: DeploymentType,
) -> str:
    exchange_name = db_stat_collector.exchange_name.replace(" ", "_").replace(".", "").replace("-", "_").lower()
    pod_prefix = db_stat_collector.get_container_prefix(data_type)

    if len(pod_prefix) > MAX_POD_NAME_LENGTH:
        raise ValueError(f"Pod name too long: {pod_prefix}")

    if not re.match(r"^(?!-)[-a-z\d]+(?<!-)$", pod_prefix):
        raise ValueError(f"Invalid pod name: {pod_prefix}")

    if deployment.entrypoint_args.postgres_exch_out is not None:
        deployment.entrypoint_args.postgres_exch_out = _get_full_postgres_out(
            market_type=db_stat_collector.market_type, data_type=data_type, deployment_type=deployment_type, is_sharded=True
        )

    deployment_settings = deepcopy(db_stat_collector.deployment_settings)
    update_obj_attributes_recursively(deployment, deployment_settings, raise_error_on_missing_attributes=True)

    # Always set the Prometheus
    deployment.entrypoint_args.prometheus = "$CM_FEED_HANDLER_PROMETHEUS_HOST"

    if deployment.entrypoint_args.cluster_machines:
        deployment.entrypoint_args.cluster_machines = [
            m.replace("scrapers-production-", "") for m in deployment.entrypoint_args.cluster_machines
        ]

    command = (
        f"python -m src.octopus.applications.{data_type.m_name}_db_stat_collector "
        f"{db_stat_collector.exchange_name} "
        f"{deployment.entrypoint_args.to_string(data_type=data_type, market_type=db_stat_collector.market_type)}".replace(
            "{{ inventory_hostname }}", "${FEED_HANDLER_INSTANCE}"
        )
    )

    template_config = {
        "scraper_name": pod_prefix,
        "exchange_name": db_stat_collector.exchange_name,
        "data_type": data_type.m_name,
        "cpu_limit": "# no limit" if deployment.cpu_limit == "0" else f'cpu: "{deployment.cpu_limit}"',
        "cpu_request": deployment.cpu_request,
        "memory_limit": deployment.mem_limit.replace("g", "Gi").replace("m", "Mi"),
        "node_affinity": _generate_affinity_rules((exchange_name, deployment)),
        "args": command.split(),
    }

    j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
    return str(j2loader.get_template("k8s_db_stat_collector_yaml_template.j2").render(template_config))


def generate_shipper_script(
    deployment: DeploymentType, pod_prefix: str, market_type: MarketType, data_type: DataType, exchanges: Set[str]
) -> str:
    # Sharded shippers have a list of exchanges indicating what tables to check, non-sharded do not (only one table)
    is_sharded = len(exchanges) > 0 or data_type == DataType.TRADE_EXTRA
    exch = "_exch" if is_sharded and data_type != DataType.TRADE_EXTRA else ""
    exch_list = " ".join(sorted(exchanges)) if len(exchanges) > 0 else ""

    postgres_out = _get_postgres_out(market_type, data_type, is_sharded)
    local_postgres = _get_local_postgres_name(deployment)
    shipper_type = f"{market_type.m_name}_{data_type.m_name}{exch}"

    # Down-scale the batch size for books because the records can be extremely large
    batch_size = 500 if data_type == DataType.BOOK else 2000

    command = (
        f"python -m src.octopus.applications.shipper {shipper_type} {local_postgres} {postgres_out} {exch_list} "
        f"--prometheus $CM_FEED_HANDLER_PROMETHEUS_HOST --batch-size {batch_size} --machine ${{DOLLAR}}(HOSTNAME)"
    )

    template_config = {
        "scraper_name": pod_prefix,
        "exchange_type": data_type.m_name,
        "exchange_market": market_type.m_name,
        "cpu_limit": "# no limit",
        "cpu_request": "250m",
        "memory_limit": "3Gi",
        "args": command.split(),
        "node_affinity": _generate_affinity_rules(),
    }

    j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
    return str(j2loader.get_template("k8s_shipper_yaml_template.j2").render(template_config))


def _get_local_postgres_name(deployment: DeploymentType) -> str:
    return (
        "$(CM_DB_LOCAL_HOST):$(CM_DB_LOCAL_PORT):$(CM_DB_LOCAL_DATABASE)-${FEED_HANDLER_INSTANCE}:"
        "$(CM_DB_LOCAL_USERNAME):$(CM_DB_LOCAL_PASSWORD)"
    )


def get_deployment_template_name(script_name: str) -> str:
    if script_name in ["daily-totals.sh", "historical-patch.sh"]:
        return "k8s_with_minio_deploy_script_template.j2"
    if script_name == "fh-alerting.sh":
        return "k8s_with_slack_deploy_script_template.j2"
    if script_name == "fh-monitoring.sh":
        return "k8s_with_mimir_deploy_script_template.j2"
    return "k8s_deploy_script_template.j2"


def write_master_deploy_scripts(destination: str, master_deploy: Dict[str, List[str]]) -> None:
    for script_name, releases in master_deploy.items():
        j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
        template = j2loader.get_template(get_deployment_template_name(script_name))
        template_config = {
            "stack_name": basename(script_name).replace(".sh", ""),
            "releases": releases,
        }
        with open(join(destination, script_name), "w") as script_file:
            script_file.write(template.render(template_config))


def write_gitlab_yaml_file(deployment: DeploymentType, destination: str, deployment_list: List[str]) -> None:
    if deployment in (DeploymentType.CDEV1,):
        env_short_name = "stg"
        env_long_name = "staging"
        only = "/.*/"
    elif deployment in (DeploymentType.CP1, DeploymentType.CP2):
        env_short_name = "prd"
        env_long_name = "production"
        only = "master"
    else:
        raise ValueError(f"Unsupported deployment: {deployment}")

    j2loader = Environment(loader=FileSystemLoader(GENERATOR_TEMPLATE_DIR), trim_blocks=True, lstrip_blocks=True)
    template = j2loader.get_template("k8s_gitlab_template.j2")
    template_config = {
        "deployment": deployment.m_name,
        "env_short_name": env_short_name,
        "env_long_name": env_long_name,
        "only": only,
        "targets": [basename(script).replace(".sh", "") for script in sorted(deployment_list)],
        "single_instance_deployments": SINGLE_INSTANCE_DEPLOYMENTS,
        "single_stack_deployments": SINGLE_STACK_DEPLOYMENTS,
        "kubernetes_tag": _generate_kubernetes_tag(deployment),
        "kube_namespace": _get_kube_namespace(deployment),
    }
    with open(join(destination, ".gitlab-ci.yml"), "w") as gitlab_file:
        gitlab_file.write(template.render(template_config))


def get_master_deploy_script_name(
    data_type: DataType, collection_mode: CollectionMode, market_type: MarketType, fh_group: Optional[FHGroup] = None
) -> str:
    # Special case for TRADE_EXTRA feed handlers which get deployed with the rest of the TRADE feed handlers
    data_type = DataType.TRADE if data_type == DataType.TRADE_EXTRA else data_type
    base_script_name = f"{data_type.m_name}-{collection_mode.m_name}-{market_type.m_name}"
    if fh_group:
        return f"deploy-{base_script_name}-{fh_group}.sh".replace("_", "-")
    return f"deploy-{base_script_name}.sh".replace("_", "-")


_DB_MAPPINGS: Dict[DataType, Dict[MarketType, str]] = {
    DataType.BOOK: {MarketType.FUTURES: "FUTURES_BOOKS", MarketType.SPOT: "SPOT_BOOKS", MarketType.OPTION: "OPTIONS_BOOKS"},
    DataType.FUNDING_RATE: {MarketType.FUTURES: "FUNDING_RATE"},
    DataType.LIQUIDATION: {MarketType.FUTURES: "LIQUIDATIONS"},
    DataType.OPEN_INTEREST: {MarketType.FUTURES: "OPEN_INTEREST_FUTURES", MarketType.OPTION: "OPEN_INTEREST_OPTIONS"},
    DataType.TRADE: {MarketType.FUTURES: "FUTURES_TRADES", MarketType.OPTION: "OPTIONS_TRADES", MarketType.SPOT: "SPOT_TRADES"},
    DataType.TRADE_EXTRA: {MarketType.OPTION: "OPTIONS_TRADES"},
    DataType.METADATA: {
        MarketType.FUTURES: "FUTURES_METADATA",
        MarketType.OPTION: "OPTIONS_METADATA",
        MarketType.SPOT: "SPOT_METADATA",
    },
    DataType.TICKER_F: {MarketType.FUTURES: "FUTURES_TICKER"},
    DataType.TICKER_O: {MarketType.OPTION: "OPTIONS_TICKER"},
}


def _get_postgres_out(market_type: MarketType, data_type: DataType, is_sharded: bool) -> Optional[str]:
    # Short-circuit the quote feed handlers, which have no database support
    if data_type == DataType.QUOTE:
        return None

    db_short_name = _DB_MAPPINGS.get(data_type, {}).get(market_type)
    if db_short_name is None:
        raise ValueError(f"Unknown scraper configuration: {data_type}, {market_type}")

    if is_sharded:
        db_short_name += "_EXCH"

    if data_type == DataType.TRADE and is_sharded is False:  # Temporary belt and suspenders check
        raise NotImplementedError(f"No non-sharded trades DB defined for {data_type} and {market_type}")

    return (
        f"$(CM_DB_{db_short_name}_HOST):$(CM_DB_{db_short_name}_PORT):$(CM_DB_{db_short_name}_DATABASE)"
        f":$(CM_DB_{db_short_name}_USERNAME):$(CM_DB_{db_short_name}_PASSWORD)"
    )


def _get_full_postgres_out(
    market_type: MarketType, data_type: DataType, deployment_type: DeploymentType, is_sharded: bool
) -> Optional[str]:
    postgres_out = _get_postgres_out(market_type, data_type, is_sharded)
    if postgres_out and data_type != DataType.METADATA:
        postgres_out += f"+{_get_local_postgres_name(deployment_type)}"
    return postgres_out


_JOB_TYPES = [
    "book",
    "funding_rate-history",
    "funding_rate-realtime",
    "liquidation-history",
    "liquidation-realtime",
    "metadata-realtime",
    "open_interest-history",
    "open_interest-realtime",
    "trade-history",
    "trade-realtime",
    "quote-realtime",
    "ticker-f-realtime",
    "ticker-o-realtime",
]


_SEARCH_MAPPINGS = {
    '\\(sum\\(rate\\(node_cpu_seconds_total.*"': (
        'sum(rate(node_cpu_seconds_total{mode=~\\"user|system|iowait\\"}[1m])) by (instance) * on (instance) '
        'group_left kubelet_node_name{node_role_kubernetes_io_scraper=~\\"[0-9]+\\"}"'
    ),
    'node_memory_MemAvailable_bytes.*"': (
        "sum(node_memory_MemAvailable_bytes) by (instance) * on (instance) group_left "
        'kubelet_node_name{node_role_kubernetes_io_scraper=~\\"[0-9]+\\"}"'
    ),
    'rate\\(node_network_receive_bytes_total.*"': (
        'sum(rate(node_network_receive_bytes_total{device=~\\"cali.*\\"}[1m])) by (instance) * on (instance) group_left '
        'kubelet_node_name{node_role_kubernetes_io_scraper=~\\"[0-9]+\\"}"'
    ),
    'rate\\(node_network_transmit_bytes_total.*"': (
        'sum(rate(node_network_transmit_bytes_total{device=~\\"cali.*\\"}[1m])) by (instance) * on (instance) group_left '
        'kubelet_node_name{node_role_kubernetes_io_scraper=~\\"[0-9]+\\"}"'
    ),
    'node_filesystem_avail_bytes.*"': (
        'node_filesystem_avail_bytes{device=~\\"/dev/.*|tank/containerd\\"} * on (instance) '
        'group_left kubelet_node_name{node_role_kubernetes_io_scraper=~\\"[0-9]+\\"}"'
    ),
}


# Generate a k8s-style grafana prometheus query filter (might be useful in other places)
def _k8s_filter(
    pod: Optional[str] = None,
    exchange: Optional[str] = None,
    data: Optional[str] = None,
    market: Optional[str] = None,
    collection: Optional[str] = None,
    connection: Optional[str] = None,
    cluster: Optional[str] = None,
) -> str:
    items = []
    if pod is not None:
        match_expr = "=~" if "*" in pod else "="
        items.append(f'pod{match_expr}"{pod}"')
    if exchange is not None:
        items.append(f'feed_handler_coinmetrics_io_exchange="{exchange}"')
    if data is not None:
        items.append(f'feed_handler_coinmetrics_io_type="{data}"')
    if market is not None:
        items.append(f'feed_handler_coinmetrics_io_market="{market}"')
    if collection is not None:
        items.append(f'feed_handler_coinmetrics_io_collection_mode="{collection}"')
    if connection is not None:
        items.append(f'feed_handler_coinmetrics_io_connection_mode="{connection}"')
    if cluster is not None:
        items.append(f'cluster="{cluster}"')
    if not items:
        raise ValueError("No expression arguments defined")
    return f"{{{', '.join(items)}}}"


# dashboard.title -> panel.title -> targets[0].expr
_DASHBOARD_EXPR_REPLACEMENTS = {
    "Hourly Book": {
        "CPU": f"increase(process_cpu_seconds_total{_k8s_filter(data='book', collection='hourly')}[1m]) / 60",
        "RAM": f"process_resident_memory_bytes{_k8s_filter(data='book', collection='hourly')} / 1024 / 1024",
        "Channel free space": f"100 - channel_fill{_k8s_filter(data='book', collection='hourly')} * 100",
    },
    "Errors": {
        "Unrecognized spot markets": (
            f"contracts_with_unknown_assets{_k8s_filter(pod='feed-handler.*', market='spot', cluster='$cluster')}"
        ),
        "Futures contracts with unknown assets": (
            f"contracts_with_unknown_assets{_k8s_filter(pod='feed-handler.*', market='futures', cluster='$cluster')}"
        ),
    },
}


def k8s_dashboard_spec_transform(dashboard: Dict[str, Any]) -> None:
    if dashboard["title"] in _DASHBOARD_EXPR_REPLACEMENTS:
        replacements = _DASHBOARD_EXPR_REPLACEMENTS[dashboard["title"]]
        for row in dashboard["rows"]:
            for panel in row.panels:
                if panel.title in replacements:
                    panel.targets[0].expr = replacements[panel.title]

    for row in dashboard["rows"]:
        for panel in row.panels:
            # Patch the link titles
            for link in panel.links:
                link.title = re.sub(r"^.*-(\d) log", r"instance-\1 log", link.title)
            # Transform all the target data sources
            for target in panel.targets:
                target.datasource = {"type": "prometheus"}


def k8s_dashboard_json_transform(content: str, deployment: DeploymentType) -> str:
    # Transform known legacy strings into k8s-style naming conventions
    k8s_content = (
        content.replace("instance", "app_kubernetes_io_instance")
        .replace("[10s]) / 10", "[1m]) / 60")
        .replace("{{nodename}} {{device}} {{mountpoint}}", "{{instance}} {{device}}")
        .replace("{{nodename}} {{device}}", "{{instance}}")
        .replace('"{{nodename}}"', '"{{instance}}"')
    )

    k8s_content = (
        k8s_content.replace(
            f'app_kubernetes_io_instance=~\\"{deployment.m_short_name}-', 'app_kubernetes_io_instance=~\\"feed-handler-'
        )
        .replace(f'app_kubernetes_io_instance=~\\"{deployment.m_name}.*\\"', 'app_kubernetes_io_instance=~\\"feed-handler.*\\"')
        .replace(f'\\"{deployment.m_short_name}-([a-zA-Z0-9]*', '\\"feed-handler-([a-zA-Z0-9]*')
    )

    # Final legacy hostname cleanups
    k8s_content = (
        k8s_content.replace("-scrapers-production-", "-")
        .replace(f"%22{deployment.m_short_name}-", "%22")
        .replace("sftp_scraper", "sftp-feed-handler")
        .replace(f'"{deployment.m_short_name}.*', '"feed-handler.*')
        .replace("--", "-")
    )

    # Transform overview job descriptions into the appropriate type and method references
    for mapping_from in _JOB_TYPES:
        if "ticker" in mapping_from:
            data_type = "_".join(mapping_from.split("-")[:2])
        else:
            data_type = mapping_from.split("-")[0]
        collection = "history" if "history" in mapping_from else "realtime"
        k8s_content = k8s_content.replace(
            f'\\"market-data-{deployment.m_name}-{mapping_from}-scraper\\"',
            f'feed_handler_coinmetrics_io_type=\\"{data_type}\\",'
            f' feed_handler_coinmetrics_io_collection_mode=\\"{collection}\\"',
        )

    k8s_content = (
        k8s_content.replace(f'\\"{deployment.m_short_name}.*\\"', '\\"feed-handler-.*\\"')
        .replace("job=feed_handler", "feed_handler")
        .replace("job=~feed_handler", "feed_handler")
        .replace('=~\\"feed-handler-.*metrics-daily-.*\\"', '=~\\"metrics-daily-.*\\"')
        .replace('=~\\"feed-handler-option-ticker.*\\"', '=~\\"option-ticker-.*\\"')
        .replace("feed-handler-proxy-check", "proxy-check")
    )

    # Search / Replace mappings -- a lot of the above could be replaced with this as well...
    for key, value in _SEARCH_MAPPINGS.items():
        if (match_object := re.search(key, k8s_content)) is not None:
            legacy_string = match_object.group()
            k8s_content = k8s_content.replace(legacy_string, value)

    # Fix shipper naming convention to pod (from docker container)
    for market_t in MarketType:
        for data_t in DataType:
            container_name = f"{data_t.m_name}-realtime-shipper-{market_t.m_name}".replace("_", "-")
            pod_name = f"shipper-{market_t.m_name}-{data_t.m_name}".replace("_", "-")
            k8s_content = k8s_content.replace(container_name, pod_name)

    for query_pattern in ['"shipper-spot-trade', '"shipper-futures-trade', '"shipper-option-trade']:
        k8s_content = k8s_content.replace(f"{query_pattern}.*", f"{query_pattern}-[12].*")

    # Clean up log names
    k8s_content = re.sub(r'"app_kubernetes_io_instance-(\d) log"', r'"instance-\1 log"', k8s_content)

    for handler in ["option-ticker", "metrics-daily"]:
        for version in ["1", "2"]:
            k8s_content = k8s_content.replace(f"{handler}-{version}", f"{handler}-feed-handler-{version}")

    return k8s_content
