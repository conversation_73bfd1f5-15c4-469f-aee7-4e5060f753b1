from typing import List

from grafanalib.core import SECONDS_FORMAT  # type: ignore
from grafanalib.core import (
    HOURS_FORMAT,
    RTYPE_AVG,
    RTYPE_MAX,
    RTYPE_MIN,
    SHORT_FORMAT,
    STATE_NO_DATA,
    Dashboard,
    Evaluator,
    Graph,
    GreaterThan,
    LowerThan,
    Row,
    TimeRange,
    Tooltip,
    YAxis,
)

from src.octopus.generators.dashboards.common import (
    DEFAULT_REFRESH,
    adjust_panels_span_in_rows,
    alerting_channel,
    broken_books_expr,
    stackdriver_log_url,
)
from src.octopus.generators.dashboards.grafana_types import (
    BYTES_PER_SECOND,
    COUNTS_PER_MINUTE_FORMAT,
    COUNTS_PER_SECOND_FORMAT,
    MEGABYTES_FORMAT,
    NONE_FORMAT,
    PERCENT_FORMAT,
    TOOLTIP_SORTING_DECREASING,
    TOOLTIP_SORTING_INCREASING,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Cm<PERSON>lertCondition,
    CmDashboardLink,
    CmIndividualCmGraph,
    CmPanelLink,
    CmTarget,
    CmYAxes,
)
from src.octopus.generators.dashboards.modifiers import modify_dashboard
from src.octopus.generators.utils import get_machines
from src.octopus.inventory.types import ConnectionMode, DataType, DeploymentType, MarketType, Scraper


def individual_dashboards(scrapers: List[Scraper], data_type: DataType, deployment: DeploymentType) -> List[Dashboard]:
    return [
        individual_scraper_dashboard(scraper, deployment, data_type)
        for scraper in sorted(scrapers, key=lambda scraper: scraper.exchange_name)
    ]


def logs_and_errors_rows(instance: str, deployment: DeploymentType, data_type: DataType) -> Row:
    panels = [
        individual_log_output(instance, is_mgmt=deployment == DeploymentType.MGMT1),
        individual_total_errors(instance, deployment, data_type, is_mgmt=deployment == DeploymentType.MGMT1),
        individual_error_types(instance, is_mgmt=deployment == DeploymentType.MGMT1),
        individual_open_fds(instance, is_mgmt=deployment == DeploymentType.MGMT1),
    ]
    if "http" in instance:
        panels.append(individual_error_counters(instance, is_mgmt=deployment == DeploymentType.MGMT1))
    return Row(panels=adjust_panels_span_in_rows(panels))


def individual_scraper_dashboard(scraper: Scraper, deployment: DeploymentType, data_type: DataType) -> Dashboard:
    instance = scraper.get_instance(deployment, data_type)

    rows = [
        Row(
            panels=adjust_panels_span_in_rows([
                individual_cpu_seconds(instance, is_mgmt=deployment == DeploymentType.MGMT1),
                individual_items_received(instance, is_mgmt=deployment == DeploymentType.MGMT1),
                individual_ram(instance, is_mgmt=deployment == DeploymentType.MGMT1),
                individual_channel_free_space(instance, is_mgmt=deployment == DeploymentType.MGMT1),
            ])
        ),
        logs_and_errors_rows(instance, deployment, data_type),
        bottom_row(instance, scraper.connection_mode, deployment, data_type),
    ]

    if db_ws_stats_rows := db_and_ws_items_stats_row(scraper.exchange_name, instance, deployment, data_type):
        rows.extend(db_ws_stats_rows)

    if custom_trade_latency_rows := trade_latency_row_gen(
        exchange_name=scraper.exchange_name, instance=instance, connection_mode=scraper.connection_mode, data_type=data_type
    ):
        rows.extend(custom_trade_latency_rows)

    if stat_collector_rows := db_stat_collector_rows(
        exchange_name=scraper.exchange_name,
        instance=instance,
        deployment=deployment,
        market_type=scraper.market_type,
        data_type=data_type,
    ):
        rows.extend(stat_collector_rows)

    dashboard = Dashboard(
        title=scraper.get_dashboard_title(deployment, data_type),
        refresh=DEFAULT_REFRESH,
        rows=rows,
        links=[CmDashboardLink(tags=[f"{deployment.m_name}-scraper-overview"])],  # type: ignore
    ).auto_panel_ids()

    modify_dashboard(
        dashboard,
        {
            "WS items not delivered": {"alert": {"gracePeriod": "10m"}},
            "Kafka items sent": {"alert": {"gracePeriod": "10m"}},
        },
    )
    modify_dashboard(dashboard, scraper.dashboard_modifiers)

    if scraper.connection_mode == ConnectionMode.HISTORY:
        modify_dashboard(dashboard, {"Items received": {"alert": None}})

    alert_suffix = f"{deployment.t_name} {scraper.exchange_name} {scraper.connection_mode.t_name} {data_type.t_name}"
    if data_type == DataType.TRADE:
        alert_suffix += f" {scraper.market_type.t_name}"
    add_dashboards_alerts_suffix(dashboard, alert_suffix)
    return dashboard


def add_dashboards_alerts_suffix(dashboard: Dashboard, suffix: str) -> None:
    for row in dashboard.rows:
        for panel in row.panels:
            if panel.alert is not None:
                panel.alert.name = f"{panel.alert.name}: {suffix}"


def bottom_row(instance: str, connection_mode: ConnectionMode, deployment: DeploymentType, data_type: DataType) -> Row:
    panels = [
        individual_markets_scraped(instance, is_mgmt=deployment == DeploymentType.MGMT1),
        individual_http_request_latency(instance, is_mgmt=deployment == DeploymentType.MGMT1),
    ]

    if connection_mode == ConnectionMode.HISTORY:
        panels.append(historical_collection_lag(instance, is_mgmt=deployment == DeploymentType.MGMT1))

    if connection_mode == ConnectionMode.HTTP:
        panels.append(http_trade_lag(instance, is_mgmt=deployment == DeploymentType.MGMT1))

    if data_type not in [DataType.BOOK, DataType.OPEN_INTEREST]:
        panels.append(items_collection_lag(instance, is_mgmt=deployment == DeploymentType.MGMT1))

    if data_type == DataType.BOOK:
        panels.append(skipped_books_count(instance, is_mgmt=deployment == DeploymentType.MGMT1))

    panels.extend(proxy_stat_panels(instance, connection_mode, deployment))
    panels.append(threads_count(instance, is_mgmt=deployment == DeploymentType.MGMT1))
    return Row(panels=adjust_panels_span_in_rows(panels))


def get_data_loader_instance(exchange_name: str, data_type: DataType) -> str:
    return f"feed-handler-{exchange_name.split()[0]}-.*-?{data_type.m_short_name}-loader"


def get_kotlin_data_loader_instance(exchange_name: str, data_type: DataType) -> str:
    data_type_suffix = "trade" if data_type in [DataType.TRADE] else data_type.m_short_name
    return f"feed-handler-{exchange_name.split()[0]}-.*-?{data_type_suffix}-loader"


def get_db_stat_collector_instance(exchange_name: str, data_type: DataType, market_type: MarketType) -> str:
    _exchange_name = exchange_name.replace(".", "").lower()
    _market_type = market_type.name.lower()[:4]
    return f"feed-handler-{_exchange_name}-{data_type.m_short_name}-{_market_type}-dbstat"


def trade_latency_rows(instance: str) -> List[Row]:
    return [
        Row(
            panels=adjust_panels_span_in_rows([
                individual_trade_exchange_latency(instance),
                individual_trade_receive_latency(instance),
            ])
        )
    ]


def db_stat_collector_rows(
    instance: str, exchange_name: str, market_type: MarketType, deployment: DeploymentType, data_type: DataType
) -> List[Row]:
    _instance = get_db_stat_collector_instance(exchange_name=exchange_name, market_type=market_type, data_type=data_type)
    return [
        Row(
            panels=adjust_panels_span_in_rows([
                individual_db_stat_collector_markets_updated(instance=_instance, is_mgmt=deployment == DeploymentType.MGMT1),
            ])
        )
    ]


def data_loader_rows(instance: str, deployment: DeploymentType, data_type: DataType) -> List[Row]:
    return [
        Row(
            panels=adjust_panels_span_in_rows([
                individual_cpu_seconds(
                    instance, custom_title="Data Loader CPU seconds", is_mgmt=deployment == DeploymentType.MGMT1
                ),
                individual_data_loader_items_received(
                    instance, custom_title="Data Loader Items received", is_mgmt=deployment == DeploymentType.MGMT1
                ),
                individual_data_loader_kafka_lag(
                    instance, custom_title="Data Loader Kafka Lag", is_mgmt=deployment == DeploymentType.MGMT1
                ),
                individual_ram(instance, custom_title="Data Loader RAM", is_mgmt=deployment == DeploymentType.MGMT1),
            ])
        ),
        Row(
            panels=adjust_panels_span_in_rows([
                individual_log_output(
                    instance, custom_title="Data Loader Log debug", is_mgmt=deployment == DeploymentType.MGMT1
                ),
                individual_total_errors(
                    instance,
                    deployment,
                    data_type,
                    custom_title="Data Loader Error count",
                    is_mgmt=deployment == DeploymentType.MGMT1,
                ),
                individual_error_types(
                    instance, custom_title="Data Loader Error types", is_mgmt=deployment == DeploymentType.MGMT1
                ),
                individual_time_per_operation(
                    instance, custom_title="Data Loader Time Spent (per minute)", is_mgmt=deployment == DeploymentType.MGMT1
                ),
            ])
        ),
    ]


def kotlin_data_loader_rows(instance: str, is_mgmt: bool = False) -> List[Row]:
    return [
        Row(
            panels=adjust_panels_span_in_rows([
                individual_cpu_seconds(instance, custom_title="Data Loader CPU seconds", is_mgmt=is_mgmt),
                kotlin_individual_items_saved(instance, custom_title="Data Loader Items saved", is_mgmt=is_mgmt),
                kotlin_individual_ram(instance, custom_title="Data Loader RAM", is_mgmt=is_mgmt),
            ])
        ),
        Row(
            panels=adjust_panels_span_in_rows([
                kotlin_individual_gaps_number(instance, custom_title="Data Loader Gaps number", is_mgmt=is_mgmt),
                kotlin_individual_db_insert_time(instance, custom_title="Data Loader DB insert time", is_mgmt=is_mgmt),
            ])
        ),
    ]


def ws_stats_row(
    instance: str,
    deployment: DeploymentType,
) -> Row:
    return Row([
        individual_count_based_stats(
            "WS items not delivered",
            "websocket_consumer_items_undelivered_total",
            instance,
            GreaterThan(0),
            "{{ instance }} - {{ address }}",
            is_mgmt=deployment == DeploymentType.MGMT1,
        ),
        individual_count_based_stats(
            "Kafka items sent",
            "websocket_consumer_items_sent_total",
            instance,
            LowerThan(0.01),
            "{{ instance }} - {{ address }} - {{ kafka_topic_name }}",
            is_mgmt=deployment == DeploymentType.MGMT1,
        ),
        individual_count_based_stats(
            "WS items dropped",
            "dropping_channel_processor_dropped_item_count_total",
            instance,
            GreaterThan(0),
            "{{ instance }} - {{ channel_name }}",
            'channel_name=~".+ws queue.+"',
            is_mgmt=deployment == DeploymentType.MGMT1,
        ),
    ])


def db_stats_row(instance: str, is_mgmt=False) -> Row:
    return Row(
        panels=adjust_panels_span_in_rows([
            individual_count_based_stats(
                "DB items not saved", "database_consumer_items_lost_total", instance, GreaterThan(0), is_mgmt=is_mgmt
            ),
            individual_count_based_stats(
                "DB items stored",
                "database_consumer_items_stored_total",
                instance,
                LowerThan(0.01),
                enable_alert=True,
                is_mgmt=is_mgmt,
            ),
            individual_count_based_stats(
                "DB items dropped",
                "dropping_channel_processor_dropped_item_count_total",
                instance,
                GreaterThan(0),
                "{{ instance }} - {{ channel_name }}",
                'channel_name=~".+db queue .+"',
                is_mgmt=is_mgmt,
            ),
        ])
    )


def trade_latency_row_gen(exchange_name: str, instance: str, connection_mode: ConnectionMode, data_type: DataType) -> List[Row]:
    enabled_exchanges = ["BinanceAgg"]
    if data_type == DataType.TRADE and connection_mode == ConnectionMode.STREAMING and exchange_name in enabled_exchanges:
        return trade_latency_rows(instance=instance)
    return []


def db_and_ws_items_stats_row(exchange_name: str, instance: str, deployment: DeploymentType, data_type: DataType) -> List[Row]:
    rows = []
    enable_ws_panels = data_type not in [DataType.OPEN_INTEREST, DataType.LIQUIDATION, DataType.FUNDING_RATE]
    enable_db_panels = True

    if enable_ws_panels:
        rows.append(ws_stats_row(instance, deployment))

    if enable_db_panels:
        _exchange_name = exchange_name.lower().split()[0].replace(".", "")
        if deployment != DeploymentType.PROD and (
            data_type in [DataType.BOOK]
            or (data_type in [DataType.TRADE] and _exchange_name not in ["binanceagg", "dydx", "gfox", "deribit"])
        ):
            # Kotlin data loaders
            loader_pod_prefix = get_kotlin_data_loader_instance(_exchange_name, data_type)

            rows.append(db_stats_row(instance, is_mgmt=deployment == DeploymentType.MGMT1))
            rows.extend(kotlin_data_loader_rows(loader_pod_prefix, is_mgmt=deployment == DeploymentType.MGMT1))
        elif data_type in [DataType.TRADE, DataType.FUNDING_RATE, DataType.OPEN_INTEREST, DataType.LIQUIDATION]:
            # Python data loaders
            loader_pod_prefix = get_data_loader_instance(_exchange_name, data_type)

            rows.append(db_stats_row(instance, is_mgmt=deployment == DeploymentType.MGMT1))
            rows.extend(data_loader_rows(loader_pod_prefix, deployment, data_type))

    return rows


def proxy_stat_panels(instance: str, connection_mode: ConnectionMode, deployment: DeploymentType) -> List[Graph]:
    panels = [
        individual_connections_awaiting_proxy(instance, connection_mode, deployment, is_mgmt=deployment == DeploymentType.MGMT1),
        individual_available_proxy(instance, connection_mode, deployment, is_mgmt=deployment == DeploymentType.MGMT1),
    ]

    if connection_mode == ConnectionMode.HTTP:
        panels.append(individual_desired_proxies(instance, deployment, is_mgmt=deployment == DeploymentType.MGMT1))
        panels.append(individual_http_poll_interval_budget(instance, deployment, is_mgmt=deployment == DeploymentType.MGMT1))

    if connection_mode == ConnectionMode.STREAMING:
        panels.append(
            individual_connections_awaiting_proxy(
                instance, ConnectionMode.HTTP, deployment, is_mgmt=deployment == DeploymentType.MGMT1
            )
        )
        panels.append(
            individual_available_proxy(instance, ConnectionMode.HTTP, deployment, is_mgmt=deployment == DeploymentType.MGMT1)
        )

    return panels


def http_trade_lag(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Trade Lag",
        yAxes=CmYAxes(left=YAxis(format=SECONDS_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (http_producer_trade_lag_seconds{{instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(f'sum by (instance) (http_producer_trade_lag_seconds{{cluster="$cluster", instance=~"{instance}.*"}})'),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_total_errors(
    instance: str, deployment: DeploymentType, data_type: DataType, custom_title: str = None, is_mgmt=False
) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Error count",
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_MINUTE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(application_errors_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="{{ instance }}",
            )
        ],
        links=[
            CmPanelLink(uri=stackdriver_log_url(instance, machine), title=f"{machine} log")  # type: ignore
            for machine in get_machines(deployment, data_type, instance)
        ],
        alert=CmAlert(
            name="Total errors",
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=GreaterThan(0), reducerType=RTYPE_MIN, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(application_errors_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_error_types(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Error types",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_MINUTE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance, error_type) (increase(application_error_types_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="{{ error_type }} - {{ instance }}",
            )
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance, error_type) (increase(application_error_types_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="{{ error_type }} - {{ instance }}",
            )
        ]
    return graph


def individual_time_per_operation(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Time Spent (per minute)",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        yAxes=CmYAxes(left=YAxis(format=SECONDS_FORMAT)),
        targets=[
            CmTarget(
                expr=f'rate(fetching_time_spent_total{{instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Fetching time - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'rate(processing_time_spent_total{{instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Processing time - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'rate(db_saving_time_spent_total{{instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="DB insert time - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f'rate(slicing_time_spent_total{{instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Book slicing time - {{ instance }}",
                refId="D",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'rate(fetching_time_spent_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Fetching time - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'rate(processing_time_spent_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Processing time - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'rate(db_saving_time_spent_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="DB insert time - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f'rate(slicing_time_spent_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]) * 60',
                legendFormat="Book slicing time - {{ instance }}",
                refId="D",
            ),
        ]
    return graph


def individual_ram(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "RAM",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (process_resident_memory_bytes{{instance=~"{instance}.*"}} / 1024 / 1024)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=MEGABYTES_FORMAT)),
        alert=CmAlert(
            name="RAM",
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=GreaterThan(512), reducerType=RTYPE_AVG, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (process_resident_memory_bytes{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}} / 1024 / 1024)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
        graph.dataSource = "$datasource"
    return graph


def individual_data_loader_items_received(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Items received",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(data_loader_items_received_total{{instance=~"{instance}.*"}}[1m]) / 60)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_SECOND_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(data_loader_items_received_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]) / 60)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_data_loader_kafka_lag(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Kafka Lag",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (data_loader_kafka_lag{{instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=SECONDS_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'sum by (instance) (data_loader_kafka_lag{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_items_received(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "Items received",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(scraper_items_received_total{{instance=~"{instance}.*"}}[1m]) / 60)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_SECOND_FORMAT)),
        alert=CmAlert(
            name="Items received",
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=LowerThan(0.01), reducerType=RTYPE_MAX, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(scraper_items_received_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]) / 60)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_channel_free_space(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    """
    alert=CmAlert(
            name='Channel free space',
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId='A',
                    evaluator=LowerThan(25),
                    reducerType=RTYPE_AVG,
                    timeRange=TimeRange('2m', 'now'))
            ],
            notifications=alerting_channel(instance))
    """

    graph = CmIndividualCmGraph(
        title=custom_title or "Channel free space",
        targets=[
            CmTarget(
                expr=f'sum by (instance, channel_name) (100 - channel_fill{{instance=~"{instance}.*"}} * 100)',
                legendFormat="{{ channel_name }}",
                refId="A",
            ),
        ],
        tooltip=Tooltip(sort=TOOLTIP_SORTING_INCREASING),
        yAxes=CmYAxes(left=YAxis(format=PERCENT_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance, channel_name) (100 - channel_fill{{cluster="$cluster", instance=~"{instance}.*"}} * 100)'
                ),
                legendFormat="{{ channel_name }}",
                refId="A",
            ),
        ]
    return graph


def individual_log_output(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    """
    alert=CmAlert(
            name='Log output',
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId='A',
                    evaluator=GreaterThan(16384),
                    reducerType=RTYPE_AVG,
                    timeRange=TimeRange('2m', 'now'))
            ],
            notifications=alerting_channel(instance))
    """

    graph = CmIndividualCmGraph(
        title=custom_title or "Log output",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(log_output_bytes_total{{instance=~"{instance}.*"}}[1m]) / 60)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=BYTES_PER_SECOND)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(log_output_bytes_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]) / 60)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_cpu_seconds(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title=custom_title or "CPU seconds",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(process_cpu_seconds_total{{instance=~"{instance}.*"}}[10s]) / 10)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_SECOND_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(process_cpu_seconds_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[10s]) / 10)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_open_fds(instance: str, is_mgmt=False) -> Graph:
    """
    alert=CmAlert(
            name='Open file descriptors',
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId='A',
                    evaluator=GreaterThan(384),
                    reducerType=RTYPE_AVG,
                    timeRange=TimeRange('2m', 'now'))
            ],
            notifications=alerting_channel(instance))
    """

    graph = CmIndividualCmGraph(
        title="Open file descriptors",
        targets=[
            CmTarget(expr=f'sum by (instance) (process_open_fds{{instance=~"{instance}.*"}})', legendFormat="{{ instance }}")
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'sum by (instance) (process_open_fds{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_error_counters(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Duplicated Data Counters",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(same_exchange_sequence_id_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="Same exchange sequence id - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'sum by (instance) (increase(same_exchange_time_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="Same exchange time - {{ instance }}",
                refId="B",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(same_exchange_sequence_id_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="Same exchange sequence id - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(same_exchange_time_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="Same exchange time - {{ instance }}",
                refId="B",
            ),
        ]
    return graph


def individual_broken_books(instance: str) -> Graph:
    return CmIndividualCmGraph(
        title="Broken books",
        targets=[CmTarget(expr=broken_books_expr(f'instance=~"{instance}.*"'), legendFormat="{{ instance }}")],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_MINUTE_FORMAT)),
    )


def individual_count_based_stats(
    title: str,
    stat: str,
    instance: str,
    alert_evaluator: Evaluator,
    legend: str = "{{ instance }}",
    additional_filter: str = "",
    enable_alert: bool = False,
    is_mgmt: bool = False,
) -> Graph:
    additional_filter = f",{additional_filter}" if additional_filter else ""

    if enable_alert:
        alert_args = {
            "alert": CmAlert(
                name=title,
                noDataState=STATE_NO_DATA,
                alertConditions=[
                    CmAlertCondition(  # type: ignore
                        targetRefId="A", evaluator=alert_evaluator, reducerType=RTYPE_MAX, timeRange=TimeRange("2m", "now")
                    )
                ],
                notifications=alerting_channel(instance),
            )
        }
    else:
        alert_args = {}

    graph = CmIndividualCmGraph(
        title=title,
        targets=[
            CmTarget(expr=f'increase({stat}{{instance=~"{instance}.*"{additional_filter}}}[1m]) / 60', legendFormat=legend)
        ],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_SECOND_FORMAT, min=None, logBase=10)),
        **alert_args,
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'increase({stat}{{cluster="$cluster", instance=~"{instance}.*"{additional_filter}}}[1m]) / 60',
                legendFormat=legend,
            )
        ]
    return graph


def individual_proxy_health(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Proxy health",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (healthy_proxy_share_percent{{instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=PERCENT_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'sum by (instance) (healthy_proxy_share_percent{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_connections_awaiting_proxy(
    instance: str, connection_mode: ConnectionMode, deployment: DeploymentType, is_mgmt=False
) -> Graph:
    if connection_mode == ConnectionMode.STREAMING:
        pool_name = "streaming_connection_proxy_pool"
        # alert_name = 'Streaming connections proxy starvation'
    else:
        pool_name = "http_client_proxy_pool"
        # alert_name = 'HTTP connections proxy starvation'

    """
    if deployment == DeploymentType.PROD:
        alert_args = {
            'alert': CmAlert(
                name=alert_name,
                alertConditions=[
                    CmAlertCondition(  # type: ignore
                        targetRefId='A',
                        evaluator=GreaterThan(0),
                        reducerType=RTYPE_MIN,
                        timeRange=TimeRange("2m", "now"))
                ],
                notifications=alerting_channel(instance))}
    else:
        alert_args = {}
    """

    connection_type = f"{connection_mode.t_name}" if connection_mode != ConnectionMode.HISTORY else "HTTP"

    graph = CmIndividualCmGraph(
        title=f"{connection_type} connections awaiting proxy",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (rlrp_waiter_count{{pool_name="{pool_name}",instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=SHORT_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (rlrp_waiter_count{{cluster="$cluster", pool_name="{pool_name}", '
                    f'instance=~"{instance}.*"}})'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_available_proxy(
    instance: str, connection_mode: ConnectionMode, deployment: DeploymentType, is_mgmt=False
) -> Graph:
    if connection_mode == ConnectionMode.STREAMING:
        pool_name = "streaming_connection_proxy_pool"
        # alert_name = 'Streaming connections proxy starvation'
    else:
        pool_name = "http_client_proxy_pool"
        # alert_name = 'HTTP connections proxy starvation'

    connection_type = f"{connection_mode.t_name}" if connection_mode != ConnectionMode.HISTORY else "HTTP"

    graph = CmIndividualCmGraph(
        title=f"{connection_type} available proxies",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (rlrp_available_resource_count{{pool_name="{pool_name}",instance=~"{instance}.*"}})',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=SHORT_FORMAT)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (rlrp_available_resource_count{{cluster="$cluster", pool_name="{pool_name}", '
                    f'instance=~"{instance}.*"}})'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def individual_http_poll_interval_budget(instance: str, deployment: DeploymentType, is_mgmt=False) -> Graph:
    """
    alert=CmAlert(
        name='HTTP poll interval budget violation',
        alertConditions=[
            CmAlertCondition(  # type: ignore
                targetRefId='A',
                evaluator=LowerThan(0),
                reducerType=RTYPE_AVG,
                timeRange=TimeRange('2m', 'now'))
        ],
        notifications=alerting_channel(instance))}
    """

    graph = CmIndividualCmGraph(
        title="HTTP poll interval budget",
        targets=[
            CmTarget(
                expr=(
                    f'sum by (instance) (clamp_min(http_producer_poll_stats_budget{{instance=~"{instance}.*"}} '
                    f'- http_producer_poll_stats_median{{instance=~"{instance}.*"}}, 0))'
                ),
                legendFormat="{{ instance }} - reserve",
            ),
            CmTarget(
                refId="B",
                expr=f'sum by (instance) (http_producer_poll_stats_median{{instance=~"{instance}.*"}})',
                legendFormat="{{ instance }} - latency",
            ),
        ],
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT, min=None)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (clamp_min(http_producer_poll_stats_budget{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}} - http_producer_poll_stats_median{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}, 0))'
                ),
                legendFormat="{{ instance }} - reserve",
            ),
            CmTarget(
                refId="B",
                expr=f'sum by (instance) (http_producer_poll_stats_median{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="{{ instance }} - latency",
            ),
        ]
    return graph


def individual_desired_proxies(instance: str, deployment: DeploymentType, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Proxies Count",
        targets=[
            CmTarget(
                expr=f'desired_proxies_count{{instance=~"{instance}.*"}}',
                legendFormat="Desired for {{ instance }} (roughly)",
                refId="A",
            ),
            CmTarget(
                expr=f'available_proxies_count{{instance=~"{instance}.*"}}',
                legendFormat="Available for {{ instance }}",
                refId="B",
            ),
        ],
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT, min=None)),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'desired_proxies_count{{cluster="$cluster", instance=~"{instance}.*"}}',
                legendFormat="Desired for {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'available_proxies_count{{cluster="$cluster", instance=~"{instance}.*"}}',
                legendFormat="Available for {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=(
                    f'desired_proxies_count{{cluster="$cluster", instance=~"{instance}.*"}} - '
                    f'available_proxies_count{{cluster="$cluster", instance=~"{instance}.*"}}'
                ),
                legendFormat="Diff for {{ instance }}",
                refId="C",
            ),
        ]
    return graph


def individual_markets_scraped(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Markets scraped",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (scraped_market_count{{instance=~"{instance}.*"}})',
                legendFormat="Scraped {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=(
                    f'sum by (instance) (recognized_market_count{{instance=~"{instance}.*"}}) - '
                    f'sum by (instance) (scraped_market_count{{instance=~"{instance}.*"}})'
                ),
                legendFormat="Not scraped {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'sum by (instance) (unrecognized_market_count{{instance=~"{instance}.*"}})',
                legendFormat="Unrecognized {{ instance }}",
                refId="C",
            ),
        ],
        alert=CmAlert(
            name="Markets scraped",
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="B", evaluator=GreaterThan(0), reducerType=RTYPE_AVG, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'sum by (instance) (scraped_market_count{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="Scraped {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=(
                    f'sum by (instance) (recognized_market_count{{cluster="$cluster", instance=~"{instance}.*"}}) - '
                    f'sum by (instance) (scraped_market_count{{cluster="$cluster", instance=~"{instance}.*"}})'
                ),
                legendFormat="Not scraped {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'sum by (instance) (unrecognized_market_count{{cluster="$cluster", instance=~"{instance}.*"}})',
                legendFormat="Unrecognized {{ instance }}",
                refId="C",
            ),
        ]
    return graph


def individual_http_request_latency(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="HTTP requests latency",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (avg_over_time(http_requests_latency{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="HTTP requests latency {{ instance }}",
                refId="A",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (avg_over_time(http_requests_latency{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="HTTP requests latency {{ instance }}",
                refId="A",
            ),
        ]
    return graph


def historical_collection_lag(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Maximum collection lag",
        yAxes=CmYAxes(left=YAxis(format=HOURS_FORMAT)),
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'max by (instance) (history_collection_lag{{instance=~"{instance}.*"}} / 3600)',
                legendFormat="{{ instance }}",
                refId="A",
            )
        ],
        alert=CmAlert(
            name="Historical collection lag",
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=GreaterThan(48), reducerType=RTYPE_MIN, timeRange=TimeRange("1h", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'max by (instance) (history_collection_lag{{cluster="$cluster", instance=~"{instance}.*"}} / 3600)',
                legendFormat="{{ instance }}",
                refId="A",
            )
        ]
    return graph


def items_collection_lag(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Items collection lag",
        yAxes=CmYAxes(left=YAxis(format=SECONDS_FORMAT)),
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'quantile(1, items_collection_lag{{instance=~"{instance}.*"}}) by (instance)',
                legendFormat="100% percentile lag - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'quantile(0.99, items_collection_lag{{instance=~"{instance}.*"}}) by (instance)',
                legendFormat="99% percentile lag - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'quantile(0.95, items_collection_lag{{instance=~"{instance}.*"}}) by (instance)',
                legendFormat="95% percentile lag - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f'quantile(0.5, items_collection_lag{{instance=~"{instance}.*"}}) by (instance)',
                legendFormat="50% percentile lag - {{ instance }}",
                refId="D",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'quantile(1, items_collection_lag{{cluster="$cluster", instance=~"{instance}.*"}}) by (instance)',
                legendFormat="100% percentile lag - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f'quantile(0.99, items_collection_lag{{cluster="$cluster", instance=~"{instance}.*"}}) by (instance)',
                legendFormat="99% percentile lag - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f'quantile(0.95, items_collection_lag{{cluster="$cluster", instance=~"{instance}.*"}}) by (instance)',
                legendFormat="95% percentile lag - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f'quantile(0.5, items_collection_lag{{cluster="$cluster", instance=~"{instance}.*"}}) by (instance)',
                legendFormat="50% percentile lag - {{ instance }}",
                refId="D",
            ),
        ]
    return graph


def individual_db_stat_collector_markets_updated(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Db stats markets updated",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(db_collection_stat_updates_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="Market stats updated {{ instance }}",
                refId="A",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(db_collection_stat_updates_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="Market stats updated {{ instance }}",
                refId="A",
            ),
        ]
    return graph


def individual_trade_exchange_latency(instance: str) -> Graph:
    return CmIndividualCmGraph(
        title="Trade exchange latency seconds",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (avg_over_time(trade_exchange_latency{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="Exchange latency {{ instance }}",
                refId="A",
            ),
        ],
    )


def individual_trade_receive_latency(instance: str) -> Graph:
    return CmIndividualCmGraph(
        title="Trade Received latency seconds",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (avg_over_time(trade_receive_latency{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="Receive latency {{ instance }}",
                refId="A",
            ),
        ],
    )


def skipped_books_count(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Skipped books count",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase(skipped_books_count_total{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="{{ instance }}",
            )
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase(skipped_books_count_total{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]))'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def threads_count(instance: str, is_mgmt=False) -> Graph:
    graph = CmIndividualCmGraph(
        title="Threads count",
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'threads{{instance=~"{instance}.*"}}',
                legendFormat="{{ instance }} - {{ type }}",
            )
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(f'threads{{cluster="$cluster", instance=~"{instance}.*"}}'),
                legendFormat="{{ instance }} - {{ type }}",
            )
        ]
    return graph


def kotlin_individual_items_saved(instance: str, custom_title: str = None, is_mgmt=False) -> Graph:
    items_saves_metric = "book_data_loader_items_saved_total" if "book-loader" in instance else "data_loader_items_saved_total"
    graph = CmIndividualCmGraph(
        title=custom_title or "Items saved",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        targets=[
            CmTarget(
                expr=f'sum by (instance) (increase({items_saves_metric}{{instance=~"{instance}.*"}}[1m]) / 60)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=COUNTS_PER_SECOND_FORMAT)),
        alert=CmAlert(
            name="Items received",
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=LowerThan(0.01), reducerType=RTYPE_MAX, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (increase({items_saves_metric}{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}}[1m]) / 60)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def kotlin_individual_ram(instance, custom_title, is_mgmt=False):
    graph = CmIndividualCmGraph(
        title=custom_title or "RAM",
        targets=[
            CmTarget(
                expr=f'sum by (instance) (process_resident_memory_bytes{{instance=~"{instance}.*"}} / 1024 / 1024)',
                legendFormat="{{ instance }}",
            )
        ],
        yAxes=CmYAxes(left=YAxis(format=MEGABYTES_FORMAT)),
        alert=CmAlert(
            name="RAM",
            noDataState=STATE_NO_DATA,
            alertConditions=[
                CmAlertCondition(  # type: ignore
                    targetRefId="A", evaluator=GreaterThan(512), reducerType=RTYPE_AVG, timeRange=TimeRange("2m", "now")
                )
            ],
            notifications=alerting_channel(instance),
        ),
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=(
                    f'sum by (instance) (process_resident_memory_bytes{{cluster="$cluster", '
                    f'instance=~"{instance}.*"}} / 1024 / 1024)'
                ),
                legendFormat="{{ instance }}",
            )
        ]
    return graph


def kotlin_individual_gaps_number(instance, custom_title, is_mgmt=False):
    graph = CmIndividualCmGraph(
        title=custom_title or "Gaps Number",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        yAxes=CmYAxes(left=YAxis(format=NONE_FORMAT)),
        targets=[
            CmTarget(
                expr=f'increase(book_data_loader_gaps_number_total{{instance=~"{instance}.*"}}[1m])',
                legendFormat="gaps - {{ app_kubernetes_io_instance }} - {{market}}",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f'increase(book_data_loader_gaps_number_total{{cluster="$cluster", instance=~"{instance}.*"}}[1m])',
                legendFormat="gaps - {{ app_kubernetes_io_instance }} - {{market}}",
            ),
        ]
    return graph


def kotlin_individual_db_insert_time(instance, custom_title, is_mgmt=False):
    graph = CmIndividualCmGraph(
        title=custom_title or "DB insert Time",
        tooltip=Tooltip(sort=TOOLTIP_SORTING_DECREASING),
        yAxes=CmYAxes(left=YAxis(format=SECONDS_FORMAT)),
        targets=[
            CmTarget(
                expr=f"histogram_quantile(1, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="100% percentile lag - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.99, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="99% percentile lag - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.95, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="95% percentile lag - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.5, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="50% percentile lag - {{ instance }}",
                refId="D",
            ),
        ],
    )
    if is_mgmt:
        graph.dataSource = "$datasource"
        graph.targets = [
            CmTarget(
                expr=f"histogram_quantile(1, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{cluster="$cluster", '
                f'instance=~"{instance}.*"}}[1m]))',
                legendFormat="100% percentile lag - {{ instance }}",
                refId="A",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.99, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{cluster="$cluster", '
                f'instance=~"{instance}.*"}}[1m]))',
                legendFormat="99% percentile lag - {{ instance }}",
                refId="B",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.95, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{cluster="$cluster", '
                f'instance=~"{instance}.*"}}[1m]))',
                legendFormat="95% percentile lag - {{ instance }}",
                refId="C",
            ),
            CmTarget(
                expr=f"histogram_quantile(0.5, rate("
                f'book_data_loader_db_insert_time_seconds_bucket{{instance=~"{instance}.*"}}[1m]))',
                legendFormat="50% percentile lag - {{ instance }}",
                refId="D",
            ),
        ]
    return graph
