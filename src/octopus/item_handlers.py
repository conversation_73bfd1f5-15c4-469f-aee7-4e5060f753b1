from collections import defaultdict
from typing import Dict, List, Optional

from src.kafka.api import KafkaMessage
from src.octopus.data import Book, BookData, Instrument, Market
from src.octopus.streaming_protocols.kafka_message import json_message_from_quote
from src.utils.diagnostics import Diagnostics
from src.utils.pyroutine import M_T, IChannelOut


class DiagnosticCounter:
    def __init__(self, diagnostics: Diagnostics, channels: List[IChannelOut[List[M_T]]]):
        self.diagnostics = diagnostics
        self.drop = diagnostics.counter("dropping_channel_processor_dropped_item_count", ("channel_name",))
        self.drop_counters = [self.drop.tags({"channel_name": c.stats().name}) for c in channels]


class QuoteItemHandler:
    def __init__(self):
        self.market_last_book: Dict[Market, Book] = {}
        self.feed_handler_sequence_id: Dict[Instrument, int] = defaultdict(int)

    def transform_to_json(self, items: List[Book], diagnostics: Diagnostics) -> List[str]:
        if len(items) != 1:
            raise ValueError(f"Only one book is expected, got {len(items)} instead")
        book = self.handle_new_book(items[0])
        if book:
            return [json_message_from_quote(book)]
        return []

    def handle_new_book(self, book: Book) -> Optional[Book]:
        last_book = self.market_last_book.get(book.market)
        if (last_book and self._are_quotes_equal(last_book.data, book.data)) or (
            last_book is None and book.data.asks == [] and book.data.bids == []
        ):
            return None
        self.feed_handler_sequence_id[book.market.instrument] += 1
        # Quotes have its own 'scraper_sequence_id' that increases only when new quote different from the previous one
        # has come. 'Book' is a frozen dataclass, so to override 'scraper_sequence_id' we have to recreate the whole
        # instance
        book = Book(
            market=book.market,
            depth_limit=book.depth_limit,
            collect_time=book.collect_time,
            deduplication_time=book.deduplication_time,
            scraper_session_id=book.scraper_session_id,
            scraper_sequence_id=self.feed_handler_sequence_id[book.market.instrument],
            data=book.data,
        )
        self.market_last_book[book.market] = book
        return book

    @staticmethod
    def _are_quotes_equal(book1: BookData, book2: BookData) -> bool:
        bids_1 = book1.bids[0] if book1.bids != [] else None
        asks_1 = book1.asks[0] if book1.asks != [] else None
        bids_2 = book2.bids[0] if book2.bids != [] else None
        asks_2 = book2.asks[0] if book2.asks != [] else None

        return bids_1 == bids_2 and asks_1 == asks_2


class QuoteFromBookItemHandler(QuoteItemHandler):
    def __init__(self, channels: List[IChannelOut[List[KafkaMessage]]], diagnostics: Diagnostics) -> None:
        super().__init__()
        self.channels = channels
        self.diagnostic_counter = DiagnosticCounter(diagnostics, channels)

    def send_quote_to_channels(self, book: Book, diagnostics: Diagnostics) -> None:
        for idx, channel in enumerate(self.channels):
            if channel.try_send(self.transform_to_json([book], diagnostics)):  # type: ignore
                continue
            self.diagnostic_counter.drop_counters[idx].inc(1)
