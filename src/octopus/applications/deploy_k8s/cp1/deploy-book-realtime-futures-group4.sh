#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-book-realtime-futures-group4"
export RELEASE_LIST="feed-handler-binance-pepeusdt-book-futu-stre,feed-handler-binance-shibusdt-book-futu-stre,feed-handler-binance-flokiusdt-book-futu-stre,feed-handler-binance-wifusdt-book-futu-stre,feed-handler-binance-dogeusdt-book-futu-stre,feed-handler-binance-bonkusdt-book-futu-stre,feed-handler-binance-ethusdt-book-futu-stre,feed-handler-binance-bchusdt-book-futu-stre,feed-handler-binance-btcusdt-book-futu-stre,feed-handler-binance-glmusdt-book-futu-stre,feed-handler-binance-wldusdt-book-futu-stre,feed-handler-binance-memeusdt-book-futu-stre,feed-handler-binance-filusdt-book-futu-stre,feed-handler-binance-solusdt-book-futu-stre,feed-handler-binance-k-book-futu-stre-000-024,feed-handler-binance-k-book-futu-stre-024-048,feed-handler-binance-k-book-futu-stre-048-072,feed-handler-binance-k-book-futu-stre-072-096,feed-handler-binance-k-book-futu-stre-096-120,feed-handler-binance-k-book-futu-stre-120-144,feed-handler-binance-k-book-futu-stre-144-168,feed-handler-binance-k-book-futu-stre-168-192,feed-handler-binance-k-book-futu-stre-192-216,feed-handler-binance-k-book-futu-stre-216-240,feed-handler-binance-k-book-futu-stre-240-264,feed-handler-binance-k-book-futu-stre-264-288,feed-handler-binance-k-book-futu-stre-288-312,feed-handler-binance-k-book-futu-stre-312-336,feed-handler-binance-k-book-futu-stre-336-360,feed-handler-binance-k-book-futu-stre-360-384,feed-handler-binance-k-book-futu-stre-384-408,feed-handler-binance-k-book-futu-stre-408-432,feed-handler-binance-k-book-futu-stre-432-456,feed-handler-binance-k-book-futu-stre-456-480,feed-handler-binance-k-book-futu-stre-480-9000,feed-handler-cme-book-futu-stre,feed-handler-cryptocom-book-futu-stre-000-050,feed-handler-cryptocom-book-futu-stre-050-100,feed-handler-cryptocom-book-futu-stre-100-150,feed-handler-cryptocom-book-futu-stre-150-200,feed-handler-cryptocom-book-futu-stre-200-9000,feed-handler-deribit-all-book-futu-stre,feed-handler-huobi-busy-book-futu-stre,feed-handler-huobi-k-book-futu-stre-000-012,feed-handler-huobi-k-book-futu-stre-012-024,feed-handler-huobi-k-book-futu-stre-024-036,feed-handler-huobi-k-book-futu-stre-036-048,feed-handler-huobi-k-book-futu-stre-048-060,feed-handler-huobi-k-book-futu-stre-060-072,feed-handler-huobi-k-book-futu-stre-072-084,feed-handler-huobi-k-book-futu-stre-084-096,feed-handler-huobi-k-book-futu-stre-096-108,feed-handler-huobi-k-book-futu-stre-108-120,feed-handler-huobi-k-book-futu-stre-120-132,feed-handler-huobi-k-book-futu-stre-132-144,feed-handler-huobi-k-book-futu-stre-144-156,feed-handler-huobi-k-book-futu-stre-156-168,feed-handler-huobi-k-book-futu-stre-168-180,feed-handler-huobi-k-book-futu-stre-180-192,feed-handler-huobi-k-book-futu-stre-192-204,feed-handler-huobi-k-book-futu-stre-204-216,feed-handler-huobi-k-book-futu-stre-216-228,feed-handler-huobi-k-book-futu-stre-228-240,feed-handler-huobi-k-book-futu-stre-240-252,feed-handler-huobi-k-book-futu-stre-252-9000,feed-handler-kraken-batch-book-futu-stre,feed-handler-kraken-book-futu-stre-000-080,feed-handler-kraken-book-futu-stre-080-160,feed-handler-kraken-book-futu-stre-160-240,feed-handler-kraken-book-futu-stre-240-320,feed-handler-kraken-book-futu-stre-320-9000"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS