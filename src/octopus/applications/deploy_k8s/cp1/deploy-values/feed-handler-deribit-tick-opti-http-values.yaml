#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3072Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: deribit
  feed-handler.coinmetrics.io/type: ticker_o
  feed-handler.coinmetrics.io/market: option
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: http
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.http_option_ticker_scraper"
  - "Deribit"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--option"
  - "--poll-interval"
  - "30"
  - "--postgres-out"
  - "$(CM_DB_OPTIONS_TICKER_HOST):$(CM_DB_OPTIONS_TICKER_PORT):$(CM_DB_OPTIONS_TICKER_DATABASE):$(CM_DB_OPTIONS_TICKER_USERNAME):$(CM_DB_OPTIONS_TICKER_PASSWORD)+$(CM_DB_LOCAL_HOST):$(CM_DB_LOCAL_PORT):$(CM_DB_LOCAL_DATABASE)-${FEED_HANDLER_INSTANCE}:$(CM_DB_LOCAL_USERNAME):$(CM_DB_LOCAL_PASSWORD)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "deribit-cp1-hetzner-eu-ticker-o-realtime,deribit-cp1-smartproxy-ticker-o-realtime"
  - "--rate-limit-multiplier"
  - "0.3"
  - "--environment"
  - "cp1"
