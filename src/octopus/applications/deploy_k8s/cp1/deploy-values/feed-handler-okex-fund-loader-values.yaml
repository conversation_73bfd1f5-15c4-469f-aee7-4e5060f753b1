#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler
replicaCount: 1

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: OKEx
  feed-handler.coinmetrics.io/type: funding_rate
  feed-handler.coinmetrics.io/replica: funding_rate

args:
  - "python"
  - "-m"
  - "src.octopus.applications.funding_rate_loader"
  - "OKEx"
  - "--kafka-in"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--postgres-out-futures"
  - "$(CM_DB_FUNDING_RATE_HOST):$(CM_DB_FUNDING_RATE_PORT):$(CM_DB_FUNDING_RATE_DATABASE):$(CM_DB_FUNDING_RATE_USERNAME):$(CM_DB_FUNDING_RATE_PASSWORD)+$(CM_DB_LOCAL_HOST):$(CM_DB_LOCAL_PORT):$(CM_DB_LOCAL_DATABASE)-${FEED_HANDLER_INSTANCE}:$(CM_DB_LOCAL_USERNAME):$(CM_DB_LOCAL_PASSWORD)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
