#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "308m"
     memory: "138Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: coinbase
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: spot
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 0130-0140

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "Coinbase"
  - "--exclude-instruments"
  - "btc-usd"
  - "eth-usd"
  - "usdt-usd"
  - "sol-usd"
  - "doge-usd"
  - "shib-usd"
  - "amp-usd"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_1.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "0130:0140"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-us-book-realtime[14/42]"
  - "--rate-limit-multiplier"
  - "0.5"
  - "--spot"
  - "--book-depth"
  - "30000"
  - "--environment"
  - "cp1"
