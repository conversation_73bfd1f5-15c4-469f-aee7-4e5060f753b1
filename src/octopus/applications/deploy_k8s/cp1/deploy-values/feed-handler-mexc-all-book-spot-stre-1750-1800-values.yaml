#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3072Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: mexc
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: all-spot
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 1750-1800

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "MEXC"
  - "--exclude-instruments"
  - "eth-usdc"
  - "btc-usdc"
  - "eth-btc"
  - "sui-eur"
  - "btc-usdt"
  - "sol-usde"
  - "doge-eur"
  - "xrp-usdc"
  - "pepe-eur"
  - "sui-usdt"
  - "eth-usdt"
  - "pepe-usdc"
  - "nxpc-usdt"
  - "sol-usdt"
  - "trump-usdc"
  - "trump-eur"
  - "xrp-usdt"
  - "doge-usdt"
  - "trump-usdt"
  - "usdc-usdt"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_49.proto:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "1750:1800"
  - "--markets-per-producer"
  - "1"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-us-book-realtime[36/52],cp1-hetzner-eu-book-realtime[36/52],smartproxy-us-cp1-book-realtime[36/52]"
  - "--rate-limit-multiplier"
  - "0.2"
  - "--spot"
  - "--book-depth"
  - "30000"
  - "--environment"
  - "cp1"
