---
nameOverride: fh-monitoring

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
  GITLAB_USER_ID: "$GITLAB_USER_ID"
  GRAFANA_API_KEY: "$GRAFANA_API_KEY"
  GRAFANA_API_KEY_K8S_CDEV1: "$GRAFANA_API_KEY_K8S_CDEV1"
  GRAFANA_API_KEY_K8S_CP1: "$GRAFANA_API_KEY_K8S_CP1"
  GRAFANA_API_KEY_K8S_MGMT1: "$GRAFANA_API_KEY_K8S_MGMT1"

podLabels:
  feed-handler.coinmetrics.io/app: fh-monitoring

serviceAccount:
  create: true

args:
  - "python"
  - "-m"
  - "src.octopus.applications.fh_monitoring"
  - "--data-center"
  - "cp1"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--prometheus-server"
  - "https://mimir.mgmt1.cnmtrcs.io/prometheus/"
  - "--instance-number"
  - "$FEED_HANDLER_INSTANCE"
  - "--interval"
  - "300"
