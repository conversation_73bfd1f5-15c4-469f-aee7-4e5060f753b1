#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "530m"
     memory: "3000Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: huobi
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: k-spot
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 0400-0500

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "Huobi"
  - "--exclude-instruments"
  - "doge-usdt"
  - "bsv-usdt"
  - "nft-usdt"
  - "ygg-usdt"
  - "ldo-usdt"
  - "fil-usdt"
  - "auction-usdt"
  - "xlm-usdt"
  - "lqty-usdt"
  - "ar-usdt"
  - "skl-usdt"
  - "sui-usdt"
  - "op-usdt"
  - "xch-usdt"
  - "bch-usdt"
  - "hft-usdt"
  - "people-usdt"
  - "sol-usdt"
  - "avax-usdt"
  - "flow-usdt"
  - "ada-usdt"
  - "reef-usdt"
  - "zec-usdt"
  - "apt-usdt"
  - "arb-usdt"
  - "luna-usdt"
  - "polyx-usdt"
  - "wld-usdt"
  - "bnb-usdt"
  - "near-usdt"
  - "rune-usdt"
  - "tia-usdt"
  - "dot-usdt"
  - "xtz-usdt"
  - "xrp-usdt"
  - "agix-usdt"
  - "fet-usdt"
  - "knc-usdt"
  - "dia-usdt"
  - "ren-usdt"
  - "blur-usdt"
  - "ordi-usdt"
  - "uni-usdt"
  - "pyth-usdt"
  - "enj-usdt"
  - "shib-usdt"
  - "shib-usdt"
  - "fxs-usdt"
  - "link-usdt"
  - "jst-usdt"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-out-quotes"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_10.proto:3GB"
  - "quotes_10:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "0400:0500"
  - "--markets-per-producer"
  - "10"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-us-book-realtime[5/7]"
  - "--rate-limit-multiplier"
  - "0.05"
  - "--spot"
  - "--book-depth"
  - "30000"
  - "--environment"
  - "cp1"
