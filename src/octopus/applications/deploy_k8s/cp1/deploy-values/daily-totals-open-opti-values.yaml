---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: daily-totals-open-opti

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.daily_totals"
  - "--postgres-write-db"
  - "$(CM_DB_OPEN_INTEREST_OPTIONS_HOST):$(CM_DB_OPEN_INTEREST_OPTIONS_PORT):$(CM_DB_OPEN_INTEREST_OPTIONS_DATABASE):$(CM_DB_OPEN_INTEREST_OPTIONS_USERNAME):$(CM_DB_OPEN_INTEREST_OPTIONS_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_OPEN_INTEREST_OPTIONS_HOST):$(CM_DB_OPEN_INTEREST_OPTIONS_PORT):$(CM_DB_OPEN_INTEREST_OPTIONS_DATABASE):$(CM_DB_OPEN_INTEREST_OPTIONS_USERNAME):$(CM_DB_OPEN_INTEREST_OPTIONS_PASSWORD)"
  - "--host-env"
  - "cp1"
  - "--exchanges"
  - "Binance,Bitfinex,BitMEX,Bybit,CME,Deribit,Huobi,Kraken,OKEx"
  - "--data-type"
  - "open_interest"
  - "--option"
  - "--begin"
  - "2024-07-20"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
