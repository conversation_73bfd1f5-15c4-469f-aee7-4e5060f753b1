---
nameOverride: "proxy-check"

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "1Gi"
   requests:
     cpu: "500m"
     memory: "1Gi"

envName: $CI_ENVIRONMENT_SLUG

livenessProbe: {}

imagePullSecrets:
  type: none

podLabels:
  feed-handler.coinmetrics.io/app: proxy-check

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

ports:
  - name: http-metrics
    containerPort: 8080
    protocol: TCP

args:
  - "python"
  - "-m"
  - "src.octopus.applications.proxy_check"
  - "--proxy-groups"
  - "cp1-hetzner-eu"
  - "cp1-hetzner-us"
  - "smartproxy-us-cp1"
  - "--interval=60"
  - "--prometheus"
  - "[$(POD_IP)]:8080"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
