---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "8Gi"
   requests:
     cpu: "250m"
     memory: "8Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: historical-patch-trad-spot

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.historical_patch"
  - "--postgres-write-db"
  - "$(CM_DB_SPOT_TRADES_EXCH_HOST):$(CM_DB_SPOT_TRADES_EXCH_PORT):$(CM_DB_SPOT_TRADES_EXCH_DATABASE):$(CM_DB_SPOT_TRADES_EXCH_USERNAME):$(CM_DB_SPOT_TRADES_EXCH_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_SPOT_TRADES_EXCH_HOST):$(CM_DB_SPOT_TRADES_EXCH_PORT):pg-trades-spot-1-r-1:$(CM_DB_SPOT_TRADES_EXCH_USERNAME):$(CM_DB_SPOT_TRADES_EXCH_PASSWORD)"
  - "--exchanges"
  - "Binance,Binance.US,BinanceAgg,BitMEX,Bitbank,Bitfinex,Bithumb,Bitstamp,Bullish,Bybit,CME,Coinbase,CoinbaseDer,CoinbaseInt,Crypto.com,Deribit,ErisX,GFOX,Gate.io,Gemini,HitBTC,Huobi,Kraken,KuCoin,LBank,LMAX,MEXC,OKEx,Poloniex,Upbit,bitFlyer,dYdX,itBit"
  - "--data-type"
  - "trade"
  - "--spot"
  - "--host-env"
  - "cp1"
  - "--other-env"
  - "prod"
  - "--begin"
  - "2025-03-28"
  - "--end"
  - "2025-04-06"
  - "--no-import"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
