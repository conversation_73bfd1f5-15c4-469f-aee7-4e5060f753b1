#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: shipper

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"


imagePullSecrets:
  type: none

livenessProbe: {}

readinessProbe:
  httpGet:
    path: /metrics
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podLabels:
  feed-handler.coinmetrics.io/type: ticker_f
  feed-handler.coinmetrics.io/market: futures

args:
  - "python"
  - "-m"
  - "src.octopus.applications.shipper"
  - "futures_ticker_f_exch"
  - "$(CM_DB_LOCAL_HOST):$(CM_DB_LOCAL_PORT):$(CM_DB_LOCAL_DATABASE)-${FEED_HANDLER_INSTANCE}:$(CM_DB_LOCAL_USERNAME):$(CM_DB_LOCAL_PASSWORD)"
  - "$(CM_DB_FUTURES_TICKER_EXCH_HOST):$(CM_DB_FUTURES_TICKER_EXCH_PORT):$(CM_DB_FUTURES_TICKER_EXCH_DATABASE):$(CM_DB_FUTURES_TICKER_EXCH_USERNAME):$(CM_DB_FUTURES_TICKER_EXCH_PASSWORD)"
  - "Binance"
  - "Bybit"
  - "Deribit"
  - "OKEx"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--batch-size"
  - "2000"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
