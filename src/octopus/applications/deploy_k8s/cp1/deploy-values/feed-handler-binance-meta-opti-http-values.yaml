#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "4m"
     memory: "119Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: binance
  feed-handler.coinmetrics.io/type: metadata
  feed-handler.coinmetrics.io/market: option
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: http
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.http_metadata_scraper"
  - "Binance"
  - "--exclusive-proxies"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--option"
  - "--database"
  - "$(CM_DB_OPTIONS_METADATA_HOST):$(CM_DB_OPTIONS_METADATA_PORT):$(CM_DB_OPTIONS_METADATA_DATABASE):$(CM_DB_OPTIONS_METADATA_USERNAME):$(CM_DB_OPTIONS_METADATA_PASSWORD)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "binance-cp1-hetzner-eu-metadata-realtime"
  - "--rate-limit-multiplier"
  - "0.1"
  - "--environment"
  - "cp1"
