#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3072Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: kucoin
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 020-040

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "KuCoin"
  - "--exclude-instruments"
  - "XBTUSDTM"
  - "ETHUSDTM"
  - "SOLUSDTM"
  - "MAGICUSDTM"
  - "FARTCOINUSDTM"
  - "TURBOUSDTM"
  - "XRPUSDTM"
  - "SUIUSDTM"
  - "OMUSDTM"
  - "NKNUSDTM"
  - "PEPEUSDTM"
  - "ADAUSDTM"
  - "TRUMPUSDTM"
  - "--futures"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-out-quotes"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_39.proto:3GB"
  - "quotes_39:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "020:040"
  - "--markets-per-producer"
  - "10"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-us-book-realtime[2/24],cp1-hetzner-eu-book-realtime[2/24],smartproxy-us-cp1-book-realtime[2/24]"
  - "--rate-limit-multiplier"
  - "0.15"
  - "--book-depth"
  - "30000"
  - "--environment"
  - "cp1"
