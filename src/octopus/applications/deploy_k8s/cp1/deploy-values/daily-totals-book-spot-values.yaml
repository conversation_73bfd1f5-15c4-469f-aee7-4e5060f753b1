---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: daily-totals-book-spot

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.daily_totals"
  - "--postgres-write-db"
  - "$(CM_DB_SPOT_BOOKS_HOST):$(CM_DB_SPOT_BOOKS_PORT):$(CM_DB_SPOT_BOOKS_DATABASE):$(CM_DB_SPOT_BOOKS_USERNAME):$(CM_DB_SPOT_BOOKS_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_SPOT_BOOKS_HOST):$(CM_DB_SPOT_BOOKS_PORT):pg-books-1-r-1:$(CM_DB_SPOT_BOOKS_USERNAME):$(CM_DB_SPOT_BOOKS_PASSWORD)"
  - "--host-env"
  - "cp1"
  - "--data-type"
  - "book"
  - "--spot"
  - "--exchanges"
  - "Binance,Binance.US,Bitbank,Bitfinex,Bitstamp,Bullish,Bybit,CEX.IO,Coinbase,Crypto.com,ErisX,Gate.io,Gemini,Huobi,Kraken,KuCoin,LMAX,MEXC,OKEx,Poloniex,bitFlyer,itBit"
  - "--begin"
  - "2025-02-18"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
