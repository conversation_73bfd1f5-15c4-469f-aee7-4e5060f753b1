---
nameOverride: "feed-handler"
kind: Job

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "1Gi"
   requests:
     cpu: "500m"
     memory: "1Gi"

envName: $CI_ENVIRONMENT_SLUG

livenessProbe: {}

imagePullSecrets:
  type: none

podLabels:
  feed-handler.coinmetrics.io/app: job

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true


ports:
#  - name: http-metrics
#    containerPort: 8080
#    protocol: TCP
  - name: ws
    containerPort: 13331
    protocol: TCP

service:
  ports:
    - name: proxy-port
      port: 13331
      targetPort: ws

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.patch"
  - "--exchanges"
  - "Deribit"
  - "--type"
  - "books"
  - "--source-database"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):$(CM_DB_OPTIONS_BOOKS_DATABASE):$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--target-database"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):$(CM_DB_OPTIONS_BOOKS_DATABASE):$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--option"
  - "--begin"
  - "2024-09-02"
  - "--end"
  - "2025-01-01"
  - "--max-writes-per-second"
  - "15000"
  - "--commit"
