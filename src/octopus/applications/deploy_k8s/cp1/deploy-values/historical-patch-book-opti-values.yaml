---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "12Gi"
   requests:
     cpu: "1000m"
     memory: "8Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: historical-patch-book-opti

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.historical_patch"
  - "--exchanges"
  - "Binance,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,OKEx"
  - "--postgres-write-db"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):$(CM_DB_OPTIONS_BOOKS_DATABASE):$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_OPTIONS_BOOKS_HOST):$(CM_DB_OPTIONS_BOOKS_PORT):pg-books-1-r-1:$(CM_DB_OPTIONS_BOOKS_USERNAME):$(CM_DB_OPTIONS_BOOKS_PASSWORD)"
  - "--data-type"
  - "book"
  - "--option"
  - "--host-env"
  - "cp1"
  - "--other-env"
  - "prod"
  - "--begin"
  - "2025-04-02"
  - "--end"
  - "2025-04-16"
  - "--no-export"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
