#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "186m"
     memory: "159Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: cme
  feed-handler.coinmetrics.io/type: trade
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_trade_scraper"
  - "CME"
  - "--api-params"
  - "API_COINMETRICS_REF"
  - "7SKBXE3ZVAFZHYY2OARJCTFJEVAMD5EEDEBNQCQ="
  - "********************"
  - "k1WDwcvZyrwiiPv5MDA56ZkoJ5EUQ2m4v0UAdNqK"
  - "--futures"
  - "--kafka-out-proto"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "trades_40.proto:-1"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-us-trade-realtime,cp1-hetzner-eu-trade-realtime,smartproxy-us-cp1-trade-realtime"
  - "--rate-limit-multiplier"
  - "0.5"
  - "--streaming-api-params"
  - "coinmetrics-f5d38728cc2a.json"
  - "ny5-${DOLLAR}(HOSTNAME)"
  - "--environment"
  - "cp1"
