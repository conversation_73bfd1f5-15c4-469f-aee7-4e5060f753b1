#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "530m"
     memory: "749Mi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: coinbase
  feed-handler.coinmetrics.io/type: trade
  feed-handler.coinmetrics.io/market: spot
  feed-handler.coinmetrics.io/collection-mode: history
  feed-handler.coinmetrics.io/connection-mode: history
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.history_trade_scraper"
  - "Coinbase"
  - "--exch-database"
  - "$(CM_DB_SPOT_TRADES_EXCH_HOST):$(CM_DB_SPOT_TRADES_EXCH_PORT):$(CM_DB_SPOT_TRADES_EXCH_DATABASE):$(CM_DB_SPOT_TRADES_EXCH_USERNAME):$(CM_DB_SPOT_TRADES_EXCH_PASSWORD)"
  - "--cluster-machines"
  - "1"
  - "2"
  - "--sleep-time"
  - "30"
  - "--machine"
  - "${FEED_HANDLER_INSTANCE}"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp1-hetzner-eu-trade-history,smartproxy-us-cp1-trade-history,cp1-hetzner-eu-option-ticker,smartproxy-us-cp1-ticker-o-realtime,cp1-hetzner-eu-futures-ticker,smartproxy-us-cp1-ticker-f-realtime,cp1-hetzner-eu-book-1h-realtime,smartproxy-us-cp1-book-1h-realtime,cp1-hetzner-eu-open-interest-realtime,smartproxy-us-cp1-open-interest-realtime,cp1-hetzner-eu-liquidations-realtime,smartproxy-us-cp1-liquidations-realtime,cp1-hetzner-eu-funding-rate-realtime,smartproxy-us-cp1-funding-rate-realtime"
  - "--rate-limit-multiplier"
  - "0.5"
  - "--spot"
  - "--environment"
  - "cp1"
