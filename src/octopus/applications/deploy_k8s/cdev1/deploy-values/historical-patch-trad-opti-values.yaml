---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "16Gi"
   requests:
     cpu: "1000m"
     memory: "12Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: historical-patch-trad-opti

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.historical_patch"
  - "--postgres-write-db"
  - "$(CM_DB_OPTIONS_TRADES_EXCH_HOST):$(CM_DB_OPTIONS_TRADES_EXCH_PORT):$(CM_DB_OPTIONS_TRADES_EXCH_DATABASE):$(CM_DB_OPTIONS_TRADES_EXCH_USERNAME):$(CM_DB_OPTIONS_TRADES_EXCH_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_OPTIONS_TRADES_EXCH_HOST):$(CM_DB_OPTIONS_TRADES_EXCH_PORT):$(CM_DB_OPTIONS_TRADES_EXCH_DATABASE):$(CM_DB_OPTIONS_TRADES_EXCH_USERNAME):$(CM_DB_OPTIONS_TRADES_EXCH_PASSWORD)"
  - "--exchanges"
  - "Binance,CME,Deribit,OKEx"
  - "--data-type"
  - "trade"
  - "--option"
  - "--host-env"
  - "hel1"
  - "--other-env"
  - "cp1"
  - "--begin"
  - "2024-12-01"
  - "--no-export"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
