---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     cpu: "3"
     memory: "8Gi"
   requests:
     cpu: "1000m"
     memory: "8Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none

readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

env:
    GITLAB_USER_ID: "$GITLAB_USER_ID"

podLabels:
  feed-handler.coinmetrics.io/app: historical-patch-open-futu

args:
  - "python"
  - "-m"
  - "src.octopus.applications.storage.historical_patch"
  - "--exchanges"
  - "Binance,<PERSON>finex,BitMEX,Bybit,CME,Deribit,<PERSON><PERSON><PERSON>,Kraken,OKEx"
  - "--postgres-write-db"
  - "$(CM_DB_OPEN_INTEREST_FUTURES_HOST):$(CM_DB_OPEN_INTEREST_FUTURES_PORT):$(CM_DB_OPEN_INTEREST_FUTURES_DATABASE):$(CM_DB_OPEN_INTEREST_FUTURES_USERNAME):$(CM_DB_OPEN_INTEREST_FUTURES_PASSWORD)"
  - "--postgres-read-db"
  - "$(CM_DB_OPEN_INTEREST_FUTURES_HOST):$(CM_DB_OPEN_INTEREST_FUTURES_PORT):$(CM_DB_OPEN_INTEREST_FUTURES_DATABASE):$(CM_DB_OPEN_INTEREST_FUTURES_USERNAME):$(CM_DB_OPEN_INTEREST_FUTURES_PASSWORD)"
  - "--data-type"
  - "open_interest"
  - "--futures"
  - "--host-env"
  - "hel1"
  - "--other-env"
  - "cp1"
  - "--begin"
  - "2024-12-01"
  - "--no-export"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
