#!/usr/bin/env bash
# Updated script to deploy sequentially helm charts

# define env variable TRACE=1 to get script execution details
[[ "${TRACE}" ]] && set -x

DEPLOYMENT_TARGET="$1"

set -eEuo pipefail
cd "$(dirname "$0")"

export DIR=.
export CHART=k8s
export HELM_CHART="oci://registry.gitlab.com/coinmetrics/ops/k8s-cm-helm/chart/k8s"
export HELM_CHART_VERSION="2.0.0-stable"
export HELM_CHART_DOWNLOAD="0"
export HELM_WAIT_FOR_RELEASE="0"

export FEED_HANDLER_INSTANCE=${FEED_HANDLER_INSTANCE:-"1"}
export HELM_RELEASE_SUFFIX="${FEED_HANDLER_INSTANCE}"

# Directory where values files are located, relative to "DIR"
export HELM_VALUE_FILES_DIR=deploy-values
# common value files with db details
export HELM_VALUE_FILES="shared-feed-handler-db-values.yaml,shared-feed-handler-db-secret-values.yaml"

# stack name - used to detect removed feed handlers between deployments and uninstall helm charts
export STACK="deploy-book-realtime-spot-group2"
export RELEASE_LIST="feed-handler-bitbank-all-book-spot-stre,feed-handler-bitflyer-all-book-spot-stre,feed-handler-bybit-book-spot-stre-000-080,feed-handler-bybit-book-spot-stre-080-160,feed-handler-bybit-book-spot-stre-160-240,feed-handler-bybit-book-spot-stre-240-320,feed-handler-bybit-book-spot-stre-320-400,feed-handler-bybit-book-spot-stre-400-480,feed-handler-bybit-book-spot-stre-480-560,feed-handler-bybit-book-spot-stre-560-9000,feed-handler-coinbase-busy1-book-spot-stre,feed-handler-coinbase-busy2-book-spot-stre,feed-handler-coinbase-busy3-book-spot-stre,feed-handler-coinbase-book-spot-stre-0000-0010,feed-handler-coinbase-book-spot-stre-0010-0020,feed-handler-coinbase-book-spot-stre-0020-0030,feed-handler-coinbase-book-spot-stre-0030-0040,feed-handler-coinbase-book-spot-stre-0040-0050,feed-handler-coinbase-book-spot-stre-0050-0060,feed-handler-coinbase-book-spot-stre-0060-0070,feed-handler-coinbase-book-spot-stre-0070-0080,feed-handler-coinbase-book-spot-stre-0080-0090,feed-handler-coinbase-book-spot-stre-0090-0100,feed-handler-coinbase-book-spot-stre-0100-0110,feed-handler-coinbase-book-spot-stre-0110-0120,feed-handler-coinbase-book-spot-stre-0120-0130,feed-handler-coinbase-book-spot-stre-0130-0140,feed-handler-coinbase-book-spot-stre-0140-0150,feed-handler-coinbase-book-spot-stre-0150-0160,feed-handler-coinbase-book-spot-stre-0160-0170,feed-handler-coinbase-book-spot-stre-0170-0180,feed-handler-coinbase-book-spot-stre-0180-0190,feed-handler-coinbase-book-spot-stre-0190-0200,feed-handler-coinbase-book-spot-stre-0200-0210,feed-handler-coinbase-book-spot-stre-0210-0220,feed-handler-coinbase-book-spot-stre-0220-0230,feed-handler-coinbase-book-spot-stre-0230-0240,feed-handler-coinbase-book-spot-stre-0240-0250,feed-handler-coinbase-book-spot-stre-0250-0260,feed-handler-coinbase-book-spot-stre-0260-0270,feed-handler-coinbase-book-spot-stre-0270-0280,feed-handler-coinbase-book-spot-stre-0280-0290,feed-handler-coinbase-book-spot-stre-0290-0300,feed-handler-coinbase-book-spot-stre-0300-0310,feed-handler-coinbase-book-spot-stre-0310-0320,feed-handler-coinbase-book-spot-stre-0320-0330,feed-handler-coinbase-book-spot-stre-0330-0340,feed-handler-coinbase-book-spot-stre-0340-0350,feed-handler-coinbase-book-spot-stre-0350-0360,feed-handler-coinbase-book-spot-stre-0360-0370,feed-handler-coinbase-book-spot-stre-0370-0380,feed-handler-coinbase-book-spot-stre-0380-0390,feed-handler-coinbase-book-spot-stre-0390-0400,feed-handler-coinbase-book-spot-stre-0400-0410,feed-handler-coinbase-book-spot-stre-0410-9000,feed-handler-gateio-book-spot-stre-0000-0200,feed-handler-gateio-book-spot-stre-0200-0400,feed-handler-gateio-book-spot-stre-0400-0600,feed-handler-gateio-book-spot-stre-0600-0800,feed-handler-gateio-book-spot-stre-0800-1000,feed-handler-gateio-book-spot-stre-1000-1200,feed-handler-gateio-book-spot-stre-1200-1400,feed-handler-gateio-book-spot-stre-1400-1600,feed-handler-gateio-book-spot-stre-1600-1800,feed-handler-gateio-book-spot-stre-1800-2000,feed-handler-gateio-book-spot-stre-2000-2200,feed-handler-gateio-book-spot-stre-2200-2400,feed-handler-gateio-book-spot-stre-2400-2600,feed-handler-gateio-book-spot-stre-2600-9000,feed-handler-kraken-book-spot-stre-0000-0100,feed-handler-kraken-book-spot-stre-0100-0200,feed-handler-kraken-book-spot-stre-0200-0300,feed-handler-kraken-book-spot-stre-0300-0400,feed-handler-kraken-book-spot-stre-0400-0500,feed-handler-kraken-book-spot-stre-0500-0600,feed-handler-kraken-book-spot-stre-0600-0700,feed-handler-kraken-book-spot-stre-0700-0800,feed-handler-kraken-book-spot-stre-0800-0900,feed-handler-kraken-book-spot-stre-0900-1000,feed-handler-kraken-book-spot-stre-1000-1100,feed-handler-kraken-book-spot-stre-1100-9000"

# Pull the helm chart deployment package (if needed) once before running the deployment scripts in parallel
if [[ ! -d $CHART ]] ; then
  pull-helm-chart
fi

LOGDIR="/tmp/feed-handler-logs/$STACK"
mkdir -p $LOGDIR

# Individual installation of each helm chart
COUNT=0
CHUNK_SIZE=20
CHUNK_INDEX_COUNTER=0
for release in ${RELEASE_LIST//,/ }; do
  if [[ -z "$DEPLOYMENT_TARGET" ]] || [[ $(echo $release | egrep -e "$DEPLOYMENT_TARGET") ]] ; then
    if [[ ${DRYRUN:-0} -eq 0 ]] ; then
      HELM_RELEASE_NAME=$release HELM_VALUE_FILES="$HELM_VALUE_FILES,${release}-values.yaml" install-helm-chart > $LOGDIR/${release}.log 2>&1 &
      let "COUNT+=1"
    else
      echo "DRYRUN deploy: $release"
    fi
  fi
  let "CHUNK_INDEX_COUNTER+=1"
  if [[ $CHUNK_INDEX_COUNTER -eq $CHUNK_SIZE ]] ; then
    CHUNK_INDEX_COUNTER=0
    # Wait for all of the deployment scripts to complete and check for errors
    ERRORS=0
    for job in `jobs -p` ; do
      wait $job || let 'ERRORS+=1'
    done
  fi
done

# Wait for all of the deployment scripts to complete and check for errors
ERRORS=0
for job in `jobs -p` ; do
  wait $job || let 'ERRORS+=1'
done

# Dump all of the log files
for FILE in $(ls -1 $LOGDIR/*.log 2>/dev/null) ; do
  echo "-------- $(basename $FILE) --------"
  cat $FILE
done

# Only do the cleanup if there were no deployment errors and ran at least one deployment
if [[ $ERRORS -eq 0 ]] && [[ $COUNT -gt 0 ]] ; then
  # Remove unused feed handlers releases
  clean-helm-releases
fi

exit $ERRORS