#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "35m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: cme
  feed-handler.coinmetrics.io/type: book
  feed-handler.coinmetrics.io/market: futures
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_book_scraper"
  - "CME"
  - "--api-params"
  - "API_COINMETRICS_REF"
  - "7SKBXE3ZVAFZHYY2OARJCTFJEVAMD5EEDEBNQCQ="
  - "********************"
  - "k1WDwcvZyrwiiPv5MDA56ZkoJ5EUQ2m4v0UAdNqK"
  - "--futures"
  - "--kafka-out-proto"
  - "kafka-books-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-out-quotes"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "books_40.proto:3GB"
  - "quotes_40:3GB"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "cp2-hetzner-us-book-realtime,cp2-hetzner-eu-book-realtime,smartproxy-us-cp2-book-realtime"
  - "--rate-limit-multiplier"
  - "0.05"
  - "--streaming-api-params"
  - "coinmetrics-f5d38728cc2a.json"
  - "cp2-fut-${DOLLAR}(HOSTNAME)"
  - "--book-depth"
  - "30000"
  - "--environment"
  - "cp2"
