#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "3Gi"
   requests:
     cpu: "250m"
     memory: "3Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: coinbase
  feed-handler.coinmetrics.io/type: trade
  feed-handler.coinmetrics.io/market: spot
  feed-handler.coinmetrics.io/collection-mode: realtime
  feed-handler.coinmetrics.io/connection-mode: streaming
  feed-handler.coinmetrics.io/market-range: 400-9000

args:
  - "python"
  - "-m"
  - "src.octopus.applications.streaming_trade_scraper"
  - "Coinbase"
  - "--kafka-out-proto"
  - "kafka-trades-${FEED_HANDLER_INSTANCE}.kafka.svc"
  - "--kafka-topic-retention-bytes"
  - "trades_1.proto:-1"
  - "--machine"
  - "${DOLLAR}(HOSTNAME)"
  - "--market-range"
  - "400:9000"
  - "--markets-per-producer"
  - "40"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "None,cp2-hetzner-us-trade-realtime[11/11]"
  - "cp2-hetzner-eu-trade-realtime[11/11],smartproxy-us-cp2-trade-realtime[11/11]"
  - "--rate-limit-multiplier"
  - "0.04"
  - "--spot"
  - "--environment"
  - "cp2"
