#
# NOTICE: The helm value files are auto-generated by the deployment scripts.
#         Any direct edits to these files will be lost!
#
---
nameOverride: feed-handler

image:
  repository: registry.gitlab.com/coinmetrics/feed-handlers/octopus
  tag: $CI_COMMIT_SHA

resources:
   limits:
     # no limit
     memory: "9Gi"
   requests:
     cpu: "51m"
     memory: "9Gi"

envName: $CI_ENVIRONMENT_SLUG

nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: coinmetrics.io/stack
            operator: In
            values:
              - "${FEED_HANDLER_INSTANCE}"

livenessProbe: {}

imagePullSecrets:
  type: none


readinessProbe:
  httpGet:
    path: /ready
    port: 8082
  initialDelaySeconds: 5
  periodSeconds: 3

deploymentStrategy:
  type: Recreate

prometheusMetrics:
  enabled: true

podAnnotations:
    GITLAB_USER_ID: "$GITLAB_USER_ID"
    STACK: "${FEED_HANDLER_INSTANCE}"

podLabels:
  feed-handler.coinmetrics.io/exchange: binance
  feed-handler.coinmetrics.io/type: trade
  feed-handler.coinmetrics.io/market: nodelay-spot
  feed-handler.coinmetrics.io/collection-mode: history
  feed-handler.coinmetrics.io/connection-mode: history
  feed-handler.coinmetrics.io/market-range: all

args:
  - "python"
  - "-m"
  - "src.octopus.applications.history_trade_scraper"
  - "Binance"
  - "--exch-database"
  - "$(CM_DB_SPOT_TRADES_EXCH_HOST):$(CM_DB_SPOT_TRADES_EXCH_PORT):$(CM_DB_SPOT_TRADES_EXCH_DATABASE):$(CM_DB_SPOT_TRADES_EXCH_USERNAME):$(CM_DB_SPOT_TRADES_EXCH_PASSWORD)"
  - "--cluster-machines"
  - "1"
  - "2"
  - "1-no-delay"
  - "2-no-delay"
  - "--sleep-time"
  - "0"
  - "--instruments"
  - "btc-usdt"
  - "eth-usdt"
  - "--machine"
  - "${FEED_HANDLER_INSTANCE}-no-delay"
  - "--prometheus"
  - "$CM_FEED_HANDLER_PROMETHEUS_HOST"
  - "--proxy-groups"
  - "binance-cp2-hetzner-eu-trade-history"
  - "--rate-limit-multiplier"
  - "0.25"
  - "--spot"
  - "--environment"
  - "cp2"
