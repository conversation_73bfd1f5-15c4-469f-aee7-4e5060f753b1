from src.octopus.run_loader import BookDataLoader, DataLoaderParams, run_loader_application

if __name__ == "__main__":
    run_loader_application(
        BookDataLoader,
        DataLoaderParams(
            db_channel_capacity=1024,
            db_consumer_max_items_per_tick=1024,
            db_consumer_interval=1.0,
            ws_channel_capacity=1024,
            ws_consumer_interval=0.01,
            ws_consumer_max_items_per_tick=128,
            kafka_channel_capacity=32768,
            kafka_consumer_interval=0.01,
            kafka_consumer_max_items_per_tick=128,
        ),
    )
