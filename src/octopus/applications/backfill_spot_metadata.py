import functools
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from collections import defaultdict
from datetime import datetime
from typing import Callable, DefaultDict, Dict, List, Optional, Set, Tuple

import ciso8601
import pytz

from src.octopus.arguments import parse_database_arg
from src.octopus.data import SpotContract, SpotContractData
from src.octopus.storage.postgres.spot_metadata import PostgresSpotMetadataStorage
from src.resources.exchange import glib_currency, glib_exchange
from src.utils.postgres import PgConnectionParams, postgres_connect
from src.utils.timeutil import get_ciso8601


def parse_args() -> Namespace:
    parser = ArgumentParser()
    parser.add_argument(
        "--database",
        type=parse_database_arg,
        required=True,
        help="Spot Metadata DB connection - host:port:db_name:user:password or `blackhole`",
    )
    parser.add_argument(
        "--trades-database",
        type=parse_database_arg,
        required=True,
        help="Trades DB connection - host:port:db_name:user:password or `blackhole`",
    )
    parser.add_argument("--test", action="store_true", required=False, help="Run tests.")
    parser.add_argument(
        "--check-symbols-prediction",
        action="store_true",
        required=False,
        help="Run a symbol generation check instead of processing.",
    )
    parser.add_argument("--analyze", action="store_true", required=False, help="Print missing symbols count.")
    return parser.parse_args()


class SymbolInfo:
    exchange_id: int
    base_id: int
    quote_id: int
    _first_listing_date: Optional[datetime] = None
    _last_listing_date: Optional[datetime] = None

    def __init__(self, trades_conn_param: PgConnectionParams, exchange_id: int, base_id: int, quote_id: int):
        self.trades_conn_param = trades_conn_param
        self.exchange_id = exchange_id
        self.base_id = base_id
        self.quote_id = quote_id

    @property
    def base(self) -> str:
        return glib_currency().ticker_by_currency_id(self.base_id)

    @property
    def quote(self) -> str:
        return glib_currency().ticker_by_currency_id(self.quote_id)

    @property
    def ex_base(self) -> str:
        return self._get_exchange_ticker(self.base_id)

    @property
    def ex_quote(self) -> str:
        return self._get_exchange_ticker(self.quote_id)

    @property
    def first_listing_date(self) -> datetime:
        if not self._first_listing_date:
            self._first_listing_date, self._last_listing_date = self._calculate_listing_date_range()

        return self._first_listing_date

    @property
    def last_listing_date(self) -> datetime:
        if not self._last_listing_date:
            self._first_listing_date, self._last_listing_date = self._calculate_listing_date_range()

        return self._last_listing_date

    @functools.cached_property
    def symbol(self) -> str:
        try:
            return get_symbol_for_exchange(self)
        except ValueError:
            return ""

    def _calculate_listing_date_range(self) -> Tuple[datetime, datetime]:
        with postgres_connect(self.trades_conn_param) as connection:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT min(trade_time), max(trade_time)
                    FROM public.trades
                    WHERE trade_exchange_id = %s
                        and trade_base_id = %s
                        and trade_quote_id = %s;
                """,
                    (self.exchange_id, self.base_id, self.quote_id),
                )
                row = cursor.fetchone()

                first_listing_date = pytz.utc.localize(row[0]) if row and row[0] else SpotContractData.DEFAULT_LISTING_DATE
                last_listing_date = pytz.utc.localize(row[1]) if row and row[1] else SpotContractData.DEFAULT_LISTING_DATE
                return first_listing_date, last_listing_date

    def _get_exchange_ticker(self, currency_id: int) -> str:
        cm_ticker = glib_currency().ticker_by_currency_id(currency_id)
        return glib_exchange().reverse_exchange_tickers(self.exchange_id).get(cm_ticker, cm_ticker)


def get_bitfinex_symbol(s: SymbolInfo) -> str:
    if s.base_id == 321:  # amp
        return "tAMPold" + s.ex_quote.upper()

    exception_bases = [
        "dusk",
        "b21x",
        "eth2x",
        "bchabc",
        "ocean",
        "dora",
        "aave",
        "btse",
        "forth",
        "sushi",
        "link",
        "suku",
        "rose",
        "terraust",
        "waves",
        "matic",
        "tlos",
        "xaut",
        "nexo",
        "trade",
        "wild",
        "boba",
        "avax",
        "theta",
        "cnh",
        "albt",
        "chex",
        "egld",
        "shib",
        "planets",
        "best",
        "wncg",
        "boson",
        "band",
        "chsb",
        "1inch",
        "jasmy",
        "comp",
        "reef",
        "doge",
        "velo",
        "spell",
        "qrdo",
        "near",
        "bchn",
        "luna",
    ]
    exception_quotes = ["cnht", "xaut"]
    if s.ex_base in exception_bases or s.ex_quote in exception_quotes:
        return "t" + f"{s.ex_base}:{s.ex_quote}".upper()
    else:
        return "t" + f"{s.ex_base}{s.ex_quote}".upper()


def get_kraken_symbol(s: SymbolInfo) -> str:
    base_fiat = glib_currency().is_currency_ticker_fiat(s.base)
    quote_fiat = glib_currency().is_currency_ticker_fiat(s.quote)

    if s.base == "doge":
        return f"{s.ex_base[1:]}{s.quote}".upper()

    if base_fiat and quote_fiat:
        if (s.base, s.quote) in [("eur", "usd"), ("gbp", "usd"), ("usd", "cad"), ("usd", "jpy")]:
            return f"{s.ex_base}{s.ex_quote}".upper()
        return f"{s.base}{s.quote}".upper()

    if not base_fiat and not quote_fiat:
        if (s.base, s.quote) in [("etc", "eth"), ("mln", "eth"), ("rep", "eth")]:
            return f"{s.ex_base}{s.ex_quote}".upper()

        return f"{s.base}{s.quote}".upper()

    if (s.base, s.quote) in [
        ("eth", "aud"),
        ("eth", "chf"),
        ("ltc", "aud"),
        ("ltc", "gbp"),
        ("usdt", "aud"),
        ("usdt", "cad"),
        ("usdt", "eur"),
        ("usdt", "gbp"),
        ("usdt", "jpy"),
        ("xrp", "aud"),
        ("xrp", "gbp"),
    ]:
        return f"{s.base}{s.quote}".upper()

    if s.base in ["usdt", "etc", "eth", "ltc", "mln", "rep", "xlm", "xmr", "xrp", "zec"]:
        return f"{s.ex_base}{s.ex_quote}".upper()

    return f"{s.base}{s.quote}".upper()


def get_zb_com_symbol(s: SymbolInfo) -> str:
    if s.base == "hc_hypercash":
        return f"{s.ex_base}_{s.ex_quote}"
    return f"{s.base}_{s.quote}"


def get_bibox_symbol(s: SymbolInfo) -> str:
    if s.base == "bsv":
        return f"{s.base}_{s.quote}".upper()

    if s.base == "wnxm":
        return f"{s.base}_{s.quote}".upper().replace("WNXM", "wNXM")

    return f"{s.ex_base}_{s.ex_quote}".upper()


def get_poloniex_symbol(s: SymbolInfo) -> str:
    if s.ex_base == "ersdl":
        return f"{s.ex_quote.upper()}_eRSDL"

    return f"{s.ex_quote}_{s.ex_base}".upper()


def get_gate_io_symbol(s: SymbolInfo) -> str:
    if s.base == "bsv":
        return f"{s.base}_{s.quote}".upper()

    return f"{s.ex_base}_{s.ex_quote}".upper()


def get_the_rock_trading_symbol(s: SymbolInfo) -> str:
    if s.ex_base == "doge":
        return f"DOG{s.ex_quote.upper()}"

    return f"{s.ex_base}{s.ex_quote}".upper()


def get_kucoin_symbol(s: SymbolInfo) -> str:
    replacements = {"waxp": "WAX", "bsv": "BCHSV", "rev": "R", "gala": "GALAX"}
    base = replacements.get(s.ex_base, s.ex_base)
    return f"{base}-{s.ex_quote}".upper()


GET_SYMBOLS_BY_EXCHANGE: Dict[int, Callable[[SymbolInfo], str]] = {
    0: lambda s: f"{s.ex_base}{s.ex_quote}",  # Bitstamp
    1: lambda s: f"{s.ex_base}-{s.ex_quote}".upper(),  # Coinbase
    2: get_bitfinex_symbol,  # Bitfinex
    4: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Binance
    5: lambda s: f"{s.ex_base}{s.ex_quote}",  # Gemini
    6: get_kraken_symbol,  # Kraken
    7: lambda s: f"{s.ex_base}_{s.ex_quote}".upper(),  # bitFlyer
    8: get_zb_com_symbol,  # ZB.COM
    9: lambda s: f"{s.ex_base}-{s.ex_quote}".upper(),  # OKEx
    10: lambda s: f"{s.ex_base}{s.ex_quote}",  # Huobi
    11: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # HitBTC
    12: lambda s: f"{s.ex_base}_{s.ex_quote}",  # LBank
    16: get_bibox_symbol,  # Bibox
    19: lambda s: s.ex_base.upper(),  # Bithumb
    20: get_poloniex_symbol,  # Poloniex
    21: lambda s: f"{s.ex_quote}-{s.ex_base}".upper(),  # Upbit
    22: lambda s: s.ex_quote.upper(),  # LocalBitcoins
    23: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Mt.Gox
    24: get_gate_io_symbol,  # Gate.io
    26: lambda s: f"{s.ex_base}_{s.ex_quote}",  # Bitbank
    27: get_the_rock_trading_symbol,  # TheRockTrading
    28: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # itBit
    30: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Gatecoin
    31: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Liquid
    32: lambda s: f"{s.ex_base}/{s.ex_quote}".upper(),  # CEX.IO
    33: lambda s: f"{s.ex_base}-{s.ex_quote}".upper(),  # Bittrex
    35: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Binance.US
    38: lambda s: f"{s.ex_base}/{s.ex_quote}".upper(),  # FTX
    39: get_kucoin_symbol,  # KuCoin
    41: lambda s: f"{s.ex_base}/{s.ex_quote}".upper(),  # LMAX
    42: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Bybit
    46: lambda s: f"{s.ex_base}/{s.ex_quote}".upper(),  # FTX.US
    50: lambda s: f"{s.ex_base}{s.ex_quote}".upper(),  # Bullish
}


def get_spot_metadata(conn_params: PgConnectionParams, trades_conn_params: PgConnectionParams) -> List[SymbolInfo]:
    with postgres_connect(conn_params) as connection:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT market_exchange_id, market_base_id, market_quote_id
                FROM public.spot_metadata
                WHERE market_base_id is not null
                    AND market_quote_id is not null;
            """)
            return [SymbolInfo(trades_conn_params, *row) for row in cursor.fetchall()]


def get_all_symbols(trades_conn_params: PgConnectionParams) -> List[SymbolInfo]:
    with postgres_connect(trades_conn_params) as connection:
        with connection.cursor() as cursor:
            cursor.execute("""
                WITH RECURSIVE t AS (
                    (SELECT trade_exchange_id, trade_base_id, trade_quote_id
                    FROM public.trades
                    ORDER BY trade_exchange_id, trade_base_id, trade_quote_id LIMIT 1)
                    UNION ALL
                    SELECT (
                        SELECT trade_exchange_id
                        FROM public.trades
                        WHERE (trade_exchange_id, trade_base_id, trade_quote_id) >
                              (t.trade_exchange_id, t.trade_base_id, t.trade_quote_id)
                        ORDER BY trade_exchange_id, trade_base_id, trade_quote_id LIMIT 1),
                        (SELECT trade_base_id
                        FROM public.trades
                        WHERE (trade_exchange_id, trade_base_id, trade_quote_id) >
                              (t.trade_exchange_id, t.trade_base_id, t.trade_quote_id)
                        ORDER BY trade_exchange_id, trade_base_id, trade_quote_id LIMIT 1),
                        (SELECT trade_quote_id
                        FROM public.trades
                        WHERE (trade_exchange_id, trade_base_id, trade_quote_id) >
                              (t.trade_exchange_id, t.trade_base_id, t.trade_quote_id)
                        ORDER BY trade_exchange_id, trade_base_id, trade_quote_id LIMIT 1)
                    FROM t
                    WHERE t.trade_exchange_id IS NOT NULL
                )
                SELECT trade_exchange_id, trade_base_id, trade_quote_id FROM t
                WHERE t.trade_exchange_id IS NOT NULL;
            """)
            return [SymbolInfo(trades_conn_params, *row) for row in cursor.fetchall()]


def get_missing_metadata(symbols: List[SymbolInfo], existing: List[SymbolInfo]) -> List[SymbolInfo]:
    existing_set = {(m.exchange_id, m.base_id, m.quote_id) for m in existing}
    return [s for s in symbols if (s.exchange_id, s.base_id, s.quote_id) not in existing_set]


def get_symbol_for_exchange(s: SymbolInfo) -> str:
    if s.exchange_id not in GET_SYMBOLS_BY_EXCHANGE:
        raise ValueError(f"No symbol extractor is defined for exchange: {s.exchange_id}.")

    return GET_SYMBOLS_BY_EXCHANGE[s.exchange_id](s)


def get_spot_contract(
    s: SymbolInfo,
    listing_date: datetime = SpotContractData.DEFAULT_LISTING_DATE,
    end_date: Optional[datetime] = None,
    is_current: bool = True,
) -> SpotContract:
    return SpotContract(
        exchange_id=s.exchange_id,
        data=SpotContractData(
            symbol=s.symbol,
            base_id=s.base_id,
            quote_id=s.quote_id,
            base_name=s.base,
            quote_name=s.quote,
            listing_date=listing_date,
            end_date=end_date,
            is_current=is_current,
        ),
    )


def print_symbols_conflict_message(symbol_info: SymbolInfo, prev_symbol: SymbolInfo) -> None:
    print("CONFLICT")
    print(
        f"{symbol_info.symbol, symbol_info.exchange_id, symbol_info.base_id, symbol_info.quote_id}"
        f"{get_ciso8601(symbol_info.first_listing_date or SpotContractData.DEFAULT_LISTING_DATE)}"
        f"{get_ciso8601(symbol_info.last_listing_date or SpotContractData.DEFAULT_LISTING_DATE)}"
    )
    print(
        f"{prev_symbol.symbol, prev_symbol.exchange_id, prev_symbol.base_id, prev_symbol.quote_id}"
        f"{get_ciso8601(prev_symbol.first_listing_date or SpotContractData.DEFAULT_LISTING_DATE)}"
        f"{get_ciso8601(prev_symbol.last_listing_date or SpotContractData.DEFAULT_LISTING_DATE)}"
    )
    print()


def prepare_spot_contracts(existing: List[SymbolInfo], missing: List[SymbolInfo]) -> List[SpotContract]:
    symbol_info_per_symbol: DefaultDict[Tuple[int, str], List[SymbolInfo]] = defaultdict(list)

    existing_symbols_set: Set[Tuple[int, int, int]] = set()
    for s in existing:
        info = (s.exchange_id, s.base_id, s.quote_id)
        if s.symbol and info not in existing_symbols_set:
            symbol_info_per_symbol[(s.exchange_id, s.symbol)].append(s)
            existing_symbols_set.add(info)

    missing_symbols_set: Set[Tuple[int, int, int]] = set()
    for s in missing:
        info = (s.exchange_id, s.base_id, s.quote_id)
        if s.symbol and info not in missing_symbols_set and info not in existing_symbols_set:
            symbol_info_per_symbol[(s.exchange_id, s.symbol)].append(s)
            missing_symbols_set.add((s.exchange_id, s.base_id, s.quote_id))

    contracts: List[SpotContract] = []
    for exchange_id, symbol in symbol_info_per_symbol:
        _symbol_entries = symbol_info_per_symbol[(exchange_id, symbol)]

        if len(_symbol_entries) == 1:
            for symbol_info in _symbol_entries:
                if (symbol_info.exchange_id, symbol_info.base_id, symbol_info.quote_id) in missing_symbols_set:
                    contracts.append(get_spot_contract(symbol_info))
            continue

        prev_symbol: Optional[SymbolInfo] = None
        for index, symbol_info in enumerate(sorted(_symbol_entries, key=lambda x: x.last_listing_date)):
            if prev_symbol and symbol_info.first_listing_date < prev_symbol.last_listing_date:
                print_symbols_conflict_message(symbol_info, prev_symbol)

            if index == len(_symbol_entries) - 1:
                listing_date = prev_symbol.last_listing_date if prev_symbol else SpotContractData.DEFAULT_LISTING_DATE
                contract = get_spot_contract(listing_date=listing_date, end_date=None, s=symbol_info, is_current=True)
            else:
                listing_date = prev_symbol.last_listing_date if prev_symbol else SpotContractData.DEFAULT_LISTING_DATE
                contract = get_spot_contract(
                    listing_date=listing_date, end_date=symbol_info.last_listing_date, s=symbol_info, is_current=False
                )

            prev_symbol = symbol_info
            if (contract.exchange_id, contract.data.base_id, contract.data.quote_id) in missing_symbols_set:
                contracts.append(contract)

    return contracts


def save_spot_metadata(conn_params: PgConnectionParams, contracts: List[SpotContract]) -> None:
    print("Saving...")
    storage = PostgresSpotMetadataStorage(conn_params)
    storage.save_spot_metadata(contracts)
    print(f"Spot metadata contracts saved: {len(contracts)}")


def check_get_symbols_by_exchange(conn_params: PgConnectionParams, trades_conn_params: PgConnectionParams) -> None:
    """Used to check symbol prediction accuracy base on existing spot metadata"""
    storage = PostgresSpotMetadataStorage(conn_params)
    known_markets = storage.get_spot_metadata(count=100000)

    markets_per_exchange = {}
    for market in known_markets:
        if market.exchange_id not in markets_per_exchange:
            markets_per_exchange[market.exchange_id] = {"recognized": 0, "unrecognized": 0}

        if not market.data.base_id or not market.data.quote_id:
            continue

        symbol_info = SymbolInfo(
            trades_conn_param=trades_conn_params,
            exchange_id=market.exchange_id,
            base_id=market.data.base_id,
            quote_id=market.data.quote_id,
        )

        generated_symbol = get_symbol_for_exchange(symbol_info)
        base_fiat = glib_currency().is_currency_ticker_fiat(market.data.base_name)
        quote_fiat = glib_currency().is_currency_ticker_fiat(market.data.quote_name)
        if generated_symbol != market.data.symbol:
            print(
                f"{market.exchange_id} gen. {generated_symbol} exp. {market.data.symbol} "
                f"[{symbol_info.ex_base, symbol_info.ex_quote}, "
                f"{market.data.base_name, market.data.quote_name}] "
                f"{base_fiat} {quote_fiat}"
            )
            markets_per_exchange[market.exchange_id]["unrecognized"] += 1
        else:
            markets_per_exchange[market.exchange_id]["recognized"] += 1

    print()
    for exchange_id in sorted(markets_per_exchange):
        recognized = markets_per_exchange[exchange_id]["recognized"]
        unrecognized = markets_per_exchange[exchange_id]["unrecognized"]

        exchange_name = glib_exchange().exchange_name_by_id(exchange_id)
        if not recognized and not unrecognized:
            print(f"{exchange_id} {exchange_name}: 0 / 0 [100.0%]")
        else:
            print(
                f"{exchange_id} {exchange_name}: {recognized} / {recognized + unrecognized} "
                f"[{round(100 * recognized / (recognized + unrecognized), 2)}%]"
            )


def test_prepare_exchange_symbols(conn_params: PgConnectionParams) -> None:
    s1 = SymbolInfo(conn_params, 2, 321, 0)
    s1._last_listing_date = pytz.utc.localize(ciso8601.parse_datetime("2021-02-25 14:57:30.336"))
    s2 = SymbolInfo(conn_params, 2, 1362, 0)
    s2._last_listing_date = pytz.utc.localize(ciso8601.parse_datetime("2021-02-26 14:57:30.336"))

    missing_symbols = [s1, s2]
    contracts = prepare_spot_contracts(existing=[], missing=missing_symbols)
    assert contracts == [
        get_spot_contract(
            s1, listing_date=SpotContractData.DEFAULT_LISTING_DATE, end_date=s1.last_listing_date, is_current=False
        ),
        get_spot_contract(s2, listing_date=s1.last_listing_date, end_date=None, is_current=True),
    ]
    print("OK")


def print_aggregated_symbols(missing_symbols: List[SymbolInfo]) -> None:
    aggregated_missing_symbols: DefaultDict[int, int] = defaultdict(int)
    for symbol in missing_symbols:
        aggregated_missing_symbols[symbol.exchange_id] += 1

    print("exchange_id, missing_symbols_count")
    for exchange_id, missing_symbols_count in aggregated_missing_symbols.items():
        print(exchange_id, missing_symbols_count)


def print_result_contracts(contracts: List[SpotContract]) -> None:
    print("OLD_SPOT_METADATA: Dict[str, Tuple[SpotContractData, ...]] = {")
    last_exchange_id = -1
    for c in sorted(contracts, key=lambda x: x.exchange_id):
        if c.exchange_id != last_exchange_id:
            if last_exchange_id != -1:
                print("    ),")

            last_exchange_id = c.exchange_id
            exchange_name = glib_exchange().exchange_name_by_id(c.exchange_id)
            print(f'    "{exchange_name}": (')

        d = c.data
        listing_date_str = f'ciso8601.parse_datetime("{get_ciso8601(d.listing_date)}")' if d.listing_date else None
        end_date_str = f'ciso8601.parse_datetime("{get_ciso8601(d.end_date)}")' if d.end_date else None
        print(f'        SpotContractData(symbol="{d.symbol}", is_current={d.is_current},')
        print(
            f"                         base_id={d.base_id}, quote_id={d.quote_id}, "
            f'base_name="{d.base_name}", quote_name="{d.quote_name}",'
        )
        print(f"                         listing_date={listing_date_str},")
        print(f"                         end_date={end_date_str}),")

    if last_exchange_id != -1:
        print("    ),")

    print("}")


if __name__ == "__main__":
    args = parse_args()
    if args.test:
        test_prepare_exchange_symbols(args.trades_database)
        exit()

    if args.check_symbols_prediction:
        check_get_symbols_by_exchange(args.database, args.trades_database)
        exit()

    if args.analyze:
        spot_metadata = get_spot_metadata(args.database, args.trades_database)
        all_symbols = get_all_symbols(args.trades_database)
        missing_symbols = get_missing_metadata(all_symbols, spot_metadata)
        print_aggregated_symbols(missing_symbols)
        exit()

    spot_metadata = get_spot_metadata(args.database, args.trades_database)
    all_symbols = get_all_symbols(args.trades_database)
    missing_symbols = get_missing_metadata(all_symbols, spot_metadata)
    print("Missing symbols:", len(missing_symbols), [(s.exchange_id, s.base_id, s.quote_id) for s in missing_symbols])
    spot_contracts = prepare_spot_contracts(existing=spot_metadata, missing=missing_symbols)
    print("-----Printing results without saving-----")
    print_result_contracts(spot_contracts)
