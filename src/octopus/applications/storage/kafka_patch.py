import argparse
import logging
import sys
import time
from datetime import datetime
from typing import List, Union

from src.kafka.api import KafkaConnection
from src.octopus.applications.storage.util import parse_exchange_id
from src.octopus.data import MarketType
from src.octopus.inventory.types import DataType
from src.octopus.storage.postgres.patch.db_patcher import DBPatcher
from src.octopus.storage.postgres.trade import PostgresTradeStorage
from src.octopus.streaming_protocols.protobuf import message_from_trade
from src.octopus.utils import hms_string
from src.utils.application import _LogFormatter
from src.utils.diagnostics import Diagnostics
from src.utils.postgres import PgConnectionParams

MESSAGES: List[Union[str, bytes]] = []
MESSAGES_SENT: int = 0


def on_message_delivery(error, message) -> None:  # type: ignore
    global MESSAGES, MESSAGES_SENT
    if error:
        raise Exception(f"Error occurred while sending message to Kafka: {error}")
    MESSAGES_SENT += 1
    MESSAGES.remove(message.value())


def send_trades_to_kafka(
    args: argparse.Namespace,
    diagnostics: Diagnostics,
) -> None:
    global MESSAGES
    market_type = MarketType.SPOT if args.spot else MarketType.FUTURES if args.futures else MarketType.OPTION
    storage = PostgresTradeStorage(
        exchange_id=args.exchange,
        market_type=market_type,
        schema_name=args.source_schema,
        connection_params=args.source_database,
        diagnostics=diagnostics,
    )
    kafka_conn = KafkaConnection(
        url=args.kafka_out, topic=args.kafka_out_topic, on_message_delivery=on_message_delivery, diagnostics=diagnostics
    )
    for batch_begin, batch_end in DBPatcher.batch_datetimes_hourly(begin=args.begin, end=args.end):
        diagnostics.info(f"Processing batch {batch_begin} to {batch_end}")
        batch: List[Union[str, bytes]] = [
            message_from_trade(trade).SerializeToString() for trade in storage.get_trades_between(batch_begin, batch_end)
        ]
        MESSAGES.extend(batch)
        diagnostics.info(f"Trades: {len(batch)}")
        if args.commit:
            if kafka_conn.send(messages=batch):
                diagnostics.info(f"Pushed {len(batch)} trades to Kafka")
        if batch_end < args.end:
            time.sleep(5)

    kafka_conn._producer.flush()
    diagnostics.info(f"Messages sent to kafka: {MESSAGES_SENT}")
    diagnostics.info(f"Messages remaining: {len(MESSAGES)}")


class PatchKafka:
    diagnostics: Diagnostics
    arg_parser: argparse.ArgumentParser

    def __init__(self) -> None:
        self.diagnostics = Diagnostics()
        self._init_arg_parser()

    def _init_diagnostics(self, level: str) -> None:
        stream_handler = logging.StreamHandler(stream=sys.stdout)
        stream_handler.setFormatter(_LogFormatter("%(asctime)s %(name)-12s %(levelname)-8s %(message)s"))
        logger = logging.getLogger("root")
        logger.setLevel(logging.getLevelName(level.upper()))
        logger.addHandler(stream_handler)
        logger.propagate = False
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.getLevelName(level.upper()))
        root_logger.addHandler(stream_handler)
        self.diagnostics = Diagnostics(log=logger)

    def _init_arg_parser(self) -> None:
        description = """
        Push db rows to kafka.
        """
        self.arg_parser = argparse.ArgumentParser(
            prog="kafka_patch.py",
            description=description,
            formatter_class=argparse.RawTextHelpFormatter,
        )
        self.arg_parser.add_argument(
            "--exchange",
            required=True,
            type=parse_exchange_id,
            help="Exchange name (e.g Gemini)",
        )
        self.arg_parser.add_argument(
            "--data-type", required=True, type=lambda data_type: DataType[data_type.upper()], choices=[DataType.TRADE]
        )
        self.arg_parser.add_argument(
            "--source-database",
            metavar="source_database",
            type=PgConnectionParams,
            required=True,
            help="host:port:db_name:user:password",
        )
        self.arg_parser.add_argument(
            "--kafka-out",
            type=str,
            help="Patcher will connect to kafka URL provided and send db rows using Kafka client using protobuf serializer",
        )
        self.arg_parser.add_argument(
            "--kafka-out-topic",
            type=str,
            required=True,
            help="Kafka topic to send messages to",
        )
        self.arg_parser.add_argument(
            "--source-schema",
            metavar="source_schema",
            type=str,
            default="public",
            help="Source DB schema",
        )
        self.arg_parser.add_argument(
            "--begin",
            type=datetime.fromisoformat,
            required=True,
            help="Start date in iso format (e.g. 2022-11-18T14:00:00)",
        )
        self.arg_parser.add_argument(
            "--end",
            type=datetime.fromisoformat,
            required=True,
            help="End date in iso format (e.g. 2022-11-18T15:00:00)",
        )
        self.arg_parser.add_argument("--spot", action="store_true", default=False, help="Switch to spot trades")
        self.arg_parser.add_argument(
            "--futures",
            action="store_true",
            default=False,
            help="Switch to futures trades",
        )
        self.arg_parser.add_argument(
            "--option",
            action="store_true",
            default=False,
            help="Switch to option trades",
        )
        self.arg_parser.add_argument(
            "--commit",
            action="store_true",
            default=False,
            help="Switch from preview mode and perform patch",
        )
        self.arg_parser.add_argument(
            "--log-level",
            type=str,
            choices=["info", "debug", "error", "warn"],
            default="info",
            help="Log level [info, debug, error, warn]",
        )

    def run(self) -> None:
        args = self.arg_parser.parse_args()
        self._init_diagnostics(level=args.log_level)
        start = datetime.utcnow()

        if args.commit:
            self.diagnostics.info("COMMIT MODE: Pushing rows to Kafka")
        else:
            self.diagnostics.info("PREVIEW MODE: use --commit to push rows to Kafka")

        self.diagnostics.info(f"Patching {args.data_type}")
        self.diagnostics.info(f"Exchange: {args.exchange}")
        self.diagnostics.info(f"Begin: {args.begin}")
        self.diagnostics.info(f"End: {args.end}")
        self.diagnostics.info(f"Source: {args.source_database}:{args.source_schema}")
        self.diagnostics.info(f"Kafka out: {args.kafka_out}")
        if args.data_type == DataType.TRADE:
            send_trades_to_kafka(args=args, diagnostics=self.diagnostics)
        else:
            raise NotImplementedError(f"Data type {args.data_type} not implemented")
        end = datetime.utcnow()
        self.diagnostics.info(f"Completed in {hms_string((end - start).total_seconds())}")


if __name__ == "__main__":
    PatchKafka().run()
