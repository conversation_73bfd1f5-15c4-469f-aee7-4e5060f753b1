import argparse
import time
from typing import List

from src.octopus.arguments import proxies_arguments_parser, proxies_from_args
from src.utils.application import Application, application_arguments_parser
from src.utils.diagnostics import IGaugeMetric
from src.utils.http import ProxyParams, check_proxy_health
from src.utils.pyroutine import PyRoutine, PyRoutineSystem


def _checker_routine(
    check_interval: float,
    proxies: List[ProxyParams],
    proxy_base_index: int,
    proxy_state: List[bool],
    status_gauge: IGaugeMetric,
    lag_gauge: IGaugeMetric,
) -> PyRoutine:
    env = yield None

    while env.stop_not_requested():
        with env.tick(check_interval):
            for index, proxy in enumerate(proxies):
                proxy_index = proxy_base_index + index
                start_time = time.time()
                is_healthy = check_proxy_health(proxy, env.diagnostics)
                check_duration = time.time() - start_time
                if proxy_state[proxy_index] != is_healthy:
                    env.diagnostics.info(f"{proxy} state changed to {is_healthy}")
                    proxy_state[proxy_index] = is_healthy
                status_gauge.tags({"proxy_address": str(proxy)}).set(1 if is_healthy else 0)
                lag_gauge.tags({"proxy_address": str(proxy)}).set(check_duration)


def run() -> None:
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument("--interval", type=int, default=1)
    arg_parser.add_argument("--group-size", type=int, default=100)
    proxies_arguments_parser(arg_parser)
    application_arguments_parser(arg_parser)
    args = arg_parser.parse_args()

    proxies = proxies_from_args(args)
    if proxies and isinstance(proxies[0], tuple):
        proxies = [proxy_params for proxy_tier, proxy_params in proxies]

    with Application(prometheus=args.prometheus) as app:
        with PyRoutineSystem(app.diagnostics) as system:
            proxy_state = [True for _ in proxies]
            status_gauge = app.diagnostics.gauge("proxy_status", ("proxy_address",))
            lag_gauge = app.diagnostics.gauge("proxy_lag", ("proxy_address",))

            for start in range(0, len(proxies), args.group_size):
                proxy_list = proxies[start : start + args.group_size]
                system.launch(
                    _checker_routine(args.interval, proxy_list, start, proxy_state, status_gauge, lag_gauge),
                    name=f"group-{start}",
                )


if __name__ == "__main__":
    run()
