import multiprocessing

from src.octopus.run import <PERSON>raperParams, StreamingQuoteFeedHandler, run_scraper_application, RESTART_REQUEST_EXIT_CODE


def run_feed_handler():
    run_scraper_application(
        StreamingQuoteFeedHandler,
        ScraperParams(
            db_channel_capacity=32768,
            db_consumer_max_items_per_tick=8192,
            db_consumer_interval=1.0,
            ws_channel_capacity=32768,
            ws_consumer_interval=0.01,
            ws_consumer_max_items_per_tick=128,
            kafka_channel_capacity=32768,
            kafka_consumer_interval=0.05,
            kafka_consumer_max_items_per_tick=1024,
        ),
    )


if __name__ == "__main__":
    multiprocessing.set_start_method("spawn")
    fh_process = multiprocessing.Process(target=run_feed_handler, args=())
    fh_process.start()

    while True:
        while fh_process.exitcode is None:
            fh_process.join(timeout=1)

        if fh_process.exitcode == RESTART_REQUEST_EXIT_CODE:
            fh_process = multiprocessing.Process(target=run_feed_handler, args=())
            fh_process.start()
        else:
            break
