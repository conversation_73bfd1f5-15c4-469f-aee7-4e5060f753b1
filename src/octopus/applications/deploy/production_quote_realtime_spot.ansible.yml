- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_quote_realtime_spot
    file:
      path: "/opt/production_quote_realtime_spot"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_quote_realtime_spot
    template:
      src: "{{ item }}"
      dest: "/opt/production_quote_realtime_spot/{{ item | basename }}"
    with_fileglob: "production_quote_realtime_spot/*"
  - name: pull new images production_quote_realtime_spot
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_quote_realtime_spot"
  - name: build containers production_quote_realtime_spot
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_quote_realtime_spot"
  - name: deploy containers production_quote_realtime_spot
    shell:
      cmd: docker-compose down --remove-orphans && docker-compose up -d
      chdir: "/opt/production_quote_realtime_spot"
