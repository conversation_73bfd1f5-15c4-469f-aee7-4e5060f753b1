- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_book_loader
    file:
      path: "/opt/production_book_loader"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_book_loader
    template:
      src: "{{ item }}"
      dest: "/opt/production_book_loader/{{ item | basename }}"
    with_fileglob: "production_book_loader/*"
  - name: pull new images production_book_loader
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_book_loader"
  - name: build containers production_book_loader
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_book_loader"
  - name: deploy containers production_book_loader
    shell:
      cmd: docker-compose down --remove-orphans && docker-compose up -d
      chdir: "/opt/production_book_loader"
