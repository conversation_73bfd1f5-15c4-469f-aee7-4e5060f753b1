version: "3.7"

services:
  binance-busy-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-busy-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binance-trad-spot-stre-0000-0250:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-trad-spot-stre-0000-0250-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0000:0250
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime[1/5]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binance-trad-spot-stre-0250-0500:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-trad-spot-stre-0250-0500-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0250:0500
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime[2/5]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binance-trad-spot-stre-0500-0750:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-trad-spot-stre-0500-0750-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0500:0750
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime[3/5]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binance-trad-spot-stre-0750-1000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-trad-spot-stre-0750-1000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0750:1000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime[4/5]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binance-trad-spot-stre-1000-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-trad-spot-stre-1000-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance
      --exclude-instruments btc-fdusd floki-usdt pepe-usdt btc-usdt shib-usdt doge-usdt fdusd-usdt meme-usdt slp-usdt eth-usdt
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_4.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 1000:9000
      --markets-per-producer 10
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-trade-realtime[5/5]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 9g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  binanceus-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceus-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Binance.US
      --api-params CIJ7WFCfCKDXhiNYPN6FeMK7Coqu8uCyNMY8LbpFqL7kucDmDb9UvZjQaPmhMvvN
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_35.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 200
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitbank-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitbank-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Bitbank
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_26.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitfinex-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitfinex-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Bitfinex
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_2.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 30
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitflyer-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitflyer-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper bitFlyer
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_7.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bithumb-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bithumb-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Bithumb
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_19.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.2
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-busy-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-busy-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Bitstamp
      --cache-size 65536
      --instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_0.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.06
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Bitstamp
      --cache-size 65536
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_0.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bitstamp-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitstamp-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Bitstamp
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_0.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bullish-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bullish-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Bullish
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_50.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups bullish-hetzner-hetzner-us-trade-realtime,bullish-hetzner-webshare-eu-trade-realtime,bullish-hetzner-smartproxy-trade-realtime
      --rate-limit-multiplier 0.2
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bullish-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bullish-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Bullish
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_50.proto:-1
      --machine {{ inventory_hostname }}
      --market-source-cache-lifetime 60
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups bullish-hetzner-hetzner-us-trade-realtime,bullish-hetzner-webshare-eu-trade-realtime,bullish-hetzner-smartproxy-trade-realtime
      --rate-limit-multiplier 0.02
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Bybit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_42.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-trade-realtime,bybit-prod-webshare-eu-trade-realtime
      --rate-limit-multiplier 0.4
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  bybit-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Bybit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_42.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-trade-realtime,bybit-prod-webshare-eu-trade-realtime
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-btceth-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-btceth-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,hetzner-eu-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.02
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-btcusd-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-btcusd-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --instruments btc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-ethusd-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-ethusd-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --instruments eth-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-ltcusd-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-ltcusd-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --instruments ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-http-000-080:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-http-000-080-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 000:080
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[1/5],hetzner-eu-trade-realtime[1/5],smartproxy-us-hetzner-trade-realtime[1/5]
      --rate-limit-multiplier 0.3
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 4g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-http-080-160:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-http-080-160-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 080:160
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[2/5],hetzner-eu-trade-realtime[2/5],smartproxy-us-hetzner-trade-realtime[2/5]
      --rate-limit-multiplier 0.3
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 4g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-http-160-240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-http-160-240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 160:240
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[3/5],hetzner-eu-trade-realtime[3/5],smartproxy-us-hetzner-trade-realtime[3/5]
      --rate-limit-multiplier 0.3
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 4g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-http-240-320:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-http-240-320-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 240:320
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[4/5],hetzner-eu-trade-realtime[4/5],smartproxy-us-hetzner-trade-realtime[4/5]
      --rate-limit-multiplier 0.3
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 4g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-http-320-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-http-320-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Coinbase
      --exclude-instruments btc-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 320:9000
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[5/5],hetzner-eu-trade-realtime[5/5],smartproxy-us-hetzner-trade-realtime[5/5]
      --rate-limit-multiplier 0.3
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 4g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-000-040:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-000-040-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 000:040
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[1/11],hetzner-eu-trade-realtime[1/11],smartproxy-us-hetzner-trade-realtime[1/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-040-080:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-040-080-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 040:080
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[2/11],hetzner-eu-trade-realtime[2/11],smartproxy-us-hetzner-trade-realtime[2/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-080-120:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-080-120-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 080:120
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[3/11],hetzner-eu-trade-realtime[3/11],smartproxy-us-hetzner-trade-realtime[3/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-120-160:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-120-160-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 120:160
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[4/11],hetzner-eu-trade-realtime[4/11],smartproxy-us-hetzner-trade-realtime[4/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-160-200:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-160-200-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 160:200
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[5/11],hetzner-eu-trade-realtime[5/11],smartproxy-us-hetzner-trade-realtime[5/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-200-240:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-200-240-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 200:240
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[6/11],hetzner-eu-trade-realtime[6/11],smartproxy-us-hetzner-trade-realtime[6/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-240-280:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-240-280-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 240:280
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[7/11],hetzner-eu-trade-realtime[7/11],smartproxy-us-hetzner-trade-realtime[7/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-280-320:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-280-320-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 280:320
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[8/11],hetzner-eu-trade-realtime[8/11],smartproxy-us-hetzner-trade-realtime[8/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-320-360:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-320-360-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 320:360
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[9/11],hetzner-eu-trade-realtime[9/11],smartproxy-us-hetzner-trade-realtime[9/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-360-400:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-360-400-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 360:400
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[10/11],hetzner-eu-trade-realtime[10/11],smartproxy-us-hetzner-trade-realtime[10/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  coinbase-trad-spot-stre-400-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbase-trad-spot-stre-400-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Coinbase
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_1.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 400:9000
      --markets-per-producer 40
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[11/11],hetzner-eu-trade-realtime[11/11],smartproxy-us-hetzner-trade-realtime[11/11]
      --rate-limit-multiplier 0.04
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  cryptocom-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-cryptocom-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Crypto.com
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_48.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 10
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  cryptocom-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-cryptocom-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Crypto.com
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_48.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 100
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  deribit-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-deribit-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Deribit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_37.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  erisx-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-erisx-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper ErisX
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_53.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-trad-spot-http-0000-0900:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-trad-spot-http-0000-0900-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gate.io
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_24.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0000:0900
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[1/4]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-trad-spot-http-0900-1800:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-trad-spot-http-0900-1800-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gate.io
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_24.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 0900:1800
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[2/4]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-trad-spot-http-1800-2700:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-trad-spot-http-1800-2700-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gate.io
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_24.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 1800:2700
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[3/4]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-trad-spot-http-2700-9000:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-trad-spot-http-2700-9000-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gate.io
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_24.proto:-1
      --machine {{ inventory_hostname }}
      --market-range 2700:9000
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime[4/4]
      --rate-limit-multiplier 0.25
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gateio-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Gate.io
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_24.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 100
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime
      --rate-limit-multiplier 0.125
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-busy-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-busy-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gemini
      --instruments btc-usd usdt-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_5.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-hetzner-hetzner-us-trade-realtime,gemini-hetzner-webshare-eu-trade-realtime,gemini-hetzner-hetzner-eu-trade-realtime
      --rate-limit-multiplier 0.1
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Gemini
      --exclude-instruments btc-usd usdt-usd eth-usd ltc-usd
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_5.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-hetzner-hetzner-us-trade-realtime,gemini-hetzner-webshare-eu-trade-realtime,gemini-hetzner-hetzner-eu-trade-realtime
      --rate-limit-multiplier 0.2
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  gemini-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gemini-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Gemini
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_5.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 1
      --prometheus 0.0.0.0:8080
      --proxy-groups gemini-hetzner-hetzner-us-trade-realtime,gemini-hetzner-webshare-eu-trade-realtime,gemini-hetzner-hetzner-eu-trade-realtime
      --rate-limit-multiplier 0.2
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  hitbtc-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-hitbtc-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper HitBTC
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_11.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  huobi-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Huobi
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_10.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  itbit-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-itbit-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper itBit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_28.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  itbit-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-itbit-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper itBit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_28.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  kraken-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Kraken
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_6.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 100
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,hetzner-eu-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.05
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  kucoin-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kucoin-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper KuCoin
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_39.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 100
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  lbank-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-lbank-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper LBank
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_12.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 5
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  lbank-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-lbank-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper LBank
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_12.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  lmax-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-lmax-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper LMAX
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_41.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --streaming-api-params {{ inventory_hostname }}:scrapers-production-1,scrapers-production-2
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  mexc-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-mexc-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper MEXC
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_49.proto:-1
      --machine {{ inventory_hostname }}
      --markets-per-producer 20
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  okex-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper OKEx
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_9.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 0
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.1
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  okex-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper OKEx
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_9.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-trad-spot-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-trad-spot-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_trade_scraper Poloniex
      --exclude-proxies *************:20000
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_20.proto:-1
      --machine {{ inventory_hostname }}
      --poll-interval 30
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-trade-realtime,smartproxy-us-hetzner-trade-realtime
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  poloniex-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-poloniex-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Poloniex
      --exclude-proxies *************:20000
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_20.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]

  upbit-trad-spot-stre:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-upbit-trad-spot-stre-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.streaming_trade_scraper Upbit
      --kafka-out-proto
      kafka-{{ inventory_hostname }}
      --kafka-topic-retention-bytes trades_21.proto:-1
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --spot
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-market-trades-realtime, kafka_coinmetrics-kafka-zookeeper-local]


networks:
  coinmetrics-market-trades-realtime:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
