- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_book_realtime_futures_group2
    file:
      path: "/opt/production_book_realtime_futures_group2"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_book_realtime_futures_group2
    template:
      src: "{{ item }}"
      dest: "/opt/production_book_realtime_futures_group2/{{ item | basename }}"
    with_fileglob: "production_book_realtime_futures_group2/*"
  - name: pull new images production_book_realtime_futures_group2
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_book_realtime_futures_group2"
  - name: build containers production_book_realtime_futures_group2
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_book_realtime_futures_group2"
  - name: deploy containers production_book_realtime_futures_group2
    shell:
      cmd: docker-compose down --remove-orphans && docker-compose up -d
      chdir: "/opt/production_book_realtime_futures_group2"
