- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_proxy_check
    file:
      path: "/opt/production_proxy_check"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_proxy_check
    template:
      src: "{{ item }}"
      dest: "/opt/production_proxy_check/{{ item | basename }}"
    with_fileglob: "production_proxy_check/*"
  - name: pull new images production_proxy_check
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_proxy_check"
  - name: build containers production_proxy_check
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_proxy_check"
  - name: deploy containers production_proxy_check
    shell:
      cmd: "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"
      chdir: "/opt/production_proxy_check"
