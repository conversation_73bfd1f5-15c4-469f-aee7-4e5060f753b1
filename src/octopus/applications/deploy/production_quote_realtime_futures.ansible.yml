- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_quote_realtime_futures
    file:
      path: "/opt/production_quote_realtime_futures"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_quote_realtime_futures
    template:
      src: "{{ item }}"
      dest: "/opt/production_quote_realtime_futures/{{ item | basename }}"
    with_fileglob: "production_quote_realtime_futures/*"
  - name: pull new images production_quote_realtime_futures
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_quote_realtime_futures"
  - name: build containers production_quote_realtime_futures
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_quote_realtime_futures"
  - name: deploy containers production_quote_realtime_futures
    shell:
      cmd: docker-compose down --remove-orphans && docker-compose up -d
      chdir: "/opt/production_quote_realtime_futures"
