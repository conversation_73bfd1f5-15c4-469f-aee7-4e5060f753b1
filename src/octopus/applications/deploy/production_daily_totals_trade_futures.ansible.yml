- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_daily_totals_trade_futures
    file:
      path: "/opt/production_daily_totals_trade_futures"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_daily_totals_trade_futures
    template:
      src: "{{ item }}"
      dest: "/opt/production_daily_totals_trade_futures/{{ item | basename }}"
    with_fileglob: "production_daily_totals_trade_futures/*"
  - name: pull new images production_daily_totals_trade_futures
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_daily_totals_trade_futures"
  - name: build containers production_daily_totals_trade_futures
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_daily_totals_trade_futures"
  - name: deploy containers production_daily_totals_trade_futures
    shell:
      cmd: "docker-compose down --remove-orphans && docker-compose up -d {{ service }}"
      chdir: "/opt/production_daily_totals_trade_futures"
