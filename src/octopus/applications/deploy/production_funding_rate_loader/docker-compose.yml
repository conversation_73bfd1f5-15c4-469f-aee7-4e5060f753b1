version: "3.7"

services:
  handler-binance-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-binance-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader Binance
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-bitfinex-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-bitfinex-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader Bitfinex
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-bitmex-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-bitmex-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader BitMEX
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-deribit-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-deribit-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader Deribit
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-dydx-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-dydx-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader dYdX
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-huobi-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-huobi-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader Huobi
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-kraken-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-kraken-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader Kraken
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  handler-okex-fund-loader:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "feed-handler-okex-fund-loader-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.funding_rate_loader OKEx
      --kafka-in kafka-{{ inventory_hostname }}
      --machine {{ inventory_hostname }}
      --postgres-out-futures pgbouncer:6432:postgres-futures:swt:swt+local-postgres-fund-real-futu-{{ inventory_hostname }}:5432:local:local:local
      --prometheus 0.0.0.0:8080
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates, kafka_coinmetrics-kafka-zookeeper-local, local-postgres-network-funding-rate]

  local-postgres-fund-real-futu:
    image: "postgres:11-alpine"
    container_name: "local-postgres-fund-real-futu-{{ inventory_hostname }}"
    restart: unless-stopped
    volumes: ['/opt/market-data-fund-real-futu/:/var/lib/postgresql/data:rw']
    ports: ['127.0.0.1:11011:5432']
    environment: [POSTGRES_DB=local, POSTGRES_USER=local, POSTGRES_HOST_AUTH_METHOD=trust]
    deploy:
      resources:
        limits:
          memory: 10g
    networks: [local-postgres-network-funding-rate]


networks:
  coinmetrics-local-pgbouncer:
    external: true
  coinmetrics-market:
    external: true
    driver: overlay
  coinmetrics-market-funding-rates:
    external: true
    driver: overlay
  kafka_coinmetrics-kafka-zookeeper-local:
    external: true
  local-postgres-network-funding-rate:
    driver: bridge
