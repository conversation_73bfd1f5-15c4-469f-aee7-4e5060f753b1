version: "3.7"

services:
  binance-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binance-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Binance
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-metadata-realtime
      --rate-limit-multiplier 0.2
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  binanceagg-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-binanceagg-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper BinanceAgg
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups binance-hetzner-hetzner-eu-metadata-realtime
      --rate-limit-multiplier 0.2
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bitfinex-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitfinex-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Bitfinex
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bitflyer-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitflyer-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper bitFlyer
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bitget-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitget-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Bitget
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bitmex-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bitmex-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper BitMEX
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bullish-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bullish-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Bullish
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups bullish-hetzner-webshare-eu-metadata-realtime,bullish-hetzner-smartproxy-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  bybit-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-bybit-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Bybit
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups bybit-ny5-hetzner-eu-metadata-realtime,bybit-prod-webshare-eu-metadata-realtime
      --rate-limit-multiplier 0.16
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  cme-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-cme-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper CME
      --database pgbouncer:6432:postgres-futures:swt:swt
      --api-params API_COINMETRICS_REF 7SKBXE3ZVAFZHYY2OARJCTFJEVAMD5EEDEBNQCQ= ******************** k1WDwcvZyrwiiPv5MDA56ZkoJ5EUQ2m4v0UAdNqK
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  coinbaseder-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbaseder-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper CoinbaseDer
      --database pgbouncer:6432:postgres-futures:swt:swt
      --api-params {{ inventory_hostname }}:scrapers-production-1,scrapers-production-2
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --poll-interval 3600
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.05
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  coinbaseint-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-coinbaseint-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper CoinbaseInt
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups coinbase-int-ny5-hetzner-eu-metadata-realtime
      --rate-limit-multiplier 0.05
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  cryptocom-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-cryptocom-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Crypto.com
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  deribit-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-deribit-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Deribit
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.05
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  dydx-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-dydx-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper dYdX
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  erisx-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-erisx-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper ErisX
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  gateio-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gateio-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Gate.io
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-eu-trade-realtime
      --rate-limit-multiplier 0.02
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  gfox-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-gfox-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper GFOX
      --database pgbouncer:6432:postgres-futures:swt:swt
      --api-params prod
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --poll-interval 3600
      --prometheus 0.0.0.0:8080
      --rate-limit-multiplier 0.5
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  hitbtc-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-hitbtc-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper HitBTC
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  huobi-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-huobi-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Huobi
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  kraken-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kraken-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper Kraken
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  kucoin-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-kucoin-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper KuCoin
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  mexc-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-mexc-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper MEXC
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.25
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]

  okex-meta-futu-http:
    image: "registry.gitlab.com/coinmetrics/feed-handlers/octopus:{{ tag }}"
    container_name: "prod-okex-meta-futu-http-{{ inventory_hostname }}"
    restart: unless-stopped
    entrypoint: python -m src.octopus.applications.http_metadata_scraper OKEx
      --database pgbouncer:6432:postgres-futures:swt:swt
      --exclusive-proxies
      --futures
      --machine {{ inventory_hostname }}
      --prometheus 0.0.0.0:8080
      --proxy-groups hetzner-us-metadata-realtime,smartproxy-us-hetzner-metadata-realtime
      --rate-limit-multiplier 0.15
      --environment prod
    deploy:
      resources:
        limits:
          memory: 3g
    networks: [coinmetrics-local-pgbouncer, coinmetrics-market-funding-rates]


networks:
  coinmetrics-local-pgbouncer:
    external: true
  coinmetrics-market-funding-rates:
    external: true
    driver: overlay
