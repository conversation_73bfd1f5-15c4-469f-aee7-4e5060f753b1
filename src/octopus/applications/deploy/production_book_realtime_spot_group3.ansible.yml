- hosts: "{{ hosts }}"
  become: yes
  environment:
    COMPOSE_HTTP_TIMEOUT: 120
  tasks:
  - name: create directory production_book_realtime_spot_group3
    file:
      path: "/opt/production_book_realtime_spot_group3"
      state: directory
      mode: 0755
  - name: copy docker-compose file production_book_realtime_spot_group3
    template:
      src: "{{ item }}"
      dest: "/opt/production_book_realtime_spot_group3/{{ item | basename }}"
    with_fileglob: "production_book_realtime_spot_group3/*"
  - name: pull new images production_book_realtime_spot_group3
    shell:
      cmd: docker-compose pull
      chdir: "/opt/production_book_realtime_spot_group3"
  - name: build containers production_book_realtime_spot_group3
    shell:
      cmd: docker-compose build
      chdir: "/opt/production_book_realtime_spot_group3"
  - name: deploy containers production_book_realtime_spot_group3
    shell:
      cmd: docker-compose down --remove-orphans && docker-compose up -d
      chdir: "/opt/production_book_realtime_spot_group3"
