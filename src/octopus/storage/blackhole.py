from datetime import datetime
from typing import List, Optional, Tuple

from src.octopus.data import (
    FundingRate,
    FuturesContract,
    InstitutionMetrics,
    Liquidation,
    Market,
    OpenInterest,
    OptionContract,
    SpotContract,
    Trade,
)
from src.octopus.exchange.api import IExchangeHttpApi
from src.octopus.storage.api import (
    IFundingRateStorage,
    IFuturesMetadataStorage,
    IInstitutionMetricsStorage,
    ILiquidationStorage,
    IOpenInterestStorage,
    IOptionMetadataStorage,
    ISpotMetadataStorage,
    ITradeStorage,
)
from src.utils.http import IHttpClient


class BlackholeTradeStorage(ITradeStorage):
    def __init__(self, api: IExchangeHttpApi, client: IHttpClient) -> None:
        self._api = api
        self._client = client

    def __repr__(self) -> str:
        return "blackhole"

    def save_trades_from_historical_scraper(self, scraper_replica_id: int, trades: List[Trade], id_ordering: bool) -> None:
        return

    def get_historical_scraper_state(self, scraper_replica_id: int, market: Market) -> Optional[Trade]:
        last_trades = self._api.last_trades(self._client, market.instrument)
        if len(last_trades) > 0:
            return Trade(market, last_trades[0])
        else:
            return None


class BlackholeLiquidationStorage(ILiquidationStorage):
    def __init__(self, api: IExchangeHttpApi, client: IHttpClient) -> None:
        self._api = api
        self._client = client

    def __repr__(self) -> str:
        return "blackhole"

    def save_liquidations_from_historical_scraper(
        self, scraper_replica_id: int, liquidations: List[Liquidation], id_ordering: bool
    ) -> None:
        return

    def get_historical_scraper_state(self, scraper_replica_id: int, market: Market) -> Optional[Liquidation]:
        if len(last_liquidations := self._api.last_liquidations(self._client, market.instrument)) > 0:
            return Liquidation(market, last_liquidations[0])

        return None


class BlackholeFundingRateStorage(IFundingRateStorage):
    def __init__(self, api: IExchangeHttpApi, client: IHttpClient) -> None:
        self._api = api
        self._client = client

    def __repr__(self) -> str:
        return "blackhole"

    def save_funding_rates_from_historical_scraper(
        self, scraper_replica_id: int, funding_rates: List[FundingRate], id_ordering: bool = False
    ) -> None:
        return

    def get_historical_scraper_state(self, scraper_replica_id: int, market: Market) -> Optional[FundingRate]:
        if len(last_funding_rate := self._api.last_funding_rates(self._client, market.instrument)) > 0:
            return FundingRate(market, last_funding_rate[0])

        return None


class BlackholeOpenInterestStorage(IOpenInterestStorage):
    def __init__(self, api: IExchangeHttpApi, client: IHttpClient) -> None:
        self._api = api
        self._client = client

    def __repr__(self) -> str:
        return "blackhole"

    def save_open_interest_from_historical_scraper(
        self, scraper_replica_id: int, open_interest: List[OpenInterest], id_ordering: bool = False
    ) -> None:
        return

    def get_historical_scraper_state(self, scraper_replica_id: int, market: Market) -> Optional[OpenInterest]:
        if len(last_open_interest := self._api.last_open_interest(self._client, market.instrument)) > 0:
            return OpenInterest(market, last_open_interest[0])

        return None


class BlackholeSpotMetadataStorage(ISpotMetadataStorage):
    def __repr__(self) -> str:
        return "blackhole"

    def save_spot_metadata(self, contracts: List[SpotContract]) -> None:
        pass


class BlackholeFuturesMetadataStorage(IFuturesMetadataStorage):
    def __repr__(self) -> str:
        return "blackhole"

    def save_futures_metadata(self, contracts: List[FuturesContract]) -> None:
        pass


class BlackholeOptionMetadataStorage(IOptionMetadataStorage):
    def __repr__(self) -> str:
        return "blackhole"

    def save_option_metadata(self, contracts: List[OptionContract]) -> None:
        pass

    def get_expired_markets_with_no_settlement_price(self, exchange_id: int) -> List[Tuple[str, datetime]]:
        return []


class BlackholeInstitutionMetricsStorage(IInstitutionMetricsStorage):
    def __repr__(self) -> str:
        return "blackhole"

    def save_institution_metrics(self, im: InstitutionMetrics) -> None:
        pass

    def get_max_date(self, institution_id: int) -> Optional[datetime]:
        return None

    def get_max_fund_date(self, institution_id: int, short_name: str) -> Optional[datetime]:
        return None
