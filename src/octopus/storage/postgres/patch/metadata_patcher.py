from datetime import datetime
from io import <PERSON><PERSON>
from typing import Any, List, Optional

import psycopg2
from dateutil import rrule
from psycopg2 import sql

from src.octopus.data import MarketType
from src.octopus.storage.postgres.patch.db_patcher import DB<PERSON>atcher, DBPatcherInterface, TableColumns
from src.utils.diagnostics import Diagnostics
from src.utils.postgres import PgConnectionParams


class MetadataPatcher(DBPatcherInterface):
    def __init__(
        self,
        exchange_id: int,
        source_connection_params: PgConnectionParams,
        target_connection_params: PgConnectionParams,
        source_schema_name: str,
        target_schema_name: str,
        market_type: MarketType,
        diagnostics: Diagnostics,
        batch_row_size: int = 500,
        copy_wait_time: float = 1,
        max_writes_per_second: int = 500,
        source_table_columns: Optional[TableColumns] = None,
        target_table_columns: Optional[TableColumns] = None,
    ) -> None:
        self._exchange_id = exchange_id
        self._market_type = market_type
        super().__init__(
            source_connection_params=source_connection_params,
            source_schema_name=source_schema_name,
            target_connection_params=target_connection_params,
            target_schema_name=target_schema_name,
            diagnostics=diagnostics,
            batch_row_size=batch_row_size,
            copy_wait_time=copy_wait_time,
            max_writes_per_second=max_writes_per_second,
            source_table_columns=source_table_columns,
            target_table_columns=target_table_columns,
        )

    @property
    def _exchange_id_column(self) -> str:
        if self._market_type == MarketType.SPOT:
            return "market_exchange_id"
        else:
            return "contract_exchange_id"

    @property
    def _source_datetime_column(self) -> str:
        if self._market_type == MarketType.SPOT:
            return "market_listing_date"
        else:
            return "contract_listing_date"

    @property
    def _target_datetime_column(self) -> str:
        return self._source_datetime_column

    @property
    def _source_table_name(self) -> str:
        return f"{self._market_type.name.lower()}_metadata"

    @property
    def _target_table_name(self) -> str:
        return self._source_table_name

    def unique_key_from_row(self, row: List[Any], columns: TableColumns) -> str:
        if self._market_type == MarketType.SPOT:
            return DBPatcher.unique_metadata_spot_key_from_row(row=row, columns=columns)
        elif self._market_type == MarketType.FUTURES:
            return DBPatcher.unique_metadata_futures_key_from_row(row=row, columns=columns)
        elif self._market_type == MarketType.OPTION:
            return DBPatcher.unique_metadata_option_key_from_row(row=row, columns=columns)
        else:
            raise ValueError(f"Unsupported market type: {self._market_type}")

    def copy_rows_between_to_buffer(
        self,
        conn: psycopg2.extensions.connection,
        table_name: str,
        schema_name: str,
        datetime_column: str,
        begin: datetime,
        end: datetime,
        columns: TableColumns,
    ) -> StringIO:
        query = sql.SQL(
            "COPY (SELECT {fields} FROM {schema_name}.{table_name} "
            "WHERE {datetime_column} >= {begin} AND {datetime_column} < {end} AND {exchange_id_column} = {exchange_id} "
            "ORDER BY {datetime_column} ASC, {database_time} ASC) TO STDOUT WITH CSV HEADER;"
        ).format(
            fields=sql.SQL(", ").join(map(sql.Identifier, columns.ordered_column_names)),
            schema_name=sql.Identifier(schema_name),
            table_name=sql.Identifier(table_name),
            datetime_column=sql.Identifier(datetime_column),
            begin=sql.Literal(begin),
            end=sql.Literal(end),
            database_time=sql.Identifier("contract_database_time"),
            exchange_id_column=sql.Identifier(self._exchange_id_column),
            exchange_id=sql.Literal(self._exchange_id),
        )
        return DBPatcher.copy_to_buffer(conn, query)

    @staticmethod
    def _get_batch_rule(begin: datetime) -> rrule.rrule:
        return rrule.rrule(rrule.YEARLY, byminute=0, bysecond=0, dtstart=begin)
