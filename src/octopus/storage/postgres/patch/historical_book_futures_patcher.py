from typing import Any, List

from src.octopus.data import MarketType
from src.octopus.storage.postgres.patch.db_patcher import DBPatcher
from src.octopus.storage.postgres.patch.historical_book_spot_patcher import HistoricalBookSpotPatcher
from src.octopus.storage.postgres.patch.historical_patcher import IHistoricalPatcher


class HistoricalBookFuturesPatcher(HistoricalBookSpotPatcher, IHistoricalPatcher):
    def _unique_key_from_row(self, row: List[Any]) -> str:
        return DBPatcher.unique_book_futures_key_from_row(row=row, columns=self.table_columns)

    @property
    def _market_type(self) -> MarketType:
        return MarketType.FUTURES
