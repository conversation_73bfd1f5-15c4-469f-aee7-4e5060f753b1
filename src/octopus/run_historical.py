import argparse
import time
from argparse import Namespace
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import Callable, Generic, List, Optional, Type, TypeVar

from src.octopus.arguments import (
    Disable,
    historical_scraper_arguments_parser,
    proxies_from_args,
    replica_id_from_args,
    scraper_arguments_parser,
)
from src.octopus.constants import DATETIME_1970
from src.octopus.consumer import (
    HistoryDatabaseConsumer,
    HistoryIdRangeDatabaseConsumer,
    HistoryTimeRangeDatabaseConsumer,
    IBatchTimeRangeConsumer,
    KafkaChannelConsumer,
)
from src.octopus.data import (
    FundingRate,
    FundingRateData,
    HistoryMarker,
    Liquidation,
    LiquidationData,
    Market,
    MarketType,
    OpenInterest,
    OpenInterestData,
    Trade,
    TradeData,
)
from src.octopus.exchange.api import ExchangeMarkets, IExchangeHttpApi, TraversalMethod
from src.octopus.exchange.api_factory import exchange_http_api
from src.octopus.exchange.deribit import EXCHANGE_NAME as DERIBIT_EXCHANGE_NAME
from src.octopus.feed_protocol_pb2 import TradeEntry
from src.octopus.http_producer import HistoryProducer
from src.octopus.market_control import MarketSource, ProducerData, market_controller
from src.octopus.market_filter import IncludeExcludeMarketFilter
from src.octopus.storage.api import IFundingRateStorage, ILiquidationStorage, IOpenInterestStorage, ITradeStorage
from src.octopus.storage.blackhole import (
    BlackholeFundingRateStorage,
    BlackholeLiquidationStorage,
    BlackholeOpenInterestStorage,
    BlackholeTradeStorage,
)
from src.octopus.storage.postgres.funding_rate import PostgresFundingRateStorage
from src.octopus.storage.postgres.liquidations import PostgresLiquidationStorage
from src.octopus.storage.postgres.open_interest import PostgresFuturesOpenInterestStorage
from src.octopus.storage.postgres.trade import (
    DeribitPostgresTradeStorage,
    PostgresFuturesTradeStorage,
    PostgresOptionTradeStorage,
    PostgresSpotTradeStorage,
)
from src.octopus.streaming_protocols.protobuf import message_from_trade
from src.octopus.utils import (
    get_funding_rate_history_marker,
    get_liquidation_history_marker,
    get_open_interest_history_marker,
    get_trade_history_marker,
)
from src.resources.exchange import glib_exchange
from src.utils.application import Application
from src.utils.diagnostics import Diagnostics, ICounterMetric
from src.utils.http import IHttpClient, RateLimitedProxyHttpClient
from src.utils.kafka import set_up_kafka_topic
from src.utils.proxies import report_desired_proxies_func
from src.utils.pyroutine import (
    IBatchConsumer,
    IBatchProducer,
    IBlockingChannel,
    IPyRoutineSystem,
    ProcessBatch,
    PyRoutineSystem,
    chain_transform_batch,
    continuous_batch_producer,
    periodic_batch_consumer,
    send_batch_to_blocking_channels,
    send_batch_to_channels,
    sequence_batch,
)

D_T = TypeVar("D_T")  # scraped data type: Trade, Book, OpenInterest etc.
S_D_T = TypeVar("S_D_T")  # scraped sub data type: TradeData, LiquidationData, OpenInterestData etc.
S_T = TypeVar("S_T")  # type of storage.
N_T = TypeVar("N_T")  # # representation of D_T suitable to be sent over network.: TradeEntry


class HistoryScraper(Generic[D_T, S_D_T, S_T]):
    def __init__(
        self,
        app: Application,
        args: Namespace,
        exchange_id: int,
        http_api: IExchangeHttpApi,
        client: IHttpClient,
        diagnostics: Diagnostics,
        replica_id: int,
    ):
        self.app = app
        self.args = args
        self.exchange_id = exchange_id
        self.http_api = http_api
        self.http_client = client
        self.diagnostics = diagnostics
        self.replica_id = replica_id
        self._storage: List[S_T] = []

        self.db_channel: Optional[IBlockingChannel] = None
        self.kafka_channel: Optional[IBlockingChannel] = None

        self.market_type: MarketType
        if bool(args.spot):
            self.market_type = MarketType.SPOT
        elif bool(args.futures):
            self.market_type = MarketType.FUTURES
        elif bool(args.option):
            self.market_type = MarketType.OPTION
        else:
            raise Exception("Unspecified MarketType")

    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = argparse.ArgumentParser()
        scraper_arguments_parser(arg_parser, Disable.PG_CONSUMER | Disable.WS_CONSUMER)
        historical_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def run(self, system: IPyRoutineSystem) -> None:
        if self.args.start_date is None and self.args.start_id is None:
            self.run_regular(system)
        elif self.args.start_date is not None:
            self.run_time_range(system)
        else:
            self.run_id_range(system)

    def run_regular(self, system: IPyRoutineSystem) -> None:
        """
        Batch is generated by producer, sent to channels via processor and consumed by consumer on the other end of
        each channel. This setup should be initialized backwards.
        """
        self.init_channels(system)
        self.launch_consumers(system, self.construct_consumer)

        # producers are launched by market controller dynamically
        market_source = self.get_market_source()
        system.launch(
            market_controller(market_source, self.producer, markets_per_producer=1, refresh_interval=5.0),
            name="market_controller",
        )

    def run_time_range(self, system: IPyRoutineSystem) -> None:
        if self.id_ordering:
            self.diagnostics.critical("--start-date is not supported for ID history traversal")
            exit(1)

        self.init_channels(system)
        market_source = self.get_market_source()
        markets = market_source.get_markets()

        if isinstance(markets, str):
            self.diagnostics.critical("failed to fetch markets: {}".format(markets))
            exit(1)

        consumer = self.construct_time_range_consumer(markets)
        system.launch(periodic_batch_consumer(consumer, self.db_channel.inbound(), self.args.db_queue_size, 1.0), name="storage")

        for market in markets:
            system.launch(
                continuous_batch_producer(
                    self.construct_time_range_history_producer(market), send_batch_to_channels([self.db_channel.outbound()])
                ),
                name=str(market),
            )

        self._request_stop_when_finished(system, consumer)

    def run_id_range(self, system: IPyRoutineSystem) -> None:
        if not self.id_ordering:
            self.diagnostics.critical("--start-id is not supported for TIME history traversal")
            exit(1)

        self.init_channels(system)
        market_source = self.get_market_source()
        markets = market_source.get_markets()

        if isinstance(markets, str):
            self.diagnostics.critical("failed to fetch markets: {}".format(markets))
            exit(1)

        consumer = self.construct_id_range_consumer(markets)
        system.launch(periodic_batch_consumer(consumer, self.db_channel.inbound(), self.args.db_queue_size, 1.0), name="storage")

        for market in markets:
            system.launch(
                continuous_batch_producer(
                    self.construct_id_range_history_producer(market), send_batch_to_channels([self.db_channel.outbound()])
                ),
                name=str(market),
            )

        self._request_stop_when_finished(system, consumer)

    def init_channels(self, system: IPyRoutineSystem) -> None:
        self.db_channel = system.blocking_channel(self.args.db_queue_size, "DB channel")
        if self.args.kafka_out_proto:
            kafka_channel_capacity = 32768
            self.kafka_channel = system.blocking_channel(kafka_channel_capacity, "Kafka channel")

    def launch_consumers(self, system: IPyRoutineSystem, construct_consumer: Callable[[], IBatchConsumer[D_T]]) -> None:
        system.launch(
            periodic_batch_consumer(construct_consumer(), self.db_channel.inbound(), self.args.db_queue_size, 1.0),
            name="DB storage",
        )

        if self.kafka_channel:
            system.launch(
                periodic_batch_consumer(
                    KafkaChannelConsumer(
                        addresses=self.args.kafka_out_proto,
                        kafka_topic_name=self.kafka_topic_name_proto,
                        args=self.args,
                        diagnostics=self.diagnostics,
                    ),
                    self.kafka_channel.inbound(),
                    max_batch_consumed_per_interval=8192,
                    interval=1.0,
                ),
                name="Kafka storage",
            )
            if self.args.kafka_topic_retention_bytes:
                set_up_kafka_topic(self.args.kafka_topic_retention_bytes, self.args.kafka_out_proto, self.diagnostics)

    def get_market_source(self) -> MarketSource:
        reqs_per_second_limit = glib_exchange().http_api_rate_limit(self.exchange_id) * self.args.rate_limit_multiplier
        reqs_per_instrument = self.http_api.get_requests_per_instruments(self.scraper_type_tag)
        report_desired_proxies = report_desired_proxies_func(
            desired_proxies_gauge=self.diagnostics.gauge("desired_proxies_count"),
            requests_per_instrument=reqs_per_instrument,
            requests_per_second_limit=reqs_per_second_limit,
        )

        return MarketSource(
            self.exchange_id,
            self.market_type,
            self.markets_getter(),
            IncludeExcludeMarketFilter(self.market_type, self.args.instruments, self.args.exclude_instruments),
            self.diagnostics,
            report_desired_proxies,
        )

    def producer(self, markets: List[Market]) -> ProducerData:
        market = markets[0]
        return ProducerData(
            routine=continuous_batch_producer(self.construct_history_producer(market), self.processor()),
            name=str(market.instrument),
        )

    def processor(self) -> ProcessBatch[D_T]:
        processors: List[ProcessBatch[D_T]] = []

        if self.db_channel:
            processors.append(send_batch_to_blocking_channels([self.db_channel.outbound()]))

        if self.kafka_channel:
            processors.append(
                chain_transform_batch(
                    transforms=[self.transform_to_protobuf],
                    process=send_batch_to_blocking_channels([self.kafka_channel.outbound()]),
                )
            )

        return sequence_batch(processors)

    def _request_stop_when_finished(self, system: IPyRoutineSystem, consumer: IBatchTimeRangeConsumer):
        while True:
            time.sleep(5)
            unfinished_markets = consumer.get_unfinished()

            if len(unfinished_markets) > 0:
                self.diagnostics.info("waiting for {} markets".format(len(unfinished_markets)))
            else:
                self.diagnostics.info("collection complete")
                system.request_stop()
                break

    def item_received_counter(self) -> ICounterMetric:
        return self.diagnostics.counter("scraper_items_received")

    def transform_to_protobuf(self, items: List[D_T], diagnostics: Diagnostics) -> List[N_T]:
        raise NotImplementedError

    def construct_consumer(self) -> IBatchConsumer[D_T]:
        raise NotImplementedError

    def construct_id_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[D_T, Market]:
        raise NotImplementedError

    def construct_time_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[D_T, Market]:
        raise NotImplementedError

    def construct_history_producer(self, market: Market) -> IBatchProducer[D_T]:
        raise NotImplementedError

    def construct_id_range_history_producer(self, market: Market) -> IBatchProducer[D_T]:
        raise NotImplementedError

    def construct_time_range_history_producer(self, market: Market) -> IBatchProducer[D_T]:
        raise NotImplementedError

    def obtain_exchange_last_entity(self, market: Market) -> Optional[S_D_T]:
        raise NotImplementedError

    def last_entity_in_storage(self, market: Market) -> Optional[D_T]:
        raise NotImplementedError

    @property
    def storage(self) -> List[S_T]:
        raise NotImplementedError

    @property
    def id_ordering(self) -> bool:
        raise NotImplementedError

    @property
    def kafka_topic_name_proto(self) -> str:
        raise NotImplementedError

    @property
    def scraper_type_tag(self) -> str:
        raise NotImplementedError

    def markets_getter(self) -> Callable[[], ExchangeMarkets]:
        if self.market_type == MarketType.SPOT:
            return lambda: self.http_api.spot_markets(self.http_client)
        if self.market_type == MarketType.FUTURES:
            return lambda: self.http_api.historical_futures_markets(self.http_client)
        if self.market_type == MarketType.OPTION:
            return lambda: self.http_api.option_markets(self.http_client)
        else:
            raise NotImplementedError("None of spot/futures/option arguments specified.")


class HistoryTradesScraper(HistoryScraper[Trade, TradeData, ITradeStorage]):
    def transform_to_protobuf(self, items: List[Trade], diagnostics: Diagnostics) -> List[TradeEntry]:
        return [message_from_trade(trade) for trade in items]

    def construct_history_producer(self, market: Market) -> IBatchProducer[Trade]:
        last_trade = self.last_entity_in_storage(market)
        return HistoryProducer[Trade, TradeData](
            market,
            self.args.sleep_time,
            self.http_api.trade_history_traversal(self.http_client, market.instrument),
            get_trade_history_marker(last_trade.data if last_trade else None),
            lambda m, d: Trade(m, d),
            lambda t: t.trade_id,
            lambda t: t.time,
            lambda: self.obtain_exchange_last_entity(market),
            self.app.diagnostics.counter("scraper_items_received"),
            self.app.diagnostics.gauge("scraped_market_count"),
        )

    def construct_id_range_history_producer(self, market: Market) -> IBatchProducer[Trade]:
        return HistoryProducer[Trade, TradeData](
            market,
            self.args.sleep_time,
            self.http_api.trade_history_traversal(self.http_client, market.instrument),
            HistoryMarker(self.args.start_id - 1, DATETIME_1970),
            lambda m, d: Trade(m, d),
            lambda t: t.trade_id,
            lambda t: t.time,
            stop_entry_marker=HistoryMarker(self.args.end_id, datetime.now(UTC)),
        )

    def construct_time_range_history_producer(self, market: Market) -> IBatchProducer[Trade]:
        return HistoryProducer[Trade, TradeData](
            market,
            self.args.sleep_time,
            self.http_api.trade_history_traversal(self.http_client, market.instrument),
            HistoryMarker(0, self.args.start_date - timedelta(microseconds=1)),
            lambda m, d: Trade(m, d),
            lambda t: t.trade_id,
            lambda t: t.time,
            stop_entry_marker=HistoryMarker(0, self.args.end_date),
        )

    def construct_consumer(self) -> IBatchConsumer[Trade]:
        return HistoryDatabaseConsumer[Trade](
            [s.save_trades_from_historical_scraper for s in self.storage], self.replica_id, self.id_ordering, "trades"
        )

    def construct_id_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[Trade, Market]:
        return HistoryIdRangeDatabaseConsumer[Trade](
            [s.save_trades for s in self.storage], self.args.end_id, markets, lambda t: t.data.trade_id, lambda t: t.market
        )

    def construct_time_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[Trade, Market]:
        return HistoryTimeRangeDatabaseConsumer[Trade](
            [s.save_trades for s in self.storage], self.args.end_date, markets, lambda t: t.data.time, lambda t: t.market
        )

    def obtain_exchange_last_entity(self, market: Market) -> Optional[TradeData]:
        last_trades = self.http_api.last_trades(self.http_client, market.instrument)
        return last_trades[-1] if len(last_trades) > 0 else None

    def last_entity_in_storage(self, market: Market) -> Optional[Trade]:
        return self.storage[0].get_historical_scraper_state(self.replica_id, market)

    @property
    def storage(self) -> List[ITradeStorage]:
        if len(self._storage) == 0:
            # order is important here - whichever DB is first in the list will be used for reads when both are set
            if self.args.exch_database:
                if self.args.spot:
                    if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                        exch_storage = DeribitPostgresTradeStorage(
                            MarketType.SPOT, self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                    else:
                        exch_storage: ITradeStorage = PostgresSpotTradeStorage(
                            self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                elif self.args.futures:
                    if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                        exch_storage = DeribitPostgresTradeStorage(
                            MarketType.FUTURES, self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                    else:
                        exch_storage = PostgresFuturesTradeStorage(
                            self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                elif self.args.option:
                    if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                        exch_storage = DeribitPostgresTradeStorage(
                            MarketType.OPTION, self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                    else:
                        exch_storage = PostgresOptionTradeStorage(
                            self.exchange_id, self.args.exch_database, diagnostics=self.diagnostics
                        )
                else:
                    raise NotImplementedError("No spot/futures/option arguments set")
                self._storage.append(exch_storage)

            if len(self._storage) == 0:  # None means Blackhole
                self._storage.append(BlackholeTradeStorage(self.http_api, self.http_client))

        return self._storage

    @property
    def id_ordering(self) -> bool:
        return self.http_api.trade_history_traversal_method() == TraversalMethod.ID

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"trades_{exchange_id}.proto"

    @property
    def scraper_type_tag(self) -> str:
        return "trades_history"


class HistoryLiquidationsScraper(HistoryScraper[Liquidation, LiquidationData, ILiquidationStorage]):
    def construct_history_producer(self, market: Market) -> IBatchProducer[Liquidation]:
        last_liquidation = self.last_entity_in_storage(market)
        return HistoryProducer[Liquidation, LiquidationData](
            market,
            self.args.sleep_time,
            self.http_api.liquidation_history_traversal(self.http_client, market.instrument),
            get_liquidation_history_marker(last_liquidation.data if last_liquidation else None),
            lambda m, d: Liquidation(m, d),
            lambda liq: liq.liquidation_id,
            lambda liq: liq.time,
            lambda: self.obtain_exchange_last_entity(market),
            self.app.diagnostics.counter("scraper_items_received"),
            self.app.diagnostics.gauge("scraped_market_count"),
        )

    def construct_time_range_history_producer(self, market: Market) -> IBatchProducer[Liquidation]:
        return HistoryProducer[Liquidation, LiquidationData](
            market,
            self.args.sleep_time,
            self.http_api.liquidation_history_traversal(self.http_client, market.instrument),
            HistoryMarker(0, self.args.start_date - timedelta(microseconds=1)),
            lambda m, d: Liquidation(m, d),
            lambda liq: liq.liquidation_id,
            lambda liq: liq.time,
        )

    def construct_consumer(self) -> IBatchConsumer[Liquidation]:
        return HistoryDatabaseConsumer[Liquidation](
            [s.save_liquidations_from_historical_scraper for s in self.storage],
            self.replica_id,
            self.id_ordering,
            "liquidations",
        )

    def construct_time_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[Liquidation, Market]:
        return HistoryTimeRangeDatabaseConsumer[Liquidation](
            [s.save_liquidations for s in self.storage],
            self.args.end_date,
            markets,
            lambda liquidation: liquidation.data.time,
            lambda liquidation: liquidation.market,
        )

    def obtain_exchange_last_entity(self, market: Market) -> Optional[LiquidationData]:
        last_liquidations = self.http_api.last_liquidations(self.http_client, market.instrument)
        return last_liquidations[-1] if len(last_liquidations) > 0 else None

    def last_entity_in_storage(self, market: Market) -> Optional[Liquidation]:
        return self.storage[0].get_historical_scraper_state(self.replica_id, market)

    @property
    def storage(self) -> List[ILiquidationStorage]:
        if len(self._storage) == 0:
            if self.args.database is None:
                self._storage.append(BlackholeLiquidationStorage(self.http_api, self.http_client))
            else:
                self._storage.append(PostgresLiquidationStorage(self.args.database, diagnostics=self.diagnostics))

        return self._storage

    @property
    def id_ordering(self) -> bool:
        return self.http_api.liquidation_history_traversal_method() == TraversalMethod.ID

    @property
    def scraper_type_tag(self) -> str:
        return "liquidations_history"


class HistoryFundingRatesScraper(HistoryScraper[FundingRate, FundingRateData, IFundingRateStorage]):
    def construct_history_producer(self, market: Market) -> IBatchProducer[FundingRate]:
        last_funding_rate = self.last_entity_in_storage(market)
        return HistoryProducer[FundingRate, FundingRateData](
            market,
            self.args.sleep_time,
            self.http_api.funding_rate_traversal(self.http_client, market.instrument),
            get_funding_rate_history_marker(last_funding_rate.data if last_funding_rate else None),
            lambda m, d: FundingRate(m, d),
            lambda fr: 0,
            lambda fr: fr.time,
            lambda: self.obtain_exchange_last_entity(market),
            self.app.diagnostics.counter("scraper_items_received"),
            self.app.diagnostics.gauge("scraped_market_count"),
        )

    def construct_time_range_history_producer(self, market: Market) -> IBatchProducer[FundingRate]:
        return HistoryProducer[FundingRate, FundingRateData](
            market,
            self.args.sleep_time,
            self.http_api.funding_rate_traversal(self.http_client, market.instrument),
            HistoryMarker(0, self.args.start_date - timedelta(microseconds=1)),
            lambda m, d: FundingRate(m, d),
            lambda fr: 0,
            lambda fr: fr.time,
        )

    def construct_consumer(self) -> IBatchConsumer[FundingRate]:
        return HistoryDatabaseConsumer[FundingRate](
            [s.save_funding_rates_from_historical_scraper for s in self.storage],
            self.replica_id,
            self.id_ordering,
            "funding_rates",
        )

    def construct_time_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[FundingRate, Market]:
        return HistoryTimeRangeDatabaseConsumer[FundingRate](
            [s.save_funding_rates for s in self.storage],
            self.args.end_date,
            markets,
            lambda fr: fr.data.time,
            lambda fr: fr.market,
        )

    def obtain_exchange_last_entity(self, market: Market) -> Optional[FundingRateData]:
        last_funding_rate = self.http_api.last_funding_rates(self.http_client, market.instrument)
        return last_funding_rate[-1] if len(last_funding_rate) > 0 else None

    def last_entity_in_storage(self, market: Market) -> Optional[FundingRate]:
        return self.storage[0].get_historical_scraper_state(self.replica_id, market)

    @property
    def storage(self) -> List[IFundingRateStorage]:
        if len(self._storage) == 0:
            if self.args.database is None:
                self._storage.append(BlackholeFundingRateStorage(self.http_api, self.http_client))
            else:
                self._storage.append(PostgresFundingRateStorage(self.args.database))

        return self._storage

    @property
    def id_ordering(self) -> bool:
        return False

    def markets_getter(self) -> Callable[[], ExchangeMarkets]:
        return lambda: self.http_api.futures_markets_perpetual(self.http_client)

    @property
    def scraper_type_tag(self) -> str:
        return "funding_rates_history"


class HistoryOpenInterestScraper(HistoryScraper[OpenInterest, OpenInterestData, IOpenInterestStorage]):
    def construct_history_producer(self, market: Market) -> IBatchProducer[OpenInterest]:
        last_open_interest_date = self.last_entity_in_storage(market)
        return HistoryProducer[OpenInterest, OpenInterestData](
            market,
            self.args.sleep_time,
            self.http_api.open_interest_history_traversal(self.http_client, market.instrument),
            get_open_interest_history_marker(last_open_interest_date.data if last_open_interest_date else None),
            lambda m, d: OpenInterest(m, d),
            lambda oi: 0,
            lambda oi: oi.time,
            lambda: self.obtain_exchange_last_entity(market),
            self.app.diagnostics.counter("scraper_items_received"),
            self.app.diagnostics.gauge("scraped_market_count"),
            to_skip=lambda data: data.contract_value_usd == Decimal("-1"),  # skip entries with no price
        )

    def construct_time_range_history_producer(self, market: Market) -> IBatchProducer[OpenInterest]:
        return HistoryProducer[OpenInterest, OpenInterestData](
            market,
            self.args.sleep_time,
            self.http_api.open_interest_history_traversal(self.http_client, market.instrument),
            HistoryMarker(0, self.args.start_date - timedelta(microseconds=1)),
            lambda m, d: OpenInterest(m, d),
            lambda oi: 0,
            lambda oi: oi.time,
        )

    def construct_consumer(self) -> IBatchConsumer[OpenInterest]:
        return HistoryDatabaseConsumer[OpenInterest](
            [s.save_open_interest_from_historical_scraper for s in self.storage],
            self.replica_id,
            self.id_ordering,
            "open_interest",
        )

    def construct_time_range_consumer(self, markets: List[Market]) -> IBatchTimeRangeConsumer[OpenInterest, Market]:
        return HistoryTimeRangeDatabaseConsumer[OpenInterest](
            [s.save_open_interests for s in self.storage],
            self.args.end_date,
            markets,
            lambda oi: oi.data.time,
            lambda oi: oi.market,
        )

    def obtain_exchange_last_entity(self, market: Market) -> Optional[OpenInterestData]:
        last_open_interest = self.http_api.last_open_interest(self.http_client, market.instrument)
        return last_open_interest[-1] if len(last_open_interest) > 0 else None

    def last_entity_in_storage(self, market: Market) -> Optional[OpenInterest]:
        return self.storage[0].get_historical_scraper_state(self.replica_id, market)

    @property
    def storage(self) -> List[IOpenInterestStorage]:
        if len(self._storage) == 0:
            if self.args.database is None:
                self._storage.append(BlackholeOpenInterestStorage(self.http_api, self.http_client))
            else:
                self._storage.append(PostgresFuturesOpenInterestStorage(self.args.database))

        return self._storage

    @property
    def id_ordering(self) -> bool:
        return False

    @property
    def scraper_type_tag(self) -> str:
        return "open_interests_history"


def run_history_scraper(scraper_type: Type[HistoryScraper[D_T, S_D_T, S_T]]) -> None:
    args = scraper_type.parse_cli()

    with Application(prometheus=args.prometheus, log_level=args.log_level) as app:
        proxies = proxies_from_args(args)
        exchange_id = glib_exchange().exchange_id_by_name(args.exchange)
        http_api = exchange_http_api(args.exchange, args.api_params, app.diagnostics)

        replica_id = replica_id_from_args(args)
        app.diagnostics.info(f"historical scraper replica id is {replica_id}")

        http_client = RateLimitedProxyHttpClient(
            proxies=proxies,
            rate_limit=glib_exchange().http_api_rate_limit(exchange_id) * args.rate_limit_multiplier,
            diagnostics=app.diagnostics,
            name="http_client_proxy_pool",
            check_rate_limited=http_api.check_rate_limited,
        )

        with http_client, PyRoutineSystem(app.diagnostics) as system:
            scraper_type(app, args, exchange_id, http_api, http_client, app.diagnostics, replica_id).run(system)
