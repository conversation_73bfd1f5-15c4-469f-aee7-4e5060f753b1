import re
import traceback
from dataclasses import dataclass, field
from datetime import datetime
from http import HTTPStatus
from typing import Any, Callable, Dict, List, Optional, Sequence, Tuple, Union
from urllib.parse import parse_qs, urlsplit

import ciso8601

from src.octopus.data import Instrument
from src.octopus.vex.api import (
    IVirtualExchangeHttpApi,
    IVirtualMarketMatchingEngine,
    IVirtualMarketStorage,
    VirtualExchangeMatchingEngine,
    VirtualExchangeStorage,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_from_ms, dt_from_s, dt_from_us


class IEndpoint:
    def can_respond(self, request: HttpRequest) -> bool:
        raise NotImplementedError

    def respond(self, request: HttpRequest) -> HttpResponse:
        raise NotImplementedError


EndpointParamParse = Callable[[str], Any]


def string_endpoint_param(value: str) -> str:
    return value


def timestamp_seconds_endpoint_param(value: str) -> datetime:
    return dt_from_s(int(value))


def timestamp_endpoint_param(value: str) -> datetime:
    return datetime.utcfromtimestamp(float(value))


def timestamp_milliseconds_endpoint_param(value: str) -> datetime:
    return dt_from_ms(int(value))


def timestamp_microseconds_endpoint_param(value: str) -> datetime:
    return dt_from_us(int(value))


def iso_8601_date_endpoint_param(value: str) -> datetime:
    return ciso8601.parse_datetime_as_naive(value)


def int_endpoint_param(min_value: Optional[int] = None, max_value: Optional[int] = None) -> Callable[[str], int]:
    def proc(value: str) -> int:
        integer = int(value)

        if min_value is not None and integer < min_value:
            raise ValueError("value should be >= {}".format(min_value))

        if max_value is not None and integer > max_value:
            raise ValueError("value should be <= {}".format(max_value))

        return integer

    return proc


def float_endpoint_param(min_value: Optional[float] = None, max_value: Optional[float] = None) -> Callable[[str], float]:
    def proc(value: str) -> float:
        float_value = float(value)

        if min_value is not None and float_value < min_value:
            raise ValueError("value should be >= {}".format(min_value))

        if max_value is not None and float_value > max_value:
            raise ValueError("value should be <= {}".format(max_value))

        return float_value

    return proc


@dataclass
class InstrumentWithStorage:
    instrument: Instrument
    storage: IVirtualMarketStorage


@dataclass
class InstrumentWithMatchingEngine:
    instrument: Instrument
    matching_engine: IVirtualMarketMatchingEngine


def instrument_param(api: "VirtualExchangeHttpApi") -> Callable[[str], InstrumentWithStorage]:
    def proc(value: str) -> InstrumentWithStorage:
        instrument = api.get_instrument_by_market_id(value)

        if (storage := api.get_instrument_storage(instrument)) is None:
            raise ValueError("no storage found for {}".format(instrument))
        else:
            return InstrumentWithStorage(instrument, storage)

    return proc


def instrument_matching_engine_param(api: "VirtualExchangeHttpApi") -> Callable[[str], InstrumentWithMatchingEngine]:
    def proc(value: str) -> InstrumentWithMatchingEngine:
        instrument = api.get_instrument_by_market_id(value)

        if (matching_engine := api.get_instrument_matching_engine(instrument)) is None:
            raise ValueError("no matching engine found for {}".format(instrument))
        else:
            return InstrumentWithMatchingEngine(instrument, matching_engine)

    return proc


@dataclass(frozen=True)
class EndpointMatchFailure:
    template_mismatch: bool = False
    bad_url: bool = False
    invalid_params: Dict[str, str] = field(default_factory=dict)
    missing_required_params: List[str] = field(default_factory=list)


EndpointMatchResult = Union[Dict[str, Any], EndpointMatchFailure]

_SplitTemplate = List[Union[str, Tuple[str, EndpointParamParse]]]


def _split_template(template: str, params: Dict[str, EndpointParamParse]) -> _SplitTemplate:
    result: _SplitTemplate = []
    expression = re.compile(r"\{([a-zA-Z_]+)\}")

    while len(template) > 0:
        if (match := expression.search(template)) is None:
            result.append(template)
            template = ""
        else:
            string_before = template[: match.start()]
            if len(string_before) > 0:
                result.append(string_before)

            parameter = match.group(1)
            if parameter not in params:
                raise ValueError("`{}` not in params".format(parameter))
            else:
                result.append((parameter, params[parameter]))

            template = template[match.end() :]

    return result


class EndpointParam:
    """no dataclass here due to https://github.com/python/mypy/issues/5485"""

    def __init__(self, required: bool, parser: EndpointParamParse, argument: Optional[str] = None):
        self.required = required
        self.parser = parser
        self.argument = argument


class Endpoint(IEndpoint):
    def __init__(self, handler: Callable[..., HttpResponse], template: str, params: Dict[str, EndpointParam] = {}) -> None:
        self._handler = handler
        self._template = _split_template(template, {name: spec.parser for name, spec in params.items()})
        self._params = params

    def can_respond(self, request: HttpRequest) -> bool:
        result = self._fetch_parameters(request)
        if isinstance(result, EndpointMatchFailure) and (result.template_mismatch or result.bad_url):
            return False
        else:
            return True

    def respond(self, request: HttpRequest) -> HttpResponse:
        try:
            result = self._fetch_parameters(request)

            if isinstance(result, EndpointMatchFailure):
                bad_params_response = self._bad_params_response(result)
                if bad_params_response is not None:
                    return bad_params_response
                else:
                    return HttpResponse(HTTPStatus.INTERNAL_SERVER_ERROR, "unhandled match failure: {}".format(result))

            keyword_args = {}
            for name, value in result.items():
                keyword_args[name] = value

            return self._handler(request, **keyword_args)

        except Exception as e:
            return HttpResponse(HTTPStatus.INTERNAL_SERVER_ERROR, "{}\n{}".format(e, traceback.format_exc()))

    def _fetch_parameters(self, request: HttpRequest) -> EndpointMatchResult:
        result_params: Dict[str, Any] = {}
        invalid_params = {}
        missing_required_params = []

        try:
            components = urlsplit(request.url)
            current_path = components.path
            query_params = parse_qs(components.query)
        except Exception:
            return EndpointMatchFailure(bad_url=True)

        for param_name, param_values in query_params.items():
            if param_name not in self._params:
                invalid_params[param_name] = "unknown"
            else:
                try:
                    argument_name = self._params[param_name].argument
                    name = param_name if argument_name is None else argument_name
                    result_params[name] = self._params[param_name].parser(param_values[0])
                except Exception as e:
                    invalid_params[param_name] = str(e)

        for template_piece in self._template:
            if isinstance(template_piece, str):
                if not current_path.startswith(template_piece):
                    return EndpointMatchFailure(template_mismatch=True)
                else:
                    current_path = current_path[len(template_piece) :]
            else:
                param_name, param_parse = template_piece

                try:
                    end_index = current_path.index("/")
                except ValueError:
                    end_index = len(current_path)

                try:
                    param_value = param_parse(current_path[:end_index])
                    result_params[param_name] = param_value
                except Exception as e:
                    invalid_params[param_name] = str(e)

                current_path = current_path[end_index:]

        for param_name, spec in self._params.items():
            name = spec.argument if spec.argument is not None else param_name
            if spec.required and name not in result_params:
                missing_required_params.append(param_name)

        if len(invalid_params) > 0 or len(missing_required_params) > 0:
            return EndpointMatchFailure(missing_required_params=missing_required_params, invalid_params=invalid_params)
        else:
            return result_params

    def _bad_params_response(self, failure: EndpointMatchFailure) -> Optional[HttpResponse]:
        if len(failure.invalid_params) > 0:
            errors = [
                "{} -- {}".format(param_name, error_message) for param_name, error_message in failure.invalid_params.items()
            ]
            return HttpResponse(HTTPStatus.BAD_REQUEST, "invalid params: {}".format(",".join(errors)))

        if len(failure.missing_required_params) > 0:
            names = [param_name for param_name in failure.missing_required_params]
            return HttpResponse(HTTPStatus.BAD_REQUEST, "missing required params: {}".format(",".join(names)))

        return None


class VirtualExchangeHttpApi(IVirtualExchangeHttpApi):
    def __init__(
        self,
        exchange_name: str,
        storage: VirtualExchangeStorage,
        matching_engine: VirtualExchangeMatchingEngine,
        endpoints: Sequence[IEndpoint],
        real_exchange_params: Optional[List[str]] = None,
    ) -> None:
        self._instrument_names = {instrument.symbol: instrument for instrument in storage}
        for instrument in matching_engine:
            self._instrument_names[instrument.symbol] = instrument
        self._reverse_instrument_names = {v: k for k, v in self._instrument_names.items()}

        self._storage = storage
        self._matching_engine = matching_engine
        self._endpoints = endpoints

    def respond(self, request: HttpRequest) -> HttpResponse:
        try:
            for endpoint in self._endpoints:
                if endpoint.can_respond(request):
                    return endpoint.respond(request)
            return HttpResponse(HTTPStatus.NOT_FOUND)
        except Exception as e:
            return HttpResponse(HTTPStatus.INTERNAL_SERVER_ERROR, "{}\n{}".format(e, traceback.format_exc()))

    def get_instrument_by_market_id(self, market_id: str) -> Instrument:
        return self._instrument_names[market_id]

    def get_instrument_storage(self, instrument: Instrument) -> Optional[IVirtualMarketStorage]:
        return self._storage[instrument] if instrument in self._storage else None

    def get_market_id_by_instrument(self, instrument: Instrument) -> str:
        return self._reverse_instrument_names[instrument]

    def get_instrument_matching_engine(self, instrument: Instrument) -> Optional[IVirtualMarketMatchingEngine]:
        return self._matching_engine[instrument] if instrument in self._matching_engine else None
