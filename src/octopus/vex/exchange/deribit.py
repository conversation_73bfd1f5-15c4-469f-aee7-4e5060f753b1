import json
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import Optional

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.deribit import EXCHANGE_NAME, MAX_FUNDING_RATES_PER_REQUEST, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
    timestamp_milliseconds_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms
from src.utils.types import Order


class VirtualDeribitHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/v2/public/get_last_trades_by_instrument_and_time",
                    {
                        "instrument_name": EndpointParam(True, instrument_param(self)),
                        "count": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "sorting": EndpointParam(True, string_endpoint_param),
                        "start_timestamp": EndpointParam(False, timestamp_milliseconds_endpoint_param),
                        "end_timestamp": EndpointParam(False, timestamp_milliseconds_endpoint_param),
                        "include_old": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_funding_rates,
                    "/api/v2/public/get_funding_rate_history",
                    {
                        "instrument_name": EndpointParam(True, instrument_param(self)),
                        "start_timestamp": EndpointParam(False, timestamp_milliseconds_endpoint_param),
                        "end_timestamp": EndpointParam(False, timestamp_milliseconds_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/api/v2/public/get_order_book",
                    {
                        "instrument_name": EndpointParam(True, instrument_matching_engine_param(self)),
                        "depth": EndpointParam(False, int_endpoint_param(1)),
                    },
                ),
            ],
        )

    @staticmethod
    def _get_trades(
        request: HttpRequest,
        instrument_name: InstrumentWithStorage,
        count: int,
        sorting: str,
        include_old: str,
        start_timestamp: Optional[datetime] = None,
        end_timestamp: Optional[datetime] = None,
    ) -> HttpResponse:
        if sorting == "desc":
            order = Order.DESC
        elif sorting == "asc":
            order = Order.ASC
        else:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "sorting param should be either `asc` or `desc`")

        if include_old != "true":
            return HttpResponse(HTTPStatus.BAD_REQUEST, "include_old param should be set to `true`")

        trades = instrument_name.storage.get_trades_in_range(
            TimeRangeTraversalRequest(start_timestamp, end_timestamp, order, count)
        )
        liquidations = instrument_name.storage.get_liquidations_in_range(
            TimeRangeTraversalRequest(start_timestamp, end_timestamp, order, count)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "result": {
                    "trades": [
                        {
                            "trade_id": str(trade.trade_id),
                            "amount": str(trade.amount * instrument_name.instrument.metadata["contract_size"]),
                            "price": str(trade.price),
                            "direction": "buy" if trade.is_buy else "sell",
                            "timestamp": dt_to_ms(trade.time),
                            "index_price": str(trade.index_price),
                            "mark_price": str(trade.mark_price),
                        }
                        for trade in trades
                    ]
                    + [
                        {
                            "trade_id": str(liquidation.liquidation_id),
                            "amount": str(liquidation.amount * instrument_name.instrument.metadata["contract_size"]),
                            "price": str(liquidation.price),
                            "liquidation": "M",
                            "direction": "buy" if liquidation.is_buy else "sell",
                            "timestamp": dt_to_ms(liquidation.time),
                        }
                        for liquidation in liquidations
                    ]
                }
            }),
        )

    @staticmethod
    def _get_funding_rates(
        request: HttpRequest,
        instrument_name: InstrumentWithStorage,
        start_timestamp: Optional[datetime] = None,
        end_timestamp: Optional[datetime] = None,
    ) -> HttpResponse:
        funding_rates = instrument_name.storage.get_funding_rates_in_range(
            TimeRangeTraversalRequest(start_timestamp, end_timestamp, Order.ASC, MAX_FUNDING_RATES_PER_REQUEST)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "result": [
                    {
                        "timestamp": dt_to_ms(funding_rate.time),
                        "index_price": "0.0",
                        "prev_index_price": "0.0",
                        "interest_8h": str(funding_rate.funding_rate),
                        "interest_1h": "0.0",
                    }
                    for funding_rate in funding_rates
                ]
            }),
        )

    @staticmethod
    def _get_book(request: HttpRequest, instrument_name: InstrumentWithMatchingEngine, depth: int) -> HttpResponse:
        book = instrument_name.matching_engine.get_last_book()

        if book.exchange_time is None:
            raise Exception("book.exchange_time is None")
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "success": True,
                "result": {
                    "timestamp": dt_to_ms(book.exchange_time),
                    "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids[:depth]],
                    "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks[:depth]],
                },
            }),
        )
