import json
from http import HTTPStatus
from typing import Optional

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.hitbtc import EXCHANGE_NAME, HitBtcHttpApi
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Direction


class VirtualHitBTCHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/3/public/trades/{instrument}",
                    {
                        "instrument": EndpointParam(True, instrument_param(self)),
                        "limit": EndpointParam(True, int_endpoint_param(1, HitBtcHttpApi.MAX_TRADES_PER_HISTORIC_REQUEST)),
                        "sort": EndpointParam(True, string_endpoint_param),
                        "by": EndpointParam(True, string_endpoint_param),
                        "from": EndpointParam(False, int_endpoint_param(), "from_id"),
                    },
                )
            ],
        )

    def _get_trades(
        self,
        request: HttpRequest,
        instrument: InstrumentWithStorage,
        sort: str,
        by: str,
        limit: int,
        from_id: Optional[int] = None,
    ) -> HttpResponse:
        if sort != "ASC":
            return HttpResponse(HTTPStatus.BAD_REQUEST, "expected `ASC` value for `sort`")

        if by != "id":
            return HttpResponse(HTTPStatus.BAD_REQUEST, "expected `id` value for `by`")

        trades = instrument.storage.get_trades_relative_to_id(
            IdTraversalRequest(from_id if from_id is not None else IdLimit.MIN, Direction.FORWARD, limit, include_reference=True)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "id": trade.trade_id,
                    "qty": str(trade.amount),
                    "price": str(trade.price),
                    "side": "buy" if trade.is_buy else "sell",
                    "timestamp": trade.time.isoformat(),
                }
                for trade in trades
            ]),
        )
