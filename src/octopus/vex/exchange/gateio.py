import json
from http import HTTPStatus
from typing import Optional

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.gateio import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms, dt_to_s
from src.utils.types import Direction


class VirtualGateIoHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/v4/spot/trades",
                    {
                        "currency_pair": EndpointParam(True, instrument_param(self)),
                        "limit": EndpointParam(False, int_endpoint_param(1)),
                        "last_id": EndpointParam(False, int_endpoint_param(1)),
                    },
                ),
                Endpoint(
                    self._get_spot_book,
                    "/api/v4/spot/order_book",
                    {
                        "currency_pair": EndpointParam(True, instrument_matching_engine_param(self)),
                        "limit": EndpointParam(True, int_endpoint_param(1, 10000)),
                        "with_id": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_futures_book,
                    "/api/v4/futures/usdt/order_book",
                    {
                        "contract": EndpointParam(True, instrument_matching_engine_param(self)),
                        "limit": EndpointParam(True, int_endpoint_param(1, 10000)),
                        "with_id": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_futures_book,
                    "/api/v4/delivery/btc/order_book",
                    {
                        "contract": EndpointParam(True, instrument_matching_engine_param(self)),
                        "limit": EndpointParam(True, int_endpoint_param(1, 10000)),
                        "with_id": EndpointParam(True, string_endpoint_param),
                    },
                ),
            ],
        )

    def _get_trades(
        self, request: HttpRequest, currency_pair: InstrumentWithStorage, limit: Optional[int], last_id: Optional[int]
    ) -> HttpResponse:
        trades = currency_pair.storage.get_trades_relative_to_id(
            IdTraversalRequest(
                last_id if last_id is not None else IdLimit.MAX,
                Direction.FORWARD if last_id is not None else Direction.BACKWARD,
                limit if limit is not None else MAX_TRADES_PER_REQUEST,
                include_reference=False,
            )
        )

        if last_id is None:
            trades.reverse()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "id": trade.trade_id,
                    "create_time": dt_to_s(trade.time),
                    "create_time_ms": dt_to_ms(trade.time),
                    "currency_pair": currency_pair.instrument.symbol,
                    "side": "buy" if trade.is_buy else "sell",
                    "amount": str(trade.amount),
                    "price": str(trade.price),
                }
                for trade in trades
            ]),
        )

    @staticmethod
    def _get_spot_book(
        request: HttpRequest, currency_pair: InstrumentWithMatchingEngine, limit: int, with_id: bool = True
    ) -> HttpResponse:
        book = currency_pair.matching_engine.get_last_book()
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "id": book.exchange_sequence_id,
                "current": 1675585424939,
                "update": dt_to_ms(book.exchange_time) if book.exchange_time else None,
                "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids[:limit]],
                "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks[:limit]],
            }),
        )

    @staticmethod
    def _get_futures_book(
        request: HttpRequest, contract: InstrumentWithMatchingEngine, limit: int, with_id: bool = True
    ) -> HttpResponse:
        book = contract.matching_engine.get_last_book()
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "id": book.exchange_sequence_id,
                "current": 1675585424939,
                "update": dt_to_ms(book.exchange_time) if book.exchange_time else None,
                "bids": [{"p": str(bid.price), "s": str(bid.amount)} for bid in book.bids[:limit]],
                "asks": [{"p": str(ask.price), "s": str(ask.amount)} for ask in book.asks[:limit]],
            }),
        )
