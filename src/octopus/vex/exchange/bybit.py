import json
from datetime import datetime, timedelta, timezone
from http import HTT<PERSON>tatus

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.bybit import EXCHANGE_NAME
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms, dt_to_s, dt_to_us
from src.utils.types import JsonValue, Order


class VirtualBybitHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_latest_ticker,
                    "/v5/market/tickers",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "category": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_funding_rate_history,
                    "/v5/market/funding/history",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "category": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/v2/public/orderBook/L2",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                    },
                ),
                Endpoint(
                    self._get_future_book,
                    "/v5/market/orderbook",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                        "category": EndpointParam(False, string_endpoint_param),
                        "limit": EndpointParam(False, int_endpoint_param(1, 200)),
                    },
                ),
                Endpoint(
                    self._get_spot_trades,
                    "/spot/quote/v1/trades",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                    },
                ),
            ],
        )

    @staticmethod
    def _get_latest_ticker(request: HttpRequest, symbol: InstrumentWithStorage, category: str) -> HttpResponse:
        traversal = TimeRangeTraversalRequest(None, None, Order.ASC, 1)
        open_interest = symbol.storage.get_open_interest_in_range(traversal)
        result = {
            "retCode": 0,
            "retMsg": "OK",
            "result": {
                "category": symbol.instrument.metadata["type"],
                "list": [
                    {
                        "openInterest": float(open_interest[0].contract_count) if open_interest else 0,
                        "openInterestValue": float(open_interest[0].contract_value_usd) if open_interest else 0,
                        "fundingRate": 0.001,
                        "nextFundingTime": "1683648000000",
                    }
                ],
            },
        }
        return HttpResponse(HTTPStatus.OK, json.dumps(result))

    @staticmethod
    def _get_funding_rate_history(request: HttpRequest, symbol: InstrumentWithStorage, category: str) -> HttpResponse:
        funding_rates = symbol.storage.get_funding_rates_in_range(TimeRangeTraversalRequest(None, None, Order.ASC, 10000))
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "result": {
                    "list": [
                        {
                            "symbol": symbol.instrument.symbol,
                            "fundingRate": str(funding_rate.funding_rate),
                            "fundingRateTimestamp": str(int(funding_rate.time.replace(tzinfo=timezone.utc).timestamp() * 1000)),
                        }
                        for funding_rate in funding_rates
                    ]
                }
            }),
        )

    @staticmethod
    def _get_book(request: HttpRequest, symbol: InstrumentWithMatchingEngine) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()

        if book.exchange_time is None:
            raise Exception("book.exchange is is None")
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "ret_code": 0,
                "ret_msg": "OK",
                "ext_code": "",
                "ext_info": "",
                "result": [
                    {"symbol": symbol.instrument.symbol, "price": str(bid.price), "size": int(bid.amount), "side": "Buy"}
                    for bid in book.bids
                ]
                + [
                    {"symbol": symbol.instrument.symbol, "price": str(ask.price), "size": int(ask.amount), "side": "Sell"}
                    for ask in book.asks
                ],
                "time_now": dt_to_us(book.exchange_time) * 1e-6,
            }),
        )

    @staticmethod
    def _get_future_book(request: HttpRequest, symbol: InstrumentWithMatchingEngine, category: str, limit: int) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps(
                {
                    "retCode": 0,
                    "retMsg": "OK",
                    "extCode": "",
                    "extInfo": "",
                    "result": {
                        "s": symbol.instrument.symbol,
                        "u": book.exchange_sequence_id,
                        "ts": book.exchange_time.timestamp() if book.exchange_time else None,
                        "b": [[str(bid.price), str(bid.amount)] for bid in book.bids],
                        "a": [[str(ask.price), str(ask.amount)] for ask in book.asks],
                    },
                },
            ),
        )

    @staticmethod
    def _get_spot_trades(request: HttpRequest, symbol: InstrumentWithStorage) -> HttpResponse:
        trades = symbol.storage.get_trades_in_range(TimeRangeTraversalRequest(None, None, Order.ASC, 1_000_000_000))

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "ret_code": 0,
                "ret_msg": "",
                "result": [
                    {
                        "price": str(trade.price),
                        "time": dt_to_ms(trade.time),
                        "qty": str(trade.amount),
                        "isBuyerMaker": not trade.is_buy,
                    }
                    for trade in trades
                ],
            }),
        )

    @staticmethod
    def _get_prev_funding_rate(request: HttpRequest, symbol: InstrumentWithStorage) -> HttpResponse:
        traversal = TimeRangeTraversalRequest(None, None, Order.ASC, 1)
        funding_rates = symbol.storage.get_funding_rates_in_range(traversal)
        result: JsonValue = {
            "symbol": symbol.instrument.symbol,
            "funding_rate": str(funding_rates[0].funding_rate),
            "funding_rate_timestamp": dt_to_s(funding_rates[0].time - timedelta(hours=8)),
        }
        return HttpResponse(HTTPStatus.OK, json.dumps({"result": result, "time_now": dt_to_us(datetime.utcnow()) / 1000000}))

    @staticmethod
    def _get_prev_funding_rate_usdt(request: HttpRequest, symbol: InstrumentWithStorage) -> HttpResponse:
        traversal = TimeRangeTraversalRequest(None, None, Order.ASC, 1)
        funding_rates = symbol.storage.get_funding_rates_in_range(traversal)
        result: JsonValue = {
            "symbol": symbol.instrument.symbol,
            "funding_rate": float(funding_rates[0].funding_rate),
            "funding_rate_timestamp": (funding_rates[0].time - timedelta(hours=8)).isoformat(),
        }
        return HttpResponse(HTTPStatus.OK, json.dumps({"result": result, "time_now": dt_to_us(datetime.utcnow()) / 1000000}))
