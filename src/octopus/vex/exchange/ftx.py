import json
from datetime import datetime
from http import HTTPStatus
from typing import Optional

from src.octopus.data import ExchangeInstrument, MarketType
from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.ftx import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.translation import ExchangeTickerTranslator
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
    timestamp_endpoint_param,
)
from src.resources.currency import glib_currency
from src.resources.exchange import glib_exchange
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Order


class VirtualFTXHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/markets/{instrument}/trades",
                    {
                        "instrument": EndpointParam(True, instrument_param(self)),
                        "limit": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "start_time": EndpointParam(False, timestamp_endpoint_param),
                        "end_time": EndpointParam(False, timestamp_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/api/markets/{base}/{quote}/orderbook",
                    {
                        "base": EndpointParam(True, string_endpoint_param),
                        "quote": EndpointParam(True, string_endpoint_param),
                        "depth": EndpointParam(False, int_endpoint_param(1)),
                    },
                ),
                Endpoint(
                    self._get_futures_book,
                    "/api/markets/{symbol}/orderbook",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                        "depth": EndpointParam(False, int_endpoint_param(1)),
                    },
                ),
                Endpoint(
                    self._get_funding_rates,
                    "/api/funding_rates",
                    {
                        "future": EndpointParam(True, instrument_param(self)),
                        "start_time": EndpointParam(False, timestamp_endpoint_param),
                        "end_time": EndpointParam(False, timestamp_endpoint_param),
                    },
                ),
            ],
        )

    @staticmethod
    def _get_trades(
        request: HttpRequest,
        instrument: InstrumentWithStorage,
        limit: int,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> HttpResponse:
        trades = instrument.storage.get_trades_in_range(
            TimeRangeTraversalRequest(start_time, end_time, Order.DESC, MAX_TRADES_PER_REQUEST)
        )
        liquidations = instrument.storage.get_liquidations_in_range(
            TimeRangeTraversalRequest(start_time, end_time, Order.DESC, MAX_TRADES_PER_REQUEST)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "success": True,
                "result": [
                    {
                        "id": trade.trade_id,
                        "size": str(trade.amount),
                        "price": str(trade.price),
                        "side": "buy" if trade.is_buy else "sell",
                        "time": trade.time.isoformat(),
                    }
                    for trade in trades
                ]
                + [
                    {
                        "id": liquidation.liquidation_id,
                        "size": str(liquidation.amount),
                        "liquidation": "true",
                        "price": str(liquidation.price),
                        "side": "buy" if liquidation.is_buy else "sell",
                        "time": liquidation.time.isoformat(),
                    }
                    for liquidation in liquidations
                ],
            }),
        )

    @staticmethod
    def _get_funding_rates(
        request: HttpRequest,
        future: InstrumentWithStorage,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> HttpResponse:
        funding_rates = future.storage.get_funding_rates_in_range(
            TimeRangeTraversalRequest(start_time, end_time, Order.DESC, MAX_TRADES_PER_REQUEST)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "success": True,
                "result": [
                    {
                        "future": future.instrument.symbol,
                        "rate": str(funding_rate.funding_rate),
                        "time": funding_rate.time.isoformat(),
                    }
                    for funding_rate in funding_rates
                ],
            }),
        )

    def _get_book(self, request: HttpRequest, base: str, quote: str, depth: int) -> HttpResponse:
        translator = ExchangeTickerTranslator("FTX", glib_exchange(), glib_currency())
        instrument_data = ExchangeInstrument(MarketType.SPOT, "{}/{}".format(base, quote), base.lower(), quote.lower())
        markets = translator.exchange_to_coinmetrics([instrument_data])
        if len(markets.recognized) == 0:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "unknown instrument name")
        instrument_matching_engine = self.get_instrument_matching_engine(markets.recognized[0])
        if instrument_matching_engine is None:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "unknown instrument name")
        book = instrument_matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "success": True,
                "result": {
                    "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks[:depth]],
                    "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids[:depth]],
                },
            }),
        )

    @staticmethod
    def _get_futures_book(request: HttpRequest, symbol: InstrumentWithMatchingEngine, depth: int) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "success": True,
                "result": {
                    "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks[:depth]],
                    "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids[:depth]],
                },
            }),
        )
