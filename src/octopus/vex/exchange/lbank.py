import json
from datetime import datetime, timedelta, timezone
from http import HTT<PERSON>tatus
from typing import Optional

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.lbank import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
    timestamp_milliseconds_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms
from src.utils.types import Order


class VirtualLBankHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/v2/trades.do",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "size": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "time": EndpointParam(False, timestamp_milliseconds_endpoint_param),
                    },
                )
            ],
        )

    def _get_trades(
        self, request: HttpRequest, symbol: InstrumentWithStorage, size: int, time: Optional[datetime] = None
    ) -> HttpResponse:
        if time is not None:
            if time.tzinfo is None:
                time = time.replace(tzinfo=timezone.utc)
            storage_request = TimeRangeTraversalRequest(time + timedelta(milliseconds=1), None, Order.ASC, size)
        else:
            storage_request = TimeRangeTraversalRequest(None, None, Order.DESC, size)

        trades = symbol.storage.get_trades_in_range(storage_request)

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "result": "true",
                "data": [
                    {
                        "tid": hex(trade.trade_id),
                        "amount": str(trade.amount),
                        "price": str(trade.price),
                        "type": "buy" if trade.is_buy else "sell",
                        "date_ms": dt_to_ms(trade.time),
                    }
                    for trade in trades
                ],
            }),
        )
