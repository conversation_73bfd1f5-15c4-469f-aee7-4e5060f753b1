import json
from http import HTTPStatus

from src.octopus.exchange.bittrex import <PERSON><PERSON><PERSON><PERSON><PERSON>_NAME, HTTP_BOOK_DEPTHS
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    int_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse


class VirtualBittrexHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_book,
                    "/v3/markets/{symbol}/orderbook",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                        "depth": EndpointParam(True, int_endpoint_param()),
                    },
                )
            ],
        )

    def _get_book(self, request: HttpRequest, symbol: InstrumentWithMatchingEngine, depth: int) -> HttpResponse:
        if depth not in HTTP_BOOK_DEPTHS:
            return HttpResponse(HTTPStatus.BAD_REQUEST, f"invalid depth -- {depth}")

        book = symbol.matching_engine.get_last_book()

        if book.exchange_sequence_id is None:
            return HttpResponse(HTTPStatus.INTERNAL_SERVER_ERROR, "unknown exchange sequence")

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "bid": [{"rate": str(bid.price), "quantity": str(bid.amount)} for bid in book.bids[:depth]],
                "ask": [{"rate": str(ask.price), "quantity": str(ask.amount)} for ask in book.asks[:depth]],
            }),
            headers={"Sequence": str(book.exchange_sequence_id)},
        )
