import json
from http import HTTPStatus

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.gemini import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms, dt_to_s
from src.utils.types import Direction


class VirtualGeminiHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/v1/trades/{symbol}",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "limit_trades": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                    },
                )
            ],
        )

    def _get_trades(self, request: HttpRequest, symbol: InstrumentWithStorage, limit_trades: int) -> HttpResponse:
        if limit_trades != MAX_TRADES_PER_REQUEST:
            return HttpResponse(
                HTTPStatus.BAD_REQUEST, "limit_trades is expected to be set to {}".format(MAX_TRADES_PER_REQUEST)
            )

        trades = symbol.storage.get_trades_relative_to_id(
            IdTraversalRequest(IdLimit.MAX, Direction.BACKWARD, MAX_TRADES_PER_REQUEST, True)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "tid": trade.trade_id,
                    "amount": str(trade.amount),
                    "price": str(trade.price),
                    "timestamp": dt_to_s(trade.time),
                    "timestampms": dt_to_ms(trade.time),
                    "type": "buy" if trade.is_buy else "sell",
                }
                for trade in trades
            ]),
        )
