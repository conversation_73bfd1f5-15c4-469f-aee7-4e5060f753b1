import json
from http import HTTPStatus

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.ftx import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.exchange.okex import MAX_ENTITIES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms
from src.utils.types import Order


class VirtualOKExHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_last_trades,
                    "/api/v5/market/trades",
                    {
                        "instId": EndpointParam(True, instrument_param(self), "instrument"),
                        "limit": EndpointParam(True, int_endpoint_param(1, MAX_ENTITIES_PER_REQUEST)),
                    },
                ),
                Endpoint(
                    self._get_last_liquidations,
                    "/api/v5/public/liquidation-orders",
                    {
                        "instType": EndpointParam(True, string_endpoint_param),
                        "uly": EndpointParam(True, string_endpoint_param),
                        "alias": EndpointParam(False, string_endpoint_param),
                        "limit": EndpointParam(True, int_endpoint_param(1, MAX_ENTITIES_PER_REQUEST)),
                        "state": EndpointParam(True, string_endpoint_param),
                    },
                ),
                Endpoint(
                    self._get_last_funding_rates,
                    "/api/v5/public/funding-rate-history",
                    {
                        "instId": EndpointParam(True, instrument_param(self), "instrument"),
                        "limit": EndpointParam(True, int_endpoint_param(1, MAX_ENTITIES_PER_REQUEST)),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/api/v5/market/books",
                    {
                        "instId": EndpointParam(True, instrument_matching_engine_param(self)),
                        "sz": EndpointParam(False, int_endpoint_param(1)),
                    },
                ),
            ],
        )

    def _get_last_trades(self, request: HttpRequest, instrument: InstrumentWithStorage, limit: int) -> HttpResponse:
        trades = instrument.storage.get_trades_in_range(
            TimeRangeTraversalRequest(None, None, Order.DESC, MAX_TRADES_PER_REQUEST)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "data": [
                    {
                        "sz": str(trade.amount),
                        "px": str(trade.price),
                        "ts": str(dt_to_ms(trade.time)),
                        "tradeId": str(trade.trade_id % 1000),
                        "side": "buy" if trade.is_buy else "sell",
                    }
                    for trade in trades
                ]
            }),
        )

    def _get_last_liquidations(
        self, request: HttpRequest, instType: str, uly: str, alias: str, limit: int, state: int
    ) -> HttpResponse:
        result = []
        for instrument in self._instrument_names.values():
            if instrument.metadata["uly"] == uly:
                storage = self.get_instrument_storage(instrument)
                if storage is None:
                    continue
                liquidations = storage.get_liquidations_in_range(
                    TimeRangeTraversalRequest(None, None, Order.DESC, MAX_TRADES_PER_REQUEST)
                )

                result.append({
                    "details": [
                        {
                            "loss": "0.0",
                            "sz": str(liquidation.amount),
                            "bkPx": str(liquidation.price),
                            "ts": str(dt_to_ms(liquidation.time)),
                            "side": "buy" if liquidation.is_buy else "sell",
                        }
                        for liquidation in liquidations
                    ],
                    "instId": instrument.symbol,
                    "uly": instrument.metadata["uly"],
                })

        return HttpResponse(HTTPStatus.OK, json.dumps({"data": result}))

    def _get_last_funding_rates(self, request: HttpRequest, instrument: InstrumentWithStorage, limit: int) -> HttpResponse:
        funding_rates = instrument.storage.get_funding_rates_in_range(
            TimeRangeTraversalRequest(None, None, Order.DESC, MAX_TRADES_PER_REQUEST)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "data": [
                    {
                        "instId": instrument.instrument.symbol,
                        "fundingRate": str(funding_rate.funding_rate),
                        "realizedRate": "0.0",
                        "interestRate": "0.0",
                        "fundingTime": str(dt_to_ms(funding_rate.time)),
                    }
                    for funding_rate in funding_rates
                ]
            }),
        )

    @staticmethod
    def _get_book(request: HttpRequest, instId: InstrumentWithMatchingEngine, sz: int) -> HttpResponse:
        book = instId.matching_engine.get_last_book()
        if book.exchange_time is None:
            raise Exception("book.exchange_time is None")
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "data": [
                    {
                        "ts": dt_to_ms(book.exchange_time),
                        "bids": [[str(bid.price), str(bid.amount), "0", str(bid.count)] for bid in book.bids[:sz]],
                        "asks": [[str(ask.price), str(ask.amount), "0", str(ask.count)] for ask in book.asks[:sz]],
                    }
                ]
            }),
        )
