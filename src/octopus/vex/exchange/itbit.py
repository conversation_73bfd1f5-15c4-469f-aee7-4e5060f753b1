import json
from http import HTTPStatus

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.itbit import EXCHANGE_NAME, TradeIdMapping
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Direction


class VirtualItBitHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/v2/markets/{symbol}/recent-executions",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/v2/markets/{symbol}/order-book",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                    },
                ),
            ],
        )

    def _get_trades(self, request: HttpRequest, symbol: InstrumentWithStorage) -> HttpResponse:
        trade_id_mapping = TradeIdMapping()

        trades = symbol.storage.get_trades_relative_to_id(IdTraversalRequest(IdLimit.MAX, Direction.BACKWARD, 2000, True))

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "items": [
                    {
                        "match_number": trade_id_mapping.decode(trade.trade_id),
                        "amount": str(trade.amount),
                        "price": str(trade.price),
                        "executed_at": trade.time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    }
                    for trade in trades
                ]
            }),
        )

    def _get_book(self, request: HttpRequest, symbol: InstrumentWithMatchingEngine) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()

        books = {}
        if book.bids:
            books["bids"] = [{"price": str(bid.price), "amount": str(bid.amount)} for bid in book.bids]
        if book.asks:
            books["asks"] = [{"price": str(ask.price), "amount": str(ask.amount)} for ask in book.asks]
        return HttpResponse(HTTPStatus.OK, json.dumps(books))
