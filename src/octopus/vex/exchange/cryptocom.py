# Based on ...vex...bitstamp.py
import json
from http import HTTPStatus

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.bitstamp import EXCHANGE_NAME
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import Endpoint, EndpointParam, InstrumentWithStorage, VirtualExchangeHttpApi, instrument_param
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_ms
from src.utils.types import Order


class VirtualCryptoComHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/exchange/v1/public/get-trades",
                    {
                        "instrument_name": EndpointParam(True, instrument_param(self)),
                    },
                )
            ],
        )

    def _get_trades(self, request: HttpRequest, instrument_name: InstrumentWithStorage) -> HttpResponse:
        trades = instrument_name.storage.get_trades_in_range(TimeRangeTraversalRequest(None, None, Order.ASC, 1_000_000_000))

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "code": 0,
                "method": "public/get-trades",
                "result": {
                    "instrument_name": "BTC_USDT",  # TODO: fix inst name:  instrument_name.instrument.symbol?
                    "data": [
                        {
                            "d": trade.trade_id,
                            "q": str(trade.amount),
                            "p": str(trade.price),
                            "s": "buy" if trade.is_buy else "sell",
                            "t": dt_to_ms(trade.time),
                            "i": "BTC_USDT",
                        }
                        for trade in trades
                    ],
                },
            }),
        )
