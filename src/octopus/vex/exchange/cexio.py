import json
from http import HTTPStatus

from src.octopus.data import MarketType
from src.octopus.entry_history import IdTraversalRequest
from src.octopus.exchange.api import ExchangeInstrument
from src.octopus.exchange.cexio import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.translation import ExchangeTickerTranslator
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import Endpoint, EndpointParam, VirtualExchangeHttpApi, int_endpoint_param, string_endpoint_param
from src.resources.currency import glib_currency
from src.resources.exchange import glib_exchange
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_s
from src.utils.types import Direction


class VirtualCEXIOHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/trade_history/{base}/{quote}",
                    {
                        "base": EndpointParam(True, string_endpoint_param),
                        "quote": EndpointParam(True, string_endpoint_param),
                        "since": EndpointParam(True, int_endpoint_param(0)),
                    },
                )
            ],
        )

    def _get_trades(self, request: HttpRequest, base: str, quote: str, since: int) -> HttpResponse:
        translator = ExchangeTickerTranslator("CEX.IO", glib_exchange(), glib_currency())
        instrument_data = ExchangeInstrument(MarketType.SPOT, "{}/{}".format(base, quote), base.lower(), quote.lower())
        markets = translator.exchange_to_coinmetrics([instrument_data])

        if len(markets.recognized) == 0:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "unknown instrument name")

        if (instrument_storage := self.get_instrument_storage(markets.recognized[0])) is None:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "unknown instrument name")

        trades = instrument_storage.get_trades_relative_to_id(
            IdTraversalRequest(since, Direction.FORWARD, MAX_TRADES_PER_REQUEST, include_reference=True)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "tid": trade.trade_id,
                    "amount": str(trade.amount),
                    "price": str(trade.price),
                    "type": "buy" if trade.is_buy else "sell",
                    "date": dt_to_s(trade.time),
                }
                for trade in reversed(trades)
            ]),
        )
