import json
from http import HTTPStatus

from src.octopus.exchange.bitflyer import EXCH<PERSON>GE_NAME
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
)
from src.utils.http import HttpRequest, HttpResponse


class VirtualBitFlyerHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_book, "/v1/board", {"product_code": EndpointParam(True, instrument_matching_engine_param(self))}
                )
            ],
        )

    def _get_book(self, request: HttpRequest, product_code: InstrumentWithMatchingEngine) -> HttpResponse:
        book = product_code.matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "mid_price": str((book.asks[0].price + book.bids[0].price) / 2),
                "asks": [{"price": str(ask.price), "size": str(ask.amount)} for ask in book.asks],
                "bids": [{"price": str(bid.price), "size": str(bid.amount)} for bid in book.bids],
            }),
        )
