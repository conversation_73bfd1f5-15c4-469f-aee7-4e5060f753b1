import json
from http import HTTPStatus
from typing import Optional

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.localbitcoins import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_to_s
from src.utils.types import Direction


class VirtualLocalBitcoinsHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/bitcoincharts/{asset}/trades.json",
                    {
                        "asset": EndpointParam(True, instrument_param(self)),
                        "max_tid": EndpointParam(False, int_endpoint_param(0)),
                    },
                )
            ],
        )

    def _get_trades(self, request: HttpRequest, asset: InstrumentWithStorage, max_tid: Optional[int] = None) -> HttpResponse:
        trades = asset.storage.get_trades_relative_to_id(
            IdTraversalRequest(
                max_tid if max_tid is not None else IdLimit.MAX,
                Direction.BACKWARD,
                MAX_TRADES_PER_REQUEST,
                include_reference=True,
            )
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {"tid": trade.trade_id, "amount": str(trade.amount), "price": str(trade.price), "date": dt_to_s(trade.time)}
                for trade in trades
            ]),
        )
