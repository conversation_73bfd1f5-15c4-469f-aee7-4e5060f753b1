import json
from http import HTTPStatus

from src.octopus.entry_history import IdTraversalRequest
from src.octopus.exchange.therocktrading import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Direction


class VirtualTheRockTradingHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/v1/funds/{instrument}/trades",
                    {
                        "instrument": EndpointParam(True, instrument_param(self)),
                        "per_page": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "trade_id": EndpointParam(True, int_endpoint_param(0)),
                        "order": EndpointParam(True, string_endpoint_param),
                    },
                )
            ],
        )

    def _get_trades(
        self, request: HttpRequest, instrument: InstrumentWithStorage, per_page: int, trade_id: int, order: str
    ) -> HttpResponse:
        if order != "ASC":
            return HttpResponse(HTTPStatus.BAD_REQUEST, "expected ASC value for order")

        trades = instrument.storage.get_trades_relative_to_id(
            IdTraversalRequest(trade_id, Direction.FORWARD, per_page, include_reference=True)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "trades": [
                    {
                        "id": trade.trade_id,
                        "amount": str(trade.amount),
                        "price": str(trade.price),
                        "side": "buy" if trade.is_buy else "sell",
                        "date": trade.time.isoformat(),
                    }
                    for trade in trades
                ]
            }),
        )
