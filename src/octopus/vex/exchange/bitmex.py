import json
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import Optional

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.bitmex import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST, TIMESTAMP_RESOLUTION
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
    iso_8601_date_endpoint_param,
    string_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Order


class VirtualBitMEXHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/api/v1/trade",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "count": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "reverse": EndpointParam(True, string_endpoint_param),
                        "startTime": EndpointParam(True, iso_8601_date_endpoint_param, "start_time"),
                        "endTime": EndpointParam(False, iso_8601_date_endpoint_param, "end_time"),
                    },
                ),
                Endpoint(
                    self._get_liquidations,
                    "/api/v1/liquidation",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "count": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "reverse": EndpointParam(True, string_endpoint_param),
                        "startTime": EndpointParam(False, iso_8601_date_endpoint_param, "start_time"),
                        "endTime": EndpointParam(False, iso_8601_date_endpoint_param, "end_time"),
                    },
                ),
                Endpoint(
                    self._get_funding_rates,
                    "/api/v1/funding",
                    {
                        "symbol": EndpointParam(True, instrument_param(self)),
                        "count": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "reverse": EndpointParam(True, string_endpoint_param),
                        "startTime": EndpointParam(False, iso_8601_date_endpoint_param, "start_time"),
                        "endTime": EndpointParam(False, iso_8601_date_endpoint_param, "end_time"),
                    },
                ),
            ],
        )

    def _get_trades(
        self,
        request: HttpRequest,
        symbol: InstrumentWithStorage,
        count: int,
        reverse: str,
        start_time: datetime,
        end_time: Optional[datetime] = None,
    ) -> HttpResponse:
        if reverse == "true":
            order = Order.DESC
        elif reverse == "false":
            order = Order.ASC
        else:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "reverse param should be either `true` or `false`")

        if end_time is not None:
            end_time = end_time - TIMESTAMP_RESOLUTION

        trades = symbol.storage.get_trades_in_range(TimeRangeTraversalRequest(start_time, end_time, order, count))

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "trdMatchID": hex(trade.trade_id),
                    "size": str(trade.amount),
                    "price": str(trade.price),
                    "side": "Buy" if trade.is_buy else "Sell",
                    "trdType": "Regular",
                    "timestamp": trade.time.isoformat(),
                }
                for trade in trades
            ]),
        )

    def _get_liquidations(
        self,
        request: HttpRequest,
        symbol: InstrumentWithStorage,
        count: int,
        reverse: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> HttpResponse:
        if reverse == "true":
            order = Order.DESC
        elif reverse == "false":
            order = Order.ASC
        else:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "reverse param should be either `true` or `false`")

        if end_time is not None:
            end_time = end_time - TIMESTAMP_RESOLUTION

        liquidations = symbol.storage.get_liquidations_in_range(TimeRangeTraversalRequest(start_time, end_time, order, count))
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "orderID": hex(liquidation.liquidation_id),
                    "symbol": symbol.instrument.symbol,
                    "leavesQty": str(liquidation.amount),
                    "price": str(liquidation.price),
                    "side": "Buy" if liquidation.is_buy else "Sell",
                }
                for liquidation in liquidations
            ]),
        )

    def _get_funding_rates(
        self,
        request: HttpRequest,
        symbol: InstrumentWithStorage,
        count: int,
        reverse: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> HttpResponse:
        if reverse == "true":
            order = Order.DESC
        elif reverse == "false":
            order = Order.ASC
        else:
            return HttpResponse(HTTPStatus.BAD_REQUEST, "reverse param should be either `true` or `false`")

        if end_time is not None:
            end_time = end_time - TIMESTAMP_RESOLUTION

        funding_rates = symbol.storage.get_funding_rates_in_range(TimeRangeTraversalRequest(start_time, end_time, order, count))
        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "fundingInterval": (funding_rate.funding_interval + datetime(2000, 1, 1, 0, 0, 0)).isoformat(),
                    "symbol": symbol.instrument.symbol,
                    "fundingRate": str(funding_rate.funding_rate),
                    "timestamp": funding_rate.time.isoformat(),
                }
                for funding_rate in funding_rates
            ]),
        )
