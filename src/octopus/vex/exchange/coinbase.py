import json
from http import HTTPStatus
from typing import Optional

from src.octopus.entry_history import IdLimit, IdTraversalRequest
from src.octopus.exchange.coinbase import EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_param,
    int_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.types import Direction


class VirtualCoinbaseHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/products/{instrument}/trades",
                    {
                        "instrument": EndpointParam(True, instrument_param(self)),
                        "limit": EndpointParam(False, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                        "after": EndpointParam(False, int_endpoint_param(0)),
                    },
                )
            ],
        )

    def _get_trades(
        self, request: HttpRequest, instrument: InstrumentWithStorage, limit: int = 100, after: Optional[int] = None
    ) -> HttpResponse:
        trades = instrument.storage.get_trades_relative_to_id(
            IdTraversalRequest(after if after is not None else IdLimit.MAX, Direction.BACKWARD, limit, include_reference=False)
        )

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "trade_id": trade.trade_id,
                    "size": str(trade.amount),
                    "price": str(trade.price),
                    "side": "sell" if trade.is_buy else "buy",
                    "time": trade.time.isoformat(),
                }
                for trade in trades
            ]),
        )
