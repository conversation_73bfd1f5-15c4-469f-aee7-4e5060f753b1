import json
from datetime import datetime
from http import HTT<PERSON>tatus

from src.octopus.entry_history import TimeRangeTraversalRequest
from src.octopus.exchange.liquid import EXCHANGE_BIRTHDAY, EXCHANGE_NAME, MAX_TRADES_PER_REQUEST
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    InstrumentWithStorage,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
    instrument_param,
    int_endpoint_param,
)
from src.utils.http import HttpRequest, HttpResponse
from src.utils.timeutil import dt_from_s, dt_to_s, dt_to_us
from src.utils.types import Order


class VirtualLiquidHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_trades,
                    "/executions",
                    {
                        "product_id": EndpointParam(True, instrument_param(self)),
                        "timestamp": EndpointParam(True, int_endpoint_param(dt_to_s(EXCHANGE_BIRTHDAY))),
                        "limit": EndpointParam(True, int_endpoint_param(1, MAX_TRADES_PER_REQUEST)),
                    },
                ),
                Endpoint(
                    self._get_book,
                    "/products/{product_id}/price_levels",
                    {
                        "product_id": EndpointParam(True, instrument_matching_engine_param(self)),
                        "full": EndpointParam(True, int_endpoint_param(1, 1)),
                    },
                ),
            ],
        )
        self._instrument_names = {instrument.alt_symbol: instrument for instrument in self._instrument_names.values()}

    def _get_trades(self, request: HttpRequest, product_id: InstrumentWithStorage, timestamp: int, limit: int) -> HttpResponse:
        trades = product_id.storage.get_trades_in_range(TimeRangeTraversalRequest(dt_from_s(timestamp), None, Order.ASC, limit))

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps([
                {
                    "id": trade.trade_id,
                    "quantity": str(trade.amount),
                    "price": str(trade.price),
                    "taker_side": "buy" if trade.is_buy else "sell",
                    "created_at": dt_to_s(trade.time),
                }
                for trade in trades
            ]),
        )

    def _get_book(self, request: HttpRequest, product_id: InstrumentWithMatchingEngine, full: int) -> HttpResponse:
        book = product_id.matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "buy_price_levels": [[str(bid.price), str(bid.amount)] for bid in book.bids],
                "sell_price_levels": [[str(ask.price), str(ask.amount)] for ask in book.asks],
                "timestamp": str(dt_to_us(datetime.utcnow()) / 1_000_000),
            }),
        )
