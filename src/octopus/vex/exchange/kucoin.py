import json
from http import HTT<PERSON>tatus

from src.octopus.exchange.itbit import EX<PERSON><PERSON><PERSON>_NAME
from src.octopus.vex.api import VirtualExchangeMatchingEngine, VirtualExchangeStorage
from src.octopus.vex.http import (
    Endpoint,
    EndpointParam,
    InstrumentWithMatchingEngine,
    VirtualExchangeHttpApi,
    instrument_matching_engine_param,
)
from src.utils.http import HttpRequest, HttpResponse


class VirtualKuCoinHttpApi(VirtualExchangeHttpApi):
    def __init__(self, storage: VirtualExchangeStorage, matching_engine: VirtualExchangeMatchingEngine):
        super().__init__(
            EXCHANGE_NAME,
            storage,
            matching_engine,
            [
                Endpoint(
                    self._get_book,
                    "/api/v1/market/orderbook/level2_100",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                    },
                ),
                Endpoint(
                    self._get_future_book,
                    "/api/v1/level2/snapshot",
                    {
                        "symbol": EndpointParam(True, instrument_matching_engine_param(self)),
                    },
                ),
            ],
        )

    def _get_book(self, request: HttpRequest, symbol: InstrumentWithMatchingEngine) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "data": {
                    "sequence": "123",
                    "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids],
                    "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks],
                    "time": 1678013905,
                }
            }),
        )

    def _get_future_book(self, request: HttpRequest, symbol: InstrumentWithMatchingEngine) -> HttpResponse:
        book = symbol.matching_engine.get_last_book()

        return HttpResponse(
            HTTPStatus.OK,
            json.dumps({
                "data": {
                    "sequence": "123",
                    "bids": [[str(bid.price), str(bid.amount)] for bid in book.bids],
                    "asks": [[str(ask.price), str(ask.amount)] for ask in book.asks],
                    "ts": 1678013905,
                }
            }),
        )
