import urllib.parse
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Optional

import requests

PROMETHEUS_INSTANT_QUERY_ENDPOINT = "/api/v1/query"


@dataclass
class PrometheusResponse:
    metric: Dict[str, str]
    value: float
    time: datetime


class PrometheusSource:
    def __init__(self, base_url: str) -> None:
        self.prometheus_domain = base_url
        self.http_session = requests.Session()

    def instant_query(self, query_str: str, timeout: int = 10) -> Optional[PrometheusResponse]:
        url = urllib.parse.urljoin(self.prometheus_domain, PROMETHEUS_INSTANT_QUERY_ENDPOINT)
        r = self.http_session.get(
            url,
            params={  # type: ignore
                "query": query_str,
                "timeout": timeout,
            },
        )
        if r.status_code != 200:
            raise ValueError(f"Wrong response code: {r.text}")

        result = r.json()["data"]["result"]
        if not result:
            return None

        max_value: Optional[PrometheusResponse] = None
        for item in result:
            try:
                float_value = float(item["value"][1])
            except ValueError:
                float_value = 0

            if not max_value or max_value.value < float_value:
                max_value = PrometheusResponse(metric=item["metric"], value=float_value, time=item["value"][0])
        return max_value
