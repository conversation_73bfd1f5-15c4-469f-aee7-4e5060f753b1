from decimal import Decimal
from typing import Optional


class _StrViaRepr(Exception):
    def __str__(self) -> str:
        return self.__repr__()


class IncompleteMarketList(Exception):
    pass


class InstrumentNotFound(_StrViaRepr):
    def __init__(self, symbol: str):
        self._symbol = symbol

    def __repr__(self) -> str:
        return f"couldnt find {self._symbol} in exchange API output"


class InstrumentMetadataNotFound(_StrViaRepr):
    def __init__(self, symbol: str):
        self._symbol = symbol

    def __repr__(self) -> str:
        return f"Metadata for {self._symbol} instrument not found in exchange API output"


class InconsistentResponse(Exception):
    pass


class HistoryLoss(Exception):
    pass


class InconsistentMessageSequenceId(_StrViaRepr):
    def __init__(self, expected: int, received: int):
        self._expected = expected
        self._received = received

    def __repr__(self) -> str:
        return f"inconsistent sequence id detected, expected: {self._expected}, got: {self._received}"


class _ExchangeError(_StrViaRepr):
    _TYPE: str

    def __init__(self, description: str, code: Optional[str] = None):
        self._description = description
        self._code = code

    def __repr__(self) -> str:
        return "exchange {} error -- {}{}".format(
            self._TYPE, self._description, "({})".format(self._code) if self._code is not None else ""
        )


class ExchangeStreamError(_ExchangeError):
    _TYPE = "stream"


class ExchangeHttpError(_ExchangeError):
    _TYPE = "http"


""" Exchange message format errors """


class UnexpectedTradeIdFormat(Exception):
    pass


class UnexpectedMessageFormat(Exception):
    pass


""" Stream connection errors """


class ExchangePingPongError(Exception):
    pass


class ExchangePingPongTimeout(Exception):
    pass


class ExchangeHeartbeatTimeout(Exception):
    pass


class ConnectionConfirmationFailure(Exception):
    pass


class ConnectionAuthorizationFailure(Exception):
    pass


class UnexpectedMessageOrder(Exception):
    pass


""" Stream channel subscription errors """


class SubscriptionTimeout(Exception):
    pass


class SubscriptionFailure(Exception):
    pass


class UnexpectedSubscription(Exception):
    pass


""" Book errors """


class InconsistentBookSequenceId(Exception):
    pass


class ChecksumFailed(Exception):
    pass


class InvalidOrderCount(_StrViaRepr):
    def __init__(self, expected: str, count: int):
        self._expected = expected
        self._count = count

    def __repr__(self) -> str:
        return "expected {}, got: {}".format(self._expected, self._count)


class PriceLevelBadSort(_StrViaRepr):
    def __init__(self, prev_price: Decimal, next_price: Decimal, side: str):
        self._prev = prev_price
        self._next = next_price
        self._side = side

    def __repr__(self) -> str:
        return "bad sort of {}: {} - {}".format(self._side, self._prev, self._next)


class NonPositivePrice(Exception):
    pass


class NonPositiveAmount(Exception):
    pass


class MissingPriceLevel(Exception):
    pass


class BookIsCrossed(_StrViaRepr):
    def __init__(self, top_bid: Decimal, bottom_ask: Decimal):
        self._top_bid = top_bid
        self._bottom_ask = bottom_ask

    def __repr__(self) -> str:
        return "crossed book -- top bid: {} bottom ask: {}".format(self._top_bid, self._bottom_ask)


class InvalidOrderUpdate(Exception):
    pass


class InvalidOrder(Exception):
    pass


class DuplicateOrder(Exception):
    pass


class MissingOrder(Exception):
    pass


class UnsupportedMarketType(Exception):
    pass


class MarketError(Exception):
    pass


class NoLongerSupportedError(Exception):
    pass


class NoKafkaSourceError(Exception):
    pass


class NoDatabaseError(Exception):
    pass


class BatchSavedFailedError(Exception):
    pass


class DatabaseNotDefinedError(Exception):
    pass
