import time
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Callable, Generic, List, Optional, TypeVar, Union

import requests

from src.octopus.constants import DATETIME_1970
from src.octopus.data import HistoryMarker, Market, Trade
from src.octopus.exceptions import ExchangeHttpError, HistoryLoss
from src.octopus.exchange.api import IHistoryTraversal
from src.utils.diagnostics import Diagnostics, ICounterMetric, IGaugeMetric, ISlidingWindowMetric
from src.utils.pyroutine import IBatchProducer, IPyRoutineEnv, ProductionResult
from src.utils.timeutil import ProvideCurrentTime, dt_to_aware, dt_to_s

T = TypeVar("T")

P_T = TypeVar("P_T")  # payload type, such as TradeData, FundingRateData, etc.
D_T = TypeVar("D_T")  # data type, such as Trade, FundingRate, etc.


@dataclass(frozen=True)
class HttpProducerParams:
    poll_interval: timedelta
    time_provider: ProvideCurrentTime = lambda: datetime.utcnow()
    item_counter: Optional[ICounterMetric] = None
    scraped_market_count: Optional[IGaugeMetric] = None
    poll_stats: Optional[ISlidingWindowMetric] = None
    trade_lag_seconds: Optional[IGaugeMetric] = None


class HttpPollProducer(IBatchProducer[T], Generic[P_T, T]):
    def __init__(
        self,
        market: Market,
        http_request: Callable[[], List[P_T]],
        to_result: Callable[[Market, datetime, P_T], T],
        params: HttpProducerParams,
    ):
        self._market = market
        self._params = params
        self._http_request = http_request
        self._to_result = to_result

    def start(self, env: IPyRoutineEnv) -> None:
        if self._params.scraped_market_count is not None:
            self._params.scraped_market_count.inc(1)

    def stop(self, env: IPyRoutineEnv) -> None:
        if self._params.scraped_market_count is not None:
            self._params.scraped_market_count.dec(1)

    def produce(self, env: IPyRoutineEnv) -> ProductionResult[T]:
        requested_at = time.time()
        response = _make_request(self._http_request, env.diagnostics)

        if isinstance(response, float):
            return ProductionResult([], response)

        if self._params.item_counter is not None:
            self._params.item_counter.inc(len(response))

        current_time = self._params.time_provider()
        batch = [self._to_result(self._market, current_time, data) for data in response]
        time_spent = time.time() - requested_at

        if self._params.poll_stats is not None:
            self._params.poll_stats.observe(time_spent)

        if self._params.trade_lag_seconds and batch:
            lag_seconds = self._get_trade_lag_seconds(batch)
            if lag_seconds is not None:
                self._params.trade_lag_seconds.set(lag_seconds)

        sleep_time = max(0.0, self._params.poll_interval.total_seconds() - time_spent)
        return ProductionResult(batch, sleep_time)

    @staticmethod
    def _get_trade_lag_seconds(batch: List[T]) -> Optional[float]:
        if isinstance(batch[0], Trade) and isinstance(batch[-1], Trade):
            latest_trade_time = batch[0].data.time
            if batch[-1].data.time > latest_trade_time:
                latest_trade_time = batch[-1].data.time
            return (datetime.now(timezone.utc) - dt_to_aware(latest_trade_time)).total_seconds()
        return None


class HistoryProducer(IBatchProducer[D_T], Generic[D_T, P_T]):
    def __init__(
        self,
        market: Market,
        sleep_time: float,
        traversal: IHistoryTraversal[P_T],
        last_entry_marker: Optional[HistoryMarker],
        construct_entity: Callable[[Market, P_T], D_T],
        get_entry_id: Callable[[P_T], int],
        get_entry_time: Callable[[P_T], datetime],
        obtain_exchange_last_entry: Optional[Callable[[], Optional[P_T]]] = None,
        item_counter: Optional[ICounterMetric] = None,
        scraped_market_count: Optional[IGaugeMetric] = None,
        stop_entry_marker: Optional[HistoryMarker] = None,
        to_skip: Optional[Callable[[P_T], bool]] = None,
    ):
        self._market = market
        self._sleep_time = sleep_time
        self._traversal = traversal
        self._last_entry_marker = last_entry_marker
        self._obtain_exchange_last_entry = obtain_exchange_last_entry
        self._item_counter = item_counter
        self._scraped_market_count = scraped_market_count
        self._lag_metric_update_interval_seconds = 90.0
        self._construct_entity = construct_entity
        self._get_entry_time = get_entry_time
        self._get_entry_id = get_entry_id
        self._stop_entry_marker = stop_entry_marker
        self._to_skip = to_skip

    def start(self, env: IPyRoutineEnv) -> None:
        self._lag_metric_updated_at = float("-inf")
        self._lag_metric = env.diagnostics.gauge("history_collection_lag", ("market",)).tags({
            "market": self._market.instrument.symbol
        })

        if self._last_entry_marker is not None:
            env.diagnostics.info(f"scraper state provided: {self._last_entry_marker.id} at {self._last_entry_marker.time}")
        else:
            env.diagnostics.info("scraper state not provided, will get first entry from exchange")

        if self._scraped_market_count is not None:
            self._scraped_market_count.inc(1)

    def stop(self, env: IPyRoutineEnv) -> None:
        if self._scraped_market_count is not None:
            self._scraped_market_count.dec(1)

    def produce(self, env: IPyRoutineEnv) -> ProductionResult[D_T]:
        if self._last_entry_marker is None:
            return self._produce_first_entries(env)

        if time.time() - self._lag_metric_updated_at > self._lag_metric_update_interval_seconds:
            self._update_lag_metric(env.diagnostics)

        reference = self._last_entry_marker  # appeasing mypy
        response = _make_request(lambda: self._traversal.next_entries(reference), env.diagnostics)

        if isinstance(response, float):
            return ProductionResult([], response)

        if response.entries_missed:
            env.diagnostics.error_ns(HistoryLoss(f"some entries after {self._last_entry_marker} will be missed"))

        if response.last_entry_marker is not None and self._last_entry_marker != response.last_entry_marker:
            sleep_time = 0.0
            if response.entries:
                self._last_entry_marker = HistoryMarker(
                    id=self._get_entry_id(response.entries[-1]), time=self._get_entry_time(response.entries[-1])
                )
            else:
                self._last_entry_marker = response.last_entry_marker

            if self._item_counter is not None:
                self._item_counter.inc(len(response.entries))

            env.diagnostics.debug(
                f"{len(response.entries)} entries fetched up to {self._last_entry_marker.id} at {self._last_entry_marker.time}"
            )
        else:
            sleep_time = self._sleep_time
            self._update_lag_metric(env.diagnostics)
            env.diagnostics.debug("no new entries fetched")

        if self._stop_entry_marker:
            if (
                self._stop_entry_marker.id
                and self._last_entry_marker.id
                and self._last_entry_marker.id >= self._stop_entry_marker.id
            ):
                env.request_stop()

            elif (
                self._stop_entry_marker.time != DATETIME_1970
                and self._last_entry_marker.time != DATETIME_1970
                and self._last_entry_marker.time >= self._stop_entry_marker.time
            ):
                env.request_stop()

        return ProductionResult(
            [
                self._construct_entity(self._market, data)
                for data in response.entries
                if not self._to_skip or not self._to_skip(data)
            ],
            sleep_time,
        )

    def _produce_first_entries(self, env: IPyRoutineEnv) -> ProductionResult[D_T]:
        try:
            traversal_result = self._traversal.first_entries()
            if traversal_result.entries_missed:
                env.diagnostics.error_ns(HistoryLoss("some of the first entries will be missed"))
        except Exception as e:
            env.diagnostics.error(e, "failed to fetch first entries")
            return ProductionResult([], 1.0)

        if traversal_result.last_entry_marker is None:
            env.diagnostics.debug("exchange didn't return any first entries")
            return ProductionResult([], self._sleep_time)
        else:
            self._last_entry_marker = traversal_result.last_entry_marker

            env.diagnostics.info(f"{len(traversal_result.entries)} entries fetched up to {self._last_entry_marker}")
            return ProductionResult([self._construct_entity(self._market, data) for data in traversal_result.entries], 0.0)

    def _update_lag_metric(self, diagnostics: Diagnostics) -> None:
        if self._obtain_exchange_last_entry is None or self._last_entry_marker is None:
            return

        try:
            exchange_last_entry = self._obtain_exchange_last_entry()
        except Exception as e:
            diagnostics.error(e, "failed to obtain last entries")
        else:
            if exchange_last_entry is not None:
                lag = dt_to_s(self._get_entry_time(exchange_last_entry)) - dt_to_s(self._last_entry_marker.time)
                self._lag_metric.set(lag)
                self._lag_metric_updated_at = time.time()


def _make_request(request: Callable[[], T], diagnostics: Diagnostics) -> Union[T, float]:
    try:
        return request()
    except requests.exceptions.ReadTimeout as e:
        diagnostics.error_ns(e, f"read timeout for {e.request.url}")
        return 0.0
    except requests.exceptions.HTTPError as e:
        diagnostics.error_ns(e, f"invalid HTTP code for {e.request.url}: {e}")
        return 1.0
    except requests.exceptions.ConnectionError as e:
        diagnostics.error_ns(e)
        return 0.0
    except ExchangeHttpError as e:
        diagnostics.error_ns(e)
        return 1.0
    except Exception as e:
        diagnostics.error(e, "unexpected error")
        return 1.0
