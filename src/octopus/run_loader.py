import time
from argparse import Argument<PERSON>arser, Namespace
from dataclasses import dataclass
from datetime import UTC, datetime, timedelta
from typing import Any, Dict, Generic, List, Optional, Set, Tuple, Type, TypeVar, cast

from confluent_kafka import Consumer

from src.octopus.arguments import postgres_out_argument
from src.octopus.data import Book, FundingRate, Liquidation, MarketType, OpenInterest, Trade
from src.octopus.exceptions import DatabaseNotDefinedError, NoDatabaseError, NoKafkaSourceError
from src.octopus.exchange.deribit import EXCHANGE_NAME as DERIBIT_EXCHANGE_NAME
from src.octopus.feed_protocol_pb2 import (
    BookMessageV2,
    FundingRateEntry,
    LiquidationEntry,
    OpenInterestEntry,
    TradeEntry,
    TradeMessage,
)
from src.octopus.run import BookScraper, ScraperParams, StorageWithBackup
from src.octopus.storage.api import IBookStorage, IFundingRateStorage, ILiquidationStorage, IOpenInterestStorage, ITradeStorage
from src.octopus.storage.postgres.book import PostgresFuturesBookStorage, PostgresSpotBookStorage
from src.octopus.storage.postgres.book_old import (
    PostgresOldFuturesBookStorage,
    PostgresOldOptionBookStorage,
    PostgresOldSpotBookStorage,
)
from src.octopus.storage.postgres.funding_rate import PostgresFundingRateStorage
from src.octopus.storage.postgres.liquidations import PostgresLiquidationStorage
from src.octopus.storage.postgres.open_interest import PostgresFuturesOpenInterestStorage, PostgresOptionOpenInterestStorage
from src.octopus.storage.postgres.trade import (
    DeribitPostgresTradeStorage,
    PostgresOptionTradeStorage,
    PostgresSpotTradeStorage,
    postgres_trade_futures_storage_factory,
)
from src.octopus.streaming_protocols.protobuf import (
    book_from_message_v2,
    funding_rate_from_message,
    liquidation_from_message,
    open_interest_from_message,
)
from src.resources.currency import glib_currency
from src.resources.exchange import glib_exchange
from src.utils.application import Application, application_arguments_parser
from src.utils.regex import RegexList

D_T = TypeVar("D_T")  # scraped data type: Trade, Book, OpenInterest etc.
N_T = TypeVar("N_T")  # representation of D_T suitable to be sent over network.
S_T = TypeVar("S_T")  # type of storage.

KAFKA_POLL_MAX_RECORDS_DEFAULT = 200000
KAFKA_MAX_PARTITION_FETCH_BYTES = 20 * 1024 * 1024
KAFKA_TIMEOUT_MS = 50
DEFAULT_MARKET_TYPES = [MarketType.SPOT, MarketType.FUTURES, MarketType.OPTION]
DATETIME_1970 = datetime(1970, 1, 1, tzinfo=UTC)


def parse_market_type(value: str) -> MarketType:
    for enum_value in MarketType:
        if enum_value.name.lower() == value:
            return enum_value
    else:
        raise ValueError(f"Unknown market type: {value}")


@dataclass(frozen=True)
class DataLoaderParams(ScraperParams): ...


def _get_instrument_pairs(instruments: Set[str]) -> Set[Tuple[int, int]]:
    return {
        (
            glib_currency().currency_id_by_ticker(instruments_parts[0]),
            glib_currency().currency_id_by_ticker(instruments_parts[1]),
        )
        for instrument in instruments
        if len(instruments_parts := instrument.split("-")) == 2 and instrument == instrument.lower()
    }


def data_loader_arguments_parser(parser: ArgumentParser) -> ArgumentParser:
    parser.add_argument("exchange", type=str, help="Target exchange's name from the list found in resources/exchange.json")

    parser.add_argument("--kafka-in", type=str, default="", help="Kafka server - source for data to save")

    parser.add_argument(
        "--kafka-max-records",
        type=int,
        default=KAFKA_POLL_MAX_RECORDS_DEFAULT,
        help="Max records to consumed from kafka in one poll",
    )
    parser.add_argument(
        "--kafka-max-partition-fetch-bytes",
        type=int,
        default=KAFKA_MAX_PARTITION_FETCH_BYTES,
        help="Max bytes to consume from kafka in one poll",
    )
    parser.add_argument(
        "--kafka-timeout-ms",
        type=int,
        default=KAFKA_TIMEOUT_MS,
        help="Milliseconds spent waiting in poll if data is not available in the buffer",
    )

    parser.add_argument(
        "--postgres-out-spot",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve databases (the latter is optional) for spots",
    )
    parser.add_argument(
        "--postgres-out-futures",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve databases (the latter is optional) for futures",
    )
    parser.add_argument(
        "--postgres-out-option",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve databases (the latter is optional) for options",
    )

    parser.add_argument(
        "--postgres-exch-out-spot",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve per-exchange databases (the latter is optional) for spots",
    )
    parser.add_argument(
        "--postgres-exch-out-futures",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve per-exchange databases (the latter is optional) for futures",
    )
    parser.add_argument(
        "--postgres-exch-out-option",
        type=postgres_out_argument,
        nargs="+",
        default=[],
        help="host:port:db_name:user:password[+host:port:db_name:user:password] "
        "- primary and reserve per-exchange databases (the latter is optional) for options",
    )

    parser.add_argument(
        "--market-type",
        type=parse_market_type,
        default=None,
        help="List of machine names where replicas of scraper are deployed",
    )

    parser.add_argument(
        "--instruments",
        type=str,
        nargs="+",
        default=RegexList.ALWAYS_MATCH,
        help="Instructs data loader to save data only for specified instruments "
        "in format base-quote (look up CM tickers in resources/currency.json) for spot, "
        "symbol (can be a regex) for futures and options.",
    )
    parser.add_argument(
        "--exclude-instruments",
        type=str,
        nargs="+",
        default=RegexList.NO_MATCH,
        help="Instructs data loader to exclude data with specified instruments.Use same format as --instruments argument.",
    )

    return parser


def book_data_loader_arguments_parser(parser: ArgumentParser) -> ArgumentParser:
    parser.add_argument(
        "--instruments-100-depth",
        type=str,
        nargs="+",
        default=RegexList.ALWAYS_MATCH,
        help="Instructs data loader to save data only for specified instruments "
        "in format base-quote (look up CM tickers in resources/currency.json) for spot, "
        "symbol (can be a regex) for futures and options.",
    )
    parser.add_argument(
        "--exclude-instruments-100-depth",
        type=str,
        nargs="+",
        default=RegexList.NO_MATCH,
        help="Instructs data loader to exclude data with specified instruments.Use same format as --instruments argument.",
    )
    return parser


class DataLoader(Generic[D_T, N_T, S_T]):
    @staticmethod
    def parse_cli() -> Namespace:
        parser = ArgumentParser()
        application_arguments_parser(parser)
        data_loader_arguments_parser(parser)
        return parser.parse_args()

    def __init__(self, params: ScraperParams, app: Application, args: Namespace) -> None:
        self.params = params
        self.app = app
        self.args = args
        self.exchange_id = glib_exchange().exchange_id_by_name(args.exchange)
        self.market_type: Optional[MarketType] = args.market_type
        self.market_type_filter: Optional[bytes] = self.market_type.name.lower() if self.market_type else None
        self.diagnostics = app.diagnostics
        self._items_saved_per_symbol_counter = self.diagnostics.counter("data_loader_items_saved", ("symbol",))
        self.kafka_server = args.kafka_in
        if not self.kafka_server:
            raise NoKafkaSourceError("No Kafka source defined for data loader app")

        self.instruments = RegexList(args.instruments)
        self.exclude_instruments = RegexList(args.exclude_instruments)

        self.current_batch: Dict[MarketType, List[D_T]] = {MarketType.SPOT: [], MarketType.FUTURES: [], MarketType.OPTION: []}
        self.last_saved: float = time.time()
        self._databases = self.databases()

    def run(self) -> None:
        consumer = Consumer({
            "bootstrap.servers": self.kafka_server,
            "group.id": self.kafka_group_id,
            "max.partition.fetch.bytes": self.args.kafka_max_partition_fetch_bytes,
        })
        consumer.subscribe([self.kafka_topic_name])
        while True:
            _now = time.time()
            message = consumer.poll(1.0)
            self.diagnostics.counter("fetching_time_spent").inc(time.time() - _now)

            try:
                self._process(message)
            except KeyboardInterrupt:
                pass
            except Exception as e:
                self.diagnostics.error(e, f"Cannot process message: {message}")

    def _process(self, message: Any) -> None:
        if message:
            if message.error() is not None:
                raise Exception(f"Kafka error: {message.error()}")

            _now = time.time()
            batch = self._transform_from_protobuf(message.value())
            self.diagnostics.gauge("data_loader_kafka_lag").set(time.time() - message.timestamp()[1] / 1000)
            if len(batch) == 0:
                return

            market_type = self.extract_market(batch)
            if self.market_type and market_type != self.market_type:
                return

            batch = [item for item in batch if self._is_valid_instrument(item.market.instrument.symbol)]
            if not batch:
                return

            self.current_batch[market_type].extend(batch)
            for item in batch:
                self.diagnostics.counter("data_loader_items_received", ("symbol",)).tags({
                    "symbol": item.market.instrument.symbol
                }).inc(1)
            self.diagnostics.counter("processing_time_spent").inc(time.time() - _now)

        self._try_save_all_market_types()

    def _is_valid_instrument(self, symbol: str) -> bool:
        return self.instruments.match(symbol) and not self.exclude_instruments.match(symbol)

    def _try_save_all_market_types(self) -> None:
        _now = time.time()
        if _now - self.last_saved > 0.5:
            for market_type in self.current_batch:
                if not self.current_batch[market_type]:
                    continue

                dbs = self._databases.get(market_type)
                if not dbs:
                    raise DatabaseNotDefinedError(f"No database defined for: {market_type}.")

                self.save_to_db(self.current_batch[market_type], dbs)
                self.current_batch[market_type] = []
            self.last_saved = _now
        self.diagnostics.counter("db_saving_time_spent").inc(time.time() - _now)

    def save_to_db(self, batch: List[D_T], dbs: List[StorageWithBackup[S_T]]) -> None:
        for db in dbs:
            if self.db_save_operation(db.main, batch):
                self.diagnostics.counter("database_consumer_items_stored").inc(len(batch))
                continue

            if self.db_save_operation(db.backup, batch):
                self.diagnostics.counter("database_consumer_items_stored").inc(len(batch))
                continue

            self.diagnostics.counter("database_consumer_items_lost").inc(len(batch))

    def _transform_from_protobuf(self, content: bytes) -> List[D_T]:
        raise NotImplementedError

    @property
    def kafka_group_id(self) -> str:
        raise NotImplementedError

    @property
    def kafka_topic_name(self) -> str:
        raise NotImplementedError

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[S_T]]]:
        raise NotImplementedError

    def extract_market(self, batch: List[D_T]) -> MarketType:
        raise NotImplementedError

    def db_save_operation(self, db: S_T, batch: List[D_T]) -> bool:
        raise NotImplementedError


class TradeDataLoader(DataLoader[TradeEntry, TradeMessage, ITradeStorage]):
    def _process(self, message: Any) -> None:
        if message:
            if message.error() is not None:
                raise Exception(f"Kafka error: {message.error()}")

            _now = time.time()
            trade = self._transform_from_protobuf(message.value())
            self.diagnostics.gauge("data_loader_kafka_lag").set(time.time() - message.timestamp()[1] / 1000)

            market_type = MarketType(trade.market_type)
            if self.market_type and market_type != self.market_type:
                return

            if not self._is_valid_instrument(trade.symbol):
                return

            self.current_batch[market_type].append(trade)
            self.diagnostics.counter("data_loader_items_received", ("symbol",)).tags({"symbol": trade.symbol}).inc(1)
            self.diagnostics.counter("processing_time_spent").inc(time.time() - _now)

        self._try_save_all_market_types()

    def _transform_from_protobuf(self, content: bytes) -> TradeEntry:
        message = TradeEntry()
        message.ParseFromString(content)
        return message

    @property
    def kafka_group_id(self) -> str:
        kafka_group_id = f"trades_data_loader_{self.exchange_id}"
        if self.market_type:
            kafka_group_id = f"{kafka_group_id}_{self.market_type.name.lower()}"
        if self.instruments:
            kafka_group_id = f"{kafka_group_id}_{self.instruments[0].lower()}"
        return kafka_group_id

    @property
    def kafka_topic_name(self) -> str:
        return f"trades_{self.exchange_id}.proto"

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[ITradeStorage]]]:
        res = {}
        if self.args.postgres_exch_out_spot:
            if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                res[MarketType.SPOT] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            DeribitPostgresTradeStorage(MarketType.SPOT, self.exchange_id, main, diagnostics=self.diagnostics),
                            DeribitPostgresTradeStorage(MarketType.SPOT, self.exchange_id, backup, diagnostics=self.diagnostics),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_spot
                ]
            else:
                res[MarketType.SPOT] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            PostgresSpotTradeStorage(self.exchange_id, main, diagnostics=self.diagnostics),
                            PostgresSpotTradeStorage(self.exchange_id, backup, diagnostics=self.diagnostics),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_spot
                ]
        if self.args.postgres_exch_out_futures:
            if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                res[MarketType.FUTURES] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            DeribitPostgresTradeStorage(
                                MarketType.FUTURES, self.exchange_id, main, diagnostics=self.diagnostics
                            ),
                            DeribitPostgresTradeStorage(
                                MarketType.FUTURES, self.exchange_id, backup, diagnostics=self.diagnostics
                            ),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_futures
                ]
            else:
                res[MarketType.FUTURES] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            postgres_trade_futures_storage_factory(
                                exchange_id=self.exchange_id, connection_params=main, diagnostics=self.diagnostics
                            ),
                            postgres_trade_futures_storage_factory(
                                exchange_id=self.exchange_id, connection_params=backup, diagnostics=self.diagnostics
                            ),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_futures
                ]
        if self.args.postgres_exch_out_option:
            if self.args.exchange == DERIBIT_EXCHANGE_NAME:
                res[MarketType.OPTION] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            DeribitPostgresTradeStorage(MarketType.OPTION, self.exchange_id, main, diagnostics=self.diagnostics),
                            DeribitPostgresTradeStorage(
                                MarketType.OPTION, self.exchange_id, backup, diagnostics=self.diagnostics
                            ),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_option
                ]
            else:
                res[MarketType.OPTION] = [
                    cast(
                        StorageWithBackup[ITradeStorage],
                        StorageWithBackup(
                            PostgresOptionTradeStorage(self.exchange_id, main, diagnostics=self.diagnostics),
                            PostgresOptionTradeStorage(self.exchange_id, backup, diagnostics=self.diagnostics),
                        ),
                    )
                    for main, backup in self.args.postgres_exch_out_option
                ]

        if not res:
            raise NoDatabaseError("No postgres database specified.")
        return res

    def db_save_operation(self, db: ITradeStorage, batch: List[TradeEntry]) -> bool:
        try:
            db.save_trades_from_protobuf(batch, self.exchange_id)
            return True
        except Exception as e:
            self.diagnostics.error(e, "Couldn't insert trades batch")
            return False


def _adjust_datetime_to_interval(book_time: datetime, interval: timedelta) -> datetime:
    total_seconds = interval.total_seconds()
    return datetime.fromtimestamp(book_time.timestamp() // total_seconds * total_seconds, book_time.tzinfo)


class BookDataLoader(DataLoader[Book, BookMessageV2, IBookStorage]):
    SAVING_INTERVAL_FULL_BOOKS = timedelta(hours=1)
    SAVING_INTERVAL_100_DEPTH_BOOKS = timedelta(seconds=10)

    @staticmethod
    def parse_cli() -> Namespace:
        parser = ArgumentParser()
        application_arguments_parser(parser)
        data_loader_arguments_parser(parser)
        book_data_loader_arguments_parser(parser)
        return parser.parse_args()

    def __init__(self, params: ScraperParams, app: Application, args: Namespace) -> None:
        super().__init__(params, app, args)
        self.instruments_100_depth = RegexList(args.instruments_100_depth)
        self.exclude_instruments_100_depth = RegexList(args.exclude_instruments_100_depth)

        self.next_save_time_100_depth_books: Dict[Tuple[str, str], datetime] = {}
        self.next_save_time_full_books: Dict[Tuple[str, str], datetime] = {}

    def _process(self, message: Any) -> None:
        if message:
            self.diagnostics.gauge("data_loader_kafka_lag").set(time.time() - message.timestamp()[1] / 1000)
            is_full_book, market_type, symbol = self._parse_message_headers(message)
            self.diagnostics.counter("data_loader_items_received", ("symbol", "depth")).tags({
                "symbol": symbol,
                "depth": "30000" if is_full_book else "",
            }).inc(1)

            _now = time.time()
            if not self._message_filtered(message, is_full_book, market_type, symbol):
                return

            book = self._transform_from_protobuf(message.value())
            assert symbol == book.market.instrument.symbol
            assert market_type == book.market.instrument.market_type.name.lower()
            self.diagnostics.counter("processing_time_spent").inc(time.time() - _now)

            self._process_batch(book)

    @staticmethod
    def _parse_message_headers(message: BookMessageV2) -> Tuple[bool, str, str]:
        is_full_book, market_type, symbol = False, "", ""
        for name, header_bytes in message.headers() or ():
            if name == "snapshot_depth":
                is_full_book = header_bytes == b"30000"
            elif name == "market_type":
                market_type = header_bytes.decode("utf-8")
            elif name == "symbol":
                symbol = header_bytes.decode("utf-8")

        return is_full_book, market_type, symbol

    def _message_filtered(self, message: BookMessageV2, is_full_book: bool, market_type: str, symbol: str) -> bool:
        if not is_full_book:
            self.diagnostics.counter("message_filtered_by_book_type").inc(1)
            return False

        if self.market_type_filter is not None and self.market_type_filter != market_type:
            self.diagnostics.counter("message_filtered_by_market_type", ("market_type",)).tags({"market_type": market_type}).inc(
                1
            )
            return False

        is_valid_full_book = self._is_valid_full_book(symbol)
        is_valid_100_depth = self._is_valid_100_depth(symbol)
        if not is_valid_full_book and not is_valid_100_depth:
            self.diagnostics.counter("message_filtered_by_instrument", ("symbol",)).tags({"symbol": symbol}).inc(1)
            return False

        time_key = (symbol, market_type)
        message_time = datetime.fromtimestamp(message.timestamp()[1] / 1000, tz=UTC)
        if is_valid_100_depth and message_time >= self.next_save_time_100_depth_books.get(time_key, DATETIME_1970):
            return True

        if is_valid_full_book and message_time >= self.next_save_time_full_books.get(time_key, DATETIME_1970):
            return True

        self.diagnostics.counter("message_filtered_by_time", ("symbol",)).tags({"symbol": symbol}).inc(1)
        return False

    def _is_valid_instrument(self, symbol: str) -> bool:
        return self._is_valid_full_book(symbol) or self._is_valid_100_depth(symbol)

    def _is_valid_full_book(self, symbol: str) -> bool:
        return self.instruments.match(symbol) and not self.exclude_instruments.match(symbol)

    def _is_valid_100_depth(self, symbol: str) -> bool:
        return self.instruments_100_depth.match(symbol) and not self.exclude_instruments_100_depth.match(symbol)

    def _process_batch(self, book: Book) -> None:
        time_key = (book.market.instrument.symbol, book.market.instrument.market_type.name.lower())

        if self._is_valid_100_depth(book.market.instrument.symbol):
            next_save_time = self.next_save_time_100_depth_books.get(time_key, DATETIME_1970)
            if book.collect_time >= next_save_time:
                interval = self.SAVING_INTERVAL_100_DEPTH_BOOKS
                book_100_depth = self._slice_book(book, depth=100, interval=interval)
                self._save_book(book_100_depth)
                self.next_save_time_100_depth_books[time_key] = book_100_depth.deduplication_time + interval
            else:
                self.diagnostics.counter("message_not_processed_by_time", ("symbol", "depth")).tags({
                    "symbol": book.market.instrument.symbol,
                    "depth": 100,
                }).inc(1)

        else:
            self.diagnostics.counter("message_not_processed_by_instrument", ("symbol", "depth")).tags({
                "symbol": book.market.instrument.symbol,
                "depth": 100,
            }).inc(1)

        if self._is_valid_full_book(book.market.instrument.symbol):
            next_save_time = self.next_save_time_full_books.get(time_key, DATETIME_1970)
            if book.collect_time >= next_save_time:
                interval = self.SAVING_INTERVAL_FULL_BOOKS
                book_full_depth = self._slice_book(book, depth=30000, interval=interval)
                self._save_book(book_full_depth)
                self.next_save_time_full_books[time_key] = book_full_depth.deduplication_time + interval
            else:
                self.diagnostics.counter("message_not_processed_by_time", ("symbol", "depth")).tags({
                    "symbol": book.market.instrument.symbol,
                    "depth": 30000,
                }).inc(1)

        else:
            self.diagnostics.counter("message_not_processed_by_instrument", ("symbol", "depth")).tags({
                "symbol": book.market.instrument.symbol,
                "depth": 30000,
            }).inc(1)

    def _slice_book(self, book: Book, depth: int, interval: timedelta) -> Book:
        _now = time.time()
        deduplication_time = _adjust_datetime_to_interval(book.collect_time, interval)
        sliced_book = BookScraper.apply_desired_depth(book, depth, deduplication_time=deduplication_time)
        self.diagnostics.counter("slicing_time_spent").inc(time.time() - _now)
        return sliced_book

    def _save_book(self, book: Book) -> None:
        _now = time.time()
        dbs = self._databases.get(market_type := book.market.instrument.market_type)
        if not dbs:
            raise DatabaseNotDefinedError(f"No database defined for: {market_type}.")

        self.save_to_db([book], dbs)
        self.diagnostics.counter("data_loader_saved_items", ("symbol", "depth")).tags({
            "symbol": book.market.instrument.symbol,
            "depth": book.depth_limit,
        }).inc(1)
        self.diagnostics.counter("db_saving_time_spent").inc(time.time() - _now)

    def _transform_from_protobuf(self, content: bytes) -> Book:
        message = BookMessageV2()
        message.ParseFromString(content)
        return book_from_message_v2(message, self.exchange_id)

    @property
    def kafka_group_id(self) -> str:
        kafka_group_id = f"books_data_loader_{self.exchange_id}"
        if self.market_type:
            kafka_group_id = f"{kafka_group_id}_{self.market_type.name.lower()}"
        if self.instruments:
            kafka_group_id = f"{kafka_group_id}_{self.instruments[0].lower()}"
        return kafka_group_id

    @property
    def kafka_topic_name(self) -> str:
        return f"books_{self.exchange_id}.proto"

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[IBookStorage]]]:
        res = {}
        if self.args.postgres_exch_out_spot or self.args.postgres_out_spot:
            res[MarketType.SPOT] = [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        PostgresOldSpotBookStorage(main, schema_name="loader"),
                        PostgresOldSpotBookStorage(backup, schema_name="loader"),
                    ),
                )
                for main, backup in self.args.postgres_out_spot
            ] + [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        PostgresSpotBookStorage(exchange_id=self.exchange_id, connection_params=main, schema_name="loader"),
                        PostgresSpotBookStorage(exchange_id=self.exchange_id, connection_params=backup, schema_name="loader"),
                    ),
                )
                for main, backup in self.args.postgres_exch_out_spot
            ]
        if self.args.postgres_exch_out_futures or self.args.postgres_out_futures:
            res[MarketType.FUTURES] = [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        PostgresOldFuturesBookStorage(main, schema_name="loader"),
                        PostgresOldFuturesBookStorage(backup, schema_name="loader"),
                    ),
                )
                for main, backup in self.args.postgres_out_futures
            ] + [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        PostgresFuturesBookStorage(exchange_id=self.exchange_id, connection_params=main, schema_name="loader"),
                        PostgresFuturesBookStorage(exchange_id=self.exchange_id, connection_params=backup, schema_name="loader"),
                    ),
                )
                for main, backup in self.args.postgres_exch_out_futures
            ]
        if self.args.postgres_out_option:
            res[MarketType.OPTION] = [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        PostgresOldOptionBookStorage(main, schema_name="loader"),
                        PostgresOldOptionBookStorage(backup, schema_name="loader"),
                    ),
                )
                for main, backup in self.args.postgres_out_option
            ]
        if not res:
            raise NoDatabaseError("No postgres database specified.")
        return res

    def db_save_operation(self, db: IBookStorage, batch: List[Book]) -> bool:
        try:
            db.save_books(batch)
            return True
        except Exception as e:
            self.diagnostics.error(e, "Couldn't insert books batch")
            return False

    def extract_market(self, batch: List[Book]) -> MarketType:
        return batch[0].market.instrument.market_type


class FundingRateDataLoader(DataLoader[FundingRate, FundingRateEntry, IFundingRateStorage]):
    def _transform_from_protobuf(self, content: bytes) -> List[FundingRate]:
        message = FundingRateEntry()
        message.ParseFromString(content)
        return [funding_rate_from_message(message, self.exchange_id)]

    @property
    def kafka_group_id(self) -> str:
        return f"funding_rates_data_loader_{self.exchange_id}"

    @property
    def kafka_topic_name(self) -> str:
        return f"funding_rates_{self.exchange_id}.proto"

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[IFundingRateStorage]]]:
        if self.args.postgres_out_futures:
            return {
                MarketType.FUTURES: [
                    StorageWithBackup(
                        PostgresFundingRateStorage(main),
                        PostgresFundingRateStorage(backup),
                    )
                    for main, backup in self.args.postgres_out_futures
                ]
            }
        else:
            raise NoDatabaseError("No postgres database specified.")

    def db_save_operation(self, db: IFundingRateStorage, batch: List[FundingRate]) -> bool:
        try:
            db.save_funding_rates(batch)
            return True
        except Exception as e:
            self.diagnostics.error(e, "Couldn't insert funding rates batch")
            return False

    def extract_market(self, batch: List[Trade]) -> MarketType:
        return MarketType.FUTURES


class OpenInterestDataLoader(DataLoader[OpenInterest, OpenInterestEntry, IFundingRateStorage]):
    def _transform_from_protobuf(self, content: bytes) -> List[OpenInterest]:
        message = OpenInterestEntry()
        message.ParseFromString(content)
        return [open_interest_from_message(message, self.exchange_id)]

    @property
    def kafka_group_id(self) -> str:
        return f"open_interests_data_loader_{self.exchange_id}"

    @property
    def kafka_topic_name(self) -> str:
        return f"open_interests_{self.exchange_id}.proto"

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[IOpenInterestStorage]]]:
        res = {}
        if self.args.postgres_out_futures:
            res[MarketType.FUTURES] = [
                cast(
                    StorageWithBackup[IOpenInterestStorage],
                    StorageWithBackup(
                        PostgresFuturesOpenInterestStorage(main),
                        PostgresFuturesOpenInterestStorage(backup),
                    ),
                )
                for main, backup in self.args.postgres_out_futures
            ]
        if self.args.postgres_out_option:
            res[MarketType.OPTION] = [
                cast(
                    StorageWithBackup[IOpenInterestStorage],
                    StorageWithBackup(
                        PostgresOptionOpenInterestStorage(main),
                        PostgresOptionOpenInterestStorage(backup),
                    ),
                )
                for main, backup in self.args.postgres_out_option
            ]

        if not res:
            raise NoDatabaseError("No postgres database specified.")

        return res

    def db_save_operation(self, db: IOpenInterestStorage, batch: List[OpenInterest]) -> bool:
        try:
            db.save_open_interests(batch)
            return True
        except Exception as e:
            self.diagnostics.error(e, "Couldn't insert open interests batch")
            return False

    def extract_market(self, batch: List[Trade]) -> MarketType:
        return batch[0].market.instrument.market_type


class LiquidationDataLoader(DataLoader[Liquidation, LiquidationEntry, ILiquidationStorage]):
    def _transform_from_protobuf(self, content: bytes) -> List[Liquidation]:
        message = LiquidationEntry()
        message.ParseFromString(content)
        return [liquidation_from_message(message, self.exchange_id)]

    @property
    def kafka_group_id(self) -> str:
        return f"liquidations_data_loader_{self.exchange_id}"

    @property
    def kafka_topic_name(self) -> str:
        return f"liquidations_{self.exchange_id}.proto"

    def databases(self) -> Dict[MarketType, List[StorageWithBackup[ILiquidationStorage]]]:
        if self.args.postgres_out_futures:
            return {
                MarketType.FUTURES: [
                    StorageWithBackup(
                        PostgresLiquidationStorage(main),
                        PostgresLiquidationStorage(backup),
                    )
                    for main, backup in self.args.postgres_out_futures
                ]
            }
        else:
            raise NoDatabaseError("No postgres database specified.")

    def db_save_operation(self, db: ILiquidationStorage, batch: List[Liquidation]) -> bool:
        try:
            db.save_liquidations(batch)
            return True
        except Exception as e:
            self.diagnostics.error(e, "Couldn't insert liquidations batch")
            return False

    def extract_market(self, batch: List[Trade]) -> MarketType:
        return MarketType.FUTURES


def run_loader_application(data_loader_type: Type[DataLoader[D_T, N_T, S_T]], params: DataLoaderParams) -> None:
    args = data_loader_type.parse_cli()

    with Application(
        prometheus=args.prometheus,
        log_level=args.log_level,
        log_debug_tags=args.log_debug_tags,
    ) as app:
        data_loader_type(params, app, args).run()
