#include <Python.h>


static PyObject* decimal_zero;
static PyObject* book_entry_constructor;


static PyObject* book_side_delta(PyObject* self, PyObject* args)
{
    PyObject* a;
    PyObject* b;
    int sign;

    if (!PyArg_ParseTuple(args, "OOi", &a, &b, &sign))
        return nullptr;

    if (!PyList_Check(a))
    {
        PyErr_SetString(PyExc_TypeError, "expected list");
        return nullptr;
    }

    if (!PyList_Check(b))
    {
        PyErr_SetString(PyExc_TypeError, "expected list");
        return nullptr;
    }

    PyObject* result = PyList_New(0);
    Py_ssize_t a_index = 0;
    Py_ssize_t b_index = 0;
    Py_ssize_t a_len = PyList_Size(a);
    Py_ssize_t b_len = PyList_Size(b);
    auto comparison_op = sign > 0 ? Py_LT : Py_GT;

    while ((a_index < a_len) && (b_index < b_len))
    {
        PyObject* a_entry = PyList_GetItem(a, a_index);
        PyObject* b_entry = PyList_GetItem(b, b_index);

        PyObject* a_price = PyObject_GetAttrString(a_entry, "price");
        PyObject* b_price = PyObject_GetAttrString(b_entry, "price");

        if (PyObject_RichCompareBool(a_price, b_price, Py_EQ))
        {
            ++a_index;
            ++b_index;

            PyObject* a_amount = PyObject_GetAttrString(a_entry, "amount");
            PyObject* b_amount = PyObject_GetAttrString(b_entry, "amount");

            if (!PyObject_RichCompareBool(a_amount, b_amount, Py_EQ))
                PyList_Append(result, b_entry);
        }
        else if (PyObject_RichCompareBool(a_price, b_price, comparison_op))
        {
            ++b_index;
            PyList_Append(result, b_entry);
        }
        else
        {
            ++a_index;

            PyObject* new_entry = PyObject_CallFunctionObjArgs(
                book_entry_constructor, decimal_zero, a_price, nullptr);

            PyList_Append(result, new_entry);
        }
    }

    if (a_index == a_len)
    {
        for (Py_ssize_t i = b_index; i < b_len; ++i)
        {
            PyList_Append(result, PyList_GetItem(b, i));
        }
    }

    if (b_index == b_len)
    {
        for (Py_ssize_t i = a_index; i < a_len; ++i)
        {
            PyObject* a_price = PyObject_GetAttrString(PyList_GetItem(a, i), "price");

            PyObject* new_entry = PyObject_CallFunctionObjArgs(
                book_entry_constructor, decimal_zero, a_price, nullptr);

            PyList_Append(result, new_entry);
        }
    }

    return result;
}


static PyMethodDef cpp_octopus_methods[] = {
    {"book_side_delta", book_side_delta, METH_VARARGS, ""},
    {nullptr, nullptr, 0, nullptr}
};

static struct PyModuleDef cpp_octopus_module = {
    PyModuleDef_HEAD_INIT,
    "cpp_octopus",
    "",
    -1,
    cpp_octopus_methods
};

PyMODINIT_FUNC
PyInit_cpp_octopus(void)
{
    PyObject* decimal_module = PyImport_ImportModule("decimal");
    if (decimal_module == nullptr)
        return nullptr;

    PyObject* decimal_constructor = PyObject_GetAttrString(decimal_module, "Decimal");
    Py_DECREF(decimal_module);
    if (decimal_constructor == nullptr)
        return nullptr;

    decimal_zero = PyObject_CallFunction(decimal_constructor, "i", 0);
    Py_DECREF(decimal_constructor);
    if (decimal_zero == nullptr)
        return nullptr;

    PyObject* data_module = PyImport_ImportModule("src.octopus.data");
    if (data_module == nullptr)
        return nullptr;

    book_entry_constructor = PyObject_GetAttrString(data_module, "PriceLevel");
    Py_DECREF(data_module);
    if (book_entry_constructor == nullptr)
        return nullptr;

    PyObject* module = PyModule_Create(&cpp_octopus_module);
    return module != nullptr ? module : nullptr;
}
