import resource
import sys
import time
from argparse import ArgumentParser, Namespace
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from threading import Lock
from typing import Any, BinaryIO, Callable, DefaultDict, Dict, Generic, List, Optional, Tuple, Type, TypeVar, cast

import pytz
from sortedcontainers import SortedDict  # type: ignore

from src.kafka.api import KafkaMessage
from src.octopus.arguments import (
    Disable,
    book_scraper_arguments_parser,
    get_instance_number,
    http_scraper_arguments_parser,
    proxies_from_args,
    scraper_arguments_parser,
    streaming_scraper_arguments_parser,
)
from src.octopus.consumer import (
    BookKafkaChannelConsumer,
    BookKafkaChannelJSONConsumer,
    DatabaseConsumer,
    KafkaChannelConsumer,
    WebSocketConsumer,
)
from src.octopus.data import (
    Book,
    BookData,
    BookType,
    FundingRate,
    FuturesTicker,
    FuturesTickerData,
    Instrument,
    KafkaMessageWithHeaders,
    Liquidation,
    Market,
    MarketType,
    OpenInterest,
    OptionTicker,
    OutChannelName,
    PriceLevel,
    Trade,
)
from src.octopus.exchange.api import (
    BookCallback,
    BookStreamParams,
    ExchangeMarkets,
    IClientFactory,
    IExchangeHttpApi,
    LiquidationCallback,
    LiquidationStreamParams,
    TickerCallback,
    TickerStreamParams,
    TradeCallback,
    TradeStreamParams,
)
from src.octopus.exchange.api_factory import exchange_http_api, exchange_streaming_api
from src.octopus.feed_protocol_pb2 import (
    BookMessage,
    BookMessagesBatch,
    BookMessageV2,
    FundingRateEntry,
    LiquidationEntry,
    OpenInterestEntry,
    TradeEntry,
    TradeMessage,
)
from src.octopus.http_producer import HttpPollProducer, HttpProducerParams
from src.octopus.item_handlers import QuoteFromBookItemHandler, QuoteItemHandler
from src.octopus.market_control import MarketSource, ProducerData, market_controller
from src.octopus.market_filter import IncludeExcludeMarketFilter
from src.octopus.storage.api import (
    IBookStorage,
    IDumbStorage,
    IFundingRateStorage,
    IFuturesTickerStorage,
    ILiquidationStorage,
    IOpenInterestStorage,
    IOptionTickerStorage,
    ITradeStorage,
)
from src.octopus.storage.postgres.book import PostgresFuturesBookStorage, PostgresSpotBookStorage
from src.octopus.storage.postgres.book_old import (
    PostgresOldFuturesBookStorage,
    PostgresOldOptionBookStorage,
    PostgresOldSpotBookStorage,
)
from src.octopus.storage.postgres.funding_rate import PostgresFundingRateStorage
from src.octopus.storage.postgres.futures_ticker import PostgresFuturesTickerStorage
from src.octopus.storage.postgres.liquidations import PostgresLiquidationStorage
from src.octopus.storage.postgres.open_interest import PostgresFuturesOpenInterestStorage, PostgresOptionOpenInterestStorage
from src.octopus.storage.postgres.option_ticker import PostgresOptionTickerStorage
from src.octopus.storage.postgres.trade import (
    PostgresOptionTradeStorage,
    PostgresSpotTradeStorage,
    postgres_trade_futures_storage_factory,
)
from src.octopus.streaming_protocols.kafka_message import json_message_from_book, json_message_from_trade
from src.octopus.streaming_protocols.protobuf import (
    message_from_book,
    message_from_funding_rate,
    message_from_liquidation,
    message_from_open_interest,
    message_from_trade,
    message_v2_from_book,
)
from src.octopus.utils import (
    ClientFactory,
    ClientRecorderFactory,
    DataCache,
    SortDirection,
    compress_price_levels,
    count_and_log_transform,
    get_books_separated_by,
    http_book_transform,
)
from src.resources.exchange import glib_exchange
from src.utils.application import Application
from src.utils.atomic import AtomicInt
from src.utils.diagnostics import Diagnostics, ICounterMetric, IGaugeMetric
from src.utils.execution import IRunnable, NaiveBackgroundTaskExecutor
from src.utils.http import IHttpClient, ProxyParams, RateLimitedProxyHttpClient
from src.utils.kafka import set_up_kafka_topic
from src.utils.proxies import report_desired_proxies_func
from src.utils.pyroutine import (
    IBatchProducer,
    IChannel,
    IChannelIn,
    IChannelOut,
    IPyRoutineEnv,
    IPyRoutineSystem,
    ProcessBatch,
    PyRoutine,
    PyRoutineSystem,
    TransformBatch,
    chain_transform_batch,
    continuous_batch_producer,
    periodic_batch_consumer,
    send_batch_to_channels,
    sequence_batch,
    transform_batch,
)
from src.utils.rate_limit import RateLimitedResourcePool
from src.utils.timeutil import dt_to_us, truncate_to_minute, truncate_to_n_seconds
from src.utils.types import BatchCallback

D_T = TypeVar("D_T")  # scraped data type: Trade, Book, OpenInterest etc.
N_T = TypeVar("N_T")  # representation of D_T suitable to be sent over network.
S_T = TypeVar("S_T")  # type of storage.

DbChannel = IChannel[List[D_T]]
DbIn = IChannelIn[List[D_T]]
DbOut = IChannelOut[List[D_T]]

NetChannel = IChannel[List[N_T]]
NetOut = IChannelOut[List[N_T]]

Transform = TransformBatch[D_T, D_T]
STREAMING_FH_RESTART_INTERVAL = timedelta(hours=1)
RESTART_REQUEST_EXIT_CODE = 42


@dataclass(frozen=True)
class ScraperParams:
    db_channel_capacity: int
    ws_channel_capacity: int
    kafka_channel_capacity: int

    db_consumer_interval: float
    db_consumer_max_items_per_tick: int

    ws_consumer_interval: float
    ws_consumer_max_items_per_tick: int

    kafka_consumer_interval: float
    kafka_consumer_max_items_per_tick: int


@dataclass(frozen=True)
class StorageWithBackup(Generic[S_T]):
    main: S_T
    backup: S_T


class Scraper(Generic[D_T, N_T, S_T]):
    @staticmethod
    def parse_cli() -> Namespace:
        raise NotImplementedError

    def __init__(
        self,
        params: ScraperParams,
        app: Application,
        args: Namespace,
        exchange_id: int,
        http_api: IExchangeHttpApi,
        client: IHttpClient,
        ws_proxy_pool: RateLimitedResourcePool[Optional[ProxyParams]],
        diagnostics: Diagnostics,
    ) -> None:
        self.params = params
        self.app = app
        self.args = args
        self.exchange_id = exchange_id
        self.http_api = http_api
        self.http_client = client
        self.ws_proxy_pool = ws_proxy_pool
        self.diagnostics = diagnostics

        self.db_channels: List[DbOut[D_T]] = []
        self.backup_db_channels: List[DbOut[D_T]] = []
        self.net_channels: List[NetOut[N_T]] = []
        self.kafka_channels: List[IChannelOut[List[KafkaMessage]]] = []
        self.kafka_proto_channels: List[IChannelOut[List[KafkaMessage]]] = []
        self.kafka_channels_quotes: List[IChannelOut[List[KafkaMessage]]] = []

        self.market_type: MarketType
        if bool(args.spot):
            self.market_type = MarketType.SPOT
        elif bool(args.futures):
            self.market_type = MarketType.FUTURES
        elif bool(args.option):
            self.market_type = MarketType.OPTION
        else:
            raise Exception("Unspecified MarketType")

        if hasattr(self.args, "machine"):
            self.instance_number: Optional[int] = get_instance_number(self.args.machine)
            self.next_restart_at: Optional[datetime] = self._calculate_next_restart_time()
        else:
            self.instance_number = None
            self.next_restart_at = None

        if "record_file" in self.args and self.args.record_file is not None:
            self._record_file_handle: Optional[BinaryIO] = open(self.args.record_file, "wb")
            self._record_file_lock = Lock()
        else:
            self._record_file_handle = None

        # ensure max open descriptors limit is high enough
        self._set_max_open_descriptors()

    def _set_max_open_descriptors(self):
        _, hard = resource.getrlimit(resource.RLIMIT_NOFILE)
        new_soft = hard
        try:
            resource.setrlimit(resource.RLIMIT_NOFILE, (new_soft, hard))
            self.diagnostics.info(f"Max open descriptors set: soft={new_soft}, hard={hard}")
        except ValueError as e:
            self.diagnostics.error(e, "Cannot set max open descriptors. ValueError")
        except PermissionError as e:
            self.diagnostics.error(e, "Cannot set max open descriptors. Permission denied")

    def __del__(self) -> None:
        if hasattr(self, "_record_file_handle") and self._record_file_handle is not None:
            self._record_file_handle.close()

    def run(self, system: IPyRoutineSystem) -> None:
        """Launch database consumers and create corresponding channels."""

        for storage in self.databases():
            db_channel: DbChannel[D_T] = system.channel(self.params.db_channel_capacity, f"DB {storage.main}")
            backup_db_channel: DbChannel[D_T] = system.channel(self.params.db_channel_capacity, f"DB {storage.backup}")

            system.launch(
                self.db_consumer(storage.main, db_channel.inbound(), backup_db_channel.outbound()), name=f"db-out-{storage.main}"
            )
            system.launch(
                self.db_consumer(storage.backup, backup_db_channel.inbound(), None), name=f"db-backup-out-{storage.backup}"
            )

            self.db_channels.append(db_channel.outbound())
            self.backup_db_channels.append(backup_db_channel.outbound())

        """ Launch WebSocket consumer and create corresponding channel. """

        ws_urls = [str(url) for url in self.args.websocket_out] if "websocket_out" in self.args else []

        if len(ws_urls) > 0:
            ws_channel: NetChannel[N_T] = system.channel(self.params.ws_channel_capacity, "ws-out")

            system.launch(
                periodic_batch_consumer(
                    WebSocketConsumer[N_T](ws_urls, self.glue_network_messages),
                    ws_channel.inbound(),
                    self.params.ws_consumer_max_items_per_tick,
                    self.params.ws_consumer_interval,
                ),
                name="ws-out",
            )

            self.net_channels.append(ws_channel.outbound())

        self._run_kafka_consumers_and_channels(system)

        """ Launch market controller. """
        reqs_per_second_limit = glib_exchange().http_api_rate_limit(self.exchange_id) * self.args.rate_limit_multiplier
        reqs_per_instrument = self.http_api.get_requests_per_instruments(self.scraper_type_tag)
        report_desired_proxies = report_desired_proxies_func(
            desired_proxies_gauge=self.diagnostics.gauge("desired_proxies_count"),
            requests_per_instrument=reqs_per_instrument,
            requests_per_second_limit=reqs_per_second_limit,
        )

        market_source = MarketSource(
            self.exchange_id,
            self.market_type,
            self.markets_getter(),
            IncludeExcludeMarketFilter(self.market_type, self.args.instruments, self.args.exclude_instruments),
            self.diagnostics,
            report_desired_proxies,
            self.args.market_range,
            cache_lifetime=self.args.market_source_cache_lifetime or 90,
        )

        system.launch(
            market_controller(
                market_source,
                lambda markets: self.producer(markets, self.batch_processor(), self.item_received_counter()),
                self.markets_per_producer(self.args.markets_per_producer),
                refresh_interval=5.0,
            ),
            name="market_controller",
        )

    def _run_kafka_consumers_and_channels(
        self,
        system: IPyRoutineSystem,
        kafka_consumer_class: type = KafkaChannelConsumer,
        kafka_consumer_json_class: type = KafkaChannelConsumer,
    ) -> None:
        """Run Kafka consumers and create corresponding channels."""

        if "kafka_out" in self.args and self.args.kafka_out:
            self._run_kafka_channel(
                kafka_servers=self.args.kafka_out,
                kafka_channel_name="kafka-out",
                kafka_topic_name=self.kafka_topic_name,
                channels=self.kafka_channels,
                system=system,
                kafka_consumer_class=kafka_consumer_json_class,
            )

        if "kafka_out_proto" in self.args and self.args.kafka_out_proto:
            self._run_kafka_channel(
                kafka_servers=self.args.kafka_out_proto,
                kafka_channel_name="kafka-out-proto",
                kafka_topic_name=self.kafka_topic_name_proto,
                channels=self.kafka_proto_channels,
                system=system,
                kafka_consumer_class=kafka_consumer_class,
            )

    def _run_kafka_channel(
        self,
        kafka_servers: List[str],
        kafka_channel_name: str,
        kafka_topic_name: str,
        channels: List[IChannelOut[List[KafkaMessage]]],
        system: IPyRoutineSystem,
        kafka_consumer_class: type,
    ) -> None:
        new_channel: NetChannel[KafkaMessage] = system.channel(self.params.kafka_channel_capacity, kafka_channel_name)
        system.launch(
            periodic_batch_consumer(
                kafka_consumer_class(kafka_servers, kafka_topic_name, self.args, self.diagnostics, self.next_restart_at),
                new_channel.inbound(),
                self.params.kafka_consumer_max_items_per_tick,
                self.params.kafka_consumer_interval,
            ),
            name=kafka_channel_name,
        )

        channels.append(new_channel.outbound())
        if self.args.kafka_topic_retention_bytes:
            set_up_kafka_topic(self.args.kafka_topic_retention_bytes, kafka_servers, self.diagnostics)

    def batch_processor(self) -> ProcessBatch[D_T]:
        processors: List[ProcessBatch[D_T]] = []
        initial_batch_transform = self.initial_batch_transform()
        db_batch_transform = self.db_batch_transform()

        if len(self.db_channels) > 0:
            processors.append(
                chain_transform_batch(
                    transforms=[db_batch_transform],
                    process=send_batch_to_channels(self.db_channels, self.backup_db_channels, self.diagnostics),
                )
            )

        if len(self.net_channels) > 0:
            processors.append(
                chain_transform_batch(
                    transforms=[self.ws_batch_transform(OutChannelName.WS_OUT), self.transform_to_network],
                    process=send_batch_to_channels(self.net_channels, None, self.diagnostics),
                )
            )

        if len(self.kafka_channels) > 0:
            processors.append(
                chain_transform_batch(
                    transforms=[self.ws_batch_transform(OutChannelName.KAFKA_OUT), self.transform_to_json],
                    process=send_batch_to_channels(self.kafka_channels, None, self.diagnostics),
                )
            )

        if len(self.kafka_proto_channels) > 0:
            processors.append(
                chain_transform_batch(
                    transforms=[self.ws_batch_transform(OutChannelName.KAFKA_OUT_PROTO), self.transform_to_protobuf],
                    process=send_batch_to_channels(self.kafka_proto_channels, None, self.diagnostics),
                )
            )

        # http books fhs don't send BookType.DELTA messages, they send BookType.FULL messages only. That is why we can
        # leverage book stream for quotes cutting top 1 of bids and asks right before sending them to downstream
        # channels
        if self.__class__.__name__ == "HttpBookScraper" and len(self.kafka_channels_quotes) > 0:
            processors.append(
                transform_batch(
                    send_batch_to_channels(self.kafka_channels_quotes, None, self.diagnostics),
                    QuoteItemHandler().transform_to_json,  # type: ignore
                )
            )
        send_to_db_and_net = sequence_batch(processors)

        if initial_batch_transform is not None:
            return transform_batch(send_to_db_and_net, initial_batch_transform)
        else:
            return send_to_db_and_net

    def db_consumer(self, db: S_T, channel: DbIn[D_T], backup_channel: Optional[DbOut[D_T]]) -> PyRoutine:
        return periodic_batch_consumer(
            DatabaseConsumer(self.db_save_operation(db), backup_channel, str(db)),
            channel,
            self.params.db_consumer_max_items_per_tick,
            self.params.db_consumer_interval,
        )

    def item_received_counter(self) -> ICounterMetric:
        return self.diagnostics.counter("scraper_items_received")

    def item_received_adj_counter(self) -> ICounterMetric:
        return self.diagnostics.counter("scraper_items_received_adjusted")

    def scraped_market_gauge(self) -> IGaugeMetric:
        return self.diagnostics.gauge("scraped_market_count")

    def initial_batch_transform(self) -> Optional[Transform[D_T]]:
        raise NotImplementedError

    def transform_to_json(self, items: List[D_T], diagnostics: Diagnostics) -> List[Any]:
        raise NotImplementedError

    def transform_to_network(self, items: List[D_T], diagnostics: Diagnostics) -> List[N_T]:
        raise NotImplementedError

    def transform_to_protobuf(self, items: List[D_T], diagnostics: Diagnostics) -> List[N_T]:
        raise NotImplementedError

    def glue_network_messages(self, items: List[N_T]) -> bytes:
        raise NotImplementedError

    @property
    def kafka_topic_name(self) -> str:
        raise NotImplementedError

    @property
    def kafka_topic_name_proto(self) -> str:
        raise NotImplementedError

    @property
    def scraper_type_tag(self) -> str:
        raise NotImplementedError

    def databases(self) -> List[StorageWithBackup[S_T]]:
        raise NotImplementedError

    def db_batch_transform(self) -> Optional[Transform[D_T]]:
        raise NotImplementedError

    def db_save_operation(self, db: S_T) -> Callable[[List[D_T]], None]:
        raise NotImplementedError

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[D_T]]:
        raise NotImplementedError

    def producer(self, markets: List[Market], processor: ProcessBatch[D_T], counter: ICounterMetric) -> ProducerData:
        raise NotImplementedError

    def markets_per_producer(self, cli_value: int) -> int:
        raise NotImplementedError

    def markets_getter(self) -> Callable[[], ExchangeMarkets]:
        if self.market_type == MarketType.SPOT:
            return lambda: self.http_api.spot_markets(self.http_client)
        elif self.market_type == MarketType.FUTURES:
            return lambda: self.http_api.futures_markets(self.http_client)
        elif self.market_type == MarketType.OPTION:
            return lambda: self.http_api.option_markets(self.http_client)
        else:
            raise NotImplementedError("None of spot/futures/option markets specified.")

    def _calculate_next_restart_time(self) -> Optional[datetime]:
        return None

    def schedule_restart(self) -> None:
        if not self.next_restart_at:
            return

        self.diagnostics.info(f"[Restart] scheduler for {self.next_restart_at}")
        time.sleep((self.next_restart_at - datetime.now()).total_seconds())
        self.diagnostics.info("[Restart] gracefully terminating Python process...")
        sys.exit(RESTART_REQUEST_EXIT_CODE)


def run_scraper_application(scraper_type: Type[Scraper[D_T, N_T, S_T]], params: ScraperParams):
    args = scraper_type.parse_cli()

    with Application(
        prometheus=args.prometheus,
        log_level=args.log_level,
        log_debug_tags=args.log_debug_tags,
    ) as app:
        proxies = proxies_from_args(args)
        if hasattr(args, "machine") and get_instance_number(args.machine) == 2:
            proxies = proxies[::-1]

        exchange_id = glib_exchange().exchange_id_by_name(args.exchange)
        http_api = exchange_http_api(exchange_id, args.api_params, app.diagnostics)

        ws_proxy_pool = RateLimitedResourcePool[Optional[ProxyParams]](
            resources=[p for p in proxies] if len(proxies) > 0 else [None],
            rate_limit=glib_exchange().streaming_api_connection_rate_limit(exchange_id) * args.rate_limit_multiplier,
            ws_connections_limit=glib_exchange().streaming_connections_per_ip_limit(exchange_id),
            diagnostics=app.diagnostics,
            name="streaming_connection_proxy_pool",
        )

        http_client = RateLimitedProxyHttpClient(
            proxies=proxies,
            rate_limit=glib_exchange().http_api_rate_limit(exchange_id) * args.rate_limit_multiplier,
            diagnostics=app.diagnostics,
            name="http_client_proxy_pool",
            check_rate_limited=http_api.check_rate_limited,
            session_ttl=args.session_ttl,
        )

        # DON'T CHANGE the order of the objects in `with` statement. Their order determine the order of __exit__() invoked.
        # __exit__() method will be invoked in the reversed order of the objects.
        # We need to run __exit__ for proxy pools first, otherwise PyRoutines can get stuck in proxy_pool.acquire_resource()
        with PyRoutineSystem(app.diagnostics) as system, ws_proxy_pool, http_client:
            scraper = scraper_type(params, app, args, exchange_id, http_api, http_client, ws_proxy_pool, app.diagnostics)
            scraper.run(system)
            scraper.schedule_restart()
            system.wait_for_termination()


class HttpMixin(Generic[D_T]):
    def http_producer_params(self, scraper: Scraper[D_T, N_T, S_T]) -> HttpProducerParams:
        poll_interval = scraper.args.poll_interval
        scraper.diagnostics.gauge("http_producer_poll_stats_budget").set(poll_interval)
        poll_stats = scraper.diagnostics.sliding_window("http_producer_poll_stats", timedelta(seconds=10))
        trade_lag_seconds = scraper.diagnostics.gauge("http_producer_trade_lag_seconds")

        return HttpProducerParams(
            timedelta(seconds=scraper.args.poll_interval),
            item_counter=scraper.item_received_counter(),
            scraped_market_count=scraper.scraped_market_gauge(),
            poll_stats=poll_stats,
            trade_lag_seconds=trade_lag_seconds,
        )

    def producer(self, markets: List[Market], processor: ProcessBatch[D_T], counter: ICounterMetric) -> ProducerData:
        return ProducerData(continuous_batch_producer(self.batch_producer(markets[0]), processor), str(markets[0].instrument))

    def markets_per_producer(self, cli_value: int) -> int:
        return 1

    def batch_producer(self, market: Market) -> IBatchProducer[D_T]:
        raise NotImplementedError


class StreamingMixin(Generic[D_T]):
    _PRODUCER_REQUEST_COUNT = AtomicInt(0)

    @staticmethod
    def items_collection_lag_gauge(item: D_T, diagnostics: Diagnostics) -> Optional[IGaugeMetric]:
        if isinstance(item, Trade):
            return diagnostics.gauge("items_collection_lag", ("exchange", "market_type", "symbol")).tags({
                "exchange": glib_exchange().exchange_name_by_id(item.market.exchange_id),
                "market": item.market.instrument.market_type.name,
                "symbol": item.market.instrument.symbol,
            })
        return None

    def producer(
        self,
        markets: List[Market],
        processor: ProcessBatch[D_T],
        counter: ICounterMetric,
    ) -> ProducerData:
        pid = StreamingMixin._PRODUCER_REQUEST_COUNT.fetch_add(1)

        def routine(processor: ProcessBatch[D_T], counter: ICounterMetric) -> PyRoutine:
            def dtor(env: IPyRoutineEnv) -> None:
                stream.request_stop()
                stream.wait_for_termination()

            def update_items_collection_lag_gauge(batch: List[D_T]) -> None:
                if isinstance(batch[0], Trade):
                    for item in batch:
                        if gauge := self.items_collection_lag_gauge(item, env.diagnostics):
                            gauge.set(item.data.latency.total_seconds())

            def callback(batch: List[D_T]) -> None:
                counter.inc(len(batch))
                processor(batch, env)
                update_items_collection_lag_gauge(batch)

            env = yield dtor
            stream = self.stream([m.instrument for m in markets], callback, env.diagnostics)
            stream.start()
            env.wait_till_stop_requested()

        return ProducerData(routine(processor, counter), f"exchange-in-{pid}")

    def markets_per_producer(self, cli_value: int) -> int:
        return cli_value if cli_value > 0 else 2**64

    def stream(self, instruments: List[Instrument], cb: BatchCallback[D_T], diagnostics: Diagnostics) -> IRunnable:
        raise NotImplementedError


class TradeScraper(Scraper[Trade, TradeEntry, ITradeStorage]):
    def initial_batch_transform(self) -> Optional[Transform[Trade]]:
        cache = DataCache[Trade](self.args.cache_size, lambda t: t.data.trade_id, lambda t: t.market)
        count_and_log: Transform[Trade] = count_and_log_transform(self.item_received_adj_counter())
        return lambda batch, diagnostics: count_and_log(cache.filter(batch), diagnostics)

    def databases(self) -> List[StorageWithBackup[ITradeStorage]]:
        if self.market_type == MarketType.SPOT:
            return [
                cast(
                    StorageWithBackup[ITradeStorage],
                    StorageWithBackup(
                        PostgresSpotTradeStorage(self.exchange_id, main, diagnostics=self.diagnostics),
                        PostgresSpotTradeStorage(self.exchange_id, backup, diagnostics=self.diagnostics),
                    ),
                )
                for main, backup in self.args.postgres_exch_out
            ]
        elif self.market_type == MarketType.FUTURES:
            return [
                cast(
                    StorageWithBackup[ITradeStorage],
                    StorageWithBackup(
                        postgres_trade_futures_storage_factory(
                            exchange_id=self.exchange_id, connection_params=main, diagnostics=self.diagnostics
                        ),
                        postgres_trade_futures_storage_factory(
                            exchange_id=self.exchange_id, connection_params=backup, diagnostics=self.diagnostics
                        ),
                    ),
                )
                for main, backup in self.args.postgres_exch_out
            ]
        elif self.market_type == MarketType.OPTION:
            return [
                cast(
                    StorageWithBackup[ITradeStorage],
                    StorageWithBackup(
                        PostgresOptionTradeStorage(self.exchange_id, main, diagnostics=self.diagnostics),
                        PostgresOptionTradeStorage(self.exchange_id, backup, diagnostics=self.diagnostics),
                    ),
                )
                for main, backup in self.args.postgres_exch_out
            ]
        else:
            raise NotImplementedError("None of spot/futures/option markets specified.")

    def db_batch_transform(self) -> Optional[Transform[Trade]]:
        return None

    def db_save_operation(self, db: ITradeStorage) -> Callable[[List[Trade]], None]:
        return lambda batch: db.save_trades(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[Trade]]:
        return None

    def transform_to_json(self, items: List[Trade], diagnostics: Diagnostics) -> List[KafkaMessage]:
        return [json_message_from_trade(trade) for trade in items]

    def transform_to_network(self, items: List[Trade], diagnostics: Diagnostics) -> List[TradeEntry]:
        return [message_from_trade(trade) for trade in items]

    def transform_to_protobuf(self, items: List[Trade], diagnostics: Diagnostics) -> List[TradeEntry]:
        return [message_from_trade(trade) for trade in items]

    def glue_network_messages(self, items: List[TradeEntry]) -> bytes:
        batch_message = TradeMessage()
        batch_message.exchange_id = self.exchange_id
        batch_message.entries.extend(items)
        return batch_message.SerializeToString()

    @property
    def kafka_topic_name(self) -> str:
        return self.get_kafka_topic_name_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_by_id(exchange_id: int) -> str:
        return "trades"

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"trades_{exchange_id}.proto"


class LiquidationScraper(Scraper[Liquidation, Liquidation, ILiquidationStorage]):
    def initial_batch_transform(self) -> Optional[Transform[Liquidation]]:
        cache = DataCache[Liquidation](self.args.cache_size, lambda liq: liq.data.liquidation_id, lambda liq: liq.market)
        count_and_log: Transform[Liquidation] = count_and_log_transform(self.item_received_adj_counter())
        return lambda batch, diagnostics: count_and_log(cache.filter(batch), diagnostics)

    def databases(self) -> List[StorageWithBackup[ILiquidationStorage]]:
        return [
            StorageWithBackup(
                PostgresLiquidationStorage(main, diagnostics=self.diagnostics),
                PostgresLiquidationStorage(backup, diagnostics=self.diagnostics),
            )
            for main, backup in self.args.postgres_out
        ]

    def db_batch_transform(self) -> Optional[Transform[Liquidation]]:
        return None

    def db_save_operation(self, db: ILiquidationStorage) -> Callable[[List[Liquidation]], None]:
        return lambda batch: db.save_liquidations(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[Liquidation]]:
        return None

    def transform_to_protobuf(self, items: List[Liquidation], diagnostics: Diagnostics) -> List[LiquidationEntry]:
        return [message_from_liquidation(liquidation) for liquidation in items]

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"liquidations_{exchange_id}.proto"


def _merge_sorted_prices(a: List[PriceLevel], b: List[PriceLevel], sign: int) -> List[PriceLevel]:
    """Merge sorted lists of PriceLevel into sorted list.
    @param: "sign" - `-1` for descending order, `1` for ascending.
    """
    result = []
    a_index = b_index = 0
    while a_index < len(a) and b_index < len(b):
        if sign * a[a_index].price < sign * b[b_index].price:
            result.append(a[a_index])
            a_index += 1
        else:
            result.append(b[b_index])
            b_index += 1

    if a_index == len(a):
        result.extend(b[b_index:])

    elif b_index == len(b):
        result.extend(a[a_index:])

    return result


class BookScraper(Scraper[Book, BookMessage, IBookStorage]):
    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

        self.prev_bids_per_market: DefaultDict[str, Dict[Instrument, SortedDict[Decimal, PriceLevel]]] = defaultdict(dict)
        self.prev_asks_per_market: DefaultDict[str, Dict[Instrument, SortedDict[Decimal, PriceLevel]]] = defaultdict(dict)

    def _run_kafka_consumers_and_channels(
        self,
        system: IPyRoutineSystem,
        kafka_consumer_class: type = BookKafkaChannelConsumer,
        kafka_consumer_json_class: type = BookKafkaChannelJSONConsumer,
    ) -> None:
        """Run Kafka consumers and create corresponding channels."""
        super()._run_kafka_consumers_and_channels(system, kafka_consumer_class, kafka_consumer_json_class)
        if "kafka_out_quotes" in self.args and self.args.kafka_out_quotes:
            self._run_kafka_channel(
                kafka_servers=self.args.kafka_out_quotes,
                kafka_channel_name="kafka-out-quotes",
                kafka_topic_name=self.kafka_topic_name_quotes,
                channels=self.kafka_channels_quotes,
                system=system,
                kafka_consumer_class=KafkaChannelConsumer,
            )

    def initial_batch_transform(self) -> Optional[Transform[Book]]:
        return None

    def databases(self) -> List[StorageWithBackup[IBookStorage]]:
        if self.market_type == MarketType.SPOT:
            return [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(main=PostgresOldSpotBookStorage(main), backup=PostgresOldSpotBookStorage(backup)),
                )
                for main, backup in self.args.postgres_out
            ] + [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        main=PostgresSpotBookStorage(self.exchange_id, main),
                        backup=PostgresSpotBookStorage(self.exchange_id, backup),
                    ),
                )
                for main, backup in self.args.postgres_exch_out
            ]
        if self.market_type == MarketType.FUTURES:
            return [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(main=PostgresOldFuturesBookStorage(main), backup=PostgresOldFuturesBookStorage(backup)),
                )
                for main, backup in self.args.postgres_out
            ] + [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(
                        main=PostgresFuturesBookStorage(self.exchange_id, main),
                        backup=PostgresFuturesBookStorage(self.exchange_id, backup),
                    ),
                )
                for main, backup in self.args.postgres_exch_out
            ]
        if self.market_type == MarketType.OPTION:
            return [
                cast(
                    StorageWithBackup[IBookStorage],
                    StorageWithBackup(main=PostgresOldOptionBookStorage(main), backup=PostgresOldOptionBookStorage(backup)),
                )
                for main, backup in self.args.postgres_out
            ]
        raise NotImplementedError("None of spot/futures/option markets specified.")

    def db_batch_transform(self) -> Optional[Transform[Book]]:
        only_full_books = "save_deltas" not in self.args or not self.args.save_deltas
        interval = timedelta(seconds=self.args.storage_interval)
        market_last_book_time: Dict[Market, datetime] = {}

        def book_filter(batch: List[Book], diagnostics: Diagnostics) -> List[Book]:
            sliced_batch = [self.apply_desired_depth(book, self.args.book_depth) for book in batch]

            skipped_books_counter = diagnostics.counter("skipped_books_count")
            filtered = get_books_separated_by(
                sliced_batch,
                interval,
                market_last_book_time,
                lambda book: (not only_full_books) or (book.data.book_type == BookType.FULL),
                skipped_books_counter,
            )

            for book in filtered:
                last_time = market_last_book_time.get(book.market, datetime(1970, 1, 1, tzinfo=timezone.utc))
                market_last_book_time[book.market] = max(book.collect_time, last_time)

            return filtered

        return book_filter

    def db_save_operation(self, db: IBookStorage) -> Callable[[List[Book]], None]:
        return lambda batch: db.save_books(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[Book]]:
        def book_filter(batch: List[Book], diagnostics: Diagnostics) -> List[Book]:
            results = []
            for book in batch:
                if book.data.book_type == BookType.FULL:
                    self._calculate_prev_top_per_market(book, channel_name)
                    new_book = self.apply_desired_depth(book, self.args.book_depth, is_websocket=True)
                else:
                    new_book = self._compute_limited_delta(
                        full_delta=book, depth=self.args.book_depth, channel_name=channel_name
                    )
                results.append(new_book)

            return results

        return book_filter

    def transform_to_network(self, items: List[Book], diagnostics: Diagnostics) -> List[BookMessage]:
        if self.args.remove_empty_book_updates:
            items = self._remove_empty_book_updates(items)
        if self.args.compress_books_before_send:
            items = self._compress_books_before_send(items)
        if self.args.remove_network_exchange_sequence_id:
            return [
                message_from_book(
                    Book(
                        market=book.market,
                        depth_limit=book.depth_limit,
                        collect_time=book.collect_time,
                        deduplication_time=book.deduplication_time,
                        scraper_session_id=book.scraper_session_id,
                        scraper_sequence_id=book.scraper_sequence_id,
                        data=BookData(
                            book_type=book.data.book_type,
                            exchange_sequence_id=None,
                            exchange_time=book.data.exchange_time,
                            bids=book.data.bids,
                            asks=book.data.asks,
                        ),
                    ),
                    book.data.book_type == BookType.FULL,
                )
                for book in items
                if (
                    book.market.instrument.market_type != MarketType.SPOT
                    or (book.market.instrument.base != -1 and book.market.instrument.quote != -1)
                )
            ]
        return [
            message_from_book(book, book.data.book_type == BookType.FULL)
            for book in items
            if (
                book.market.instrument.market_type != MarketType.SPOT
                or (book.market.instrument.base != -1 and book.market.instrument.quote != -1)
            )
        ]

    def transform_to_protobuf(self, items: List[Book], diagnostics: Diagnostics) -> List[BookMessageV2]:  # type: ignore
        if self.args.remove_empty_book_updates:
            items = self._remove_empty_book_updates(items)
        if self.args.compress_books_before_send:
            items = self._compress_books_before_send(items)
        if self.args.remove_network_exchange_sequence_id:
            return [
                message_v2_from_book(
                    Book(
                        market=book.market,
                        depth_limit=book.depth_limit,
                        collect_time=book.collect_time,
                        deduplication_time=book.deduplication_time,
                        scraper_session_id=book.scraper_session_id,
                        scraper_sequence_id=book.scraper_sequence_id,
                        data=BookData(
                            book_type=book.data.book_type,
                            exchange_sequence_id=None,
                            exchange_time=book.data.exchange_time,
                            bids=book.data.bids,
                            asks=book.data.asks,
                        ),
                    ),
                    book.data.book_type == BookType.FULL,
                )
                for book in items
            ]
        return [message_v2_from_book(book, book.data.book_type == BookType.FULL) for book in items]

    def glue_network_messages(self, items: List[BookMessage]) -> bytes:
        batch_message = BookMessagesBatch()
        batch_message.book_messages.extend(items)
        return batch_message.SerializeToString()

    def transform_to_json(self, items: List[Book], diagnostics: Diagnostics) -> List[KafkaMessageWithHeaders]:
        if self.args.remove_empty_book_updates:
            items = self._remove_empty_book_updates(items)
        if self.args.compress_books_before_send:
            items = self._compress_books_before_send(items)
        if self.args.remove_network_exchange_sequence_id:
            return [
                json_message_from_book(
                    Book(
                        market=book.market,
                        depth_limit=book.depth_limit,
                        collect_time=book.collect_time,
                        deduplication_time=book.deduplication_time,
                        scraper_session_id=book.scraper_session_id,
                        scraper_sequence_id=book.scraper_sequence_id,
                        data=BookData(
                            book_type=book.data.book_type,
                            exchange_sequence_id=None,
                            exchange_time=book.data.exchange_time,
                            bids=book.data.bids,
                            asks=book.data.asks,
                        ),
                    ),
                    book.data.book_type == BookType.FULL,
                )
                for book in items
            ]
        return [json_message_from_book(book, book.data.book_type == BookType.FULL) for book in items]

    @staticmethod
    def apply_desired_depth(
        book: Book, depth: Optional[int] = None, is_websocket: bool = False, deduplication_time: Optional[datetime] = None
    ) -> Book:
        data = book.data
        bids_depth = asks_depth = depth

        if depth == 100 and not is_websocket and (data.bids or data.asks):
            """
            Special case for our 10s order book snapshots:
            we want to save at least depth=100 and at least all items that are priced less than 10% away
            from order book 'midprice'. 'midprice' here is an average number between top bid and bottom ask.
            """
            if data.bids and data.asks:
                mid_price = (data.bids[0].price + data.asks[0].price) / 2
            elif data.bids:
                mid_price = data.bids[0].price
            else:
                mid_price = data.asks[0].price

            min_price = Decimal("0.9") * mid_price
            max_price = Decimal("1.1") * mid_price

            bids_depth = max(BookScraper.next_index(data.bids, min_price, SortDirection.DESCENDING), 100)
            asks_depth = max(BookScraper.next_index(data.asks, max_price, SortDirection.ASCENDING), 100)

        return BookScraper.book_from_prototype(
            prototype=book,
            bids=data.bids[:bids_depth],
            asks=data.asks[:asks_depth],
            depth_limit=depth,
            deduplication_time=deduplication_time,
        )

    @staticmethod
    def next_index(array: List[PriceLevel], target: Decimal, sort_direction: SortDirection) -> int:
        """Finds an index of a 'target' after it would be added to a sorted 'array'.
        If there is the same element in 'array' already, next index will be returned.
        """
        start = 0
        end = len(array) - 1

        ans = len(array)
        while start <= end:
            mid = (start + end) // 2
            if sort_direction * array[mid].price <= sort_direction * target:
                start = mid + 1
            else:
                ans = mid
                end = mid - 1

        return ans

    @staticmethod
    def book_from_prototype(
        prototype: Book,
        bids: List[PriceLevel],
        asks: List[PriceLevel],
        depth_limit: Optional[int] = None,
        deduplication_time: Optional[datetime] = None,
    ) -> Book:
        return Book(
            market=prototype.market,
            depth_limit=depth_limit if depth_limit else prototype.depth_limit,
            collect_time=prototype.collect_time,
            deduplication_time=deduplication_time if deduplication_time else prototype.deduplication_time,
            scraper_session_id=prototype.scraper_session_id,
            scraper_sequence_id=prototype.scraper_sequence_id,
            data=BookData(
                book_type=prototype.data.book_type,
                exchange_sequence_id=prototype.data.exchange_sequence_id,
                exchange_time=prototype.data.exchange_time,
                bids=bids,
                asks=asks,
            ),
        )

    def _compute_limited_delta(self, full_delta: Book, depth: Optional[int], channel_name: str) -> Book:
        if depth is None:
            return full_delta

        prev_bids = self.prev_bids_per_market[channel_name][full_delta.market.instrument]
        delta_bids, prev_bids = self._compute_limited_delta_side(prev_bids, full_delta.data.bids, depth, sign=-1)
        self.prev_bids_per_market[channel_name][full_delta.market.instrument] = prev_bids

        prev_asks = self.prev_asks_per_market[channel_name][full_delta.market.instrument]
        delta_asks, prev_asks = self._compute_limited_delta_side(prev_asks, full_delta.data.asks, depth, sign=1)
        self.prev_asks_per_market[channel_name][full_delta.market.instrument] = prev_asks

        return self.book_from_prototype(prototype=full_delta, bids=delta_bids, asks=delta_asks)

    @staticmethod
    def _compute_limited_delta_side(
        prev_side: "SortedDict[Decimal, PriceLevel]", side: List[PriceLevel], depth: int, sign: int
    ) -> Tuple[List[PriceLevel], "SortedDict[Decimal, PriceLevel]"]:
        """
        Calculates a side of a DELTA book that needs to be applied to book of depth={depth} to maintain its state accurately.

        @param: "sign" - `-1` for descending order, `1` for ascending.

        Use case:
        API want to maintain a book of depth=100 to serve it to the clients. With this function FHs can transform
        updates for the full book (depth=30000) to the depth=100 book updates and thus minimize the calculations on API side.
        """
        if not side:
            return [], prev_side

        # """ FOR DEBUG uncomment: preserving the input book state for the test below. """
        # input_book_state = copy.deepcopy(prev_side)

        removed_prices = added_prices = []
        delta = []
        # iterate through updates while calculating new book state
        for side_item in side:
            exists = side_item.price in prev_side

            if side_item.amount == 0:
                # remove item from book
                if exists:
                    delta.append(side_item)
                    removed_prices.append(side_item.price)
                    del prev_side[side_item.price]
            else:
                # update item in the book
                if exists:
                    if side_item.amount != prev_side[side_item.price].amount:
                        delta.append(side_item)
                        prev_side[side_item.price] = side_item

                # add item to book
                else:
                    delta.append(side_item)
                    added_prices.append(side_item.price)
                    prev_side[side_item.price] = side_item

        # truncate delta items by the last price from the new book state
        # calculate removed and added items number
        last_price = list(prev_side.keys())[depth - 1] if len(prev_side) >= depth else None
        if last_price:
            delta = [price_level for price_level in delta if sign * price_level.price <= sign * last_price]
            removed = len([price for price in removed_prices if sign * price <= sign * last_price])
            added = len([price for price in added_prices if sign * price <= sign * last_price])
        else:
            removed = len(removed_prices)
            added = len(added_prices)

        if (tail_len := removed - added) > 0:
            # Since a downstream app does not know anything about depth+1, depth+2, depth+3... items'
            # we must add them to the delta in case we remove more items than add
            tail = [prev_side[key] for key in prev_side.keys()[depth - tail_len : depth]]
        else:
            # if we add more than remove, we must remove the depth+1, depth+2... items from the downstream snapshot
            tail = [PriceLevel(price=key, amount=Decimal(0)) for key in prev_side.keys()[depth : depth - tail_len]]
        delta = _merge_sorted_prices(delta, tail, sign)

        # """FOR DEBUG uncomment: run the test to ensure:
        # 1. delta is sorted properly
        # 2. the new state is accurate
        # 3. delta is accurate
        # """
        # standard_book_delta_transform_test(
        #     full_book_state=input_book_state,
        #     input_updates=side,
        #     depth=depth,
        #     sign=sign,
        #     new_updates=delta,
        #     new_book_state=prev_side
        # )

        return delta, prev_side

    def _calculate_prev_top_per_market(self, book: Book, channel_name: str) -> None:
        self.prev_bids_per_market[channel_name][book.market.instrument] = SortedDict(
            lambda price: -price, ((bid.price, bid) for bid in book.data.bids)
        )
        self.prev_asks_per_market[channel_name][book.market.instrument] = SortedDict((ask.price, ask) for ask in book.data.asks)

    @property
    def kafka_topic_name(self) -> str:
        return self.get_kafka_topic_name_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_by_id(exchange_id: int) -> str:
        return f"books_{exchange_id}"

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"books_{exchange_id}.proto"

    @property
    def kafka_topic_name_quotes(self) -> str:
        return self.get_kafka_topic_name_quotes_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_quotes_by_id(exchange_id: int) -> str:
        return f"quotes_{exchange_id}"

    def _compress_books_before_send(self, items: List[Book]) -> List[Book]:
        return [
            Book(
                market=book.market,
                depth_limit=book.depth_limit,
                collect_time=book.collect_time,
                deduplication_time=book.deduplication_time,
                scraper_session_id=book.scraper_session_id,
                scraper_sequence_id=book.scraper_sequence_id,
                data=BookData(
                    book_type=book.data.book_type,
                    exchange_sequence_id=None,
                    exchange_time=book.data.exchange_time,
                    bids=compress_price_levels(book.data.bids),
                    asks=compress_price_levels(book.data.asks),
                ),
            )
            for book in items
        ]

    @staticmethod
    def _remove_empty_book_updates(items: List[Book]) -> List[Book]:
        res = []
        for book in items:
            # sending FULL book even if it is empty
            if book.data.book_type == BookType.FULL:
                res.append(book)
            else:
                if book.data.bids or book.data.asks:
                    res.append(book)
        return [book for book in items if book.data.bids or book.data.asks]

    def _calculate_next_restart_time(self) -> Optional[datetime]:
        start_at_minute = 30 if self.instance_number == 1 else 0
        now = datetime.now()
        next_restart = now.replace(minute=start_at_minute, second=0, microsecond=0)
        if next_restart < now:
            next_restart += STREAMING_FH_RESTART_INTERVAL
        return next_restart


class QuoteScraper(Scraper[Book, BookMessage, IDumbStorage]):
    def __init__(self, *args_, **kwargs) -> None:  # type: ignore
        super().__init__(*args_, **kwargs)
        self.item_handler = QuoteItemHandler()

    @property
    def scraper_type_tag(self) -> str:
        return "quotes"

    def initial_batch_transform(self) -> Optional[Transform[Book]]:
        return None

    def databases(self) -> List[StorageWithBackup[IDumbStorage]]:
        return []

    def db_batch_transform(self) -> Optional[Transform[Book]]:
        def book_filter(batch: List[Book], diagnostics: Diagnostics) -> List[Book]:
            if len(batch) != 1:
                raise ValueError(f"Only one book is expected, got {len(batch)} instead")
            book = self.item_handler.handle_new_book(batch[0])
            if book:
                return [book]
            return []

        return book_filter

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[Book]]:
        return None

    def transform_to_json(self, items: List[Book], diagnostics: Diagnostics) -> List[str]:
        return self.item_handler.transform_to_json(items, diagnostics)

    @property
    def kafka_topic_name(self) -> str:
        return self.get_kafka_topic_name_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_by_id(exchange_id: int) -> str:
        return f"quotes_{exchange_id}"

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"quotes_{exchange_id}.proto"


class OpenInterestScraper(Scraper[OpenInterest, OpenInterest, IOpenInterestStorage]):
    def initial_batch_transform(self) -> Optional[Transform[OpenInterest]]:
        return count_and_log_transform(self.item_received_adj_counter())

    def databases(self) -> List[StorageWithBackup[IOpenInterestStorage]]:
        if self.market_type == MarketType.FUTURES:
            return [
                StorageWithBackup(PostgresFuturesOpenInterestStorage(main), PostgresFuturesOpenInterestStorage(backup))
                for main, backup in self.args.postgres_out
            ]
        elif self.market_type == MarketType.OPTION:
            return [
                StorageWithBackup(PostgresOptionOpenInterestStorage(main), PostgresOptionOpenInterestStorage(backup))
                for main, backup in self.args.postgres_out
            ]
        else:
            raise NotImplementedError("Open Interest DB support is only defined for futures and options")

    def db_batch_transform(self) -> Optional[Transform[OpenInterest]]:
        return None

    def db_save_operation(self, db: IOpenInterestStorage) -> Callable[[List[OpenInterest]], None]:
        return lambda batch: db.save_open_interests(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[OpenInterest]]:
        return None

    def transform_to_protobuf(self, items: List[OpenInterest], diagnostics: Diagnostics) -> List[OpenInterestEntry]:
        return [message_from_open_interest(open_interest) for open_interest in items]

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"open_interests_{exchange_id}.proto"


class FundingRateScraper(Scraper[FundingRate, FundingRate, IFundingRateStorage]):
    def initial_batch_transform(self) -> Optional[Transform[FundingRate]]:
        cache = DataCache[FundingRate](self.args.cache_size, lambda fr: dt_to_us(fr.data.time), lambda fr: fr.market)
        count_and_log: Transform[FundingRate] = count_and_log_transform(self.item_received_adj_counter())
        return lambda batch, diagnostics: count_and_log(cache.filter(batch), diagnostics)

    def databases(self) -> List[StorageWithBackup[IFundingRateStorage]]:
        return [
            StorageWithBackup(PostgresFundingRateStorage(main), PostgresFundingRateStorage(backup))
            for main, backup in self.args.postgres_out
        ]

    def db_batch_transform(self) -> Optional[Transform[FundingRate]]:
        return None

    def db_save_operation(self, db: IFundingRateStorage) -> Callable[[List[FundingRate]], None]:
        return lambda batch: db.save_funding_rates(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[FundingRate]]:
        return None

    def markets_getter(self) -> Callable[[], ExchangeMarkets]:
        return lambda: self.http_api.futures_markets_perpetual(self.http_client)

    def transform_to_protobuf(self, items: List[FundingRate], diagnostics: Diagnostics) -> List[FundingRateEntry]:
        return [message_from_funding_rate(funding_rate) for funding_rate in items]

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"funding_rates_{exchange_id}.proto"


class FuturesTickerScraper(Scraper[FuturesTicker, FuturesTicker, IFuturesTickerStorage]):
    def initial_batch_transform(self) -> Optional[Transform[FuturesTicker]]:
        return count_and_log_transform(self.item_received_adj_counter())

    def databases(self) -> List[StorageWithBackup[IFuturesTickerStorage]]:
        if self.market_type == MarketType.FUTURES:
            return [
                StorageWithBackup(
                    PostgresFuturesTickerStorage(self.exchange_id, main), PostgresFuturesTickerStorage(self.exchange_id, backup)
                )
                for main, backup in self.args.postgres_exch_out
            ]
        else:
            raise NotImplementedError("Futures Ticker DB support is only defined for futures")

    def db_batch_transform(self) -> Optional[Transform[FuturesTicker]]:
        return None

    def db_save_operation(self, db: IFuturesTickerStorage) -> Callable[[List[FuturesTicker]], None]:
        return lambda batch: db.save_futures_ticker(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[FuturesTicker]]:
        return None

    @property
    def scraper_type_tag(self) -> str:
        return "futures_ticker"


class OptionTickerScraper(Scraper[OptionTicker, OptionTicker, IOptionTickerStorage]):
    def initial_batch_transform(self) -> Optional[Transform[OptionTicker]]:
        return count_and_log_transform(self.item_received_adj_counter())

    def databases(self) -> List[StorageWithBackup[IOptionTickerStorage]]:
        if self.market_type == MarketType.OPTION:
            return [
                StorageWithBackup(PostgresOptionTickerStorage(main), PostgresOptionTickerStorage(backup))
                for main, backup in self.args.postgres_out
            ]
        else:
            raise NotImplementedError("Option Ticker DB support is only defined for options")

    def db_batch_transform(self) -> Optional[Transform[OptionTicker]]:
        return None

    def db_save_operation(self, db: IOptionTickerStorage) -> Callable[[List[OptionTicker]], None]:
        return lambda batch: db.save_option_ticker(batch)

    def ws_batch_transform(self, channel_name: OutChannelName) -> Optional[Transform[OptionTicker]]:
        return None

    @property
    def scraper_type_tag(self) -> str:
        return "option_ticker"

    @property
    def kafka_topic_name(self) -> str:
        return self.get_kafka_topic_name_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_by_id(exchange_id: int) -> str:
        return f"ticker_{exchange_id}"

    @property
    def kafka_topic_name_proto(self) -> str:
        return self.get_kafka_topic_name_proto_by_id(self.exchange_id)

    @staticmethod
    def get_kafka_topic_name_proto_by_id(exchange_id: int) -> str:
        return f"ticker_{exchange_id}.proto"


class HttpTradeScraper(HttpMixin[Trade], TradeScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        http_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--cache-size", type=int, default=2048, help="Maximum size of cache for trades of individual market"
        )

        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[Trade]:
        return HttpPollProducer(
            market,
            lambda: self.http_api.last_trades(self.http_client, market.instrument),
            lambda market, current_time, data: Trade(market, data),
            self.http_producer_params(self),
        )

    @property
    def scraper_type_tag(self) -> str:
        return "trades_realtime"


class StreamingTradeScraper(StreamingMixin[Trade], TradeScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--cache-size", type=int, default=2048, help="Maximum size of cache for liquidations of individual market"
        )

        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], on_trades: TradeCallback, diagnostics: Diagnostics) -> IRunnable:
        factory: IClientFactory = ClientFactory(self.ws_proxy_pool)

        if self._record_file_handle is not None:
            factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, factory)

        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = TradeStreamParams(on_trades, diagnostics, self.scraped_market_gauge())

        if self.market_type == MarketType.SPOT:
            return streaming_api.trades(factory, instruments, stream_params)
        elif self.market_type == MarketType.FUTURES:
            return streaming_api.futures_trades(factory, instruments, stream_params)
        elif self.market_type == MarketType.OPTION:
            return streaming_api.option_trades(factory, instruments, stream_params)
        else:
            raise NotImplementedError("None of spot/futures/option arguments specified.")

    @property
    def scraper_type_tag(self) -> str:
        return "trades_realtime"

    def _calculate_next_restart_time(self) -> Optional[datetime]:
        start_at_minute = 30 if self.instance_number == 1 else 0
        now = datetime.now()
        next_restart = now.replace(minute=start_at_minute, second=0, microsecond=0)
        if next_restart < now:
            next_restart += STREAMING_FH_RESTART_INTERVAL
        return next_restart


class StreamingOptionTickerScraper(StreamingMixin[Trade], OptionTickerScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], on_ticker: TickerCallback, diagnostics: Diagnostics) -> IRunnable:
        factory: IClientFactory = ClientFactory(self.ws_proxy_pool)

        if self._record_file_handle is not None:
            factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, factory)

        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = TickerStreamParams(on_ticker, diagnostics, self.scraped_market_gauge())

        if self.market_type == MarketType.OPTION:
            return streaming_api.option_ticker(factory, instruments, stream_params)
        else:
            raise NotImplementedError("None of option arguments specified.")

    @property
    def scraper_type_tag(self) -> str:
        return "ticker_realtime"


class StreamingFutureTickerScraper(StreamingMixin[Trade], FuturesTickerScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], on_ticker: TickerCallback, diagnostics: Diagnostics) -> IRunnable:
        factory: IClientFactory = ClientFactory(self.ws_proxy_pool)

        if self._record_file_handle is not None:
            factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, factory)

        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = TickerStreamParams(on_ticker, diagnostics, self.scraped_market_gauge())

        if self.market_type == MarketType.FUTURES:
            return streaming_api.futures_ticker(factory, instruments, stream_params)
        else:
            raise NotImplementedError("None of option arguments specified.")

    @property
    def scraper_type_tag(self) -> str:
        return "ticker_realtime"


class HttpLiquidationScraper(HttpMixin[Liquidation], LiquidationScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        http_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--cache-size", type=int, default=2048, help="Maximum size of cache for liquidations of individual market"
        )

        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[Liquidation]:
        return HttpPollProducer(
            market,
            lambda: self.http_api.last_liquidations(self.http_client, market.instrument),
            lambda market, current_time, data: Liquidation(market, data),
            self.http_producer_params(self),
        )

    @property
    def scraper_type_tag(self) -> str:
        return "liquidations_realtime"


class StreamingLiquidationScraper(StreamingMixin[Liquidation], LiquidationScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--cache-size", type=int, default=2048, help="Maximum size of cache for liquidations of individual market"
        )

        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], cb: LiquidationCallback, diagnostics: Diagnostics) -> IRunnable:
        factory: IClientFactory = ClientFactory(self.ws_proxy_pool)

        if self._record_file_handle is not None:
            factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, factory)

        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = LiquidationStreamParams(cb, diagnostics, self.scraped_market_gauge())
        return streaming_api.liquidations(factory, instruments, stream_params)

    @property
    def scraper_type_tag(self) -> str:
        return "liquidations_realtime"


class HttpBookScraper(HttpMixin[Book], BookScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        http_scraper_arguments_parser(arg_parser)
        book_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def initial_batch_transform(self) -> Optional[Transform[Book]]:
        count_and_log: Transform[Book] = count_and_log_transform(self.item_received_adj_counter())
        filter_books = http_book_transform()
        return lambda batch, diagnostics: count_and_log(filter_books(batch, diagnostics), diagnostics)

    def batch_producer(self, market: Market) -> IBatchProducer[Book]:
        book_depth = self.args.book_depth
        storage_interval = int(self.args.storage_interval)
        scraper_session_id = dt_to_us(datetime.utcnow())
        scraper_sequence_id = 0

        def make_book(market: Market, current_time: datetime, data: BookData) -> Book:
            nonlocal scraper_sequence_id
            scraper_sequence_id += 1
            collect_time = pytz.utc.localize(current_time)
            deduplication_time = truncate_to_n_seconds(data.exchange_time or collect_time, storage_interval)
            return Book(
                market=market,
                depth_limit=book_depth,
                collect_time=collect_time,
                deduplication_time=deduplication_time,
                scraper_session_id=scraper_session_id,
                scraper_sequence_id=scraper_sequence_id,
                data=data,
            )

        return HttpPollProducer(
            market,
            lambda: self._get_book(market.instrument, book_depth),
            make_book,
            self.http_producer_params(self),
        )

    def _get_book(self, instrument: Instrument, book_depth: int) -> List[BookData]:
        book = self.http_api.book(self.http_client, instrument, book_depth, self.args.poll_max_book)
        return [book] if book else []

    @property
    def scraper_type_tag(self) -> str:
        return "books_realtime"


class StreamingBookScraper(StreamingMixin[Book], BookScraper):
    def __init__(self, *args, **kwargs) -> None:  # type: ignore
        super().__init__(*args, **kwargs)
        self.quote_from_book_item_handler: Optional[QuoteFromBookItemHandler] = None

    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)
        book_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--full-book-production-interval",
            type=float,
            default=5,
            help="Snapshot will be sent to consumers every FULL-BOOK-PRODUCTION-INTERVAL seconds",
        )

        arg_parser.add_argument(
            "--save-deltas", action="store_true", help="Experimental: delta books will be sent to the database"
        )
        arg_parser.add_argument(
            "--disable-compute-limited-delta", action="store_true", help="Turn off _compute_limited_delta calculation"
        )

        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], on_books: BookCallback, diagnostics: Diagnostics) -> IRunnable:
        factory: IClientFactory = ClientFactory(self.ws_proxy_pool)
        executor = NaiveBackgroundTaskExecutor()
        full_book_production_interval = timedelta(seconds=self.args.full_book_production_interval)

        if self.args.disable_compute_limited_delta:
            self._compute_limited_delta = lambda full_delta, depth, channel_name: full_delta

        if self._record_file_handle is not None:
            factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, factory)

        if self.kafka_channels_quotes:
            self.quote_from_book_item_handler = QuoteFromBookItemHandler(self.kafka_channels_quotes, diagnostics)
        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = BookStreamParams(
            on_books=on_books,
            depth=self.args.book_depth,
            storage_interval=self.args.storage_interval,
            full_book_interval=full_book_production_interval,
            executor=executor,
            diagnostics=diagnostics,
            scraped_market_count=self.scraped_market_gauge(),
            poll_max_book=self.args.poll_max_book,
            send_quote_to_channels=(
                self.quote_from_book_item_handler.send_quote_to_channels if self.quote_from_book_item_handler else None
            ),
        )

        if self.market_type == MarketType.SPOT:
            return streaming_api.books(factory, instruments, stream_params)
        elif self.market_type == MarketType.FUTURES:
            return streaming_api.futures_books(factory, instruments, stream_params)
        elif self.market_type == MarketType.OPTION:
            return streaming_api.option_books(factory, instruments, stream_params)
        else:
            raise NotImplementedError("None of spot/futures/option arguments specified.")

    @property
    def scraper_type_tag(self) -> str:
        return "books_realtime"


class StreamingQuoteFeedHandler(StreamingMixin[Book], QuoteScraper):
    def __init__(self, *args, **kwargs) -> None:  # type: ignore
        super().__init__(*args, **kwargs)
        self.factory: IClientFactory = ClientFactory(self.ws_proxy_pool)

    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser)
        streaming_scraper_arguments_parser(arg_parser)
        # it is needed to save as fast as possible, so --storage-interval must be 0 in case of QuoteFeedHandler
        arg_parser.add_argument(
            "--storage-interval",
            type=int,
            default=0,
            help="Minimal time interval in seconds that should separate two stored quotes",
        )

        return arg_parser.parse_args()

    def stream(self, instruments: List[Instrument], on_books: BookCallback, diagnostics: Diagnostics) -> IRunnable:
        executor = NaiveBackgroundTaskExecutor()
        full_book_production_interval = timedelta(seconds=0)  # 0 here because every tick should be saved

        if self._record_file_handle is not None:
            self.factory = ClientRecorderFactory(self._record_file_handle, self._record_file_lock, self.factory)

        streaming_api = exchange_streaming_api(self.exchange_id, self.args.streaming_api_params, self.http_client)
        stream_params = BookStreamParams(
            on_books,
            1,
            self.args.storage_interval,
            full_book_production_interval,
            executor,
            diagnostics,
            self.scraped_market_gauge(),
            False,
        )

        if self.market_type == MarketType.SPOT:
            return streaming_api.quotes(self.factory, instruments, stream_params)
        elif self.market_type == MarketType.FUTURES:
            return streaming_api.futures_quotes(self.factory, instruments, stream_params)
        else:
            raise NotImplementedError("None of spot or futures arguments specified.")

    @property
    def scraper_type_tag(self) -> str:
        return "quotes"

    def _calculate_next_restart_time(self) -> Optional[datetime]:
        start_at_minute = 30 if self.instance_number == 1 else 0
        now = datetime.now()
        next_restart = now.replace(minute=start_at_minute, second=0, microsecond=0)
        if next_restart < now:
            next_restart += STREAMING_FH_RESTART_INTERVAL
        return next_restart


class HttpOpenInterestScraper(HttpMixin[OpenInterest], OpenInterestScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser, Disable.WS_CONSUMER)
        http_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[OpenInterest]:
        if market.instrument.market_type == MarketType.FUTURES:
            return HttpPollProducer(
                market,
                lambda: [
                    oi for oi in [self.http_api.futures_open_interest(self.http_client, market.instrument)] if oi is not None
                ],
                lambda market, current_time, data: OpenInterest(market, data),
                self.http_producer_params(self),
            )
        elif market.instrument.market_type == MarketType.OPTION:
            return HttpPollProducer(
                market,
                lambda: [self.http_api.option_open_interest(self.http_client, market.instrument)],
                lambda market, current_time, data: OpenInterest(market, data),
                self.http_producer_params(self),
            )
        else:
            raise NotImplementedError(f"Unknown market type {market.instrument.market_type}")

    @property
    def scraper_type_tag(self) -> str:
        return "open_interests_realtime"


class HttpFundingRateScraper(HttpMixin[FundingRate], FundingRateScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser, Disable.WS_CONSUMER)
        http_scraper_arguments_parser(arg_parser)

        arg_parser.add_argument(
            "--cache-size", type=int, default=2048, help="Maximum size of cache for liquidations of individual market"
        )

        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[FundingRate]:
        return HttpPollProducer(
            market,
            lambda: self.http_api.last_funding_rates(self.http_client, market.instrument),
            lambda market, current_time, data: FundingRate(market, data),
            self.http_producer_params(self),
        )

    @property
    def scraper_type_tag(self) -> str:
        return "funding_rates_realtime"


class HttpFuturesTickerScraper(HttpMixin[FuturesTicker], FuturesTickerScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser, Disable.WS_CONSUMER)
        http_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[FuturesTicker]:
        def make_ticker(market: Market, current_time: datetime, data: FuturesTickerData) -> FuturesTicker:
            collect_time = data.exchange_time or pytz.utc.localize(current_time)
            deduplication_time = truncate_to_minute(collect_time)
            return FuturesTicker(market, deduplication_time, collect_time, data)

        if market.instrument.market_type == MarketType.FUTURES:
            return HttpPollProducer(
                market,
                lambda: [self.http_api.futures_ticker(self.http_client, market.instrument)],
                make_ticker,
                self.http_producer_params(self),
            )
        else:
            raise NotImplementedError(f"Unknown market type {market.instrument.market_type}")

    @property
    def scraper_type_tag(self) -> str:
        return "futures_ticker_realtime"


class HttpOptionTickerScraper(HttpMixin[OptionTicker], OptionTickerScraper):
    @staticmethod
    def parse_cli() -> Namespace:
        arg_parser = ArgumentParser()
        scraper_arguments_parser(arg_parser, Disable.WS_CONSUMER)
        http_scraper_arguments_parser(arg_parser)
        return arg_parser.parse_args()

    def batch_producer(self, market: Market) -> IBatchProducer[OptionTicker]:
        if market.instrument.market_type == MarketType.OPTION:
            return HttpPollProducer(
                market,
                lambda: [self.http_api.option_ticker(self.http_client, market.instrument)],
                lambda market, current_time, data: OptionTicker(market, data),
                self.http_producer_params(self),
            )
        else:
            raise NotImplementedError(f"Unknown market type {market.instrument.market_type}")

    @property
    def scraper_type_tag(self) -> str:
        return "option_ticker_realtime"
