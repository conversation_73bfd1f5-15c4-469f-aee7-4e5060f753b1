from typing import List

from src.octopus.applications.proxies.enums import Deribi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeribitCP2Smartproxy
from src.octopus.inventory.types import Scraper, futures_history_scraper

DERIBIT_PROXIES = [[DeribitCP2HetznerEu.LIQUIDATIONS_HISTORY, DeribitCP2Smartproxy.LIQUIDATIONS_HISTORY]]
HISTORICAL_LIQUIDATION: List[Scraper] = [
    futures_history_scraper(
        "Bitfinex",
        deployment_flags={"entrypoint_args": {"rate_limit_multiplier": 0.1}},
    ),
    futures_history_scraper(
        "Deribit",
        deployment_flags={"entrypoint_args": {"proxy_groups": DERIBIT_PROXIES}},
    ),
]
