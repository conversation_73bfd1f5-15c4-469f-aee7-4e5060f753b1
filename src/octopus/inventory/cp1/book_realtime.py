from functools import partial
from typing import List

from src.octopus.applications.proxies.enums import (
    BinanceCP1<PERSON><PERSON>zner<PERSON>u,
    CoinbaseIntNY5HetznerEu,
    Cp1HetznerEu,
    Cp1H<PERSON>znerUs,
    SmartproxyCP1,
    <PERSON>ishCp1WebShareEu,
    <PERSON>ishCp1Smartproxy,
    GeminiCP1HetznerUs,
    GeminiCP1WebShareEu,
    GeminiCP1HetznerEu,
    GfoxCP1HetznerUs,
    <PERSON>ishCp1H<PERSON>znerUs,
)
from src.octopus.inventory.api_keys import CME_API_KEY
from src.octopus.inventory.common import (
    BINANCE_BUSY_FUTURE_MARKETS,
    BINANCE_BUSY_SPOT_MARKETS,
    BITFINEX_BUSY_SPOT_MARKETS,
    COINBASE_BUSY_SPOT_MARKETS_1,
    COINBASE_BUSY_SPOT_MARKETS_2,
    COINBASE_BUSY_SPOT_MARKETS_3,
    HUOBI_BUSY_FUTURE_MARKETS,
    HU<PERSON><PERSON>_BUSY_SPOT_MARKETS,
    KRAKEN_BUSY_FUTURE_MARKETS,
    KUCOIN_BUSY_FUTURE_MARKETS_1,
    KUCOIN_BUSY_FUTURE_MARKETS_2,
    KUCOIN_BUSY_FUTURE_MARKETS_3,
    KUCOIN_BUSY_FUTURE_MARKETS_4,
    KUCOIN_BUSY_FUTURE_MARKETS_5,
    MEXC_BUSY_SPOT_MARKETS_BATCH_1,
    MEXC_BUSY_SPOT_MARKETS_BATCH_2,
    MEXC_BUSY_SPOT_MARKETS_BATCH_3,
    MEXC_BUSY_SPOT_MARKETS_BATCH_4,
    MEXC_BUSY_SPOT_MARKETS_BATCH_5,
    KUCOIN_BUSY_FUTURE_MARKETS_6,
)
from src.octopus.inventory.types import (
    FHGroup,
    Scraper,
    feed_handler_range_deployment_constructor,
    futures_http_scraper,
    futures_streaming_scraper,
    option_streaming_scraper,
    spot_http_scraper,
    spot_streaming_scraper,
    option_http_scraper,
)

PROXIES = [[Cp1HetznerUs.BOOK_REALTIME, Cp1HetznerEu.BOOK_REALTIME, SmartproxyCP1.BOOK_REALTIME]]
BYBIT_PROXIES = [[Cp1HetznerEu.BOOK_REALTIME]]
BINANCE_PROXIES = [[BinanceCP1HetznerEu.BOOK_REALTIME]]
BULLISH_PROXIES = [
    [BullishCp1HetznerUs.BOOK_TICK_BY_TICK, BullishCp1WebShareEu.BOOK_TICK_BY_TICK, BullishCp1Smartproxy.BOOK_TICK_BY_TICK]
]
COINBASE_PROXIES = [[Cp1HetznerUs.BOOK_REALTIME]]
GATE_IO_PROXIES = [[Cp1HetznerUs.BOOK_REALTIME, Cp1HetznerEu.BOOK_REALTIME]]
GEMINI_PROXIES = [
    [GeminiCP1HetznerUs.BOOK_TICK_BY_TICK, GeminiCP1WebShareEu.BOOK_TICK_BY_TICK, GeminiCP1HetznerEu.BOOK_TICK_BY_TICK]
]
HUOBI_PROXIES = [[Cp1HetznerUs.BOOK_REALTIME]]
OKEX_PROXIES = [[Cp1HetznerUs.BOOK_REALTIME]]
SPOT_HTTP = [
    spot_http_scraper(
        "Binance.US",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "instruments": ["btc-usdt"],
                "poll_interval": 0,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    spot_http_scraper(
        "itBit",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),  # don't remove, ws fh is loosing data
    spot_http_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "audio-usdt",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnt-usdt",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "doge-usdc",
                    "doge-usdt",
                    "dot-usdc",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usdt",
                    "ftm-usdc",
                    "ftm-usdt",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                    "lpt-usdt",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdc",
                    "luna-usdt",
                    "mana-usdt",
                    "matic-usdc",
                    "matic-usdt",
                    "mkr-usdt",
                    "qnt-usdt",
                    "sand-usdt",
                    "skl-usdt",
                    "slp-usdt",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdt",
                    "uni-usdt",
                    "xtz-usdt",
                    "yfi-usdt",
                    "zec-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": None,
                "postgres_out": None,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[Cp1HetznerEu.BOOK_REALTIME, SmartproxyCP1.BOOK_REALTIME]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
]

SPOT_STREAMING = [
    spot_streaming_scraper(
        "Binance Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
                "instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance",
        [f"{elem:04d}" for elem in range(0, 1419, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 30,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
                "exclude_instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
            "mem_limit": "9g",
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance.US",
        [f"{elem:03d}" for elem in range(0, 179, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitbank All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Bitfinex Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
                "instruments": BITFINEX_BUSY_SPOT_MARKETS,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "Bitfinex",
        [f"{elem:03d}" for elem in range(0, 312, 40)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
                "proxy_groups": PROXIES,
                "exclude_instruments": BITFINEX_BUSY_SPOT_MARKETS,
            },
        },
    ),
    spot_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.20,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_2,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Bitstamp",
        [f"{elem:03d}" for elem in range(0, 245, 60)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "proxy_groups": PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
        "Bullish",
        ["000", "050"],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "exclusive_proxies": True,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Bybit",
        [f"{elem:03d}" for elem in range(0, 635, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.04,
                "proxy_groups": BYBIT_PROXIES,
            }
        },
    ),
    spot_streaming_scraper(
        "Coinbase Busy1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_1,
                "proxy_groups": COINBASE_PROXIES,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Coinbase Busy2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_2,
                "proxy_groups": COINBASE_PROXIES,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    spot_streaming_scraper(
        "Coinbase Busy3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": COINBASE_PROXIES,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_3,
            }
        },
        group=FHGroup.GROUP_2,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Coinbase",
        [f"{elem:04d}" for elem in range(0, 417, 10)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": [
                    *COINBASE_BUSY_SPOT_MARKETS_1,
                    *COINBASE_BUSY_SPOT_MARKETS_2,
                    *COINBASE_BUSY_SPOT_MARKETS_3,
                ],
                "proxy_groups": COINBASE_PROXIES,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Crypto.com",
        [f"{elem:03d}" for elem in range(0, 635, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.03,
                "market_source_cache_lifetime": 180,
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": PROXIES,
                "compress_books_before_send": True,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Gate.io",
        [f"{elem:04d}" for elem in range(0, 2625, 200)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": GATE_IO_PROXIES,
                "rate_limit_multiplier": 0.25,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Gemini",
        [f"{elem:03d}" for elem in range(0, 176, 60)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.5,
                "proxy_groups": GEMINI_PROXIES,
            }
        },
    ),
    spot_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": HUOBI_PROXIES,
                "instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Huobi K",
        [f"{elem:04d}" for elem in range(0, 700, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": HUOBI_PROXIES,
                "exclude_instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
    ),
    spot_streaming_scraper(
        "itBit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_2),
        "Kraken",
        [f"{elem:04d}" for elem in range(0, 1155, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "LMAX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "streaming_api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "proxy_groups": None,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    spot_streaming_scraper(
        "MEXC Batch 1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_1,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_2,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_3,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 4",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_4,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_3,
    ),
    spot_streaming_scraper(
        "MEXC Batch 5",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_5,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_3,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "MEXC All",
        [f"{elem:04d}" for elem in range(0, 2555, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": [
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_1,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_2,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_3,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_4,
                    *MEXC_BUSY_SPOT_MARKETS_BATCH_5,
                ],
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.2,
                "proxy_groups": PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_3),
        "OKEx All",
        [f"{elem:04d}" for elem in range(0, 712, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_4),
        "Poloniex",
        [f"{elem:04d}" for elem in range(0, 995, 250)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
                "markets_per_producer": 1,
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "KuCoin Batch",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": ["btc-usdt", "eth-usdt", "floki-usdt"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.04,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(spot_streaming_scraper, group=FHGroup.GROUP_1),
        "KuCoin",
        [f"{elem:04d}" for elem in range(0, 1282, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "exclude_instruments": ["btc-usdt", "eth-usdt", "floki-usdt"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 100,
                "rate_limit_multiplier": 0.15,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": PROXIES,
            },
        },
    ),
]

FUTURES_HTTP = [
    futures_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "cp1",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCP1HetznerUs.BOOK_TICK_BY_TICK]],
                "market_source_cache_lifetime": 3600,
            },
        },
        group=FHGroup.GROUP_1,
    ),
]
FUTURES_STREAMING = [
    *[
        futures_streaming_scraper(
            f"Binance {instrument.replace('1000', '')}",
            deployment_flags={
                "entrypoint_args": {
                    "instruments": [instrument],
                    "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                    "postgres_out": None,
                    "storage_interval": None,
                    "websocket_out": None,
                    "markets_per_producer": 1,
                    "remove_network_exchange_sequence_id": True,
                    "book_depth": 30000,
                    "rate_limit_multiplier": 0.01,
                    "proxy_groups": BINANCE_PROXIES,
                },
            },
            group=FHGroup.GROUP_4,
        )
        for instrument in BINANCE_BUSY_FUTURE_MARKETS
    ],
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Binance K",
        [f"{elem:03d}" for elem in range(0, 495, 24)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
                "exclude_instruments": BINANCE_BUSY_FUTURE_MARKETS,
            },
            "mem_limit": "9g",
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "Bitfinex",
        [f"{elem:03d}" for elem in range(0, 71, 20)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
            },
        },
    ),
    futures_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 0,
                "rate_limit_multiplier": 0.20,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_2,
    ),
    futures_streaming_scraper(
        "BitMEX K",
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "exclusive_proxies": True,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_2),
        "Bybit",
        [f"{elem:04d}" for elem in range(0, 610, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "rate_limit_multiplier": 0.03,
                "book_depth": 30000,
                "proxy_groups": BYBIT_PROXIES,
            }
        },
    ),
    futures_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json cp1-fut-{{ inventory_hostname }}",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": PROXIES,
                "book_depth": 30000,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    futures_streaming_scraper(
        "CoinbaseDer",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.25,
                "book_depth": 30000,
                "streaming_api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "disable_compute_limited_delta": True,
                "market_source_cache_lifetime": 1800,
                "compress_books_before_send": True,
                "proxy_groups": PROXIES,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "CoinbaseInt",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[CoinbaseIntNY5HetznerEu.BOOK_TICK_BY_TICK]],
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Crypto.com",
        [f"{elem:03d}" for elem in range(0, 204, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "proxy_groups": PROXIES,
                "market_source_cache_lifetime": 180,
                "book_depth": 30000,
            }
        },
    ),
    futures_streaming_scraper(
        "Deribit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": None,
            },
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_1),
        "dYdX",
        [f"{elem:03d}" for elem in range(0, 234, 50)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[Cp1HetznerUs.BOOK_REALTIME, SmartproxyCP1.BOOK_REALTIME]],
            },
        },
    ),
    futures_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[Cp1HetznerUs.BOOK_REALTIME, SmartproxyCP1.BOOK_REALTIME]],
                "compress_books_before_send": True,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_2),
        "Gate.io",
        [f"{elem:03d}" for elem in range(0, 631, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": GATE_IO_PROXIES,
                "rate_limit_multiplier": 0.25,
            }
        },
    ),
    futures_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": HUOBI_PROXIES,
                "instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Huobi K",
        [f"{elem:03d}" for elem in range(0, 253, 12)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": HUOBI_PROXIES,
                "exclude_instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
    ),
    futures_streaming_scraper(
        "Kraken Batch",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": KRAKEN_BUSY_FUTURE_MARKETS,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_4,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_4),
        "Kraken",
        [f"{elem:03d}" for elem in range(0, 371, 80)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": KRAKEN_BUSY_FUTURE_MARKETS,
                "proxy_groups": PROXIES,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "MEXC All",
        [f"{elem:04d}" for elem in range(0, 766, 100)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.2,
                "proxy_groups": PROXIES,
            },
        },
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_3),
        "OKEx All",
        [f"{elem:03d}" for elem in range(0, 267, 40)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": PROXIES,
            },
        },
    ),
    futures_streaming_scraper(
        "KuCoin Batch 1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_1,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_2,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_3,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 4",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_4,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 5",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_5,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    futures_streaming_scraper(
        "KuCoin Batch 6",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": KUCOIN_BUSY_FUTURE_MARKETS_6,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
    *feed_handler_range_deployment_constructor(
        partial(futures_streaming_scraper, group=FHGroup.GROUP_1),
        "KuCoin",
        [f"{elem:03d}" for elem in range(0, 461, 20)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "exclude_instruments": [
                    *KUCOIN_BUSY_FUTURE_MARKETS_1,
                    *KUCOIN_BUSY_FUTURE_MARKETS_2,
                    *KUCOIN_BUSY_FUTURE_MARKETS_3,
                    *KUCOIN_BUSY_FUTURE_MARKETS_4,
                    *KUCOIN_BUSY_FUTURE_MARKETS_5,
                    *KUCOIN_BUSY_FUTURE_MARKETS_6,
                ],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.15,
                "proxy_groups": PROXIES,
            }
        },
    ),
]

OPTION_HTTP = [
    option_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "cp1",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCP1HetznerUs.BOOK_TICK_BY_TICK]],
                "market_source_cache_lifetime": 3600,
            },
        },
        group=FHGroup.GROUP_1,
    ),
]

OPTION_STREAMING = [
    *feed_handler_range_deployment_constructor(
        partial(option_streaming_scraper, group=FHGroup.GROUP_1),
        "Binance",
        [f"{elem:03d}" for elem in range(0, 1100, 700)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 100,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
            }
        },
    ),
    option_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": PROXIES,
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json cp1-opt-{{ inventory_hostname }}",
            },
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "Deribit",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 500,
                "book_depth": 30000,
                "proxy_groups": None,
            },
        },
        group=FHGroup.GROUP_1,
    ),
    option_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 40,
                "proxy_groups": PROXIES,
            }
        },
        group=FHGroup.GROUP_1,
    ),
]
REALTIME_BOOK: List[Scraper] = [*SPOT_HTTP, *SPOT_STREAMING, *FUTURES_HTTP, *FUTURES_STREAMING, *OPTION_HTTP, *OPTION_STREAMING]
