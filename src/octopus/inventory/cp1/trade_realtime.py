from typing import List

from grafanalib.core import TimeRange

from src.octopus.applications.proxies.enums import (
    BinanceCP1HetznerEu,
    CoinbaseIntNY5HetznerEu,
    Cp1HetznerEu,
    Cp1HetznerUs,
    SmartproxyCP1,
    <PERSON>ishCp1WebShareEu,
    <PERSON>ishCp1Smartproxy,
    GeminiCP1HetznerUs,
    GeminiCP1WebShareEu,
    GeminiCP1HetznerEu,
    <PERSON>ibitCP1HetznerEu,
    <PERSON><PERSON>tCP1Smartproxy,
    GfoxCP1HetznerUs,
    BullishCp1HetznerUs,
)
from src.octopus.inventory.api_keys import BINANCE_US_API_KEY, CME_API_KEY
from src.octopus.inventory.common import BINANCE_BUSY_SPOT_MARKETS
from src.octopus.inventory.types import (
    DataLoader,
    DbStatCollector,
    Scraper,
    feed_handler_range_deployment_constructor,
    futures_http_scraper,
    futures_streaming_scraper,
    option_http_scraper,
    option_streaming_scraper,
    spot_http_scraper,
    spot_streaming_scraper,
)

PROXIES = [[Cp1HetznerUs.TRADE_REALTIME, Cp1HetznerEu.TRADE_REALTIME, SmartproxyCP1.TRADE_REALTIME]]
BINANCE_PROXIES = [[BinanceCP1HetznerEu.TRADE_REALTIME]]
BULLISH_WS_PROXIES = [[BullishCp1HetznerUs.TRADE_REALTIME]]
BULLISH_HTTP_PROXIES = [[BullishCp1WebShareEu.TRADE_REALTIME, BullishCp1Smartproxy.TRADE_REALTIME]]
BYBIT_PROXIES = [[Cp1HetznerEu.TRADE_REALTIME]]
DERIBIT_PROXIES = [[DeribitCP1HetznerEu.TRADE_REALTIME, DeribitCP1Smartproxy.TRADE_REALTIME]]
GATE_IO_PROXIES = [[Cp1HetznerUs.TRADE_REALTIME, Cp1HetznerEu.TRADE_REALTIME]]
GEMINI_PROXIES = [[GeminiCP1HetznerUs.TRADE_REALTIME, GeminiCP1WebShareEu.TRADE_REALTIME, GeminiCP1HetznerEu.TRADE_REALTIME]]
KRAKEN_PROXIES = [[Cp1HetznerUs.TRADE_REALTIME, SmartproxyCP1.TRADE_REALTIME]]

SPOT_HTTP: List[Scraper] = [
    spot_http_scraper(
        "Bitstamp",
        deployment_flags={
            "entrypoint_args": {
                "exclude_instruments": ["btc-usd", "eth-usd", "ltc-usd"],
                "rate_limit_multiplier": 0.4,
                "cache_size": 65536,
                "websocket_out": None,
            }
        },
    ),
    spot_http_scraper(
        "Bitstamp Busy",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd", "eth-usd", "ltc-usd"],
                "rate_limit_multiplier": 0.06,
                "cache_size": 65536,
                "websocket_out": None,
            }
        },
    ),
    spot_http_scraper(
        "Bybit",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "rate_limit_multiplier": 0.4,
                "proxy_groups": BYBIT_PROXIES,
            },
            "cpu_request": 1,
        },
    ),
    spot_http_scraper(
        "Bithumb",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "rate_limit_multiplier": 0.2,
                "proxy_groups": PROXIES,
            }
        },
    ),
    spot_http_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": BULLISH_HTTP_PROXIES,
                "rate_limit_multiplier": 0.2,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        spot_http_scraper,
        "Coinbase",
        [f"{elem:03d}" for elem in range(0, 394, 80)],
        deployment_flags={
            "entrypoint_args": {
                "exclude_instruments": ["btc-usd", "eth-usd", "ltc-usd"],
                "proxy_groups": PROXIES,
                "rate_limit_multiplier": 0.3,
            },
            "mem_limit": "4g",
            "cpu_request": "150m",
        },
    ),
    # spot_http_scraper(
    #     "CEX.IO",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "rate_limit_multiplier": 0.5,
    #             "proxy_groups": PROXIES,
    #             "poll_interval": 2,
    #         },
    #     },
    # ),
    spot_http_scraper(
        "Coinbase BTC-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd"],
                "poll_interval": "0",
                "proxy_groups": PROXIES,
                "rate_limit_multiplier": 0.04,
            },
            "cpu_request": "100m",
        },
    ),
    spot_http_scraper(
        "Coinbase ETH-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["eth-usd"],
                "poll_interval": "0",
                "rate_limit_multiplier": 0.04,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "100m",
        },
    ),
    spot_http_scraper(
        "Coinbase LTC-USD",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["ltc-usd"],
                "poll_interval": "0",
                "rate_limit_multiplier": 0.04,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "100m",
        },
    ),
    spot_http_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 10,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "900m",
        },
    ),
    *feed_handler_range_deployment_constructor(
        spot_http_scraper,
        "Gate.io",
        [f"{elem:04d}" for elem in range(0, 3050, 900)],
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": GATE_IO_PROXIES,
                "rate_limit_multiplier": 0.25,
            },
        },
    ),
    spot_http_scraper(
        "Gemini",
        deployment_flags={
            "entrypoint_args": {
                "exclude_instruments": ["btc-usd", "usdt-usd", "eth-usd", "ltc-usd"],
                "websocket_out": None,
                "markets_per_producer": 1,
                "proxy_groups": GEMINI_PROXIES,
                "rate_limit_multiplier": 0.2,
            },
            "cpu_request": "800m",
        },
    ),
    spot_http_scraper(
        "Gemini Busy",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd", "usdt-usd", "eth-usd", "ltc-usd"],
                "websocket_out": None,
                "markets_per_producer": 1,
                "proxy_groups": GEMINI_PROXIES,
                "rate_limit_multiplier": 0.1,
            },
            "cpu_request": "800m",
        },
    ),
    spot_http_scraper(
        "itBit",
        {
            "Items received": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "DB items stored": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "Kafka items sent": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
        },
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "600m",
        },
    ),
    spot_http_scraper(
        "LBank",
        deployment_flags={"entrypoint_args": {"websocket_out": None, "proxy_groups": None}},
    ),
    spot_http_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "rate_limit_multiplier": 0.1,
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_http_scraper(
        "Poloniex",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 30,
                "proxy_groups": PROXIES,
                "rate_limit_multiplier": 0.05,
            }
        },
    ),
]
SPOT_STREAMING: List[Scraper] = [
    # spot_streaming_scraper(
    #     "Bibox",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "markets_per_producer": 50,
    #             "proxy_groups": PROXIES,
    #         }
    #     },
    # ),
    *feed_handler_range_deployment_constructor(
        spot_streaming_scraper,
        "Binance",
        [f"{elem:04d}" for elem in range(0, 1250, 250)],
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": BINANCE_PROXIES,
                "exclude_instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
            "mem_limit": "9g",
        },
    ),
    spot_streaming_scraper(
        "Binance Busy",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 1,
                "proxy_groups": BINANCE_PROXIES,
                "rate_limit_multiplier": 0.25,
                "instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
            "mem_limit": "9g",
        },
    ),
    spot_streaming_scraper(
        "Binance.US",
        deployment_flags={
            "entrypoint_args": {
                "api_params": BINANCE_US_API_KEY,
                "markets_per_producer": 200,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "75m",
        },
    ),
    spot_streaming_scraper(
        "Bitbank",
        {"Markets scraped": {"alert": None}},
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
                "markets_per_producer": 1,
            },
            "cpu_request": "15m",
        },
    ),
    spot_streaming_scraper(
        "Bitfinex",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 30,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "40m",
        },
    ),
    spot_streaming_scraper(
        "bitFlyer",
        {
            "Items received": {"alert": {"alertConditions": {"timeRange": TimeRange("30m", "now")}}},
            "DB items stored": {"alert": {"alertConditions": {"timeRange": TimeRange("30m", "now")}}},
            "Kafka items sent": {"alert": {"alertConditions": {"timeRange": TimeRange("30m", "now")}}},
        },
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "15m",
        },
    ),
    spot_streaming_scraper(
        "Bitstamp",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
                "rate_limit_multiplier": 0.04,
            },
            "cpu_request": "20m",
        },
    ),
    spot_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 1,
                "proxy_groups": BULLISH_WS_PROXIES,
                "market_source_cache_lifetime": 60,
                "rate_limit_multiplier": 0.1,
                "exclusive_proxies": True,
            },
            "cpu_request": "75m",
        },
    ),
    spot_streaming_scraper(
        "Bybit",
        deployment_flags={"entrypoint_args": {"rate_limit_multiplier": 0.05, "proxy_groups": BYBIT_PROXIES}},
    ),
    *feed_handler_range_deployment_constructor(
        spot_streaming_scraper,
        "Coinbase",
        [f"{elem:03d}" for elem in range(0, 420, 40)],
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 40,
                "proxy_groups": [
                    [None, Cp1HetznerUs.TRADE_REALTIME],
                    [Cp1HetznerEu.TRADE_REALTIME, SmartproxyCP1.TRADE_REALTIME],
                ],
                "rate_limit_multiplier": 0.04,
            }
        },
    ),
    spot_streaming_scraper(
        "Coinbase BTC.ETH",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd", "eth-usd", "ltc-usd"],
                "proxy_groups": None,
                "rate_limit_multiplier": 0.02,
            }
        },
    ),
    spot_streaming_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 100,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "75m",
        },
    ),
    spot_streaming_scraper(
        "Deribit",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": DERIBIT_PROXIES,
            }
        },
    ),
    spot_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "30m",
        },
    ),
    spot_streaming_scraper(
        "Gate.io",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 100,
                "proxy_groups": GATE_IO_PROXIES,
                "rate_limit_multiplier": 0.125,
            },
            "cpu_request": "150m",
        },
    ),
    spot_streaming_scraper(
        "Gemini",
        deployment_flags={
            "entrypoint_args": {"markets_per_producer": 1, "proxy_groups": GEMINI_PROXIES, "rate_limit_multiplier": 0.2}
        },
    ),
    spot_streaming_scraper(
        "HitBTC",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "40m",
        },
    ),
    spot_streaming_scraper(
        "Huobi",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "itBit",
        {
            "Items received": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "DB items stored": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "Kafka items sent": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
        },
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "15m",
        },
    ),
    spot_streaming_scraper(
        "Kraken",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 100,
                "proxy_groups": KRAKEN_PROXIES,
                "rate_limit_multiplier": 0.05,
            },
            "cpu_request": "50m",
        },
    ),
    spot_streaming_scraper(
        "KuCoin",
        deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES, "markets_per_producer": 100}},
    ),
    spot_streaming_scraper(
        "LBank",
        deployment_flags={
            "entrypoint_args": {
                "websocket_out": None,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "85m",
        },
    ),
    spot_streaming_scraper(
        "LMAX",
        deployment_flags={
            "entrypoint_args": {
                "streaming_api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "proxy_groups": None,
            },
            "cpu_request": "20m",
        },
    ),
    spot_streaming_scraper(
        "MEXC",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 20,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": PROXIES,
            }
        },
    ),
    spot_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "150m",
        },
    ),
    spot_streaming_scraper(
        "Poloniex",
        deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES, "rate_limit_multiplier": 0.1}},
    ),
    spot_streaming_scraper(
        "Upbit",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "75m",
        },
    ),
]
FUTURES_HTTP: List[Scraper] = [
    futures_http_scraper(
        "Binance Busy No Skip",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "no-skip-trades",
                "poll_interval": 0,
                "proxy_groups": BINANCE_PROXIES,
                "instruments": ["BTCUSDT", "ETHUSDT"],
                "rate_limit_multiplier": 0.05,
            },
            "mem_limit": "9g",
        },
    ),
    futures_http_scraper(
        "Binance Busy",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": BINANCE_PROXIES,
                "instruments": ["BTCUSDT", "ETHUSDT"],
                "rate_limit_multiplier": 0.05,
            },
            "mem_limit": "9g",
        },
    ),
    *feed_handler_range_deployment_constructor(
        futures_http_scraper,
        "Binance",
        [f"{elem:04d}" for elem in range(0, 459, 160)],
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": BINANCE_PROXIES,
                "rate_limit_multiplier": 0.10,
            },
            "mem_limit": "9g",
        },
    ),
    futures_http_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "poll_interval": 0,
                "proxy_groups": BULLISH_HTTP_PROXIES,
                "rate_limit_multiplier": 0.2,
            }
        },
    ),
    futures_http_scraper(
        "GFOX",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "cp1",
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCP1HetznerUs.TRADE_REALTIME]],
                "market_source_cache_lifetime": 3600,
            }
        },
    ),
    futures_http_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {"poll_interval": 0, "proxy_groups": PROXIES, "rate_limit_multiplier": 0.2},
            "mem_limit": "5g",
            "cpu_request": 1,
        },
    ),
]
FUTURES_STREAMING = [
    futures_streaming_scraper(
        "BinanceAgg",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 1,
                "proxy_groups": BINANCE_PROXIES,
                "instruments": ["BTCUSDT", "ETHUSDT"],
                "rate_limit_multiplier": 0.05,
            },
        },
    ),
    futures_streaming_scraper(
        "Bitfinex",
        {
            "Items received": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "DB items stored": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
            "Kafka items sent": {"alert": {"alertConditions": {"timeRange": TimeRange("45m", "now")}}},
        },
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 30,
                "proxy_groups": PROXIES,
            },
            "cpu_request": "20m",
        },
    ),
    futures_streaming_scraper(
        "bitFlyer",
        {
            "Items received": {"alert": {"alertConditions": {"timeRange": TimeRange("6h", "now")}}},
            "DB items stored": {"alert": {"alertConditions": {"timeRange": TimeRange("6h", "now")}}},
            "Kafka items sent": {"alert": {"alertConditions": {"timeRange": TimeRange("6h", "now")}}},
        },
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": PROXIES,
            },
            "cpu_request": "15m",
        },
    ),
    futures_streaming_scraper(
        "BitMEX",
        deployment_flags={
            "entrypoint_args": {"proxy_groups": PROXIES, "markets_per_producer": 20},
            "cpu_request": "30m",
        },
    ),
    futures_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 1,
                "proxy_groups": BULLISH_WS_PROXIES,
                "market_source_cache_lifetime": 60,
                "rate_limit_multiplier": 0.1,
                "exclusive_proxies": True,
            },
            "cpu_request": "75m",
        },
    ),
    futures_streaming_scraper(
        "Bybit",
        deployment_flags={"entrypoint_args": {"rate_limit_multiplier": 0.05, "proxy_groups": BYBIT_PROXIES}},
    ),
    futures_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json ny5-{{ inventory_hostname }}",
                "proxy_groups": PROXIES,
            }
        },
    ),
    futures_streaming_scraper(
        "CoinbaseDer",
        deployment_flags={
            "entrypoint_args": {
                "streaming_api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "api_params": "cp1-${FEED_HANDLER_INSTANCE}:cp1-1,cp1-2",
                "market_source_cache_lifetime": 1800,
            }
        },
    ),
    futures_streaming_scraper(
        "CoinbaseInt", deployment_flags={"entrypoint_args": {"proxy_groups": [[CoinbaseIntNY5HetznerEu.TRADE_REALTIME]]}}
    ),
    futures_streaming_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {"markets_per_producer": 100, "proxy_groups": PROXIES},
            "cpu_request": "25m",
        },
    ),
    futures_streaming_scraper(
        "Deribit", deployment_flags={"entrypoint_args": {"proxy_groups": DERIBIT_PROXIES}, "cpu_request": "30m"}
    ),
    futures_streaming_scraper("dYdX", deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES}}),
    futures_streaming_scraper("ErisX", deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES}, "cpu_request": "10m"}),
    futures_streaming_scraper(
        "Gate.io",
        deployment_flags={
            "entrypoint_args": {
                "proxy_groups": GATE_IO_PROXIES,
                "rate_limit_multiplier": 0.125,
            },
            "cpu_request": "150m",
        },
    ),
    futures_streaming_scraper("HitBTC", deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES}, "cpu_request": "25m"}),
    futures_streaming_scraper("Huobi", deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES}, "cpu_request": "75m"}),
    futures_streaming_scraper(
        "Kraken",
        deployment_flags={
            "entrypoint_args": {"proxy_groups": KRAKEN_PROXIES, "markets_per_producer": 100},
            "cpu_request": "30m",
        },
    ),
    futures_streaming_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {"markets_per_producer": 100, "proxy_groups": PROXIES},
            "cpu_request": "100m",
        },
    ),
    *feed_handler_range_deployment_constructor(
        futures_streaming_scraper,
        "MEXC",
        [f"{elem:03d}" for elem in range(0, 500, 300)],
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": PROXIES,
            }
        },
    ),
    futures_streaming_scraper(
        "OKEx",
        deployment_flags={"entrypoint_args": {"proxy_groups": PROXIES}},
    ),
]
OPTION_HTTP = [
    option_http_scraper(
        "GFOX",
        deployment_flags={
            "entrypoint_args": {
                "api_params": "cp1",
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCP1HetznerUs.TRADE_REALTIME]],
                "market_source_cache_lifetime": 3600,
            }
        },
    ),
    option_http_scraper(
        "OKEx",
        deployment_flags={"entrypoint_args": {"poll_interval": 0, "proxy_groups": PROXIES, "rate_limit_multiplier": 0.1}},
    ),
]
OPTION_STREAMING = [
    option_streaming_scraper(
        "Binance",
        deployment_flags={
            "entrypoint_args": {
                "markets_per_producer": 200,
                "proxy_groups": BINANCE_PROXIES,
                "rate_limit_multiplier": 0.1,
            }
        },
    ),
    option_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json ny5-{{ inventory_hostname }}",
                "proxy_groups": PROXIES,
            }
        },
    ),
    option_streaming_scraper(
        "Deribit",
        deployment_flags={
            "entrypoint_args": {"markets_per_producer": 50, "proxy_groups": DERIBIT_PROXIES},
            "cpu_request": "40m",
        },
    ),
    option_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {"markets_per_producer": 1000, "proxy_groups": PROXIES},
            "cpu_request": "15m",
        },
    ),
]
REALTIME_TRADE: List[Scraper] = [
    *SPOT_HTTP,
    *SPOT_STREAMING,
    *FUTURES_HTTP,
    *FUTURES_STREAMING,
    *OPTION_HTTP,
    *OPTION_STREAMING,
]

DATA_LOADERS: List[DataLoader] = [
    # following Python Data Loader should remain until Kotlin Data Loader will adopt inserts to custom tables
    DataLoader.trades("BinanceAgg"),
    DataLoader.trades("Deribit"),
    DataLoader.trades("dYdX"),
    DataLoader.trades("GFOX"),
]

DB_STAT_COLLECTORS: List[DbStatCollector] = [
    DbStatCollector.trades_spot("LMAX"),
    DbStatCollector.trades_spot("ErisX"),
    DbStatCollector.trades_spot("itBit"),
    # DbStatCollector.trades_spot("CEX.IO"),
    DbStatCollector.trades_spot("bitFlyer"),
    DbStatCollector.trades_spot("Gemini"),
    DbStatCollector.trades_spot("Bitbank"),
    DbStatCollector.trades_spot("Bitstamp"),
    DbStatCollector.trades_spot("Bullish"),
    DbStatCollector.trades_spot("Binance.US"),
    DbStatCollector.trades_spot("Bithumb"),
    DbStatCollector.trades_spot("Kraken"),
    DbStatCollector.trades_spot("Bitfinex"),
    DbStatCollector.trades_spot("HitBTC"),
    DbStatCollector.trades_spot("Crypto.com"),
    DbStatCollector.trades_spot("Poloniex"),
    DbStatCollector.trades_spot("Coinbase"),
    DbStatCollector.trades_spot("MEXC"),
    DbStatCollector.trades_spot("LBank"),
    DbStatCollector.trades_spot("Bibox"),
    DbStatCollector.trades_spot("Huobi"),
    DbStatCollector.trades_spot("Bybit"),
    DbStatCollector.trades_spot("Binance"),
    DbStatCollector.trades_spot("Deribit"),
    DbStatCollector.trades_spot("Gate.io"),
    DbStatCollector.trades_spot("KuCoin"),
    DbStatCollector.trades_spot("OKEx"),
    DbStatCollector.trades_spot("Upbit"),
]
