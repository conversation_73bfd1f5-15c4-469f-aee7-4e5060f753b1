from typing import List

from src.octopus.applications.proxies.enums import WebShareEu, BinanceCdev1WebShare
from src.octopus.inventory.types import (
    Scraper,
    feed_handler_range_deployment_constructor,
    option_http_scraper,
    option_streaming_scraper,
)

# There are no spot tickers
OPTION_HTTP: List[Scraper] = [
    option_http_scraper(
        "Bybit",
        deployment_flags={
            "entrypoint_args": {"proxy_groups": [[WebShareEu.OPTION_TICKER]], "market_source_cache_lifetime": 60},
        },
    ),
    option_http_scraper("Deribit"),
]
OPTION_STREAMING: List[Scraper] = [
    option_streaming_scraper(
        "Binance",
        deployment_flags={
            "entrypoint_args": {
                "rate_limit_multiplier": 0.1,
                "proxy_groups": [[BinanceCdev1WebShare.TICKER_O_REALTIME]],
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        option_streaming_scraper,
        "OKEx",
        ["0000", "0700"],
        deployment_flags={"entrypoint_args": {"rate_limit_multiplier": 0.2, "proxy_groups": [[WebShareEu.OPTION_TICKER]]}},
    ),
]

REALTIME_OPTION_TICKER: List[Scraper] = [*OPTION_HTTP, *OPTION_STREAMING]
