from typing import List

from src.octopus.applications.proxies.enums import (
    BybitHel1HetznerEu,
    CoinbaseIntHel1HetznerEu,
    WebShareEu,
    BullishCdev1WebShareEu,
    BullishCdev1Smartproxy,
    GeminiCdev1HetznerUs,
    GeminiCdev1WebShareEu,
    GeminiCdev1HetznerEu,
    BinanceCdev1WebShare,
    GfoxCdev1HetznerEu,
)
from src.octopus.inventory.api_keys import CME_API_KEY
from src.octopus.inventory.common import (
    BINANCE_BUSY_FUTURE_MARKETS,
    BINANCE_BUSY_SPOT_MARKETS,
    BITFINEX_BUSY_SPOT_MARKETS,
    COINBASE_BUSY_SPOT_MARKETS_1,
    COINBASE_BUSY_SPOT_MARKETS_2,
    COINBASE_BUSY_SPOT_MARKETS_3,
    HUOBI_BUSY_FUTURE_MARKETS,
    HUOBI_BUSY_SPOT_MARKETS,
    MEXC_BUSY_SPOT_MARKETS_BATCH_1,
    MEXC_BUSY_SPOT_MARKETS_BATCH_2,
    MEXC_BUSY_SPOT_MARKETS_BATCH_3,
    MEXC_BUSY_SPOT_MARKETS_BATCH_4,
    MEXC_BUSY_SPOT_MARKETS_BATCH_5,
)
from src.octopus.inventory.types import (
    Scraper,
    feed_handler_range_deployment_constructor,
    futures_http_scraper,
    futures_streaming_scraper,
    option_streaming_scraper,
    spot_http_scraper,
    spot_streaming_scraper,
    option_http_scraper,
)

BINANCE_PROXIES = [[BinanceCdev1WebShare.BOOK_REALTIME]]
BYBIT_PROXIES = [[BybitHel1HetznerEu.BOOK_REALTIME]]
BULLISH_PROXIES = [[BullishCdev1WebShareEu.BOOK_TICK_BY_TICK, BullishCdev1Smartproxy.BOOK_TICK_BY_TICK]]
GEMINI_PROXIES = [
    [GeminiCdev1HetznerUs.BOOK_TICK_BY_TICK, GeminiCdev1WebShareEu.BOOK_TICK_BY_TICK, GeminiCdev1HetznerEu.BOOK_TICK_BY_TICK]
]
SPOT_HTTP = [
    spot_http_scraper(
        "Binance.US",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "instruments": ["btc-usdt"],
                "poll_interval": 0,
            }
        },
    ),
    spot_http_scraper(
        "itBit",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
            },
        },
    ),
    spot_http_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {
                "instruments": [
                    "1inch-usdt",
                    "aave-usdt",
                    "amp-usdt",
                    "ankr-usdt",
                    "audio-usdt",
                    "axs-usdt",
                    "bat-usdt",
                    "bch-usdc",
                    "bch-usdt",
                    "bnt-usdt",
                    "btc-usdc",
                    "btc-usdt",
                    "comp-usdt",
                    "crv-usdt",
                    "doge-usdc",
                    "doge-usdt",
                    "dot-usdc",
                    "dot-usdt",
                    "enj-usdt",
                    "eth-usdc",
                    "eth-usdt",
                    "fil-usdt",
                    "ftm-usdc",
                    "ftm-usdt",
                    "grt-usdt",
                    "link-usdc",
                    "link-usdt",
                    "lpt-usdt",
                    "lrc-usdt",
                    "ltc-usdc",
                    "ltc-usdt",
                    "luna-usdc",
                    "luna-usdt",
                    "mana-usdt",
                    "matic-usdc",
                    "matic-usdt",
                    "mkr-usdt",
                    "qnt-usdt",
                    "sand-usdt",
                    "skl-usdt",
                    "slp-usdt",
                    "snx-usdt",
                    "sol-usdc",
                    "sol-usdt",
                    "sushi-usdt",
                    "uni-usdt",
                    "xtz-usdt",
                    "yfi-usdt",
                    "zec-usdt",
                ],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": None,
                "postgres_out": None,
                "rate_limit_multiplier": 0.1,
            },
        },
    ),
]
SPOT_STREAMING = [
    spot_streaming_scraper(
        "Binance Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
                "instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
        },
    ),
    spot_streaming_scraper(
        "Binance Kafka",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "market_range": "0:5",
                "markets_per_producer": 30,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": BINANCE_PROXIES,
                "exclude_instruments": BINANCE_BUSY_SPOT_MARKETS,
            },
        },
    ),
    spot_streaming_scraper(
        "Binance.US K",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitbank All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitfinex Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 10,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "instruments": BITFINEX_BUSY_SPOT_MARKETS,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitfinex",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
            },
        },
    ),
    spot_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["btc-usd", "btc-jpy", "eth-jpy"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.20,
            },
        },
    ),
    spot_streaming_scraper(
        "Bitstamp",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "000:060",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:50",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "exclusive_proxies": True,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
    ),
    spot_streaming_scraper(
        "Bybit K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.04,
                "proxy_groups": BYBIT_PROXIES,
                "markets_per_producer": 10,
            }
        },
    ),
    # spot_streaming_scraper(
    #     "CEX.IO",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "streaming_api_params": CEX_IO_API_KEY,
    #             "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
    #             "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
    #             "postgres_out": None,
    #             "storage_interval": None,
    #             "websocket_out": None,
    #             "book_depth": 30000,
    #             "rate_limit_multiplier": 0.05,
    #             "markets_per_producer": 30,
    #         },
    #     },
    # ),
    # spot_streaming_scraper(
    #     "CEX.IO DB",
    #     deployment_flags={
    #         "entrypoint_args": {
    #             "instruments": [
    #                 "1inch-usd",
    #                 "aave-usd",
    #                 "aave-usdt",
    #                 "audio-usd",
    #                 "axs-usd",
    #                 "bat-usd",
    #                 "bat-usdt",
    #                 "bch-usd",
    #                 "bch-usdt",
    #                 "bnt-usd",
    #                 "bnt-usdt",
    #                 "btc-usd",
    #                 "btc-usdt",
    #                 "comp-usd",
    #                 "comp-usdt",
    #                 "crv-usd",
    #                 "crv-usdt",
    #                 "doge-usd",
    #                 "dot-usd",
    #                 "dot-usdt",
    #                 "eth-usd",
    #                 "eth-usdt",
    #                 "fil-usd",
    #                 "ftm-usd",
    #                 "grt-usd",
    #                 "link-usd",
    #                 "link-usdt",
    #                 "lrc-usd",
    #                 "ltc-usd",
    #                 "ltc-usdt",
    #                 "luna-usd",
    #                 "mana-usd",
    #                 "matic-usd",
    #                 "matic-usdt",
    #                 "mkr-usd",
    #                 "qnt-usd",
    #                 "sand-usd",
    #                 "snx-usd",
    #                 "snx-usdt",
    #                 "sol-usd",
    #                 "sushi-usd",
    #                 "sushi-usdt",
    #                 "uni-usd",
    #                 "uni-usdt",
    #                 "xtz-usd",
    #                 "xtz-usdt",
    #                 "yfi-usd",
    #                 "yfi-usdt",
    #                 "zrx-usd",
    #                 "zrx-usdt",
    #             ],
    #             "streaming_api_params": CEX_IO_API_KEY,
    #         },
    #     },
    # ),
    spot_streaming_scraper(
        "Coinbase Busy1",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_1,
            }
        },
    ),
    spot_streaming_scraper(
        "Coinbase Busy2",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_2,
            }
        },
    ),
    spot_streaming_scraper(
        "Coinbase Busy3",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "instruments": COINBASE_BUSY_SPOT_MARKETS_3,
            }
        },
    ),
    spot_streaming_scraper(
        "Coinbase",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "exclude_instruments": [
                    *COINBASE_BUSY_SPOT_MARKETS_1,
                    *COINBASE_BUSY_SPOT_MARKETS_2,
                    *COINBASE_BUSY_SPOT_MARKETS_3,
                ],
            }
        },
    ),
    spot_streaming_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            }
        },
    ),
    spot_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "market_range": "0:5",
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "compress_books_before_send": True,
            },
        },
    ),
    spot_streaming_scraper(
        "Gate.io",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0200",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "rate_limit_multiplier": 0.25,
            }
        },
    ),
    *feed_handler_range_deployment_constructor(
        spot_streaming_scraper,
        "Gemini",
        [f"{elem:03d}" for elem in range(0, 176, 60)],
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.5,
                "proxy_groups": GEMINI_PROXIES,
            }
        },
    ),
    spot_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
    ),
    spot_streaming_scraper(
        "Huobi K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0100",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "exclude_instruments": HUOBI_BUSY_SPOT_MARKETS,
            }
        },
    ),
    spot_streaming_scraper(
        "itBit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
            },
        },
    ),
    spot_streaming_scraper(
        "Kraken K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0100",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            }
        },
    ),
    spot_streaming_scraper(
        "LMAX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "streaming_api_params": "staging-${FEED_HANDLER_INSTANCE}:staging-1,staging-2",
            },
        },
    ),
    spot_streaming_scraper(
        "MEXC Batch 1",
        deployment_flags={
            "entrypoint_args": {
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_1,
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "MEXC Batch 2",
        deployment_flags={
            "entrypoint_args": {
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_2,
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "MEXC Batch 3",
        deployment_flags={
            "entrypoint_args": {
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_3,
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "MEXC Batch 4",
        deployment_flags={
            "entrypoint_args": {
                "instruments": MEXC_BUSY_SPOT_MARKETS_BATCH_4,
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "MEXC Batch 5",
        deployment_flags={
            "entrypoint_args": {
                "instruments": ["manyu-usdt", *MEXC_BUSY_SPOT_MARKETS_BATCH_5],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "OKEx All",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0080",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.25,
            },
        },
    ),
    spot_streaming_scraper(
        "Poloniex",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0250",
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.2,
                "markets_per_producer": 1,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    spot_streaming_scraper(
        "KuCoin Batch",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "instruments": ["manyu-usdt", "btc-usdt", "eth-usdt", "floki-usdt"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.04,
                "remove_network_exchange_sequence_id": True,
            },
        },
    ),
    spot_streaming_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0000:0050",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 100,
                "rate_limit_multiplier": 0.15,
                "remove_network_exchange_sequence_id": True,
            },
        },
    ),
]
FUTURES_HTTP = [
    futures_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCdev1HetznerEu.BOOK_TICK_BY_TICK]],
                "market_source_cache_lifetime": 1800,
            },
        },
    ),
]
FUTURES_STREAMING = [
    *[
        futures_streaming_scraper(
            f"Binance {instrument.replace('1000', '')}",
            deployment_flags={
                "entrypoint_args": {
                    "instruments": [instrument],
                    "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                    "postgres_out": None,
                    "storage_interval": None,
                    "websocket_out": None,
                    "markets_per_producer": 1,
                    "remove_network_exchange_sequence_id": True,
                    "book_depth": 30000,
                    "rate_limit_multiplier": 0.01,
                    "proxy_groups": BINANCE_PROXIES,
                },
                "mem_limit": "9g",
            },
        )
        for instrument in BINANCE_BUSY_FUTURE_MARKETS
    ],
    futures_streaming_scraper(
        "Binance K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:12",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "remove_network_exchange_sequence_id": True,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.025,
                "proxy_groups": BINANCE_PROXIES,
                "exclude_instruments": BINANCE_BUSY_FUTURE_MARKETS,
            },
        },
    ),
    futures_streaming_scraper(
        "Bitfinex",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
            },
        },
    ),
    futures_streaming_scraper(
        "bitFlyer All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "instruments": ["FX_BTC_JPY"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
                "rate_limit_multiplier": 0.20,
            },
        },
    ),
    futures_streaming_scraper(
        "BitMEX K",
        deployment_flags={
            "entrypoint_args": {
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
            },
        },
    ),
    futures_streaming_scraper(
        "Bullish",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "exclusive_proxies": True,
                "proxy_groups": BULLISH_PROXIES,
            },
        },
    ),
    futures_streaming_scraper(
        "Bybit K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 50,
                "rate_limit_multiplier": 0.03,
                "book_depth": 30000,
                "proxy_groups": BYBIT_PROXIES,
            }
        },
    ),
    futures_streaming_scraper(
        "CoinbaseDer",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
                "streaming_api_params": "hel1-${FEED_HANDLER_INSTANCE}:hel1-1,hel1-2",
                "api_params": "hel1-${FEED_HANDLER_INSTANCE}:hel1-1,hel1-2",
                "disable_compute_limited_delta": True,
                "market_source_cache_lifetime": 1800,
                "compress_books_before_send": True,
            },
        },
    ),
    futures_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json hel1-{{ inventory_hostname }}",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "rate_limit_multiplier": 0.05,
                "book_depth": 30000,
            },
        },
    ),
    futures_streaming_scraper(
        "CoinbaseInt",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "proxy_groups": [[CoinbaseIntHel1HetznerEu.BOOK_TICK_BY_TICK]],
            }
        },
    ),
    futures_streaming_scraper(
        "Crypto.com",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            }
        },
    ),
    futures_streaming_scraper(
        "Deribit All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            },
        },
    ),
    futures_streaming_scraper(
        "dYdX",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            },
        },
    ),
    futures_streaming_scraper(
        "ErisX",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "market_range": "0:5",
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "compress_books_before_send": True,
            },
        },
    ),
    futures_streaming_scraper(
        "Gate.io",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "000:080",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "remove_network_exchange_sequence_id": True,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "rate_limit_multiplier": 0.25,
            }
        },
    ),
    futures_streaming_scraper(
        "Huobi Busy",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
    ),
    futures_streaming_scraper(
        "Huobi K",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "000:012",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "markets_per_producer": 10,
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.05,
                "proxy_groups": [[WebShareEu.BOOK_REALTIME]],
                "exclude_instruments": HUOBI_BUSY_FUTURE_MARKETS,
            }
        },
    ),
    futures_streaming_scraper(
        "Kraken",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "000:080",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
            }
        },
    ),
    futures_streaming_scraper(
        "MEXC",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:5",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "book_depth": 30000,
                "markets_per_producer": 1,
            },
        },
    ),
    futures_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "000:040",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "markets_per_producer": 1,
                "book_depth": 30000,
                "rate_limit_multiplier": 0.25,
            },
        },
    ),
    futures_streaming_scraper(
        "KuCoin",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "00:20",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "kafka_out_quotes": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 10,
                "rate_limit_multiplier": 0.15,
            }
        },
    ),
]

OPTION_HTTP = [
    option_http_scraper(
        "GFOX All",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "poll_interval": 10,
                "rate_limit_multiplier": 0.25,
                "proxy_groups": [[GfoxCdev1HetznerEu.BOOK_TICK_BY_TICK]],
                "market_source_cache_lifetime": 3600,
            },
        },
    ),
]

OPTION_STREAMING = [
    option_streaming_scraper(
        "Binance",
        deployment_flags={
            "entrypoint_args": {
                "market_range": "0:200",
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "markets_per_producer": 100,
                "remove_network_exchange_sequence_id": True,
                "rate_limit_multiplier": 0.1,
                "proxy_groups": BINANCE_PROXIES,
            }
        },
    ),
    option_streaming_scraper(
        "CME",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "api_params": CME_API_KEY,
                "streaming_api_params": "coinmetrics-f5d38728cc2a.json {{ inventory_hostname }}",
            }
        },
    ),
    option_streaming_scraper(
        "Deribit",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "market_range": "0:5",
                "markets_per_producer": 500,
                "book_depth": 30000,
            },
        },
    ),
    option_streaming_scraper(
        "OKEx",
        deployment_flags={
            "entrypoint_args": {
                "kafka_out_proto": ["kafka-{{ inventory_hostname }}"],
                "postgres_out": None,
                "storage_interval": None,
                "websocket_out": None,
                "book_depth": 30000,
                "market_range": "0:5",
                "markets_per_producer": 40,
            }
        },
    ),
]

REALTIME_BOOK: List[Scraper] = [
    *SPOT_HTTP,
    *SPOT_STREAMING,
    *FUTURES_HTTP,
    *FUTURES_STREAMING,
    *OPTION_HTTP,
    *OPTION_STREAMING,
]
