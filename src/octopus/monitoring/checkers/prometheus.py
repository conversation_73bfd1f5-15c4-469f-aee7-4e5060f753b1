from collections import defaultdict
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, Set, Type, Tuple, TypeVar, Generic

from src.octopus.generators.deployment.data_loader_types.general import DataLoaderDeployment
from src.octopus.generators.deployment.scraper_types.general import ScraperDeployment
from src.octopus.inventory.types import DataType, ConnectionMode
from src.octopus.monitoring.alerts.manager import <PERSON><PERSON>, AlertManager
from src.octopus.monitoring.alerts.models import AlertPriority
from src.octopus.monitoring.checkers.base import Checker
from src.octopus.monitoring.configs.exchanges import get_outage_tolerance_map
from src.octopus.monitoring.errors import (
    HighChannelUtilizationError,
    HighCpuUtilizationError,
    HighErrorsNumberError,
    HighLogsVolumeError,
    KafkaZeroDataError,
    MonitoringError,
    NoMetadataError,
    PostgresZeroDataError,
    PrometheusConfigError,
    NoMarketsScrapedDataError,
    HighKafkaTradesLagError,
)
from src.octopus.monitoring.models import <PERSON><PERSON><PERSON>, MonitoringLevel
from src.octopus.monitoring.sources.octopus import DeploymentsSource
from src.octopus.monitoring.sources.prometheus import PrometheusSource, PrometheusResponse
from src.utils.diagnostics import Diagnostics


def _return_true(_: Any) -> bool:
    return True


@dataclass
class PrometheusCheckConfig:
    query: str
    error_message: str
    error_type: Type[MonitoringError]
    failed_if: Callable[[PrometheusResponse], bool]
    condition: Callable[[Any], bool] = _return_true
    condition_queries: Optional[Tuple[str, ...]] = None
    args: Optional[Dict[str, Any]] = None
    priority: AlertPriority = AlertPriority.NORMAL
    use_cache: bool = False
    filter_similar_messages: bool = False


@dataclass
class CheckResult:
    failed: bool
    message: str
    container_name: str


D_T = TypeVar("D_T")


class IMonitoringCheck(Generic[D_T]):
    def __init__(self, prometheus: PrometheusSource, config: PrometheusCheckConfig, all_deployments: Dict[str, D_T]) -> None:
        self._prometheus = prometheus
        self._query_cache: Dict[str, Any] = {}
        self._validate(config)
        self._config = config
        self._query = config.query
        self._condition = config.condition
        self._condition_queries = config.condition_queries or []
        self._failed_if_func: Callable[[PrometheusResponse], bool] = config.failed_if
        self._error_message = config.error_message
        self._args = config.args

        self.deployments: Dict[str, ScraperDeployment] = self._get_applicable_deployments(all_deployments)

    def _get_applicable_deployments(self, deployments: Dict[str, ScraperDeployment]) -> Dict[str, ScraperDeployment]:
        return {container_name: deployment for container_name, deployment in deployments.items() if self._condition(deployment)}

    def query(self, data_center: DataCenter, condition_query_results: Dict[str, Set[str]]) -> Set[str]:
        response = self._run_cached_query(data_center)
        return self._get_failed_container(set(self.deployments), response, condition_query_results)

    def _get_failed_container(
        self,
        expected_containers: Set[str],
        response: Dict[str, PrometheusResponse],
        condition_query_results: Dict[str, Set[str]],
    ) -> Set[str]:
        # only check containers that comply with the condition_query
        conditioned_containers = expected_containers
        for query_name in self._condition_queries:
            filtered_containers = condition_query_results.get(query_name) or set()
            conditioned_containers = conditioned_containers.intersection(filtered_containers)

        return {
            container_name
            for container_name in conditioned_containers
            if container_name not in response or self._failed_if_func(response[container_name])
        }

    def _run_cached_query(self, data_center: DataCenter, outage_tolerance: int = 1) -> Dict[str, Any]:
        # we get false-positives when outage tolerance is 60 seconds
        query = self._query.format(cluster=data_center.value, outage_tolerance=outage_tolerance)
        if self._config.use_cache:
            if query in self._query_cache:
                response = self._query_cache[query]
            else:
                response = self._prometheus.ranged_query(query)
                self._query_cache[query] = response
        else:
            response = self._prometheus.ranged_query(query)
        return response

    def error_message(self, container_name: str) -> str:
        return self._error_message.format(container_name=container_name)

    def flush_cache(self) -> None:
        self._query_cache = {}

    @staticmethod
    def _validate(config: PrometheusCheckConfig) -> None:
        if not config.query:
            raise PrometheusConfigError(f'"queries" cannot be empty: {config}')

    @property
    def config(self) -> Any:
        return self._config


class MonitoringCheck(IMonitoringCheck[ScraperDeployment]):
    def __init__(
        self, prometheus: PrometheusSource, config: PrometheusCheckConfig, all_deployments: Dict[str, ScraperDeployment]
    ) -> None:
        super().__init__(prometheus, config, all_deployments)
        self._outage_tolerance_map = get_outage_tolerance_map(self.deployments)
        self._outage_tolerance_groups = self._get_outage_tolerance_groups(self._outage_tolerance_map)

    @staticmethod
    def _get_outage_tolerance_groups(tolerance_map: Dict[str, int]) -> Dict[int, Set[str]]:
        groups = defaultdict(set)
        for container_name in tolerance_map:
            groups[tolerance_map[container_name]].add(container_name)
        return groups

    def query(self, data_center: DataCenter, condition_query_results: Dict[str, Set[str]]) -> Set[str]:
        if "{outage_tolerance}" in self._query:
            res = set()
            for outage_tolerance, expected_containers in self._outage_tolerance_groups.items():
                response = self._run_cached_query(data_center, outage_tolerance=outage_tolerance)
                print("QUERY ", self._query)
                print("OUTAGE_TOLERANCE ", outage_tolerance)
                print("RESPONSE: ", sorted([(container_name, value.values) for container_name, value in response.items()]))
                print()
                res.update(self._get_failed_container(expected_containers, response, condition_query_results))
            return res

        else:
            response = self._run_cached_query(data_center)
            print("QUERY ", self._query)
            print("RESPONSE: ", sorted([(container_name, value.values) for container_name, value in response.items()]))
            print()
            return self._get_failed_container(set(self.deployments), response, condition_query_results)


class DataLoaderCheck(IMonitoringCheck[DataLoaderDeployment]): ...


def streaming_fh(deployment: ScraperDeployment) -> bool:
    return deployment.scraper.connection_mode != ConnectionMode.STREAMING


def has_any_kafka_channel(deployment: ScraperDeployment) -> bool:
    args = deployment.entrypoint_args
    return bool(args.kafka_out or args.kafka_out_proto or args.kafka_out_quotes)


def has_kafka_channel(deployment: ScraperDeployment) -> bool:
    args = deployment.entrypoint_args
    return bool(args.kafka_out or args.kafka_out_proto)


def has_kafka_quotes_channel(deployment: ScraperDeployment) -> bool:
    return bool(deployment.entrypoint_args.kafka_out_quotes)


def has_postgres_channel(deployment: ScraperDeployment) -> bool:
    args = deployment.entrypoint_args
    return bool(args.postgres_out or args.postgres_exch_out)


def book_fh_with_kafka_channel(deployment: ScraperDeployment) -> bool:
    return deployment.data_type == DataType.BOOK and has_kafka_channel(deployment)


def book_fh_with_quotes_kafka_channel(deployment: ScraperDeployment) -> bool:
    return deployment.data_type == DataType.BOOK and has_kafka_quotes_channel(deployment)


def trade_data_loader(deployment: DataLoaderDeployment) -> bool:
    return deployment.data_type == DataType.TRADE


def non_metadata_fh(deployment: ScraperDeployment) -> bool:
    return deployment.data_type != DataType.METADATA


def non_metadata_fh_with_postgres_channel(deployment: ScraperDeployment) -> bool:
    return deployment.data_type != DataType.METADATA and has_postgres_channel(deployment)


def non_metadata_fh_with_any_channel(deployment: ScraperDeployment) -> bool:
    return deployment.data_type != DataType.METADATA and (has_postgres_channel(deployment) or has_any_kafka_channel(deployment))


def data_types(values: List[DataType]) -> Callable[[ScraperDeployment], bool]:
    def _equal(deployment: ScraperDeployment) -> bool:
        return deployment.data_type in values

    return _equal


def equal(value: float) -> Callable[[PrometheusResponse], bool]:
    def _equal(_input: PrometheusResponse) -> bool:
        return any(abs(input_value[0] - value) < 0.000001 for input_value in _input.values)

    return _equal


def less_than(value: float) -> Callable[[PrometheusResponse], bool]:
    def _less_than(_input: PrometheusResponse) -> bool:
        return any(input_value[0] < value for input_value in _input.values)

    return _less_than


def more_than(value: float) -> Callable[[PrometheusResponse], bool]:
    def _more_than(_input: PrometheusResponse) -> bool:
        return any(input_value[0] > value for input_value in _input.values)

    return _more_than


CONDITION_QUERIES = {
    "has_recognized_markets": """
        (
            sum by (app_kubernetes_io_instance)
            (
                max_over_time(recognized_market_count{{cluster="{cluster}"}}[1m])
            ) > 0
        )
    """
}

FHS_CHECKS: List[PrometheusCheckConfig] = [
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                max_over_time(scraped_market_count
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[1m])
            )
        )
        """,
        failed_if=equal(0),
        condition=non_metadata_fh,
        condition_queries=("has_recognized_markets",),
        error_message="No markets found for FH",
        error_type=NoMarketsScrapedDataError,
        priority=AlertPriority.NORMAL,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                max_over_time(scraper_items_received_total
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[{outage_tolerance}s])
            )
        )
        """,
        failed_if=equal(0),
        condition_queries=("has_recognized_markets",),
        error_message="No data collected",
        error_type=KafkaZeroDataError,
        priority=AlertPriority.HIGH,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(websocket_consumer_items_sent_total
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[{outage_tolerance}s])
            )
        )
        """,
        failed_if=equal(0),
        condition_queries=("has_recognized_markets",),
        error_message="No data in Kafka",
        error_type=KafkaZeroDataError,
        condition=has_any_kafka_channel,
        priority=AlertPriority.HIGH,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(database_consumer_items_stored_total
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[{outage_tolerance}s])
            )
        )
        """,
        failed_if=equal(0),
        condition_queries=("has_recognized_markets",),
        error_message="No data in Postgres",
        error_type=PostgresZeroDataError,
        condition=non_metadata_fh_with_postgres_channel,
        priority=AlertPriority.HIGH,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(scraper_items_received_total
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[{outage_tolerance}s])
            )
        )
        """,
        failed_if=equal(0),
        error_message="No metadata",
        error_type=NoMetadataError,
        condition=data_types([DataType.METADATA]),
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(process_cpu_seconds_total
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[1m]) / 60
            )
        )
        """,
        failed_if=more_than(0.95),
        error_message="CPU >95%",
        error_type=HighCpuUtilizationError,
        condition=data_types([DataType.BOOK, DataType.QUOTE]),
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                max_over_time(channel_fill{{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[1m]) / 60
            )
        )
        """,
        failed_if=more_than(0.95),
        condition=non_metadata_fh_with_any_channel,
        error_message="Channel filled >95%",
        error_type=HighChannelUtilizationError,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(log_output_bytes_total{{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[60m])
            )
        )""",
        failed_if=more_than(1024 * 1024),
        error_message="Logs > 1MB [60m]",
        error_type=HighLogsVolumeError,
    ),
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                increase(application_errors_total{{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*"}}[10m])
            )
        )""",
        failed_if=more_than(100),
        error_message="Errors > 100 [10m]",
        error_type=HighErrorsNumberError,
    ),
]
DATA_LOADERS_CHECKS: List[PrometheusCheckConfig] = [
    PrometheusCheckConfig(
        query="""
        (
            sum by (app_kubernetes_io_instance)
            (
                max_over_time(data_loader_kafka_lag
                    {{cluster="{cluster}", app_kubernetes_io_instance=~"feed-handler-.*loader.*"}}[1m])
            )
        )""",
        failed_if=more_than(1),
        error_message="Kafka Trades lag > 1s",
        error_type=HighKafkaTradesLagError,
        condition=trade_data_loader,
        use_cache=True,
    ),
]


class PrometheusChecker(Checker):
    def __init__(
        self,
        diagnostics: Diagnostics,
        data_center: DataCenter,
        instance: int,
        alert_manager: AlertManager,
        prometheus_source: PrometheusSource,
        deployments_source: DeploymentsSource,
    ) -> None:
        super().__init__(diagnostics, data_center, instance, alert_manager)
        self._prometheus = prometheus_source
        self._octopus = deployments_source
        self._fhs_checks = [
            MonitoringCheck(
                prometheus=self._prometheus, config=check_config, all_deployments=self._octopus.get_deployments(self._instance)
            )
            for check_config in FHS_CHECKS
        ]
        self._data_loaders_checks = [
            DataLoaderCheck(
                prometheus=self._prometheus,
                config=check_config,
                all_deployments=self._octopus.get_data_loader_deployments(self._instance),
            )
            for check_config in DATA_LOADERS_CHECKS
        ]

    def run(self) -> None:
        condition_query_results = {
            query_name: set(self._prometheus.query(query.format(cluster=self._data_center.value)))
            for query_name, query in CONDITION_QUERIES.items()
        }

        for check in self._fhs_checks + self._data_loaders_checks:
            check.flush_cache()

            failed_containers = check.query(self._data_center, condition_query_results)
            for container_name in failed_containers:
                alert = Alert(
                    data_type=self._octopus.try_get_data_type_for_container(container_name),
                    error_type=check.config.error_type,
                    error_level=MonitoringLevel.PROMETHEUS_METRICS,
                    message=check.error_message(container_name),
                    container_name=container_name,
                    priority=check.config.priority,
                )
                self.check_failed(alert)
