import os
import time
import urllib.parse
from dataclasses import dataclass
from typing import Dict, <PERSON><PERSON>, List

import requests
from requests.auth import HTT<PERSON>BasicAuth

PROMETHEUS_INSTANT_QUERY_ENDPOINT = "api/v1/query"
PROMETHEUS_RANGE_QUERY_ENDPOINT = "api/v1/query_range"


@dataclass
class PrometheusResponse:
    metric: Dict[str, str]
    values: List[Tuple[float, float]]  # (value, time)


class PrometheusSource:
    def __init__(self, base_url: str) -> None:
        self.http_session = requests.Session()
        self._base_url = base_url
        self._user = os.getenv("MIMIR_USERNAME_MGMT1")
        self._pass = os.getenv("MIMIR_PASSWORD_MGMT1")

    def query(self, query_str: str, timeout: int = 10) -> Dict[str, PrometheusResponse]:
        url = urllib.parse.urljoin(self._base_url, PROMETHEUS_INSTANT_QUERY_ENDPOINT)

        r = self.http_session.post(
            url, auth=HTTPBasicAuth(self._user, self._pass), data={"query": query_str, "timeout": timeout}
        )
        if r.status_code != 200:
            raise ValueError(f"Wrong response code: {r.text}")

        result = r.json()["data"]["result"]
        if not result:
            return {}

        values: Dict[str, PrometheusResponse] = {}
        for item in result:
            instance_name = item["metric"]["app_kubernetes_io_instance"]
            values[instance_name] = PrometheusResponse(metric=instance_name, values=[(item["value"][1], item["value"][0])])
        return values

    def ranged_query(self, query_str: str, timeout: int = 10) -> Dict[str, PrometheusResponse]:
        end = int(time.time())
        start = end - 60
        step = 60
        url = urllib.parse.urljoin(self._base_url, PROMETHEUS_RANGE_QUERY_ENDPOINT) + f"?start={start}&end={end}&step={step}"

        r = self.http_session.post(
            url, auth=HTTPBasicAuth(self._user, self._pass), data={"query": query_str, "timeout": timeout}
        )
        if r.status_code != 200:
            raise ValueError(f"Wrong response code: {r.text}")

        result = r.json()["data"]["result"]
        if not result:
            return {}

        values: Dict[str, PrometheusResponse] = {}
        for item in result:
            instance_name = item["metric"]["app_kubernetes_io_instance"]
            values[instance_name] = PrometheusResponse(
                metric=instance_name, values=[(float(value[1]), value[0]) for value in item["values"]]
            )
        return values
