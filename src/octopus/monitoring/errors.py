class MonitoringError(Exception):
    pass


class MissingContainer(MonitoringError):
    pass


class LeftoverContainer(MonitoringError):
    pass


class NoMarketsScrapedDataError(MonitoringError):
    pass


class KafkaZeroDataError(MonitoringError):
    pass


class PostgresZeroDataError(MonitoringError):
    pass


class NoMetadataError(MonitoringError):
    pass


class HighCpuUtilizationError(MonitoringError):
    pass


class HighChannelUtilizationError(MonitoringError):
    pass


class HighLogsVolumeError(MonitoringError):
    pass


class HighErrorsNumberError(MonitoringError):
    pass


class HighKafkaBooksLagError(MonitoringError):
    pass


class HighKafkaQuotesLagError(MonitoringError):
    pass


class HighKafkaTradesLagError(MonitoringError):
    pass


class PrometheusConfigError(Exception):
    pass
