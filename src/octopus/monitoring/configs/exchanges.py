from dataclasses import dataclass
from typing import Dict

from src.octopus.generators.deployment.scraper_types.general import ScraperDeployment
from src.octopus.inventory.types import CollectionMode, ConnectionMode, DataType, MarketType

MINUTE = 60
HOUR = 60 * MINUTE
DAY = 24 * HOUR
WEEK = 7 * DAY


@dataclass(frozen=True)
class ExchangeConfigTimeout:
    spot: int = MINUTE
    futures: int = MINUTE
    option: int = MINUTE


@dataclass(frozen=True)
class ExchangeDataTypeConfig:
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=MINUTE, futures=MINUTE, option=MINUTE)

    def dict(self, market_type: MarketType) -> Dict[str, str]:
        if market_type == MarketType.SPOT:
            return {"outage_tolerance": str(self.outage_tolerance.spot)}
        elif market_type == MarketType.FUTURES:
            return {"outage_tolerance": str(self.outage_tolerance.futures)}
        else:
            return {"outage_tolerance": str(self.outage_tolerance.option)}


@dataclass(frozen=True)
class ExchangeBookHourlyConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=65 * MINUTE, futures=65 * MINUTE, option=65 * MINUTE)


@dataclass(frozen=True)
class ExchangeBookRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=3 * MINUTE, futures=3 * MINUTE, option=3 * MINUTE)


@dataclass(frozen=True)
class ExchangeFundingRateHistoryConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=12 * HOUR, futures=12 * HOUR, option=12 * HOUR)


@dataclass(frozen=True)
class ExchangeFundingRateRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=12 * HOUR, futures=12 * HOUR, option=12 * HOUR)


@dataclass(frozen=True)
class ExchangeLiquidationHistoryConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=DAY, futures=DAY, option=DAY)


@dataclass(frozen=True)
class ExchangeLiquidationRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=12 * HOUR, futures=12 * HOUR, option=12 * HOUR)


@dataclass(frozen=True)
class ExchangeMetadataRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=30 * MINUTE, futures=30 * MINUTE, option=30 * MINUTE)


@dataclass(frozen=True)
class ExchangeOpenInterestHistoryConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=DAY, futures=DAY, option=DAY)


@dataclass(frozen=True)
class ExchangeOpenInterestRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=10 * MINUTE, futures=10 * MINUTE, option=10 * MINUTE)


@dataclass(frozen=True)
class ExchangeQuotesRealtimeConfig(ExchangeDataTypeConfig):
    outage_tolerance: ExchangeConfigTimeout = ExchangeConfigTimeout(spot=3 * MINUTE, futures=3 * MINUTE, option=3 * MINUTE)


@dataclass(frozen=True)
class ExchangeTickerRealtimeConfig(ExchangeDataTypeConfig):
    pass


@dataclass(frozen=True)
class ExchangeTradeHistoryConfig(ExchangeDataTypeConfig):
    pass


@dataclass(frozen=True)
class ExchangeTradeRealtimeConfig(ExchangeDataTypeConfig):
    pass


@dataclass(frozen=True)
class ExchangeConfig:
    book_hourly: ExchangeDataTypeConfig = ExchangeBookHourlyConfig()
    book_realtime: ExchangeDataTypeConfig = ExchangeBookRealtimeConfig()
    funding_rate_history: ExchangeDataTypeConfig = ExchangeFundingRateHistoryConfig()
    funding_rate_realtime: ExchangeDataTypeConfig = ExchangeFundingRateRealtimeConfig()
    liquidation_history: ExchangeDataTypeConfig = ExchangeLiquidationHistoryConfig()
    liquidation_realtime: ExchangeDataTypeConfig = ExchangeLiquidationRealtimeConfig()
    metadata_realtime: ExchangeDataTypeConfig = ExchangeMetadataRealtimeConfig()
    open_interest_history: ExchangeDataTypeConfig = ExchangeOpenInterestHistoryConfig()
    open_interest_realtime: ExchangeDataTypeConfig = ExchangeOpenInterestRealtimeConfig()
    quotes_realtime: ExchangeDataTypeConfig = ExchangeQuotesRealtimeConfig()
    ticker_futures_realtime: ExchangeDataTypeConfig = ExchangeTickerRealtimeConfig()
    ticker_option_realtime: ExchangeDataTypeConfig = ExchangeTickerRealtimeConfig()
    trade_history: ExchangeDataTypeConfig = ExchangeTradeHistoryConfig()
    trade_realtime: ExchangeDataTypeConfig = ExchangeTradeRealtimeConfig()

    def dict(self, deployment: ScraperDeployment) -> Dict[str, str]:
        data_type_config = self.get_data_type_config(deployment)
        return data_type_config.dict(deployment.scraper.market_type)

    def get_data_type_config(self, deployment: ScraperDeployment) -> ExchangeDataTypeConfig:
        data_type, connection_mode = deployment.data_type, deployment.scraper.connection_mode
        if data_type == DataType.BOOK:
            if deployment.collection_mode == CollectionMode.HOURLY:
                return self.book_hourly
            else:
                return self.book_realtime
        elif data_type == DataType.TRADE:
            return self.trade_history if connection_mode == ConnectionMode.HISTORY else self.trade_realtime
        elif data_type == DataType.OPEN_INTEREST:
            return self.open_interest_history if connection_mode == ConnectionMode.HISTORY else self.open_interest_realtime
        elif data_type == DataType.LIQUIDATION:
            return self.liquidation_history if connection_mode == ConnectionMode.HISTORY else self.liquidation_realtime
        elif data_type == DataType.FUNDING_RATE:
            return self.funding_rate_history if connection_mode == ConnectionMode.HISTORY else self.funding_rate_realtime
        elif data_type == DataType.QUOTE:
            return self.quotes_realtime
        elif data_type == DataType.METADATA:
            return self.metadata_realtime
        elif data_type == DataType.TICKER_F:
            return self.ticker_futures_realtime
        elif data_type == DataType.TICKER_O:
            return self.ticker_option_realtime
        else:
            raise ValueError(f"Unknown data type: {data_type}")

    def get_outage_tolerance(self, deployment: ScraperDeployment) -> float:
        market_type = deployment.scraper.market_type
        data_type_config = self.get_data_type_config(deployment)
        if market_type == MarketType.SPOT:
            return data_type_config.outage_tolerance.spot
        elif market_type == MarketType.FUTURES:
            return data_type_config.outage_tolerance.futures
        elif market_type == MarketType.OPTION:
            return data_type_config.outage_tolerance.option
        else:
            raise ValueError(f"Unknown market type: {market_type}")


EXCHANGES_CONFIGS = {
    "Bibox": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
    ),
    "Binance.US": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
    ),
    "Bitbank": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE)),
    ),
    "Bitfinex": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE, futures=10 * MINUTE)
        ),
    ),
    "bitFlyer": ExchangeConfig(
        book_realtime=ExchangeBookRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=7 * MINUTE, futures=7 * MINUTE)),
        quotes_realtime=ExchangeQuotesRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(spot=7 * MINUTE, futures=7 * MINUTE)
        ),
        trade_realtime=ExchangeTradeRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(spot=15 * MINUTE, futures=15 * MINUTE)
        ),
    ),
    "BitMEX": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=2 * MINUTE)),
    ),
    "Bitstamp": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=3 * MINUTE)),
    ),
    "Bybit": ExchangeConfig(
        book_realtime=ExchangeBookRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=HOUR, spot=5 * MINUTE)),
        quotes_realtime=ExchangeQuotesRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=HOUR, spot=5 * MINUTE)),
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE, futures=5 * MINUTE)),
    ),
    "CEX.IO": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=20 * MINUTE)),
    ),
    "CME": ExchangeConfig(
        book_realtime=ExchangeBookRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(futures=62 * MINUTE, option=62 * MINUTE)
        ),
        quotes_realtime=ExchangeQuotesRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(futures=62 * MINUTE, option=62 * MINUTE)
        ),
        trade_realtime=ExchangeTradeRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(futures=125 * MINUTE, option=13 * HOUR)
        ),
    ),
    "Coinbase": ExchangeConfig(),
    "CoinbaseDer": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=20 * MINUTE)),
        metadata_realtime=ExchangeMetadataRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=13 * HOUR)),
    ),
    "Crypto.com": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
        book_realtime=ExchangeBookRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=4 * MINUTE, futures=4 * MINUTE)),
    ),
    "Deribit": ExchangeConfig(
        book_realtime=ExchangeBookRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=150)),
        quotes_realtime=ExchangeQuotesRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=150)),
        trade_realtime=ExchangeTradeRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE, futures=10 * MINUTE)
        ),
        liquidation_realtime=ExchangeLiquidationRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=WEEK)),
    ),
    "Gate.io": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE, futures=5 * MINUTE)),
    ),
    "Gemini": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=3 * MINUTE)),
    ),
    "HitBTC": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE, futures=5 * MINUTE)),
    ),
    "Huobi": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE, futures=5 * MINUTE)),
    ),
    "itBit": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=30 * MINUTE)),
    ),
    "Kraken": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE, futures=3 * MINUTE)),
    ),
    "KuCoin": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
    ),
    "LBank": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=15 * MINUTE)),
    ),
    "LMAX": ExchangeConfig(
        book_realtime=ExchangeBookRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=61 * MINUTE)),
        quotes_realtime=ExchangeQuotesRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=61 * MINUTE)),
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE)),
    ),
    "MEXC": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE, futures=5 * MINUTE)),
    ),
    "OKEx": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(
            outage_tolerance=ExchangeConfigTimeout(spot=10 * MINUTE, futures=10 * MINUTE, option=135 * MINUTE)
        ),
        ticker_futures_realtime=ExchangeTickerRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(futures=3 * MINUTE)),
    ),
    "Poloniex": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=30 * MINUTE)),
    ),
    "Upbit": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
    ),
    "ZB.COM": ExchangeConfig(
        trade_realtime=ExchangeTradeRealtimeConfig(outage_tolerance=ExchangeConfigTimeout(spot=5 * MINUTE)),
    ),
}


def get_outage_tolerance_map(deployments: Dict[str, ScraperDeployment]) -> Dict[str, int]:
    res = {}
    for container_name, deployment in deployments.items():
        exchange_name = deployment.scraper.exchange_name.split()[0]
        exchange_config = EXCHANGES_CONFIGS.get(exchange_name, ExchangeConfig())
        res[container_name] = exchange_config.get_outage_tolerance(deployment)
    return res
