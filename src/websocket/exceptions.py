"""
Code is based on:

https://github.com/websocket-client/websocket-client/releases/tag/v0.57.0

websocket - WebSocket client library for Python

Copyright (C) 2010 <PERSON><PERSON><PERSON>(liris)

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Lesser General Public
    License as published by the Free Software Foundation; either
    version 2.1 of the License, or (at your option) any later version.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public
    License along with this library; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor,
    Boston, MA  02110-1335  USA

"""

from typing import Dict, Optional


class WebSocketInvalidHttpHeader(Exception):
    pass


class WebSocketRedirectLimitExceeded(Exception):
    pass


class WebSocketProtocolViolation(Exception):
    pass


class WebSocketInvalidPayload(Exception):
    pass


class WebSocketConnectionClosed(Exception):
    """If remote host closed the connection or some network error happened, this exception will be raised."""

    pass


class WebSocketConnectionTimeout(Exception):
    pass


class WebSocketConnectionError(Exception):
    """Raised when connection attempt fails."""

    pass


class WebSocketTimeout(Exception):
    """WebSocketTimeout will be raised at socket timeout during read/write data."""

    pass


class WebSocketPingPongTimeout(Exception):
    pass


class WebSocketPingNotSent(Exception):
    """Raised when connection is unable to send ping due to send queue overflow."""

    pass


class WebSocketPongNotSent(Exception):
    """Raised when connection is unable to send pong due to send queue overflow."""

    pass


class WebSocketProxyError(Exception):
    """WebSocketProxyException will be raised when proxy error occurred."""

    pass


class WebSocketBadStatus(Exception):
    """WebSocketBadStatusException will be raised when we get bad handshake status code."""

    def __init__(
        self, status_code: Optional[int], status_message: Optional[str], response_headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(f"{status_code} -- {status_message}")
        self.status_code = status_code
        self.response_headers = response_headers


class WebSocketAddressNotFound(Exception):
    """If the websocket address info cannot be found, this exception will be raised."""

    pass


class WebSocketCloseError(Exception):
    """Raised when any failure happens during WebSocket protocol's closing phase."""

    pass
