"""
Code is based on:

https://github.com/websocket-client/websocket-client/releases/tag/v0.57.0

websocket - WebSocket client library for Python

Copyright (C) 2010 <PERSON><PERSON><PERSON>(liris)

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Lesser General Public
    License as published by the Free Software Foundation; either
    version 2.1 of the License, or (at your option) any later version.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public
    License along with this library; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor,
    Boston, MA  02110-1335  USA

"""

import errno
import select
import socket
from ssl import SSLError, SSLWantReadError, SSLWantWriteError
from typing import Optional


from src.websocket.exceptions import (
    WebSocketConnectionClosed,
    WebSocketTimeout,
)

""" Send / receive """


def websocket_receive(sock: socket.socket, bufsize: int) -> bytes:
    def _recv() -> bytes:
        try:
            return sock.recv(bufsize)
        except SSLWantReadError:
            pass
        except socket.error as exc:
            if (error_code := _extract_error_code(exc)) is None:
                raise
            if error_code not in (errno.EAGAIN, errno.EWOULDBLOCK):
                raise

        r, _, _ = select.select((sock,), (), (), sock.gettimeout())
        if r:
            return sock.recv(bufsize)
        else:
            return bytes()

    try:
        if sock.gettimeout() == 0:
            bytes_ = sock.recv(bufsize)
        else:
            bytes_ = _recv()

    except socket.timeout as e:
        message = _extract_err_message(e)
        raise WebSocketTimeout(message)

    except SSLError as e:
        message = _extract_err_message(e)
        if isinstance(message, str) and "timed out" in message:
            raise WebSocketTimeout(message)
        else:
            raise

    except ConnectionResetError:
        raise WebSocketConnectionClosed("connection was reset by peer")

    if len(bytes_) > 0:
        return bytes_
    else:
        raise WebSocketConnectionClosed("connection is already closed")


def websocket_send(sock: socket.socket, data: bytes) -> int:
    def _send() -> int:
        try:
            return sock.send(data)
        except SSLWantWriteError:
            pass
        except socket.error as exc:
            if (error_code := _extract_error_code(exc)) is None:
                raise
            if error_code not in (errno.EAGAIN, errno.EWOULDBLOCK):
                raise

        _, w, _ = select.select((), (sock,), (), sock.gettimeout())
        if w:
            return sock.send(data)
        else:
            return 0

    try:
        if sock.gettimeout() == 0:
            return sock.send(data)
        else:
            return _send()

    except socket.timeout as e:
        message = _extract_err_message(e)
        raise WebSocketTimeout(message)

    except SSLError as e:
        message = _extract_err_message(e)
        if isinstance(message, str) and "timed out" in message:
            raise WebSocketTimeout(message)
        else:
            raise

    except ConnectionResetError:
        raise WebSocketConnectionClosed("connection was reset by peer")


def websocket_send_all(sock: socket.socket, data: bytes) -> None:
    while data:
        sent_len = websocket_send(sock, data)
        data = data[sent_len:]


def _websocket_receive_line(sock: socket.socket) -> bytes:
    line = []

    while True:
        c = websocket_receive(sock, 1)
        line.append(c)
        if c == b"\n":
            break

    return b"".join(line)


def _extract_err_message(exception: Exception) -> Optional[str]:
    if exception.args:
        return str(exception.args[0])
    else:
        return None


def _extract_error_code(exception: Exception) -> Optional[int]:
    if exception.args and len(exception.args) > 1:
        return exception.args[0] if isinstance(exception.args[0], int) else None
    else:
        return None
