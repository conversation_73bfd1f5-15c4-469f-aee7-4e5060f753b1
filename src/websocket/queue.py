from collections import deque
from typing import Deque, Optional, TypeVar, Generic

from src.utils.diagnostics import Diagnostics

T = TypeVar("T")


class Queue(Generic[T]):
    def __init__(
        self,
        capacity: int,
        diagnostics: Diagnostics,
    ) -> None:
        self._capacity = capacity
        self._deque: Deque[T] = deque()
        self._diagnostics = diagnostics
        self._closed = False

    def push_back(self, message: T) -> bool:
        if not message:
            return True

        if len(self._deque) < self._capacity:
            self._deque.append(message)

            return True
        else:
            return False

    def push_front(self, message: T) -> bool:
        if not message:
            return True

        if len(self._deque) < self._capacity:
            self._deque.appendleft(message)

            return True
        else:
            return False

    def pop_front(self) -> Optional[T]:
        if len(self._deque) == 0:
            return None

        try:
            return self._deque.popleft()
        except IndexError:
            return None

    def close(self):
        self._diagnostics.info("Websocket queue closing")
        self._closed = True
        self._deque.clear()

    def empty(self) -> bool:
        return len(self._deque) == 0
