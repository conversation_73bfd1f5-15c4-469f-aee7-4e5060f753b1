import os
import selectors
import sys
import time
from ssl import SSLEOFError

import websocket
from websocket import WebSocket, ABNF

from websocket._exceptions import WebSocketConnectionClosedException, WebSocketTimeoutException
from threading import Thread, Event
from typing import Iterator, Optional, Callable

from src.utils.diagnostics import Diagnostics
from src.utils.http import ProxyParams
from src.utils.socket import DEFAULT_SOCKET_OPTION
from src.utils.stream import ConnectionEvent, IStreamingConnection, MessageReceived, ClosedByPeer
from src.websocket.data import (
    GetMaskKey,
    WebSocketMessage,
    WebSocketParams,
)
from src.websocket.exceptions import (
    WebSocketCloseError,
)
from src.websocket.queue import Queue


class WebSocketConnection(IStreamingConnection[WebSocketMessage]):
    SEND_QUEUE_CAPACITY: int = 1024 * 1024

    def __init__(
        self,
        url: str,
        params: WebSocketParams = WebSocketParams(),
        proxy: Optional[ProxyParams] = None,
        diagnostics: Diagnostics = Diagnostics(),
        timeout: float = 5.0,
        on_close: Callable[[Optional[ProxyParams]], None] = None,
    ):
        self._url = url
        self._ws_params = params
        self._proxy = proxy
        self._diagnostics = diagnostics
        self._timeout = timeout
        self._on_close_callback: Optional[Callable[[Optional[ProxyParams]], None]] = on_close

        self.ping_timeout = self._ws_params.ping.timeout if self._ws_params.ping else None
        self.ping_interval = self._ws_params.ping.ping_interval or self._ws_params.ping.idle_time if self._ws_params.ping else 0
        self.ping_payload = self._ws_params.ping.ping_message if self._ws_params.ping else None

        _tags = {"name": f"{self._url}-{id(self)}"}
        self._send_queue = Queue(
            capacity=self.SEND_QUEUE_CAPACITY,
            diagnostics=diagnostics,
        )
        self._items_received = self._diagnostics.counter("websocket_connection_items_received", ("name",)).tags(_tags)
        self.keep_running = True
        self._ws: WebSocket = self._ws_connect()
        self._ws_read_selector = selectors.DefaultSelector()
        self._ws_read_selector.register(self._ws.sock, selectors.EVENT_READ)
        self._ws_write_selector = selectors.DefaultSelector()
        self._ws_write_selector.register(self._ws.sock, selectors.EVENT_WRITE)

    def _ws_connect(
        self,
        get_mask_key: GetMaskKey = os.urandom,
    ) -> websocket.WebSocket:
        proxy_str = f" via proxy {self._proxy.host}:{self._proxy.port}" if self._proxy is not None else ""
        self._diagnostics.info(f"initiating WS connection with {self._url}{proxy_str}")

        ws = WebSocket(
            get_mask_key,
            sockopt=DEFAULT_SOCKET_OPTION,
            sslopt=self._ws_params.ssl.to_ssl_opt(),
            fire_cont_frame=False,
            skip_utf8_validation=True,
            enable_multithread=True,
        )

        ws.settimeout(timeout=self._timeout)
        ws.connect(
            self._url,
            header=self._ws_params.handshake.headers,
            cookie=self._ws_params.handshake.cookie,
            http_proxy_host=self._proxy.host if self._proxy else None,
            http_proxy_port=self._proxy.port if self._proxy else None,
            http_no_proxy=[],
            http_proxy_auth=(self._proxy.auth.user, self._proxy.auth.password) if self._proxy and self._proxy.auth else None,
            http_proxy_timeout=self._timeout,
            subprotocols=self._ws_params.handshake.subprotocols,
            host=self._ws_params.handshake.host,
            origin=self._ws_params.handshake.origin,
            suppress_origin=self._ws_params.handshake.suppress_origin,
            proxy_type="http" if self._proxy else None,  # only HTTP proxies are supported
            socket=None,  # prepared socket
        )
        if not ws.connected:
            raise ConnectionError(f"Failed to connect to WS on {self._url} via {self._proxy}")

        self._diagnostics.debug("Websocket connected")

        if self._ws_params.ping:
            self._start_ping_thread()

        return ws

    def _start_ping_thread(self) -> None:
        self.last_ping_tm = self.last_pong_tm = float(0)
        self.stop_ping = Event()
        self.ping_thread = Thread(target=self._send_ping, daemon=True, name="WebSocketConnectionPing")
        self.ping_thread.start()

    def _stop_ping_thread(self) -> None:
        if self.stop_ping:
            self.stop_ping.set()
        if self.ping_thread and self.ping_thread.is_alive():
            self.ping_thread.join(3)
        self.last_ping_tm = self.last_pong_tm = float(0)

    def _send_ping(self) -> None:
        if self.stop_ping.wait(self.ping_interval) or self.keep_running is False:
            return

        while not self.stop_ping.wait(self.ping_interval) and self.keep_running is True:
            if self._ws:
                self.last_ping_tm = time.time()
                try:
                    self._diagnostics.debug("Sending ping")
                    self._ws.ping(self.ping_payload)
                except Exception as e:
                    self._diagnostics.debug(f"Failed to send ping: {e}")

    def communicate(self, timeout: float) -> Iterator[ConnectionEvent[WebSocketMessage]]:
        deadline = time.time() + timeout

        if not self._send_queue.empty():
            self._write(deadline - 0.9 * timeout)

        yield from self._events(deadline)

    def close(self, gracefully: bool = True) -> None:
        self._diagnostics.debug("Websocket closing")
        close_timeout = 10 if gracefully else 1
        self.keep_running = False
        try:
            self._stop_ping_thread()
            if self._ws:
                self._ws.close(timeout=close_timeout)  # possibly add "status" and "reason" in the future
                self._ws = None
        except Exception as e:
            self._diagnostics.error(WebSocketCloseError(e).with_traceback(sys.exc_info()[2]))
        finally:
            self._ws_write_selector.close()
            self._ws_read_selector.close()
            if self._on_close_callback:
                self._on_close_callback(self._proxy)
            self._send_queue.close()

    def send(self, message: WebSocketMessage) -> bool:
        return self._send_queue.push_back(message)

    def _write(self, deadline: float) -> None:
        if self._ws is None or not self._ws.connected:
            # wait for the socket to be established
            return

        while self.keep_running and (data := self._send_queue.pop_front()) is not None:
            try:
                if self._ws_write_selector.select(deadline - time.time()):
                    if isinstance(data, bytes):
                        self._ws.send_bytes(data)
                    elif isinstance(data, str):
                        self._ws.send_text(data)
            except WebSocketConnectionClosedException:
                self._send_queue.push_front(data)
                break

            if time.time() >= deadline:
                break

    def _events(self, deadline: float) -> Iterator[ConnectionEvent[WebSocketMessage]]:
        try:
            while self.keep_running:
                if self._ws_read_selector.select(deadline - time.time()):
                    try:
                        op_code, frame = self._ws.recv_data_frame(True)
                    except (KeyboardInterrupt, SSLEOFError, WebSocketConnectionClosedException) as e:
                        raise e

                    if op_code == ABNF.OPCODE_CLOSE:
                        close_status_code, close_reason = self._get_close_args(frame if frame else None)
                        yield ClosedByPeer(f"{close_status_code} {close_reason}")

                    elif op_code == ABNF.OPCODE_PING:
                        pass

                    elif op_code == ABNF.OPCODE_PONG:
                        self.last_pong_tm = time.time()

                    else:
                        data = frame.data
                        if op_code == ABNF.OPCODE_TEXT:
                            data = data.decode("utf-8")
                        yield MessageReceived(data)
                        self._items_received.inc(1)

                if time.time() >= deadline:
                    break
        except (
            WebSocketConnectionClosedException,
            ConnectionRefusedError,
            KeyboardInterrupt,
            SystemExit,
            Exception,
        ) as e:
            self._diagnostics.error(e)
            yield ClosedByPeer(str(e))

    def check(self) -> bool:
        if self.ping_timeout:
            has_timeout_expired = time.time() - self.last_ping_tm > self.ping_timeout
            has_pong_not_arrived_after_last_ping = self.last_pong_tm - self.last_ping_tm < 0
            has_pong_arrived_too_late = self.last_pong_tm - self.last_ping_tm > self.ping_timeout

            if self.last_ping_tm and has_timeout_expired and (has_pong_not_arrived_after_last_ping or has_pong_arrived_too_late):
                raise WebSocketTimeoutException("ping/pong timed out")
        return True

    @staticmethod
    def _get_close_args(close_frame: ABNF) -> list:
        """
        COPIED from websocket/_app.py

        _get_close_args extracts the close code and reason from the close body
        if it exists (RFC6455 says WebSocket Connection Close Code is optional)
        """
        # Need to catch the case where close_frame is None
        # Otherwise the following if statement causes an error
        if not close_frame:
            return [None, None]

        # Extract close frame status code
        if close_frame.data and len(close_frame.data) >= 2:
            close_status_code = 256 * int(close_frame.data[0]) + int(close_frame.data[1])
            reason = close_frame.data[2:]
            if isinstance(reason, bytes):
                reason = reason.decode("utf-8")
            return [close_status_code, reason]
        else:
            # Most likely reached this because len(close_frame_data.data) < 2
            return [None, None]
