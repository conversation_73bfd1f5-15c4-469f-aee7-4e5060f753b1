import time
from typing import List, Tu<PERSON>, Dict

from confluent_kafka.admin import AdminClient, ConfigResource, ResourceType
from confluent_kafka.cimpl import NewTopic, KafkaException

from src.utils.diagnostics import Diagnostics


def kafka_servers_arguments(value: str) -> Tuple[str, str]:
    pieces = value.split("+")
    if len(pieces) == 1:
        return pieces[0], pieces[0]
    elif len(pieces) == 2:
        return pieces[0], pieces[1]
    else:
        raise Exception("kafka-sources argument should contain only two addresses: server1+server2")


def kafka_topics_arguments(value: str) -> List[str]:
    return value.split(",")


def set_up_kafka_topic(settings: List[Dict[str, str]], kafka_servers: List[str], diagnostics: Diagnostics) -> None:
    for setting in settings:
        config = {"compression.type": "lz4"}
        # In case of -1 we will use global retention setting via KAFKA_LOG_RETENTION_HOURS=48 env variable on Kafka server
        if setting["retention"] != "-1":
            config["retention.bytes"] = setting["retention"]

        _create_or_update_kafka_topic(kafka_servers, [setting["topic"]], config, diagnostics)


def _create_or_update_kafka_topic(
    kafka_servers: List[str], kafka_topic_names: List[str], config: Dict[str, str], diagnostics: Diagnostics
) -> None:
    for kafka_server in kafka_servers:
        diagnostics.info(f"Set up kafka topic for {kafka_server}: [{','.join(kafka_topic_names)}]")

        admin = AdminClient({
            "bootstrap.servers": kafka_server,
            "broker.version.fallback": "2.7.0",  # Kafka broker version on Hetzner
        })
        attempt = 0
        while attempt < 3:  # Retry up to 3 times
            try:
                metadata = admin.list_topics(timeout=60)
                break
            except KafkaException as e:
                attempt += 1
                if attempt < 3:
                    diagnostics.error(e, f"Failed to connect to Kafka server: {kafka_server}. Retrying...")
                    time.sleep(5)  # Wait before retrying to connect
        else:
            diagnostics.warning(f"Failed to connect to Kafka server: {kafka_server} after 3 attempts.")
            return

        missing_topics = [topic_name for topic_name in kafka_topic_names if topic_name not in metadata.topics]
        if missing_topics:
            _create_topics(admin, missing_topics, config, diagnostics)

        existing_topics = list(set(kafka_topic_names) - set(missing_topics))
        if existing_topics:
            _update_topics(admin, existing_topics, config, diagnostics)


def _create_topics(admin: AdminClient, topics: List[str], config: Dict[str, str], diagnostics: Diagnostics) -> None:
    fs = admin.create_topics([
        NewTopic(topic_name, num_partitions=1, replication_factor=1, config=config) for topic_name in topics
    ])

    for topic_name, future in fs.items():
        try:
            future.result()  # wait for topic creation
            diagnostics.info(f"Topic {topic_name} created successfully.")
        except Exception as e:
            diagnostics.error(e, f"Failed to create Kafka topic: {topic_name}")


def _update_topics(admin: AdminClient, topics: List[str], config: Dict[str, str], diagnostics: Diagnostics) -> None:
    config_resources = [ConfigResource(restype=ResourceType.TOPIC, name=topic_name, set_config=config) for topic_name in topics]
    futures = admin.alter_configs(config_resources)

    for resource, future in futures.items():
        try:
            future.result()  # blocks until response / raises exception on error
            diagnostics.info(f"Updated config for {resource.name} Kafka topic successfully.")
        except Exception as e:
            diagnostics.error(e, f"Failed to update config for {resource.name} Kafka topic.")
