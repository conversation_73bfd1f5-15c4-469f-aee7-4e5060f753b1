import json
from typing import Iterator

from google.api_core import retry
from google.auth import jwt  # type: ignore
from google.cloud import pubsub_v1  # type: ignore

from src.utils.diagnostics import Diagnostics
from src.utils.stream import ConnectionEvent, IStreamingConnection, MessageReceived, StreamingClient
from src.utils.types import JsonValue


class GooglePubSubConnection(IStreamingConnection[JsonValue]):
    def __init__(self, subscriber: pubsub_v1.SubscriberClient, subscription_name: str) -> None:
        self._subscriber = subscriber
        self._subscription_name = subscription_name

    def close(self, gracefully: bool = True) -> None:
        self._subscriber.close()

    def send(self, message: JsonValue) -> bool:
        raise NotImplementedError

    def communicate(
        self,
        timeout: float,
    ) -> Iterator[ConnectionEvent[JsonValue]]:
        response = self._subscriber.pull(
            request={"subscription": self._subscription_name, "max_messages": 1000},
            retry=retry.Retry(deadline=300),
        )

        ack_ids = []
        for received_message in response.received_messages:
            yield MessageReceived(received_message.message)
            ack_ids.append(received_message.ack_id)
        if ack_ids:
            self._subscriber.acknowledge(request={"subscription": self._subscription_name, "ack_ids": ack_ids})


class GooglePubSubError(Exception): ...


def google_pub_sub_client(client_json: str, topic_name: str, topic_suffix: str, hostname: str) -> StreamingClient[JsonValue]:
    def connect(timeout: float, diagnostics: Diagnostics) -> IStreamingConnection[JsonValue]:
        if not hostname:
            raise GooglePubSubError("Hostname cannot be empty")
        audience = "https://pubsub.googleapis.com/google.pubsub.v1.Subscriber"
        credentials = jwt.Credentials.from_service_account_info(json.loads(client_json), audience=audience)
        subscriber = pubsub_v1.SubscriberClient(credentials=credentials)
        subscription_name = f"projects/coinmetrics/subscriptions/cme-bitcoin-{topic_suffix}-{hostname}"
        for subscription in subscriber.list_subscriptions(request={"project": "projects/coinmetrics"}):
            if subscription.name == subscription_name:
                subscriber.get_subscription(
                    subscription=subscription_name,
                )
                break
        else:
            subscriber.create_subscription(name=subscription_name, topic=topic_name)
        diagnostics.info(f"subscribed to google pub/sub topic: {topic_name}, with subscription name: {subscription_name}")

        return GooglePubSubConnection(subscriber, subscription_name)

    return connect
