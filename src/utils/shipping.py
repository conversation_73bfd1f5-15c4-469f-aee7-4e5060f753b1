from typing import Generic, List, TypeVar, Union

from src.utils.diagnostics import Diagnostics

I_T = TypeVar("I_T")  # type of stored data
S_T = TypeVar("S_T")  # type of storage


class IShippingOperations(Generic[I_T, S_T]):
    """All methods should throw an exception if an operation fails."""

    def get_from_source(self, src: S_T, count: int) -> List[I_T]:
        raise NotImplementedError

    def put_to_destination(self, dst: S_T, objects: List[I_T]) -> None:
        raise NotImplementedError

    def delete_from_source(self, src: S_T, objects: List[I_T]) -> None:
        raise NotImplementedError


def ship(
    src: S_T, dst: S_T, operations: IShippingOperations[I_T, S_T], batch_size: int, diagnostics: Diagnostics = Diagnostics()
) -> Union[int, Exception]:
    try:
        objects = operations.get_from_source(src, batch_size)
    except Exception as e:
        diagnostics.error(e, f"failed to get objects from {src}")
        return e
    else:
        diagnostics.debug(f"fetched {len(objects)} objects from {src}")

    if len(objects) > 0:
        try:
            operations.put_to_destination(dst, objects)
        except Exception as e:
            diagnostics.error(e, f"failed to ship {len(objects)} objects from {src} to {dst}")
            return e
        else:
            diagnostics.info(f"shipped {len(objects)} objects from {src} to {dst}")

        try:
            operations.delete_from_source(src, objects)
        except Exception as e:
            diagnostics.error(e, f"failed to delete {len(objects)} shipped objects from {src}")
            return e
        else:
            diagnostics.debug(f"deleted {len(objects)} objects from {src}")

    return len(objects)
