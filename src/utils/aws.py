import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import boto3

from src.utils.diagnostics import Diagnostics
from src.utils.types import JsonValue

#
# This class caches the AWS list request responses to reduce our AWS spend. It relies on a bucket modification file
# to indicate when the response cache should be invalidated. It also caches the check of the bucket modification time
# (based on the cache_time param passed in during initialization) to further reduce the number of list requests.
# There is also a semaphore limit on the number of parallel get_object calls to avoid exhausting the connection pool.
# Finally, it single-threads the list request to AWS to reduce the throttling/error counts, as well as eliminate any
# race conditions when requesting the same prefix.
#

AWS_BUCKET_UPDATED_KEY = "bucket_updated"


class AwsS3Client:
    def __init__(self, bucket: str, client_id: str, secret_id: str, cache_time: timedelta, diagnostics: Diagnostics):
        self._s3client = boto3.client("s3", aws_access_key_id=client_id, aws_secret_access_key=secret_id)
        self._bucket = bucket
        self._diagnostics = diagnostics
        self._get_semaphore = threading.BoundedSemaphore(5)
        # Caching
        self._cache_lock = threading.Lock()
        self._cache_time = cache_time
        self._cache_expires_at = datetime.utcnow()
        self._list_cache: Dict[str, List[JsonValue]] = {}
        # Monitoring
        self._list_counter = diagnostics.counter("aws_s3_list_count")
        self._list_cache_counter = diagnostics.counter("aws_s3_list_cache_count")
        self._get_counter = diagnostics.counter("aws_s3_get_count")
        self._get_bytes_total = diagnostics.counter("aws_s3_get_bytes_total")

    def get(self, key: str) -> bytes:
        # Limit the number of parallel get_object calls to avoid exhausting the connection pool
        with self._get_semaphore:
            response = self._s3client.get_object(Bucket=self._bucket, Key=key)
            results: bytes = response.get("Body").read()
            self._get_counter.inc(1)
            self._get_bytes_total.inc(len(results))
            self._diagnostics.debug(f"loaded from s3: {key}: {len(results)} bytes")
            return results

    def list(self, prefix: str) -> List[JsonValue]:
        if prefix != AWS_BUCKET_UPDATED_KEY:
            self._check_for_updates()
        # Single-thread the list requests to avoid throttling/errors from AWS and race conditions on the same prefix
        with self._cache_lock:
            if (cached_list := self._list_cache.get(prefix)) is not None:
                return cached_list
            full_list: List[JsonValue] = []
            response = self._s3client.list_objects_v2(Bucket=self._bucket, Prefix=prefix)
            self._list_counter.inc(1)
            if response["KeyCount"] > 0:
                full_list = response["Contents"]
                while response["IsTruncated"]:
                    next_token = response["NextContinuationToken"]
                    response = self._s3client.list_objects_v2(Bucket=self._bucket, ContinuationToken=next_token)
                    self._list_counter.inc(1)
                    full_list.extend(response["Contents"])
            self._diagnostics.debug(f"list: {prefix}: {len(full_list)} keys ")
            self._list_cache[prefix] = full_list
            # Finally if the prefix matches our bucket_modification indicator, update the last checked time
            if prefix == AWS_BUCKET_UPDATED_KEY:
                self._cache_expires_at = datetime.utcnow() + self._cache_time
                self._diagnostics.debug(f"cache expires at: {self._cache_expires_at}")

            return full_list

    # If the bucket has been modified, drain the cache (keep only the bucket modification key)
    def _check_for_updates(self) -> None:
        saved_response = self._list_cache.get(AWS_BUCKET_UPDATED_KEY, [])
        if datetime.utcnow() >= self._cache_expires_at and AWS_BUCKET_UPDATED_KEY in self._list_cache:
            self._diagnostics.debug(f"removing key from cache: {AWS_BUCKET_UPDATED_KEY}")
            # It's still possible to get a race condition here, so don't fail if the key has already been removed
            self._list_cache.pop(AWS_BUCKET_UPDATED_KEY, None)
        # Fetch a new response and compare last modification times
        response = self.list(AWS_BUCKET_UPDATED_KEY)
        if len(response) == 1 and len(saved_response) == 1:
            new_modification = response[0]["LastModified"]
            if new_modification != saved_response[0]["LastModified"]:
                # Cache is no longer valid -- drain and report the new modification timestamp
                self._list_cache = {AWS_BUCKET_UPDATED_KEY: response}
                self._diagnostics.info(f"AwsS3Client: {AWS_BUCKET_UPDATED_KEY} updated as of {new_modification}")

    # Convenience methods

    def list_keys(self, prefix: str) -> List[str]:
        return [item["Key"] for item in self.list(prefix)]

    def get_last_modified(self, key: str) -> Optional[JsonValue]:
        response = self.list(key)
        if len(response) == 1:
            return response[0]["LastModified"]
        return None
