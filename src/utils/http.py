import gzip
import time
import urllib.parse
from dataclasses import dataclass, field
from datetime import datetime, timedelta, UTC
from decimal import Decimal
from enum import Enum
from http import HTTPStatus
from json import loads
from types import TracebackType
from typing import Any, Callable, Dict, List, Optional, Tuple, Type

import requests
from requests import HTTPError, Response

from src.utils.diagnostics import Diagnostics, short_exception_name
from src.utils.execution import execute_with_retries, limited_retry
from src.utils.rate_limit import RateLimitedResourcePool, ResourceHandle, T
from src.utils.types import JsonValue

_DEFAULT_TIMEOUT = 5.0

_PROXY_CHECK_URLS = ["https://google.com", "https://bing.com", "https://duckduckgo.com"]
_EXTERNAL_PROXY_CHECK_URLS = [
    "https://api.coinmetrics2.io/feed-handlers-proxy-check",
    "https://community-api.coinmetrics.io/feed-handlers-proxy-check",
    "https://api.coinmetrics2.io/v4/status",
    "https://community-api.coinmetrics.io/v4/status",
]


@dataclass(frozen=True)
class ProxyAuth:
    user: str
    password: str


@dataclass(frozen=True)
class ProxyParams:
    protocol: str
    host: str
    port: int
    auth: Optional[ProxyAuth] = None

    @staticmethod
    def from_string(proxy_string: str) -> "ProxyParams":
        pieces = proxy_string.split(":")

        if pieces[0] not in {"http", "socks4", "socks5", "socks5h"}:
            raise ValueError(f"invalid proxy protocol {pieces[0]}, only http, socks4, socks5 and socks5h supported")

        if len(pieces) == 3:
            return ProxyParams(protocol=pieces[0], host=pieces[1], port=int(pieces[2]))

        elif len(pieces) == 5:
            return ProxyParams(
                protocol=pieces[0], host=pieces[3], port=int(pieces[4]), auth=ProxyAuth(user=pieces[1], password=pieces[2])
            )

        else:
            raise ValueError(f"Expected protocol[:user:password]:host:port string, got {proxy_string}")

    def __repr__(self) -> str:
        if not self.auth:
            return f"{self.protocol}:{self.host}:{self.port}"
        return f"{self.protocol}:{self.auth.user}:{self.auth.password}:{self.host}:{self.port}"

    def to_dict(self) -> Dict[str, str]:
        url = self.get_url()
        return {"http": url, "https": url}

    def get_url(self) -> str:
        if not self.auth:
            return f"{self.protocol}://{self.host}:{self.port}"

        username = urllib.parse.quote(self.auth.user)
        password = urllib.parse.quote(self.auth.password)
        return f"{self.protocol}://{username}:{password}@{self.host}:{self.port}"


def is_internal_proxy(host: str) -> bool:
    return "forward-proxy" in host


def check_proxy_health(proxy_params: ProxyParams, diagnostics: Diagnostics, timeout: int = 10) -> bool:
    def attempt(target: str) -> Response:
        return requests.head(target, timeout=timeout, proxies=proxy_params.to_dict())

    check_urls = _PROXY_CHECK_URLS if is_internal_proxy(proxy_params.host) else _EXTERNAL_PROXY_CHECK_URLS

    # Track warnings and issue a single message for any given failed proxy
    warning_message = ""
    for url in check_urls:
        try:
            r = execute_with_retries(lambda: attempt(url), limited_retry(0))
            if r.status_code in [200, 204, 301]:
                return True
            warning_message = f"{proxy_params}: {url}: Got wrong status: {r.status_code}"
        except Exception as e:
            warning_message = f"{proxy_params}: {url}: Exception: {e}"
            if "429 Too Many Requests" in warning_message:
                diagnostics.info(f"{proxy_params}: Rate Limit Exceeded")
                time.sleep(60)

    diagnostics.warning(warning_message)

    return False


@dataclass(frozen=True)
class HttpResponse:
    status_code: HTTPStatus
    body: str = ""
    raw_body: bytes = b""
    cookies: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)


def get_json(response: HttpResponse) -> JsonValue:
    return loads(response.body, parse_float=Decimal)


class HttpRequestType(Enum):
    GET = "GET"
    POST = "POST"


class ResourceNotAcquired(Exception):
    pass


class RateLimitViolation(Exception):
    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """Initialize RateLimitViolation with `response` object."""
        self.response = kwargs.pop("response", None)
        super().__init__(*args, **kwargs)


@dataclass(frozen=True)
class HttpRequest:
    url: str
    request_type: HttpRequestType = HttpRequestType.GET
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict[str, str]] = None


def _make_http_request(
    request: HttpRequest, timeout: float, proxies: Optional[Dict[str, str]], session: Optional[requests.Session], raw=False
) -> HttpResponse:
    if session is None:
        result = requests.request(
            request.request_type.value.lower(),
            request.url,
            timeout=timeout,
            proxies=proxies,
            data=request.data,
            headers=request.headers,
        )
    else:
        result = session.request(
            request.request_type.value.lower(),
            request.url,
            timeout=timeout,
            proxies=proxies,
            data=request.data,
            headers=request.headers,
        )

    if result.status_code == HTTPStatus.TOO_MANY_REQUESTS:
        raise RateLimitViolation(response=result)
    else:
        result.raise_for_status()

    cookies = {name: value for name, value in result.cookies.items()}
    headers = {name: value for name, value in result.headers.items()}

    if raw:
        return HttpResponse(HTTPStatus(result.status_code), "", result.content, cookies, headers)

    result_text = gzip.decompress(result.content).decode() if request.url.endswith(".gz") else result.text

    return HttpResponse(HTTPStatus(result.status_code), result_text, b"", cookies, headers)


class IHttpClient:
    def get(
        self,
        url: str,
        timeout: float = _DEFAULT_TIMEOUT,
        data: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        raw: bool = False,
    ) -> HttpResponse:
        return self.request(HttpRequest(url, HttpRequestType.GET, headers, data), timeout, raw=raw)

    def post(
        self,
        url: str,
        timeout: float = _DEFAULT_TIMEOUT,
        data: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> HttpResponse:
        return self.request(HttpRequest(url, HttpRequestType.POST, headers, data), timeout)

    def request(self, request: HttpRequest, timeout: float = _DEFAULT_TIMEOUT, raw: bool = False) -> HttpResponse:
        raise NotImplementedError

    def get_proxy(self) -> Tuple[ProxyParams, ResourceHandle[T]]:
        raise NotImplementedError

    def request_via_proxy(
        self, request: HttpRequest, proxy_params: ProxyParams, handle: ResourceHandle[T], timeout: float = _DEFAULT_TIMEOUT
    ) -> HttpResponse:
        raise NotImplementedError

    def list_resources(self) -> List[str]:
        return ["None"]

    def start(self) -> None:
        pass

    def shutdown(self) -> None:
        pass

    def __enter__(self) -> "IHttpClient":
        self.start()
        return self

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_value: Optional[Type[Exception]], tb: Optional[TracebackType]
    ) -> None:
        self.shutdown()


class TracingHttpClient(IHttpClient):
    def __init__(self, client: IHttpClient, diagnostics: Diagnostics):
        self._client = client
        self._diagnostics = diagnostics

    def request(self, request: HttpRequest, timeout: float = _DEFAULT_TIMEOUT, raw: bool = False) -> HttpResponse:
        response = self._client.request(request, timeout, raw=raw)
        self._diagnostics.info(f"{response.status_code} {response.body}")
        return response

    def __enter__(self) -> "IHttpClient":
        self._client.start()
        return self

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_value: Optional[Type[Exception]], tb: Optional[TracebackType]
    ) -> None:
        self._client.shutdown()


class RequestsHttpClient(IHttpClient):
    def __init__(self, proxy: Optional[ProxyParams] = None):
        self._proxy = proxy

    def request(self, request: HttpRequest, timeout: float = _DEFAULT_TIMEOUT, raw: bool = False) -> HttpResponse:
        proxies = self._proxy.to_dict() if self._proxy is not None else None
        return _make_http_request(request, timeout, proxies, None, raw=raw)


class RateLimitedProxyHttpClient(IHttpClient):
    """
    This client load balances requests to HTTP / SOCKS5 proxies and respects specified rate limit,
    i.e. each proxy won't process more than rate_limit requests per second.
    """

    def __init__(
        self,
        proxies: List[ProxyParams],
        rate_limit: float,
        diagnostics: Diagnostics = Diagnostics(),
        name: Optional[str] = None,
        check_rate_limited: Callable[[Response], float] = lambda response: 0.0,
        session_ttl: Optional[timedelta] = None,
    ):
        self._proxy_params: List[Optional[ProxyParams]] = [p for p in proxies] if len(proxies) > 0 else [(1, None)]
        self._sessions = [requests.Session() for _ in self._proxy_params]
        self._diagnostics = diagnostics
        self._name = name
        self._make_http_request = _make_http_request
        self._check_rate_limited = check_rate_limited
        self._session_ttl = session_ttl
        self._last_session_reset = datetime.now(UTC)

        self._proxy_errors_counter = self._diagnostics.counter("proxy_errors", ("proxy", "error_type"))
        self._requests_counter = self._diagnostics.counter("scraper_http_requests_made", ("proxy",))

        self._pool = RateLimitedResourcePool(self._proxy_params, rate_limit, diagnostics, name)

    def start(self) -> None:
        self._pool.start()

    def shutdown(self) -> None:
        self._pool.shutdown()
        for session in self._sessions:
            session.close()

    def _reset_session(self) -> None:
        _old_sessions = self._sessions
        self._sessions = [requests.Session() for _ in self._proxy_params]
        for session in _old_sessions:
            session.close()

    def request(self, request: HttpRequest, timeout: float = _DEFAULT_TIMEOUT, raw: bool = False) -> HttpResponse:
        self._ensure_http_session()
        proxy_params, handle = self.get_proxy()
        return self.request_via_proxy(request, proxy_params, handle, timeout, raw=raw)

    def _ensure_http_session(self) -> None:
        if self._session_ttl:
            now = datetime.now(UTC)
            if now - self._last_session_reset > self._session_ttl:
                self._last_session_reset = now
                self._reset_session()
                self._diagnostics.info(f"HTTP sessions recreated due to TTL: {self._session_ttl}")

    def get_proxy(self) -> Tuple[ProxyParams, ResourceHandle[T]]:
        handle = self._pool.acquire_resource()
        proxy_params = handle.resource[1] if isinstance(handle.resource, tuple) else handle.resource
        self._diagnostics.debug(f"Proxy acquired: {proxy_params}")
        return proxy_params, handle

    def request_via_proxy(
        self,
        request: HttpRequest,
        proxy_params: ProxyParams,
        handle: ResourceHandle[T],
        timeout: float = _DEFAULT_TIMEOUT,
        raw: bool = False,
    ) -> HttpResponse:
        proxy_params_dict = proxy_params.to_dict() if proxy_params is not None else None
        self._requests_counter.tags({"proxy": str(proxy_params)}).inc(1)

        start_time = time.time()
        try:
            response = self._make_http_request(
                request=request, timeout=timeout, proxies=proxy_params_dict, session=self._sessions[handle.index], raw=raw
            )
            self._pool.report_resource_status(handle, True)
        except (HTTPError, RateLimitViolation) as e:
            self._pool.report_resource_status(handle, False, inactivity_time=self._check_rate_limited(e.response))
            error_type = f"{e.response.status_code} - {short_exception_name(e)}"
            self._proxy_errors_counter.tags({"proxy": str(proxy_params), "error_type": error_type}).inc(1)
            raise
        except Exception as e:
            self._pool.report_resource_status(handle, False)
            error_type = short_exception_name(e)
            self._proxy_errors_counter.tags({"proxy": str(proxy_params), "error_type": error_type}).inc(1)
            raise
        finally:
            self._diagnostics.gauge("http_requests_latency").set(time.time() - start_time)

        return response

    def list_resources(self) -> List[str]:
        return list(map(str, self._proxy_params))
