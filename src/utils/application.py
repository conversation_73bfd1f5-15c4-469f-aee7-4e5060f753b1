import argparse
import logging
import socket
import sys
import threading
import time
import traceback
from datetime import datetime
from types import TracebackType
from typing import List, Optional, Type

from src.utils.diagnostics import Diagnostics, IMonitoring, PrometheusMonitoring
from src.utils.server import BindParams, ReadinessHttpServer, bind_argument


def log_level_argument(value: str) -> int:
    level = logging.getLevelName(value)
    if not isinstance(level, int):
        raise ValueError(f"unknown log level: {value}, stdlib returned: {level}")
    else:
        return level


def application_arguments_parser(parser: argparse.ArgumentParser) -> None:
    parser.add_argument(
        "--machine", type=str, default=socket.gethostname(), help="Name of the physical machine application runs on"
    )

    parser.add_argument(
        "--prometheus", type=bind_argument, default=None, help="Enables Prometheus metrics server that binds to host:port"
    )

    parser.add_argument(
        "--log-level",
        type=log_level_argument,
        default=logging.INFO,
        help="Sets application log level. Possible values: DEBUG, INFO, WARNING, ERROR, CRITICAL",
    )

    parser.add_argument(
        "--log-debug-tags",
        type=str,
        nargs="+",
        default=[],
        help="Enables debug logging labeled with provided tags. "
        "If specified, will set log level to DEBUG, overriding LOG_LEVEL.",
    )


class Application:
    def __init__(
        self,
        prometheus: Optional[BindParams] = None,
        log_level: int = logging.INFO,
        log_debug_tags: List[str] = [],
        readiness_port: int = 8082,
    ) -> None:
        self._prometheus_bind = prometheus
        self._prometheus = PrometheusMonitoring()

        if len(log_debug_tags) > 0:
            log_level = logging.DEBUG

        self._init_logging(log_level)
        self._diagnostics = Diagnostics(self._log, self._prometheus, set(log_debug_tags))

        self._readiness_port = readiness_port
        self._readiness_server = ReadinessHttpServer(self._readiness_port, self._diagnostics)

    @property
    def diagnostics(self) -> Diagnostics:
        return self._diagnostics

    def __enter__(self) -> "Application":
        self._log.info("application starting")

        if self._prometheus_bind is not None:
            self._log.info(f"starting Prometheus metric exporter on {self._prometheus_bind}")
            self._prometheus.start(self._prometheus_bind.host, self._prometheus_bind.port, self._log)

        self._readiness_server.start()

        self._start_time = datetime.utcnow()
        return self

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_value: Optional[Type[Exception]], tb: Optional[TracebackType]
    ) -> None:
        if exc_type is not None:
            self._log.error(f"{exc_value}\n{traceback.format_exc()}")

        _report_stuck_threads(0.1, 5.0, self._log)

        self._log.info(f"application run time: {datetime.utcnow() - self._start_time}")

        if exc_type is None:
            self._log.info("application shutdown successful")
        else:
            self._log.error("application terminated with an error:")

    def _init_logging(self, log_level: int) -> None:
        stream_handler = logging.StreamHandler(stream=sys.stdout)
        stream_handler.setFormatter(_LogFormatter("%(asctime)s %(name)-12s %(levelname)-8s %(message)s"))
        stream_handler.setLevel(log_level)

        self._log = logging.getLogger("root")
        self._log.setLevel(log_level)
        self._log.addHandler(stream_handler)
        self._log.addHandler(_LogHandler(self._prometheus))
        self._log.propagate = False

        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(stream_handler)


class _LogHandler(logging.Handler):
    def __init__(self, monitoring: IMonitoring) -> None:
        super().__init__()
        self._size_counter = monitoring.counter("log_output_bytes")

    def handle(self, record: logging.LogRecord) -> bool:
        self._size_counter.inc(len(record.getMessage()))
        return False


class _LogFormatter(logging.Formatter):
    def formatTime(self, record: logging.LogRecord, datefmt: Optional[str] = None) -> str:
        record_created = datetime.fromtimestamp(record.created)
        t = record_created.strftime("%Y-%m-%d %H:%M:%S")
        return f"{t}.{int(record.msecs * 1000):06d}"


def _report_stuck_threads(poll_interval: float, report_interval: float, log: logging.Logger) -> None:
    last_report_time = time.time()

    while True:
        threads = threading.enumerate()
        non_daemonic_thread_count = 0

        for thread in threads[1:]:
            if not thread.isDaemon():
                non_daemonic_thread_count += 1

        if non_daemonic_thread_count == 0:
            return

        if time.time() - last_report_time > report_interval:
            waiting_for = ", ".join([thread.getName() + ("(D)" if thread.isDaemon() else "") for thread in threads[1:]])
            log.info(f"waiting for {len(threads) - 1} thread(s): {waiting_for}")
            last_report_time = time.time()

        time.sleep(poll_interval)
