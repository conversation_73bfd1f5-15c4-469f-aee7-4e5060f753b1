import time
from collections import deque
from dataclasses import dataclass
from typing import Deque, Dict, Generic, Optional, Set, TypeVar

T = TypeVar("T")


class FifoCache(Generic[T]):
    def __init__(self, size: int):
        assert size > 0
        self._max_size = size
        self._fifo_queue: Deque[T] = deque()
        self._content_set: Set[T] = set()

    def __contains__(self, element: T) -> bool:
        return element in self._content_set

    def add(self, element: T) -> bool:
        if element in self._content_set:
            return False

        self._fifo_queue.append(element)
        self._content_set.add(element)

        if len(self._fifo_queue) > self._max_size:
            removed_element = self._fifo_queue.popleft()
            self._content_set.remove(removed_element)

        return True


@dataclass(frozen=True)
class CacheEntry(Generic[T]):
    time: float
    value: T


# Cache with timeout - Note: items added but never gotten live forever in the cache
class Cache(Generic[T]):
    def __init__(self, ttl: float = 3600.0):
        self._cache: Dict[str, CacheEntry[T]] = {}
        self.ttl = ttl

    def add(self, value: T, key: str = "") -> None:
        self._cache[key] = CacheEntry[T](time=time.time(), value=value)

    def get(self, key: str = "") -> Optional[T]:
        entry = self._cache.get(key)
        if entry is None:
            return None

        if self.ttl > 0 and time.time() > entry.time + self.ttl:
            del self._cache[key]
            return None

        return entry.value

    def keys(self):
        return self._cache.keys()
