import time

from src.octopus.inventory.types import DataType
from src.utils.diagnostics import Diagnostics


def report_exchange_api_lag(diagnostics: Diagnostics, api_response_time: float, exchange_name: str, data_type: DataType) -> None:
    diff = time.time() - api_response_time
    diagnostics.gauge("exchange_api_lag", ("exchange_name", "data_type")).tags({
        "exchange_name": exchange_name,
        "data_type": data_type.m_name,
    }).set(diff)
