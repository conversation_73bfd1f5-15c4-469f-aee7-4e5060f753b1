from collections import deque
from datetime import datetime, timed<PERSON>ta
from typing import Call<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from colorama import Fore, Style


class _ObservationHistory:
    def __init__(self, max_observations: int):
        self._max_observations = max_observations
        self._observations: Deque[Tuple[float, int]] = deque()

    def add_observation(self, time_spent: float, work_done: int) -> None:
        self._observations.append((time_spent, work_done))
        if len(self._observations) > self._max_observations:
            self._observations.popleft()

    def get_average(self) -> float:
        total_work = 0
        total_time = 0.0
        for time_spent, work_done in self._observations:
            total_time += time_spent
            total_work += work_done
        return total_time / total_work


class ETA:
    def __init__(
        self,
        log: Callable[[str], None],
        total_work_amount: int,
        max_observations: int,
        output_interval: int,
        print_prefix: str = "",
        enable_color_output: bool = False,
    ):
        assert total_work_amount > 0, "total work amount should be positive"

        self._log = log
        self._total_work_amount = total_work_amount
        self._print_prefix = print_prefix
        if len(self._print_prefix) > 0:
            self._print_prefix += " "
        self._enable_color_output = enable_color_output
        self._output_interval = output_interval

        self._work_done = 0
        self._history = _ObservationHistory(max_observations)
        self._work_started_flag = False
        self._work_started_time = datetime(1970, 1, 1)
        self._work_steps = 0

    def work_started(self) -> None:
        assert not self._work_started_flag, "work should not have been started"
        self._work_started_flag = True
        self._work_started_time = datetime.now()

    def work_finished(self, work_done: int) -> None:
        assert self._work_started_flag, "work should have been started"
        self._work_started_flag = False
        self._update_work(work_done, datetime.now() - self._work_started_time)

    def _update_work(self, work_done: int, time_spent: timedelta) -> bool:
        self._history.add_observation(time_spent.total_seconds(), work_done)
        self._work_done += work_done
        self._work_steps += 1

        if self._work_steps % self._output_interval == 0:
            self.output()
            return True
        else:
            return False

    def get_eta(self) -> float:
        return (self._total_work_amount - self._work_done) * self._history.get_average()

    def get_percent_done(self) -> float:
        return float(self._work_done) / self._total_work_amount

    def output(self) -> None:
        self._log(
            "{}{}{:0.2f}%{} done, eta is {}{}{}, speed is {}{:0.4f}/s{}".format(
                self._print_prefix,
                Fore.LIGHTYELLOW_EX if self._enable_color_output else "",
                100.0 * self.get_percent_done(),
                Style.RESET_ALL if self._enable_color_output else "",
                Fore.LIGHTCYAN_EX if self._enable_color_output else "",
                self.pretty_string_for_eta(self.get_eta()),
                Style.RESET_ALL if self._enable_color_output else "",
                Fore.LIGHTGREEN_EX if self._enable_color_output else "",
                1.0 / self._history.get_average(),
                Style.RESET_ALL if self._enable_color_output else "",
            )
        )

    def pretty_string_for_eta(self, eta_seconds: float) -> str:
        total_seconds = int(eta_seconds)

        days_modulo = total_seconds % (3600 * 24)
        days = (total_seconds - days_modulo) // (3600 * 24)
        total_seconds -= days * (3600 * 24)

        hours_modulo = total_seconds % 3600
        hours = (total_seconds - hours_modulo) // 3600
        total_seconds -= hours * 3600

        minutes_modulo = total_seconds % 60
        minutes = (total_seconds - minutes_modulo) // 60
        seconds = total_seconds - minutes * 60

        return "{} days {} hours {} minutes {} seconds".format(days, hours, minutes, seconds)
