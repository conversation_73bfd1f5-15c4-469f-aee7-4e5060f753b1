import socket
import ssl
from dataclasses import dataclass
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any


DEFAULT_SOCKET_OPTION = [(socket.SOL_TCP, socket.TCP_NODELAY, 1)]

if hasattr(socket, "SO_KEEPALIVE"):
    DEFAULT_SOCKET_OPTION.append((socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1))
if hasattr(socket, "TCP_KEEPIDLE"):
    DEFAULT_SOCKET_OPTION.append((socket.SOL_TCP, socket.TCP_KEEPIDLE, 30))
if hasattr(socket, "TCP_KEEPINTVL"):
    DEFAULT_SOCKET_OPTION.append((socket.SOL_TCP, socket.TCP_KEEPINTVL, 10))
if hasattr(socket, "TCP_KEEPCNT"):
    DEFAULT_SOCKET_OPTION.append((socket.SOL_TCP, socket.TCP_KEEPCNT, 3))

SocketOption = Tuple[int, int, int]


@dataclass(frozen=True)
class SslParams:
    version: int = ssl.PROTOCOL_TLS
    cert_reqs: int = ssl.CERT_REQUIRED
    cert_file: Optional[str] = None
    key_file: Optional[str] = None
    password: Optional[str] = None
    ca_file: Optional[str] = None
    ca_path: Optional[str] = None
    cert_chain: Optional[Tuple[str, str, str]] = None
    ciphers: Optional[str] = None
    ecdh_curve: Optional[str] = None
    check_hostname: bool = True
    suppress_ragged_eofs: bool = True
    do_handshake_on_connect: bool = True

    def to_ssl_opt(self) -> Dict[str, Any]:
        ssl_options = {
            "ssl_version": self.version,
            "cert_reqs": self.cert_reqs,
            "check_hostname": self.check_hostname,
            "suppress_ragged_eofs": self.suppress_ragged_eofs,
            "do_handshake_on_connect": self.do_handshake_on_connect,
        }
        if self.cert_file:
            ssl_options["cert_file"] = self.cert_file
        if self.key_file:
            ssl_options["key_file"] = self.key_file
        if self.password:
            ssl_options["password"] = self.password
        if self.ca_file:
            ssl_options["ca_file"] = self.ca_file
        if self.ca_path:
            ssl_options["ca_path"] = self.ca_path
        if self.cert_chain:
            ssl_options["cert_chain"] = self.cert_chain
        if self.ciphers:
            ssl_options["ciphers"] = self.ciphers
        if self.ecdh_curve:
            ssl_options["ecdh_curve"] = self.ecdh_curve

        return ssl_options


class AddressNotFound(Exception):
    pass


def ssl_socket(sock: socket.socket, params: SslParams, hostname: str) -> ssl.SSLSocket:
    context = ssl.SSLContext(params.version)

    if params.cert_reqs != ssl.CERT_NONE:
        if params.ca_file or params.ca_path:
            context.load_verify_locations(cafile=params.ca_file, capath=params.ca_path)
        elif hasattr(context, "load_default_certs"):
            context.load_default_certs(ssl.Purpose.SERVER_AUTH)

    if params.cert_file is not None:
        context.load_cert_chain(params.cert_file, params.key_file, params.password)

    # https://github.com/liris/websocket-client/commit/b96a2e8fa765753e82eea531adb19716b52ca3ca#commitcomment-10803153
    context.verify_mode = params.cert_reqs  # type: ignore
    context.check_hostname = params.cert_reqs != ssl.CERT_NONE and params.check_hostname

    if params.ciphers is not None:
        context.set_ciphers(params.ciphers)

    if params.cert_chain is not None:
        certfile, keyfile, password = params.cert_chain
        context.load_cert_chain(certfile, keyfile, password)

    if params.ecdh_curve is not None:
        context.set_ecdh_curve(params.ecdh_curve)

    return context.wrap_socket(
        sock,
        do_handshake_on_connect=params.do_handshake_on_connect,
        suppress_ragged_eofs=params.suppress_ragged_eofs,
        server_hostname=hostname,
    )
