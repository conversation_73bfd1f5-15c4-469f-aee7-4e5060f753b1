import json
from decimal import Decima<PERSON>
from typing import Any, List

from confluent_kafka import Consumer

from src.octopus.feed_protocol_pb2 import BookMessageV2


def check_bids_sorting(bid_prices: List[Decimal]) -> None:
    for i in range(len(bid_prices) - 1):
        if Decimal(bid_prices[i]) <= Decimal(bid_prices[i + 1]):
            print(f"BIDS unsorted. Index: {i}. Value: {bid_prices}")


def check_asks_sorting(ask_prices: List[Decimal]) -> None:
    for i in range(len(ask_prices) - 1):
        if Decimal(ask_prices[i]) >= Decimal(ask_prices[i + 1]):
            print(f"BIDS unsorted. Index: {i}. Value: {ask_prices}")


def parse_protobuf_message(content: bytes) -> BookMessageV2:
    value = BookMessageV2()
    value.ParseFromString(content)
    return value


def process_protobuf_message(message: Any) -> None:
    value: BookMessageV2 = parse_protobuf_message(message.value())

    bids = value.book_bids
    check_bids_sorting([Decimal(x.price) for x in bids])
    asks = value.book_asks
    check_asks_sorting([Decimal(x.price) for x in asks])


def process_json_message(message: Any) -> None:
    value = parse_json(message.value())

    bids = value["book_bids"]
    check_bids_sorting([Decimal(x[0]) for x in bids])
    asks = value["book_asks"]
    check_asks_sorting([Decimal(x[0]) for x in asks])


def parse_json(content: bytes) -> Any:
    return json.loads(content.decode("utf-8"))


consumer = Consumer({"bootstrap.servers": "127.0.0.1:9091"})
consumer.subscribe(["books_42.proto"])
while True:
    message = consumer.poll()
    offset = message.offset()
    if offset % 1000 == 0:
        print(f"OFFSET: {offset}")

    if ".proto" in message.topic():
        process_protobuf_message(message)
    else:
        process_json_message(message)
