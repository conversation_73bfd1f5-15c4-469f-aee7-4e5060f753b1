import os
import sys
from typing import List


def find_applications(directory: str) -> List[str]:
    result = []
    for root, _, files in os.walk("."):
        dir_pieces = root.split("/")
        last_dir = dir_pieces[-1]
        if last_dir != "applications":
            continue

        for file in files:
            pieces = file.split(".")
            extension = pieces[-1]
            if extension == "py":
                result.append(root + "/" + file)
    return result


def path_to_app_name(path: str) -> str:
    slash_to_dot = path.replace("/", ".")
    no_extension = slash_to_dot.split(".")[0:-1]
    return ".".join([piece for piece in no_extension if len(piece) > 0])


def run() -> None:
    if len(sys.argv) > 1:
        request_app_name = sys.argv[1]
        request_app_name_pieces = request_app_name.split(".")
        if len(request_app_name_pieces) > 2:
            print("Appname should be either {PACKAGE}.{APP_NAME} or just {APP_NAME}")
            exit(1)

        applications = find_applications(".")
        result = []
        for app_path in applications:
            app_name = path_to_app_name(app_path)
            path_pieces = app_name.split(".")

            if len(request_app_name_pieces) == 1:
                if path_pieces[-1] == request_app_name:
                    result.append(app_name)
            elif len(request_app_name_pieces) == 2:
                if len(path_pieces) > 2:
                    if path_pieces[-1] == request_app_name_pieces[-1] and path_pieces[-3] == request_app_name_pieces[0]:
                        result.append(app_name)

        if len(result) == 0:
            print("App not found")
            exit(1)
        if len(result) > 1:
            print("Ambigious name `{}`, possible options:".format(request_app_name))
            for option in result:
                print("- {}".format(option))
            exit(1)
        else:
            print(result[0])
            exit(0)
    else:
        print("App name was not specified")
        exit(1)


if __name__ == "__main__":
    run()
