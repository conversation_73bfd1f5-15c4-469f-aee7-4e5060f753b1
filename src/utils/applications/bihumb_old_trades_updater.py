import logging
import os
import time
from typing import Dict

import psycopg2.extras

UPDATE_SQL = """
    update trades_spot_19
    set trade_symbol=concat(subquery.trade_symbol, '/KRW')
    from (
        select * from (
            select *, position('/' in trade_symbol) as pos
            from trades_spot_19
            where trade_time>='{first_date}' and trade_time<'{last_date}'
        ) as t
        where t.pos = 0
    ) as subquery
    where
        trades_spot_19.trade_symbol=subquery.trade_symbol
        and trades_spot_19.trade_id=subquery.trade_id
        and trades_spot_19.trade_time>='{first_date}'
        and trades_spot_19.trade_time<'{last_date}';
"""

STATS_SQL = """
    select
        trade_symbol,
        count(*) as cnt
    from trades_spot_19
    where
        trade_time>='{first_date}'
        and trade_time<'{last_date}'
    group by trade_symbol
"""


def get_logger() -> logging.Logger:
    logging.basicConfig(
        filename=os.path.join(os.path.dirname(os.path.abspath(__file__)), "bithumb_old_trades_uploader.log"),
        filemode="a",
        format="%(asctime)s,%(msecs)d %(name)s %(levelname)s %(message)s",
        datefmt="%H:%M:%S",
        level=logging.DEBUG,
    )
    return logging.getLogger("bithumb_old_trades_uploader")


def month_year_iter(start_month, start_year, end_month, end_year):
    ym_start = 12 * start_year + start_month - 1
    ym_end = 12 * end_year + end_month - 1
    for ym in range(ym_start, ym_end):
        y, m = divmod(ym, 12)
        yield y, m + 1


def do_update(first_date: str, last_date: str, conn, cursor) -> None:
    cursor.execute(UPDATE_SQL.format(first_date=first_date, last_date=last_date))
    conn.commit()


def get_stats(first_date: str, last_date: str, cursor) -> Dict[str, int]:
    cursor.execute(STATS_SQL.format(first_date=first_date, last_date=last_date))
    raw_rows = cursor.fetchall()
    return {row["trade_symbol"]: row["cnt"] for row in raw_rows}


START_MONTH = 12
START_YEAR = 2013
END_MONTH = 2
END_YEAR = 2015
PAUSE = 5


if __name__ == "__main__":
    logger = get_logger()
    try:
        logger.info("Start processing")

        conn = psycopg2.connect(dbname="postgres", host="localhost", port=5432, user="postgres", password="postgres")
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        for year, month in month_year_iter(START_MONTH, START_YEAR, END_MONTH, END_YEAR):
            first_date = f"{year}-{month:0>2}-01"
            if month == 12:
                last_date = f"{year + 1}-01-01"
            else:
                last_date = f"{year}-{month + 1:0>2}-01"
            stats_1 = get_stats(first_date, last_date, cursor)
            logger.info(stats_1)
            do_update(first_date, last_date, conn, cursor)
            stats_2 = get_stats(first_date, last_date, cursor)
            logger.info(stats_2)
            logger.info(f"Updated: {first_date}-{last_date}")
            for key, value in stats_1.items():
                try:
                    value_2 = stats_2.pop(f"{key}/KRW")
                    if value != value_2:
                        logger.error(f"ERROR: stats_1: {key}:{value} != stats_2: {key}/KRW:{value_2}")
                except KeyError:
                    logger.error(f"ERROR: No data in stats_2 for {key}")
            if stats_2:
                logger.error(f"ERROR: left in stats_2: {stats_2}")
            time.sleep(PAUSE)

        logger.info("End processing")
    except Exception as e:
        logger.exception(e)
