import json
import time
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from enum import Enum
from threading import Event, Lock, Thread
from typing import BinaryIO, Generic, Iterator, Optional, Protocol, TypeVar, Union, Any

from google.pubsub_v1 import PubsubMessage

from src.utils import simple_binary_encoding
from src.utils.diagnostics import Diagnostics
from src.utils.execution import IR<PERSON><PERSON>ble, RetryState, RetryStrategy, Tick, endless_retry
from src.utils.rate_limit import ProxyPoolShuttingDownError
from src.utils.simple_binary_encoding import DecodedMessage, unpack_composite
from src.utils.string import cut_excessive_middle
from src.utils.types import JsonValue

C_T = TypeVar("C_T")  # connection message type
P_T = TypeVar("P_T")  # stream processor message type


@dataclass(frozen=True)
class MessageReceived(Generic[C_T]):
    data: C_T


@dataclass(frozen=True)
class ClosedByPeer:
    reason: str


class ShutdownByPeer:
    """Used to indicate that connection target will not accept connections any longer."""

    pass


ConnectionEvent = Union[MessageReceived[C_T], ClosedByPeer, ShutdownByPeer]


class IStreamingConnection(Generic[C_T]):
    """User must call ::communicate and ::close from a single thread."""

    def communicate(self, timeout: float) -> Iterator[ConnectionEvent[C_T]]:
        """Should raise an exception in case of any detected connectivity issue."""
        raise NotImplementedError

    def close(self, gracefully: bool = True) -> None:
        raise NotImplementedError

    def send(self, message: C_T) -> bool:
        """
        Implementation should be thread-safe and non-blocking.
        Conduct blocking IO required for requested transfer in ::communicate().
        Return False or raise an exception in case of a failure.
        """
        raise NotImplementedError


class StreamingClient(Protocol[C_T]):
    def __call__(self, timeout: float, diagnostics: Diagnostics) -> IStreamingConnection[C_T]:
        """Opens a streaming connection. Should raise an exception in case of a failure."""
        ...


class IStreamApi(Generic[P_T]):
    def send(self, message: P_T) -> bool:
        raise NotImplementedError

    def reconnect(self, gracefully: bool = True) -> None:
        raise NotImplementedError

    def request_stop(self) -> None:
        raise NotImplementedError

    @property
    def diagnostics(self) -> Diagnostics:
        raise NotImplementedError


class IStreamProcessor(Generic[P_T]):
    def on_tick(self, time_passed: float, stream: IStreamApi[P_T]) -> None:
        pass

    def on_open(self, stream: IStreamApi[P_T]) -> None:
        pass

    def on_message(self, message: P_T, stream: IStreamApi[P_T]) -> bool:
        """Should return True if processor recognizes a message."""
        return False

    def on_close(self, stream: IStreamApi[P_T]) -> None:
        pass


class IMessageTranslator(Generic[C_T, P_T]):
    def encode(self, message: P_T) -> C_T:
        raise NotImplementedError

    def decode(self, message: C_T) -> P_T:
        raise NotImplementedError


class UnrecognizedMessage(Exception, Generic[P_T]):
    def __init__(self, message: P_T):
        self._message = message

    def __str__(self) -> str:
        return self.__repr__()

    def __repr__(self) -> str:
        return "unrecognized message -- `{}`".format(cut_excessive_middle(str(self._message), 512))


@dataclass(frozen=True)
class StreamParams:
    connect_timeout: float = 5.0
    connect_retry: RetryStrategy = endless_retry()
    tick_interval: Optional[float] = 1.0


class _ReconnectSignal(Enum):
    UNSET = "unset"
    GRACEFULLY = "gracefully"
    IMMEDIATELY = "immediately"


class Stream(Generic[C_T, P_T], IStreamApi[P_T], IRunnable):
    def __init__(
        self,
        client: StreamingClient[C_T],
        processor: IStreamProcessor[P_T],
        translator: IMessageTranslator[C_T, P_T],
        diagnostics: Diagnostics,
        params: StreamParams = StreamParams(),
        name: Optional[str] = None,
    ):
        self._client = client
        self._processor = processor
        self._translator = translator
        self._diagnostics = diagnostics

        self._tick_interval = params.tick_interval
        self._connect_retry = params.connect_retry
        self._connect_timeout = params.connect_timeout

        self._name = name if name is not None else f"{id(self)}"

        self._connection: Optional[IStreamingConnection[C_T]] = None
        self._communicate_timeout = 1.0

        self._reconnect_signal = _ReconnectSignal.UNSET
        self._tick_stop_event = Event()
        self._stop_event = Event()
        self._thread = Thread(target=self._background_loop, name=f"{self} loop")

    def __repr__(self) -> str:
        return f"Stream {self._name}"

    @property
    def diagnostics(self) -> Diagnostics:
        return self._diagnostics

    def start(self) -> None:
        self._thread.start()

    def wait_for_termination(self) -> None:
        self._thread.join()

    def request_stop(self) -> None:
        self._stop_event.set()

    def reconnect(self, gracefully: bool = True) -> None:
        self._reconnect_signal = _ReconnectSignal.GRACEFULLY if gracefully else _ReconnectSignal.IMMEDIATELY

    def send(self, message: P_T) -> bool:
        try:
            translated_message = self._translator.encode(message)

            if self._diagnostics.is_debug_tag_enabled("stream.sent_message"):
                self._diagnostics.debug("outbound message: {}".format(translated_message))
        except Exception as e:
            self._diagnostics.error(e, "failed to encode `{}`".format(cut_excessive_middle(str(message), 512)))
            return False

        if (connection := self._connection) is None:
            return False

        try:
            return connection.send(translated_message)
        except Exception as e:
            self._diagnostics.error(e, "error sending {}".format(cut_excessive_middle(str(translated_message), 512)))
            return False

    def _background_loop(self) -> None:
        retry_state: Optional[RetryState] = None

        while not self._stop_event.is_set():
            if self._run_connection():
                retry_state = None
            else:
                retry_state = RetryState(1) if retry_state is None else retry_state.attempt_failed()

                if (sleep_time := self._connect_retry(retry_state)) is not None:
                    self._stop_event.wait(sleep_time)
                else:
                    self.request_stop()

    def _run_connection(self) -> bool:
        self._diagnostics.info(f"{self} establishing connection...")
        self._reconnect_signal = _ReconnectSignal.UNSET
        self._tick_stop_event.clear()

        try:
            self._connection = self._client(self._connect_timeout, self.diagnostics)
            self._diagnostics.debug(f"{self} opened connection")
        except ProxyPoolShuttingDownError:
            self._diagnostics.info(f"{self} paused since proxy pool shutting down")
            time.sleep(1)
            return False
        except Exception as e:
            self._diagnostics.error(e, f"{self} failed to connect")
            return False

        self._on_open()

        if self._tick_interval is not None:
            tick_thread = Thread(target=self._tick, name=f"{self} tick", args=(time.time(), self._tick_interval))
            tick_thread.start()

        try:
            while self._reconnect_signal == _ReconnectSignal.UNSET and not self._stop_event.is_set():
                for message in self._connection.communicate(self._communicate_timeout):
                    if isinstance(message, MessageReceived):
                        self._on_message(message.data)

                        if self._reconnect_signal != _ReconnectSignal.UNSET:
                            self._diagnostics.info(f"processor initiated reconnection {self._reconnect_signal.value}")
                            break

                    elif isinstance(message, ClosedByPeer):
                        self._diagnostics.info(f"peer initiated close: {message.reason}")
                        self._reconnect_signal = _ReconnectSignal.GRACEFULLY
                        break

                    else:
                        self._diagnostics.info("peer initiated shutdown")
                        self.request_stop()
                        break

                    if self._stop_event.is_set():
                        self._diagnostics.info("connection shutdown initiated")
                        break
        except Exception as e:
            self._diagnostics.error(e, "connection failed")
            self._reconnect_signal = _ReconnectSignal.IMMEDIATELY

        if self._tick_interval is not None:
            self._tick_stop_event.set()
            tick_thread.join()

        self._on_close()

        try:
            gracefully = self._reconnect_signal != _ReconnectSignal.IMMEDIATELY
            self._connection.close(gracefully)
            self._diagnostics.info(f"{self} closed connection")
        except Exception as e:
            self._diagnostics.error(e, f"{self} failed during closing connection")

        self._connection = None
        return True

    def _tick(self, open_time: float, interval: float) -> None:
        while not self._tick_stop_event.is_set():
            with Tick(seconds=interval, stop_event=self._tick_stop_event):
                try:
                    time_passed = time.time() - open_time
                    self._processor.on_tick(time_passed, self)
                except Exception as e:
                    self._diagnostics.error(e, "processor failed in on_tick")

    def _on_open(self) -> None:
        try:
            self._processor.on_open(self)
        except Exception as e:
            self._diagnostics.error(e, "processor failed during in on_open")

    def _on_close(self) -> None:
        try:
            self._processor.on_close(self)
        except Exception as e:
            self._diagnostics.error(e, "processor failed during in on_close")

    def _on_message(self, message: C_T) -> None:
        try:
            if self._diagnostics.is_debug_tag_enabled("stream.raw_message"):
                self._diagnostics.debug("inbound message (raw): {}".format(message))
            translated_message = self._translator.decode(message)
            if translated_message is None:
                return  # stop processing when no decoded message

            if self._diagnostics.is_debug_tag_enabled("stream.message"):
                self._diagnostics.debug("inbound message: {}".format(translated_message))
        except Exception as e:
            self._diagnostics.error(e, "failed to decode `{}`".format(message))
            return

        try:
            message_recognized = self._processor.on_message(translated_message, self)
            if not message_recognized:
                self._diagnostics.error_ns(UnrecognizedMessage(translated_message))
        except Exception as e:
            self._diagnostics.error(e, "processor failed in on_message")


class NullProcessor(IStreamProcessor[P_T]):
    pass


class ComponentProcessor(IStreamProcessor[P_T]):
    class Break(Exception):
        """Raise it in a component to stop further processing of a message."""

        pass

    def __init__(self, *components: IStreamProcessor[P_T], break_after_first_message_recognition: bool = False) -> None:
        self._components = [c for c in components]
        self._break_after_first_message_recognition = break_after_first_message_recognition

    def install(self, *components: IStreamProcessor[P_T]) -> "ComponentProcessor[P_T]":
        """Processor calls on_message in the component installation order."""
        self._components.extend(components)
        return self

    def on_tick(self, time_passed: float, stream: IStreamApi[P_T]) -> None:
        for component in self._components:
            try:
                component.on_tick(time_passed, stream)
            except Exception as e:
                stream.diagnostics.error(e, f"component {component} failed in on_tick")

    def on_open(self, stream: IStreamApi[P_T]) -> None:
        for component in self._components:
            try:
                component.on_open(stream)
            except Exception as e:
                stream.diagnostics.error(e, f"component {component} failed in on_open")

    def on_message(self, message: P_T, stream: IStreamApi[P_T]) -> bool:
        message_recognized = False

        for component in self._components:
            try:
                recognized = component.on_message(message, stream)
                if recognized and self._break_after_first_message_recognition:
                    return True
                message_recognized = message_recognized or recognized
            except ComponentProcessor.Break:
                stream.diagnostics.info(f"message processing interrupted by {component}")
                return True
            except Exception as e:
                stream.diagnostics.error(
                    e, "component {} failed in on_message `{}`".format(component, cut_excessive_middle(str(message), 512))
                )

        return message_recognized

    def on_close(self, stream: IStreamApi[P_T]) -> None:
        for component in self._components:
            try:
                component.on_close(stream)
            except Exception as e:
                stream.diagnostics.error(e, f"component {component} failed in on_close")


class JsonTranslator(IMessageTranslator[Union[str, bytes], JsonValue]):
    class Encoder(json.JSONEncoder):
        def default(self, obj: JsonValue) -> str:
            if isinstance(obj, Decimal):
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return str(super(JsonTranslator.Encoder, self).default(obj))

    def encode(self, message: JsonValue) -> Union[str, bytes]:
        return json.dumps(message, cls=JsonTranslator.Encoder)

    def decode(self, message: Union[str, bytes]) -> JsonValue:
        return json.loads(message, parse_float=Decimal)


class ProtobufTranslator(IMessageTranslator[Union[str, bytes], JsonValue]):
    """Protobuf is used only for the incoming data"""

    def __init__(self, protobuf_data_class: Any) -> None:
        self._protobuf_data_class = protobuf_data_class

    class Encoder(json.JSONEncoder):
        def default(self, obj: JsonValue) -> str:
            if isinstance(obj, Decimal):
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return str(super(JsonTranslator.Encoder, self).default(obj))

    def encode(self, message: JsonValue) -> Union[str, bytes]:
        return json.dumps(message, cls=JsonTranslator.Encoder)

    def decode(self, message: Union[str, bytes]) -> JsonValue:
        if isinstance(message, str):
            return json.loads(message, parse_float=Decimal)
        else:
            data = self._protobuf_data_class()
            data.ParseFromString(message)
            return data


class CmeSmartStreamFixTranslator(IMessageTranslator[PubsubMessage, Optional[DecodedMessage]]):
    def __init__(self, messages_schema_file_path: str, message_name: str):
        with open(messages_schema_file_path, "r") as schema_file:
            self._schema = simple_binary_encoding.Schema.parse(schema_file)
            self._message_name = message_name

    def decode(self, message: PubsubMessage) -> Optional[DecodedMessage]:
        buffer = message.data[14:]
        header = unpack_composite(self._schema, self._schema.types["messageHeader"], buffer)
        h = self._schema.messages[header.value["templateId"]]
        if h.name == self._message_name:
            return self._schema.decode(buffer, message.publish_time)  # type: ignore

        return None


class IdentityTranslator(IMessageTranslator[C_T, C_T]):
    def encode(self, message: C_T) -> C_T:
        return message

    def decode(self, message: C_T) -> C_T:
        return message


class RecordingConnection(IStreamingConnection[C_T]):
    def __init__(self, file_handle: BinaryIO, file_lock: Lock, connection: IStreamingConnection[C_T]):
        self._file_handle = file_handle
        self._file_lock = file_lock
        self._connection = connection

    def close(self, gracefully: bool = True) -> None:
        self._connection.close(gracefully)

    def send(self, message: C_T) -> bool:
        return self._connection.send(message)

    def communicate(self, timeout: float) -> Iterator[ConnectionEvent[C_T]]:
        for message in self._connection.communicate(timeout):
            if isinstance(message, MessageReceived):
                data = message.data

                if isinstance(data, bytes):
                    data_bytes = data
                else:
                    data_str = str(data) if not isinstance(data, str) else data
                    data_bytes = bytes(data_str, encoding="utf-8")

                with self._file_lock:
                    self._file_handle.write(data_bytes)
                    self._file_handle.write(b"\n")
                    self._file_handle.flush()

            yield message


def recording_client(handle: BinaryIO, lock: Lock, underlying: StreamingClient[C_T]) -> StreamingClient[C_T]:
    return lambda timeout, diagnostics: RecordingConnection(handle, lock, underlying(timeout, diagnostics))
