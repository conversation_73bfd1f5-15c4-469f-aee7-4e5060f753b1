import queue
import signal
import threading
import time
from collections import deque, defaultdict
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from threading import Event, Lock, Thread
from types import FrameType, TracebackType
from typing import Any, Callable, Deque, Dict, Generator, Generic, List, Optional, Type, TypeVar, Union

from src.utils.diagnostics import Diagnostics
from src.utils.execution import StopSignal, Tick

R = TypeVar("R")
M_T = TypeVar("M_T")  # Channel message type
T_T = TypeVar("T_T")
E_T = TypeVar("E_T")  # TimeRange consumer entity

DEFAULT_TIMEOUT = 60 * 60 * 24 * 365


""" Channels API """


class IChannelStats:
    @property
    def size(self) -> int:
        raise NotImplementedError

    @property
    def capacity(self) -> int:
        raise NotImplementedError

    @property
    def name(self) -> str:
        raise NotImplementedError


class IChannelIn(Generic[M_T]):
    def fetch(self) -> Optional[M_T]:
        raise NotImplementedError

    def stats(self) -> IChannelStats:
        raise NotImplementedError


class IChannelOut(Generic[M_T]):
    def try_send(self, message: M_T) -> bool:
        raise NotImplementedError

    def stats(self) -> IChannelStats:
        raise NotImplementedError


class IChannel(Generic[M_T]):
    def inbound(self) -> IChannelIn[M_T]:
        raise NotImplementedError

    def outbound(self) -> IChannelOut[M_T]:
        raise NotImplementedError

    def stats(self) -> IChannelStats:
        raise NotImplementedError


class IBlockingChannelIn(IChannelIn[M_T]):
    def wait(self, timeout: float = DEFAULT_TIMEOUT, cancel_if: Optional[StopSignal] = None) -> Optional[M_T]:
        raise NotImplementedError


class IBlockingChannelOut(IChannelOut[M_T]):
    def send(self, message: M_T, timeout: float = DEFAULT_TIMEOUT, cancel_if: Optional[StopSignal] = None) -> bool:
        raise NotImplementedError


class IBlockingChannel(Generic[M_T]):
    def inbound(self) -> IBlockingChannelIn[M_T]:
        raise NotImplementedError

    def outbound(self) -> IBlockingChannelOut[M_T]:
        raise NotImplementedError

    def stats(self) -> IChannelStats:
        raise NotImplementedError


""" PyRoutine API """


class IPyRoutineEnv:
    def request_stop(self) -> None:
        raise NotImplementedError

    def wait_for_termination(self) -> None:
        raise NotImplementedError

    def stop_requested(self) -> bool:
        raise NotImplementedError

    def stop_not_requested(self) -> bool:
        raise NotImplementedError

    def launch(self, routine: "PyRoutine", name: Optional[str] = None) -> "IPyRoutineEnv":
        raise NotImplementedError

    def sleep(self, seconds: float) -> bool:
        """Should return True if PyRoutine received a stop request while sleeping."""
        raise NotImplementedError

    def tick(self, seconds: float) -> Tick:
        raise NotImplementedError

    def wait_till_stop_requested(self) -> None:
        raise NotImplementedError

    @property
    def diagnostics(self) -> Diagnostics:
        raise NotImplementedError

    def blocking_send(self, msg: M_T, channel: IBlockingChannelOut[M_T], timeout: float = DEFAULT_TIMEOUT) -> bool:
        return channel.send(msg, timeout=timeout, cancel_if=self.stop_requested)

    def blocking_wait(self, channel: IBlockingChannelIn[M_T], timeout: float = DEFAULT_TIMEOUT) -> Optional[M_T]:
        return channel.wait(timeout=timeout, cancel_if=self.stop_requested)


class IPyRoutineSystem:
    def __enter__(self) -> "IPyRoutineSystem":
        raise NotImplementedError

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_value: Optional[Type[Exception]], tb: Optional[TracebackType]
    ) -> None:
        if exc_type is not None:
            self.request_stop()
        self.wait_for_termination()

    def blocking_channel(self, capacity: int, name: Optional[str] = None) -> IBlockingChannel[M_T]:
        raise NotImplementedError

    def channel(self, capacity: int, name: Optional[str] = None) -> IChannel[M_T]:
        raise NotImplementedError

    def launch(self, routine: "PyRoutine", name: Optional[str] = None) -> IPyRoutineEnv:
        raise NotImplementedError

    def wait_for_termination(self) -> None:
        raise NotImplementedError

    def request_stop(self) -> None:
        raise NotImplementedError


"""
PyRoutine is able to receive an environment in the first 'yield' statement.
If PyRoutine needs to do any clean-up on termination, it should yield a destructor function.

def some_computation() -> PyRoutine:
    def dtor(env: IPyRoutine) -> None:
        print('this will be called during PyRoutine termination process')

    env = yield dtor  # combining receiving an environment and providing a destructor
    ...
"""
PyRoutine = Generator[Optional["PyRoutineDestructor"], IPyRoutineEnv, None]

PyRoutineDestructor = Callable[[IPyRoutineEnv], None]


""" Misc utilities """


def loop_routine(action: Callable[[IPyRoutineEnv], R], interval: float, terminate_on_error: bool) -> PyRoutine:
    env = yield None

    while env.stop_not_requested():
        with env.tick(interval):
            try:
                action(env)
            except Exception as e:
                env.diagnostics.error(e)
                if terminate_on_error:
                    break


""" Consumer utilities """


class IBatchConsumer(Generic[M_T]):
    def start(self, env: IPyRoutineEnv) -> None:
        pass

    def stop(self, env: IPyRoutineEnv) -> None:
        pass

    def consume(self, batch: List[M_T], env: IPyRoutineEnv) -> None:
        pass


def periodic_batch_consumer(
    consumer: IBatchConsumer[M_T], channel: IChannelIn[List[M_T]], max_batch_consumed_per_interval: int, interval: float
) -> PyRoutine:
    env = yield lambda env: consumer.stop(env)
    tick = env.tick(seconds=interval)
    consumer.start(env)

    while env.stop_not_requested():
        with tick:
            full_batch = []
            consumed_batch_count = 0

            while consumed_batch_count < max_batch_consumed_per_interval:
                batch = channel.fetch()

                if batch is not None:
                    consumed_batch_count += 1
                    full_batch.extend(batch)
                else:
                    break

            if len(full_batch) > 0:
                consumer.consume(full_batch, env)


def continuous_batch_consumer(consumer: IBatchConsumer[M_T], channel: IBlockingChannelIn[List[M_T]]) -> PyRoutine:
    env = yield lambda env: consumer.stop(env)
    consumer.start(env)

    while env.stop_not_requested():
        batch = env.blocking_wait(channel)

        if batch is not None:
            consumer.consume(batch, env)


""" Producer utilities """


@dataclass(frozen=True)
class ProductionResult(Generic[M_T]):
    batch: List[M_T]
    sleep_time_seconds: float


class IBatchProducer(Generic[M_T]):
    def start(self, env: IPyRoutineEnv) -> None:
        pass

    def stop(self, env: IPyRoutineEnv) -> None:
        pass

    def produce(self, env: IPyRoutineEnv) -> ProductionResult[M_T]:
        pass


ProcessBatch = Callable[[List[M_T], IPyRoutineEnv], None]
TransformBatch = Callable[[List[M_T], Diagnostics], List[T_T]]


def continuous_batch_producer(producer: IBatchProducer[M_T], process: ProcessBatch[M_T]) -> PyRoutine:
    env = yield lambda env: producer.stop(env)
    producer.start(env)

    while env.stop_not_requested():
        start_time = time.time()
        production_result = producer.produce(env)
        time_spent = time.time() - start_time

        with env.tick(production_result.sleep_time_seconds - time_spent):
            if len(production_result.batch) == 0:
                continue
            else:
                process(production_result.batch, env)


def send_batch_to_blocking_channels(channels: List[IBlockingChannelOut[List[M_T]]]) -> ProcessBatch[M_T]:
    def proc(batch: List[M_T], env: IPyRoutineEnv) -> None:
        for channel in channels:
            env.blocking_send(batch, channel)

    return proc


def send_batch_to_channels(
    channels: List[IChannelOut[List[M_T]]],
    backup_channels: Optional[List[IChannelOut[List[M_T]]]] = None,
    diagnostics: Diagnostics = Diagnostics(),
) -> ProcessBatch[M_T]:
    if backup_channels is not None and len(backup_channels) != len(channels):
        raise ValueError("either all or none channels should have a backup")

    backup = diagnostics.counter("dropping_channel_processor_backed_up_item_count", ("channel_name",))
    drop = diagnostics.counter("dropping_channel_processor_dropped_item_count", ("channel_name",))

    backup_counters = [backup.tags({"channel_name": c.stats().name}) for c in channels]
    drop_counters = [drop.tags({"channel_name": c.stats().name}) for c in channels]

    def proc(batch: List[M_T], env: IPyRoutineEnv) -> None:
        for index, channel in enumerate(channels):
            if channel.try_send(batch):
                continue

            if backup_channels is not None:
                if backup_channels[index].try_send(batch):
                    backup_counters[index].inc(len(batch))
                    continue

            drop_counters[index].inc(len(batch))

    return proc


def transform_batch(process: ProcessBatch[T_T], transform: TransformBatch[M_T, T_T]) -> ProcessBatch[M_T]:
    def proc(batch: List[M_T], env: IPyRoutineEnv) -> None:
        if len(transformed_batch := transform(batch, env.diagnostics)) > 0:
            process(transformed_batch, env)

    return proc


def sequence_batch(processors: List[ProcessBatch[M_T]]) -> ProcessBatch[M_T]:
    def proc(batch: List[M_T], env: IPyRoutineEnv) -> None:
        for process in processors:
            process(batch, env)

    return proc


def chain_transform_batch(transforms: List[Optional[TransformBatch[Any, Any]]], process: ProcessBatch[M_T]) -> ProcessBatch[Any]:
    for transform in transforms[::-1]:
        if transform is not None:
            process = transform_batch(process, transform)
    return process


""" Implementation """


class _QueueChannel(IBlockingChannelIn[M_T], IBlockingChannelOut[M_T], IBlockingChannel[M_T], IChannelStats):
    def __init__(self, capacity: int, name: Optional[str] = None, stop_signal_check_interval: timedelta = timedelta(seconds=1)):
        if capacity < 1:
            raise ValueError("capacity must be a positive integer")

        self._capacity = capacity
        self._name = name if name is not None else f"anonymous-{id(self)}"
        self._stop_signal_check_interval = stop_signal_check_interval.total_seconds()
        self._queue: queue.Queue[M_T] = queue.Queue(maxsize=capacity)

    def __repr__(self) -> str:
        return f"channel {self._name}"

    def inbound(self) -> IBlockingChannelIn[M_T]:
        return self

    def outbound(self) -> IBlockingChannelOut[M_T]:
        return self

    def stats(self) -> IChannelStats:
        return self

    def send(self, message: M_T, timeout: float = DEFAULT_TIMEOUT, cancel_if: Optional[StopSignal] = None) -> bool:
        time_left = timeout

        while time_left > 0:
            try:
                self._queue.put(message, timeout=min(self._stop_signal_check_interval, time_left))
                return True
            except queue.Full:
                if cancel_if is not None and cancel_if():
                    return False
                else:
                    time_left -= self._stop_signal_check_interval

        return False

    def try_send(self, message: M_T) -> bool:
        try:
            self._queue.put_nowait(message)
            return True
        except queue.Full:
            return False

    def wait(self, timeout: float = DEFAULT_TIMEOUT, cancel_if: Optional[StopSignal] = None) -> Optional[M_T]:
        time_left = timeout

        while time_left > 0:
            try:
                message = self._queue.get(timeout=min(self._stop_signal_check_interval, time_left))
                return message
            except queue.Empty:
                if cancel_if is not None and cancel_if():
                    return None
                else:
                    time_left -= self._stop_signal_check_interval

        return None

    def fetch(self) -> Optional[M_T]:
        try:
            return self._queue.get_nowait()
        except queue.Empty:
            return None

    @property
    def name(self) -> str:
        return self._name

    @property
    def size(self) -> int:
        return self._queue.qsize()

    @property
    def capacity(self) -> int:
        return self._capacity


class _DequeChannel(IChannelIn[M_T], IChannelOut[M_T], IChannel[M_T], IChannelStats):
    def __init__(self, capacity: int, name: Optional[str] = None):
        if capacity < 1:
            raise ValueError("capacity must be a positive integer")

        self._capacity = capacity
        self._name = name if name is not None else f"anonymous{id(self)}"
        self._deque: Deque[M_T] = deque()

    def __repr__(self) -> str:
        return "channel `{}`".format(self._name)

    def inbound(self) -> IChannelIn[M_T]:
        return self

    def outbound(self) -> IChannelOut[M_T]:
        return self

    def stats(self) -> IChannelStats:
        return self

    def try_send(self, message: M_T) -> bool:
        if len(self._deque) < self._capacity:
            self._deque.append(message)
            return True
        else:
            return False

    def fetch(self) -> Optional[M_T]:
        try:
            return self._deque.popleft()
        except IndexError:
            return None

    @property
    def name(self) -> str:
        return self._name

    @property
    def size(self) -> int:
        return len(self._deque)

    @property
    def capacity(self) -> int:
        return self._capacity


class _PyRoutineEnv(IPyRoutineEnv):
    def __init__(
        self,
        system: IPyRoutineSystem,
        generator: PyRoutine,
        pyroutine_id: int,
        pyroutine_name: str,
        termination_callback: Callable[[int], None],
        diagnostics: Diagnostics,
    ) -> None:
        self._system = system
        self._generator = generator
        self._pyroutine_id = pyroutine_id
        self._name = pyroutine_name
        self._termination_callback = termination_callback
        self._diagnostics = diagnostics

        self._stop_signal_event = Event()
        self._stopped_event = Event()

    @property
    def diagnostics(self) -> Diagnostics:
        return self._diagnostics

    @property
    def name(self) -> str:
        return self._name

    def stop_requested(self) -> bool:
        return self._stop_signal_event.is_set()

    def stop_not_requested(self) -> bool:
        return not self._stop_signal_event.is_set()

    def launch(self, routine: PyRoutine, name: Optional[str] = None) -> IPyRoutineEnv:
        return self._system.launch(routine, name)

    def sleep(self, seconds: float) -> bool:
        self._stop_signal_event.wait(seconds)
        return self.stop_requested()

    def tick(self, seconds: float) -> Tick:
        return Tick(seconds, stop_event=self._stop_signal_event)

    def request_stop(self) -> None:
        self._stop_signal_event.set()

    def wait_for_termination(self) -> None:
        self._stopped_event.wait()

    def wait_till_stop_requested(self) -> None:
        self._stop_signal_event.wait()

    def _start(self) -> None:
        self._thread = Thread(target=self._run, name="PyRoutineEnv {} pyroutine".format(self._name))
        self._thread.start()

    def _run(self) -> None:
        destructor: Optional[PyRoutineDestructor] = None
        self_sent = False

        self.diagnostics.info(f"starting pyroutine {self._name}")

        try:
            yield_value = next(self._generator)
            if yield_value is not None:
                destructor = yield_value

        except Exception as e:
            self.diagnostics.error(e, "pyroutine failed")
            self.request_stop()

        while self.stop_not_requested():
            try:
                if not self_sent:
                    self_sent = True
                    yield_value = self._generator.send(self)
                else:
                    yield_value = next(self._generator)

                if yield_value is not None:
                    destructor = yield_value

            except StopIteration:
                self.request_stop()

            except Exception as e:
                self.diagnostics.error(e, "pyroutine failed")
                self.request_stop()

        self.diagnostics.info(f"terminating pyroutine {self._name}")

        try:
            if destructor is not None:
                destructor(self)
        except Exception as e:
            self.diagnostics.error(e, "pyroutine destructor failed")

        try:
            self._termination_callback(self._pyroutine_id)
        except Exception as e:
            self.diagnostics.error(e, "pyroutine termination callback failed")

        self._stopped_event.set()


class PyRoutineSystem(IPyRoutineSystem):
    def __init__(self, diagnostics: Diagnostics = Diagnostics(), stop_signal_check_interval: timedelta = timedelta(seconds=1)):
        self._diagnostics = diagnostics
        self._stop_signal_check_interval = stop_signal_check_interval

        self._pyroutines: Dict[int, _PyRoutineEnv] = {}
        self._pyroutine_id_counter = -1

        self._system_lock = Lock()
        self._pyroutine_lock = Lock()

        self._stopping_event = Event()
        self._stopped_event = Event()

        self._channel_size_gauge = self._diagnostics.gauge("channel_size", ("channel_name",))
        self._channel_fill_gauge = self._diagnostics.gauge("channel_fill", ("channel_name",))
        self._threads_gauge = self._diagnostics.gauge("threads", ("type",))

        self._monitored_channels_lock = Lock()
        self._monitored_channels: List[IChannelStats] = []

        self._monitor_thread = Thread(target=self._monitor_thread_func, name="SystemMonitor")
        self._monitor_thread.start()

    def __enter__(self) -> IPyRoutineSystem:
        signal.signal(signal.SIGTERM, self._on_sigterm)
        return self

    def blocking_channel(self, capacity: int, name: Optional[str] = None) -> IBlockingChannel[M_T]:
        channel: _QueueChannel[M_T] = _QueueChannel(capacity, name, self._stop_signal_check_interval)
        self._monitor_channel(channel)
        return channel

    def channel(self, capacity: int, name: Optional[str] = None) -> IChannel[M_T]:
        channel: _DequeChannel[M_T] = _DequeChannel(capacity, name)
        self._monitor_channel(channel)
        return channel

    def launch(self, pyroutine: PyRoutine, name: Optional[str] = None) -> IPyRoutineEnv:
        with self._system_lock:
            if self._stopping_event.is_set():
                raise Exception("system is stopping or is already stopped")

            self._pyroutine_id_counter += 1
            pyroutine_id = self._pyroutine_id_counter
            name = name if name is not None else "pyroutine_{}".format(pyroutine_id)

            env = _PyRoutineEnv(
                self, pyroutine, self._pyroutine_id_counter, name, self._pyroutine_stopped, self._diagnostics.child_scope(name)
            )

            with self._pyroutine_lock:
                self._pyroutines[pyroutine_id] = env

            env._start()
            return env

    def wait_for_termination(self) -> None:
        try:
            self._stopped_event.wait()
        except KeyboardInterrupt:
            self.request_stop()
            self._stopped_event.wait()

    def request_stop(self) -> None:
        with self._system_lock:
            if self._stopping_event.is_set():
                return

            self._diagnostics.info("pyroutine system shutting down!")
            self._stopping_event.set()

            pyroutines = [pyroutine for pyroutine in self._pyroutines.values()]

            for pyroutine in pyroutines:
                pyroutine.request_stop()

            progress_thread = Thread(target=self._progress_thread_func, daemon=True, name="SystemShutdownProgress")
            progress_thread.start()

            shutdown_thread = Thread(target=self._shutdown_thread_func, args=(pyroutines,), daemon=True, name="SystemShutdown")
            shutdown_thread.start()

    def _shutdown_thread_func(self, pyroutines: List[IPyRoutineEnv]) -> None:
        for pyroutine in pyroutines:
            pyroutine.wait_for_termination()

        self._monitor_thread.join()
        self._stopped_event.set()

    def _progress_thread_func(self) -> None:
        start = time.time()

        while not self._stopped_event.wait(5.0):
            with self._pyroutine_lock:
                alive_pyroutines = [pyroutine for pyroutine in self._pyroutines.values()]

            self._diagnostics.warning(
                "waiting ({}s) {} pyroutine(s): {}".format(
                    (time.time() - start), len(alive_pyroutines), [r.name for r in alive_pyroutines]
                )
            )

    def _monitor_thread_func(self) -> None:
        while not self._stopping_event.is_set():
            with self._monitored_channels_lock:
                for channel in self._monitored_channels:
                    self._channel_size_gauge.tags({"channel_name": channel.name}).set(channel.size)
                    self._channel_fill_gauge.tags({"channel_name": channel.name}).set(channel.size / channel.capacity)
                self._report_threads_count()

            self._stopping_event.wait(5.0)

        with self._monitored_channels_lock:
            for channel in self._monitored_channels:
                self._channel_size_gauge.tags({"channel_name": channel.name}).set(0)
                self._channel_fill_gauge.tags({"channel_name": channel.name}).set(0)
            self._report_threads_count()

    def _report_threads_count(self) -> None:
        threads_by_type = defaultdict(int)
        for t in threading.enumerate():
            thread_type = t.name.split()[0].split(".")[0].split("-")[0]
            threads_by_type[thread_type] += 1

        for thread_type, threads_count in threads_by_type.items():
            self._threads_gauge.tags({"type": thread_type}).set(threads_count)

    def _pyroutine_stopped(self, pyroutine_id: int) -> None:
        with self._pyroutine_lock:
            del self._pyroutines[pyroutine_id]

    def _on_sigterm(self, signal_id: int, frame: Optional[FrameType]) -> None:
        self._diagnostics.info("pyroutine system detected SIGTERM, will shutdown")
        self.request_stop()

    def _monitor_channel(self, channel: Union[IChannel[M_T], IBlockingChannel[M_T]]) -> None:
        with self._monitored_channels_lock:
            self._monitored_channels.append(channel.stats())
