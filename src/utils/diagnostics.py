import time
import traceback
from collections import deque
from dataclasses import dataclass
from datetime import <PERSON><PERSON><PERSON>
from logging import Logger, get<PERSON><PERSON>ger
from threading import Lock
from typing import Any, Callable, Deque, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

import prometheus_client
from colorama import Fore, Style
from prometheus_client import Counter, Gauge
from prometheus_client.metrics import MetricWrapperBase
from prometheus_client.registry import R<PERSON><PERSON>TR<PERSON>
from prometheus_client.values import ValueClass

from src.utils.types import Number

T = TypeVar("T")

TagValues = Dict[str, str]
Tags = Tuple[str, ...]

WARNING_CACHE_TTL = 600  # 10 minutes
ERROR_CACHE_TTL = 3600  # 1 hour


def short_exception_name(e: Union[BaseException, Type[BaseException]]) -> str:
    if isinstance(e, BaseException):
        error_class = e.__class__
    else:
        error_class = e

    try:
        full_name = ".".join([error_class.__module__, error_class.__qualname__])
    except AttributeError:
        full_name = error_class.__name__
    pieces = full_name.split(".")
    return ".".join([module[0] for module in pieces[:-1]] + [pieces[-1]])


class ICounterMetric:
    def tags(self, values: TagValues) -> "ICounterMetric":
        raise NotImplementedError

    def forget(self, tags: TagValues) -> None:
        raise NotImplementedError

    def inc(self, value: Number) -> None:
        raise NotImplementedError


class IGaugeMetric:
    def tags(self, values: TagValues) -> "IGaugeMetric":
        raise NotImplementedError

    def forget(self, tags: TagValues) -> None:
        raise NotImplementedError

    def inc(self, value: Number) -> None:
        raise NotImplementedError

    def dec(self, value: Number) -> None:
        return self.inc(-value)

    def set(self, value: Number) -> None:
        raise NotImplementedError


class ISlidingWindowMetric:
    def tags(self, values: TagValues) -> "ISlidingWindowMetric":
        raise NotImplementedError

    def forget(self, tags: TagValues) -> None:
        raise NotImplementedError

    def observe(self, value: Number) -> None:
        raise NotImplementedError


class IMonitoring:
    def counter(self, name: str, tags: Tags = tuple()) -> ICounterMetric:
        raise NotImplementedError

    def gauge(self, name: str, tags: Tags = tuple()) -> IGaugeMetric:
        raise NotImplementedError

    def sliding_window(self, name: str, size: timedelta, tags: Tags = tuple()) -> ISlidingWindowMetric:
        raise NotImplementedError

    def warning(self, warning_msg: str, warning_target: str = "") -> None:
        raise NotImplementedError

    def error(self, error_type: str, error_target: str = "") -> None:
        raise NotImplementedError


class NullMonitoring(IMonitoring):
    def counter(self, name: str, tags: Tags = tuple()) -> ICounterMetric:
        return NullCounter()

    def gauge(self, name: str, tags: Tags = tuple()) -> IGaugeMetric:
        return NullGauge()

    def warning(self, warning_msg: str, warning_target: str = "") -> None:
        pass

    def error(self, error_type: str, error_target: str = "") -> None:
        pass


class NullCounter(ICounterMetric):
    def tags(self, values: TagValues) -> "ICounterMetric":
        return NullCounter()

    def forget(self, tags: TagValues) -> None:
        pass

    def inc(self, value: Number) -> None:
        pass


class NullGauge(IGaugeMetric):
    def tags(self, values: TagValues) -> "IGaugeMetric":
        return NullGauge()

    def forget(self, tags: TagValues) -> None:
        pass

    def inc(self, value: Number) -> None:
        pass

    def set(self, value: Number) -> None:
        pass


class Diagnostics:
    def __init__(
        self, log: Optional[Logger] = None, monitoring: IMonitoring = NullMonitoring(), debug_tags: Optional[Set[str]] = None
    ):
        self._log = log if log is not None else getLogger()
        self._monitoring = monitoring
        self._enabled_debug_tags: Set[str] = debug_tags or set()
        self.warning_cache_dict: Dict[int, float] = {}
        self.error_cache_dict: Dict[int, float] = {}
        self._extract_error_massage_hash: Callable[[str], int] = lambda e: hash(e)

    def child_scope(self, name: str) -> "Diagnostics":
        return Diagnostics(self._log.getChild(name), self._monitoring, self._enabled_debug_tags)

    def info(self, template: str, args: Tuple[Any, ...] = tuple()) -> None:
        self._log.info(template, *args)

    def warning(self, template: str, args: Tuple[Any, ...] = tuple(), target: str = "", log: bool = True) -> None:
        message = template % args
        self._monitoring.warning(message, target)

        if not log:
            return

        warning_hash = hash(f"{message}/{target}")
        now = time.time()
        if now - self.warning_cache_dict.get(warning_hash, 0) <= WARNING_CACHE_TTL:
            return
        self.warning_cache_dict[warning_hash] = now

        self._log.warning(template, *args)

    def critical(self, template: str, args: Tuple[Any, ...] = tuple()) -> None:
        self._log.critical(template, *args)

    def debug(self, template: str, args: Tuple[Any, ...] = tuple(), tags: Tags = tuple()) -> None:
        for tag in tags:
            if tag not in self._enabled_debug_tags:
                return

        self._log.debug(template, *args)

    def error(
        self,
        exception: BaseException,
        description: Optional[str] = None,
        target: str = "",
        stack_trace: bool = True,
        log: bool = True,
    ) -> None:
        """Cached error logs during 'ERROR_CACHE_TTL' period are not sent to stdout."""
        error_type = short_exception_name(exception)
        self._monitoring.error(error_type, target)

        if not log:
            return

        exception_hash = self._extract_error_massage_hash(str(exception))
        now = time.time()
        if now - self.error_cache_dict.get(exception_hash, 0) <= ERROR_CACHE_TTL:
            return
        self.error_cache_dict[exception_hash] = now

        log_message = self.get_error_log_message(error_type, exception, description, target, stack_trace)
        self._log.error(log_message)

    @staticmethod
    def get_error_log_message(
        error_type: str, exception: BaseException, description: Optional[str], target: str, stack_trace: bool
    ) -> str:
        exception_str = str(exception)
        target_fmt = "[{}] ".format(target) if len(target) > 0 else ""
        error_info_fmt = "{}{}".format(error_type, " -- {}".format(exception_str) if len(exception_str) > 0 else "")
        stack_trace_fmt = ":\n\n{}".format("".join(traceback.format_tb(exception.__traceback__))) if stack_trace else ""
        if description is not None:
            log_message = "{}{}{} -- {}{}{}".format(
                Fore.LIGHTRED_EX, target_fmt, description, error_info_fmt, stack_trace_fmt, Style.RESET_ALL
            )
        else:
            log_message = "{}{}{}{}{}".format(Fore.LIGHTRED_EX, target_fmt, error_info_fmt, stack_trace_fmt, Style.RESET_ALL)
        return log_message

    def error_ns(self, exception: BaseException, description: Optional[str] = None, target: str = "", log: bool = True) -> None:
        return self.error(exception, description, target, False, log)

    def set_log_level(self, level: Union[int, str]) -> None:
        self._log.setLevel(level)

    def is_debug_tag_enabled(self, tag: str) -> bool:
        return tag in self._enabled_debug_tags

    def enable_debug_tag(self, tag: str) -> None:
        self._enabled_debug_tags.add(tag)

    def disable_debug_tag(self, tag: str) -> None:
        self._enabled_debug_tags.remove(tag)

    def counter(self, name: str, tags: Tags = tuple()) -> ICounterMetric:
        return self._monitoring.counter(name, tags)

    def gauge(self, name: str, tags: Tags = tuple()) -> IGaugeMetric:
        return self._monitoring.gauge(name, tags)

    def sliding_window(self, name: str, size: timedelta, tags: Tags = tuple()) -> ISlidingWindowMetric:
        return self._monitoring.sliding_window(name, size, tags)

    def set_extract_error_message_hash_function(self, func: Callable[[str], int]) -> None:
        self._extract_error_massage_hash = func


class PrometheusMonitoring(IMonitoring):
    def __init__(self) -> None:
        self._names: Set[str] = set()
        self._counters: Dict[str, _PrometheusCounter] = {}
        self._gauges: Dict[str, _PrometheusGauge] = {}
        self._sliding_windows: Dict[str, _PrometheusSlidingWindow] = {}

        self._lock = Lock()
        self._total_error_counter = self.counter("application_errors")
        self._error_counter = self.counter("application_error_types", ("error_type", "error_target"))
        self._total_warning_counter = self.counter("application_warnings")

    def start(self, host: str, port: int, log: Logger) -> None:
        prometheus_client.start_http_server(port, addr=host)

    def counter(self, name: str, tags: Tags = tuple()) -> ICounterMetric:
        return self._construct(name, tags, lambda *args: _PrometheusCounter(*args), self._counters)

    def gauge(self, name: str, tags: Tags = tuple()) -> IGaugeMetric:
        return self._construct(name, tags, lambda *args: _PrometheusGauge(*args), self._gauges)

    def sliding_window(self, name: str, size: timedelta, tags: Tags = tuple()) -> ISlidingWindowMetric:
        return self._construct(name, tags, lambda *args: _PrometheusSlidingWindow(size, *args), self._sliding_windows)

    def error(self, error_type: str, error_target: str = "") -> None:
        self._total_error_counter.inc(1)
        self._error_counter.tags({"error_type": error_type, "error_target": error_target}).inc(1)

    def warning(self, warning_msg: str, warning_target: str = "") -> None:
        self._total_warning_counter.inc(1)

    def _construct(self, name: str, tags: Tags, ctor: Callable[[str, Tags], T], cache: Dict[str, T]) -> T:
        if name not in self._names:
            with self._lock:
                if name not in self._names:
                    cache[name] = ctor(name, tags)
                    self._names.add(name)

        if name not in cache:
            raise ValueError(f"metric of another type named `{name}` already exists")
        else:
            return cache[name]


class _PrometheusCounter(ICounterMetric):
    def __init__(self, name: str, tags: Tags):
        self._tags = tags
        self._counter = Counter(name, "", tags)

    def tags(self, values: TagValues) -> ICounterMetric:
        return _TaggedPrometheusCounter(self._counter, self._tags, values)

    def forget(self, tag_values: TagValues) -> None:
        self._counter.remove(*_get_prometheus_labels(tag_values, self._tags))

    def inc(self, value: Number) -> None:
        self._counter.inc(float(value))


class _TaggedPrometheusCounter(ICounterMetric):
    def __init__(self, counter: Counter, tags: Tags, tag_values: TagValues):
        self._tag_values = tag_values
        self._counter = counter.labels(*_get_prometheus_labels(tag_values, tags))

    def inc(self, value: Number) -> None:
        self._counter.inc(float(value))


class _PrometheusGauge(IGaugeMetric):
    def __init__(self, name: str, tags: Tags):
        self._tags = tags
        self._gauge = Gauge(name, "", tags)

    def tags(self, values: TagValues) -> IGaugeMetric:
        return _TaggedPrometheusGauge(self._gauge, self._tags, values)

    def forget(self, tag_values: TagValues) -> None:
        self._gauge.remove(*_get_prometheus_labels(tag_values, self._tags))

    def inc(self, value: Number) -> None:
        self._gauge.inc(float(value))

    def set(self, value: Number) -> None:
        self._gauge.set(float(value))


class _TaggedPrometheusGauge(IGaugeMetric):
    def __init__(self, gauge: Gauge, tags: Tags, tag_values: TagValues):
        self._tag_values = tag_values
        self._gauge = gauge.labels(*_get_prometheus_labels(tag_values, tags))

    def inc(self, value: Number) -> None:
        self._gauge.inc(float(value))

    def set(self, value: Number) -> None:
        self._gauge.set(float(value))


class _SlidingWindow(MetricWrapperBase):
    _type = "unknown"

    @dataclass(frozen=True)
    class _DataPoint:
        value: float
        time: float

    def __init__(
        self,
        window_size: timedelta,
        name: str,
        labelnames: Tags = (),
        namespace: str = "",
        subsystem: str = "",
        unit: str = "",
        registry: Any = REGISTRY,
        labelvalues: Any = None,
    ):
        self._window_size = window_size.total_seconds()
        super().__init__(
            name=name,
            documentation="",
            labelnames=labelnames,
            namespace=namespace,
            subsystem=subsystem,
            unit=unit,
            registry=registry,
            _labelvalues=labelvalues,
        )
        self._kwargs["window_size"] = window_size

        self._history: Deque[_SlidingWindow._DataPoint] = deque()
        self._lock = Lock()

    def _metric_init(self) -> None:
        self._value = ValueClass(self._type, self._name, self._name, self._labelnames, self._labelvalues, "")

    def observe(self, value: Number) -> None:
        with self._lock:
            self._history.append(_SlidingWindow._DataPoint(float(value), time.time()))
            self._cull_history()

    def _child_samples(self):  # type: ignore
        with self._lock:
            values = [point.value for point in self._history]

        if len(values) > 0:
            values.sort()

            count = len(values)
            average = sum(values) / count
            median = values[(count // 2 - 1) if count % 2 == 0 else ((count - 1) // 2)]
            minimum = values[0]
            maximum = values[-1]

            return (
                ("_count", {}, count, None, None),
                ("_median", {}, median, None, None),
                ("_average", {}, average, None, None),
                ("_minimum", {}, minimum, None, None),
                ("_maximum", {}, maximum, None, None),
            )
        else:
            return (("_count", {}, 0, None, None),)

    def _cull_history(self) -> None:
        time_now = time.time()

        while len(self._history) > 0:
            point = self._history[0]

            if time_now - point.time > self._window_size:
                self._history.popleft()
            else:
                break


class _PrometheusSlidingWindow(ISlidingWindowMetric):
    def __init__(self, size: timedelta, name: str, tags: Tags):
        self._tags = tags
        self._sliding_window = _SlidingWindow(size, name, tags)

    def tags(self, values: TagValues) -> ISlidingWindowMetric:
        return _TaggedPrometheusSlidingWindow(self._sliding_window, self._tags, values)

    def forget(self, tag_values: TagValues) -> None:
        self._sliding_window.remove(*_get_prometheus_labels(tag_values, self._tags))

    def observe(self, value: Number) -> None:
        self._sliding_window.observe(value)


class _TaggedPrometheusSlidingWindow(ISlidingWindowMetric):
    def __init__(self, sliding_window: _SlidingWindow, tags: Tags, tag_values: TagValues):
        self._tag_values = tag_values
        self._sliding_window = sliding_window.labels(*_get_prometheus_labels(tag_values, tags))

    def observe(self, value: Number) -> None:
        self._sliding_window.observe(value)


def _get_prometheus_labels(values: TagValues, tags: Tags) -> List[str]:
    labels = []

    for index, tag in enumerate(tags):
        if tag in values:
            labels.append(values[tag])
        else:
            labels.append("")

    return labels
