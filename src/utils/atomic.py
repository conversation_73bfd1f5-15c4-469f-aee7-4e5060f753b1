from threading import Lock
from typing import Generic

from src.utils.types import Number


class _AtomicNumber(Generic[Number]):
    def __init__(self, value: Number) -> None:
        self._value: Number = value
        self._lock = Lock()

    def get(self) -> Number:
        with self._lock:
            return self._value

    def set(self, value: Number) -> None:
        with self._lock:
            self._value = value

    def fetch_add(self, other_value: Number) -> Number:
        with self._lock:
            result = self._value
            self._value += other_value
            return result


AtomicInt = _AtomicNumber[int]
