from threading import Event
from typing import Callable, Generic, List, TypeVar


T = TypeVar("T")
EventListener = Callable[[T], None]


class EventEmitter(Generic[T]):
    def __init__(self) -> None:
        self._listeners: List[EventListener[T]] = []

    def add_listener(self, listener: EventListener[T]) -> None:
        if listener in self._listeners:
            raise ValueError("listener {} already added")
        else:
            self._listeners.append(listener)

    def trigger(self, event: T) -> None:
        for listener in self._listeners[:]:
            listener(event)


class IEventDispatcher:
    def listen(self, event: Event) -> None:
        raise NotImplementedError

    def notify(self) -> None:
        raise NotImplementedError


class DispatchedEvent(Event):
    def __init__(self, dispatcher: IEventDispatcher) -> None:
        super().__init__()
        dispatcher.listen(self)
        self._dispatcher = dispatcher

    def set(self) -> None:
        super().set()
        self._dispatcher.notify()

    def clear(self) -> None:
        super().clear()
        self._dispatcher.notify()


class OrEvent(Event, IEventDispatcher):
    """Consolidated event that is
    - fired when any `self._underlying_events` is set
    - cleared when all `self._underlying_events` are cleared
    """

    def __init__(self):
        super().__init__()
        self._underlying_events: List[DispatchedEvent] = []

    def listen(self, event: DispatchedEvent) -> None:
        self._underlying_events.append(event)

    def notify(self) -> None:
        """Update event state when notified"""
        for event in self._underlying_events:
            if event.is_set():
                self.set()
                break
        else:
            self.clear()
