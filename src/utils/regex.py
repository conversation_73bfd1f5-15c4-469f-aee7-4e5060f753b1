import functools
import re
from typing import List


class RegexList:
    NO_MATCH = ["(?!x)x"]
    ALWAYS_MATCH = [".*"]

    def __init__(self, patterns: List[str]) -> None:
        self._patterns = patterns
        self._re = re.compile("|".join(patterns))

    @functools.lru_cache(maxsize=None)
    def match(self, text: str) -> bool:
        return bool(self._re.fullmatch(text))

    def __getitem__(self, index: int) -> str:
        return self._patterns[index]

    def __bool__(self) -> bool:
        return bool(self._patterns)
