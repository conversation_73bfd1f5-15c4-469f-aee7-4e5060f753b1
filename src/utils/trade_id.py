import ctypes
from datetime import datetime, timed<PERSON>ta
from decimal import Decima<PERSON>
from typing import Dict, Optional

from expiringdict import ExpiringDict  # type: ignore

# The trades DB schema requires each trade have a unique trade_id for a given market (i.e. (exchange_id, symbol)).
# In the past a number of ad-hoc solutions have been used, like int(trade_time).  The problem with trade_time is
# the exchange can issue two or more trades with the same timestamp and only the first one will be saved in the DB.
#
# There is a further problem if the exchange issues multiple identical trades with the same timestamp.  The solution
# here is the (typically five minute) cache per instrument that creates new trade_ids for identical trades with the
# same timestamp.

# Note: this algorithm cannot be used for historical trade scrapers or http interfaces that return the prior N trades.
# In these instances, the same trades are often returned call after call and this algorithm will create new trade IDs
# each time resulting in storing duplicate trades.  The best that can be done in this instance is to call
# _make_trade_id() directly.

# Note: the LBank exchange code solves this problem in a similar manner.
#
# Note also: When fixing an existing bad trade_id implementation, the new and old algorithms cannot run simultaneously.
# See bybit.py and lbank.py archives for a time based solution to this problem.


class TradeId:
    def __init__(self, max_len: int, max_age_seconds: int) -> None:
        self._cache: Dict[str, ExpiringDict[int, int]] = {}
        self._max_len = max_len
        self._max_age_seconds = max_age_seconds
        self._last_flush: Dict[str, datetime] = {}

    def make_trade_id(self, instrument: str, timestamp: str, price: Decimal, amount: Decimal, is_buy: Optional[bool]) -> int:
        # This method doesn't support data with duplicates (e.g. our usual HTTP data)
        if self._cache.get(instrument) is None:
            self._cache[instrument] = ExpiringDict(max_len=self._max_len, max_age_seconds=self._max_age_seconds)
            self._last_flush[instrument] = datetime.now()

        trade_id = _make_trade_id(timestamp, price, amount, is_buy)
        counter = self._cache[instrument].get(trade_id, -1) + 1
        self._cache[instrument][trade_id] = counter
        if counter > 0:
            trade_id = int(str(trade_id) + str(counter))

        # Every max_age_seconds, access all the timestamps for this instrument to force expiry (yuk)
        now = datetime.now()
        if instrument not in self._last_flush or (now - self._last_flush[instrument] > timedelta(seconds=self._max_age_seconds)):
            self._last_flush[instrument] = now
            for entry in list(self._cache[instrument]):
                _ = self._cache[instrument].get(entry, -1)
        return trade_id

    def clean_cache(self, instrument: str):
        self._cache[instrument] = ExpiringDict(max_len=self._max_len, max_age_seconds=self._max_age_seconds)


def _make_trade_id(prefix: str, price: Decimal, amount: Decimal, is_buy: Optional[bool]) -> int:
    """
    Used directly by http and historical feed handlers for exchanges that
    don't provide unique trade_id with their trade data.

    !!! DO NOT CHANGE
    To preserve history of id calculation logic with method shouldn't be changed.
    Preserving id calculation logic is crucial for data integrity in case of history backfill.
    Example: Bybit spot trades
    """
    side = 2
    if is_buy is not None:
        side = 0 if is_buy else 1
    hash_tuple = (price, amount, side)
    trade_hash = ctypes.c_size_t(hash(hash_tuple))
    return int(f"{prefix}{trade_hash.value}")
