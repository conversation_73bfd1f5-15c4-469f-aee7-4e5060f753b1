import operator
from datetime import datetime, timedelta, timezone
from typing import Any, Callable, Generic, Iterator, List, Optional, Protocol, Tuple, TypeVar

import ciso8601
import pytz

T = TypeVar("T")
DateCompare = Callable[[datetime, datetime], Any]
EARLIEST_ALLOWED_DATE = pytz.utc.localize(datetime(2000, 1, 1))


class ProvideCurrentTime(Protocol):
    def __call__(self) -> datetime: ...


def get_epoch_dt(value: datetime) -> datetime:
    tzinfo = None if value.tzinfo is None or value.tzinfo.utcoffset(value) is None else timezone.utc
    return datetime(1970, 1, 1, tzinfo=tzinfo)


def dt_to_s(value: datetime) -> int:
    delta = value - get_epoch_dt(value)
    return delta // timedelta(seconds=1)


def dt_to_s_ceil(value: datetime) -> int:
    delta = value - get_epoch_dt(value)
    seconds = delta // timedelta(seconds=1)
    return seconds + 1 if delta.microseconds > 0 else seconds


def dt_to_ms(value: datetime) -> int:
    delta = value - get_epoch_dt(value)
    return delta // timedelta(milliseconds=1)


def dt_to_us(value: datetime) -> int:
    delta = value - get_epoch_dt(value)
    return delta // timedelta(microseconds=1)


def td_to_us(value: timedelta) -> int:
    return value // timedelta(microseconds=1)


def dt_from_s(seconds: int) -> datetime:
    return datetime.utcfromtimestamp(seconds)


def dt_from_ms(value: int) -> datetime:
    """safe creation of date time from milliseconds"""
    seconds = value // 1000
    milliseconds = value % 1000
    return datetime.utcfromtimestamp(seconds) + timedelta(milliseconds=milliseconds)


def dt_from_us(value: int) -> datetime:
    """safe creation of date time from microseconds"""
    seconds = value // 1000000
    microseconds = value % 1000000
    milliseconds = microseconds // 1000
    microseconds = microseconds % 1000
    return datetime.utcfromtimestamp(seconds) + timedelta(milliseconds=milliseconds, microseconds=microseconds)


def dt_from_any(value: Any) -> datetime:
    date_str = str(value)
    if "-" in date_str:
        result = ciso8601.parse_datetime_as_naive(date_str)
        if dt_compare(result, operator.lt, EARLIEST_ALLOWED_DATE):
            raise ValueError(f"dt_from_any: value before {EARLIEST_ALLOWED_DATE}: {value}")
        return result
    # Assume Unix Epoch, using the integer portion to determine timescale
    # Note: Only supports Unix timestamps between 2001-09-09T01:46:40Z and 2286-11-20T17:46:39Z (inclusive)
    int_portion = str(int(float(value) // 1))
    if len(int_portion) == 10:
        return dt_from_us(int(float(value) * 1_000_000))
    if len(int_portion) == 13:
        return dt_from_us(int(float(value) * 1_000))
    if len(int_portion) == 16:
        return dt_from_us(int(value))
    if len(int_portion) == 19:
        return dt_from_us(int(value) // 1_000)
    raise ValueError(f"dt_from_any: Cannot decode datetime from: {date_str}")


def dt_from_any_aware(value: Any) -> datetime:
    return dt_to_aware(dt_from_any(value))


def dt_to_aware(dt: datetime) -> datetime:
    return pytz.utc.localize(dt) if dt.tzinfo is None else dt


# dt_from_*_aware versions compare cleanly with DB TIMESTAMPTZ data; naive datetime doesn't
def dt_from_s_aware(value: int) -> datetime:
    return dt_to_aware(dt_from_s(value))


def dt_from_ms_aware(value: int) -> datetime:
    return dt_to_aware(dt_from_ms(value))


def dt_from_us_aware(value: int) -> datetime:
    return dt_to_aware(dt_from_us(value))


def td_from_us(value: int) -> timedelta:
    seconds = value // 1000000
    microseconds = value % 1000000
    milliseconds = microseconds // 1000
    microseconds = microseconds % 1000
    return timedelta(seconds=seconds, milliseconds=milliseconds, microseconds=microseconds)


def dt_compare(a: datetime, op: DateCompare, b: datetime) -> Any:
    return op(dt_to_aware(a), dt_to_aware(b))


def align_dt_to_interval(dt: datetime, interval: timedelta) -> datetime:
    interval_count = dt_to_interval_count(dt, interval)
    return datetime(1970, 1, 1, tzinfo=dt.tzinfo) + (interval_count * interval)


def dt_to_interval_count(dt: datetime, interval: timedelta) -> int:
    date_microseconds = dt_to_us(dt)
    interval_microseconds = dt_to_us(datetime(1970, 1, 1, tzinfo=dt.tzinfo) + interval)
    return date_microseconds // interval_microseconds


# open_interest_timestamp, and the truncate functions below are used for deduplication DB time keys.


def open_interest_timestamp() -> datetime:
    """Used by open interest when exchange time is not unique across calls"""
    return truncate_to_minute(datetime.now(tz=timezone.utc))


def truncate_to_minute(tm: datetime) -> datetime:
    return tm.replace(second=0, microsecond=0)


def truncate_to_n_seconds(tm: datetime, n_seconds: int) -> datetime:
    if n_seconds <= 0:
        return tm
    return datetime.fromtimestamp(tm.timestamp() // n_seconds * n_seconds, tz=tm.tzinfo)


class TimeSeries(Generic[T]):
    def __init__(self, first_date: datetime, step: timedelta, values: List[Optional[T]]):
        assert align_dt_to_interval(first_date, step) == first_date

        self._first_date = first_date
        self._step = step

        self._first_count = dt_to_interval_count(self._first_date, self._step)

        self._values: List[Optional[T]] = []
        self._values.extend(values)

    def __repr__(self) -> str:
        return "Time series starting at {} with interval {} that has {} element(s)".format(
            self._first_date, self._step, len(self._values)
        )

    def __iter__(self) -> Iterator[Tuple[datetime, Optional[T]]]:
        current_date = self._first_date
        for i in range(len(self._values)):
            yield current_date, self._values[i]
            current_date += self._step

    @property
    def step(self) -> timedelta:
        return self._step

    def at(self, moment: datetime) -> Optional[T]:
        interval_count = dt_to_interval_count(moment, self._step)
        offset = interval_count - self._first_count
        if offset < 0 or offset >= len(self._values):
            raise ValueError("{} is out of bounds: {}".format(moment, self.bounds()))
        else:
            return self._values[offset]

    def bounds(self) -> Tuple[datetime, datetime]:
        return self._first_date, self._first_date + self._step * (len(self._values) - 1)

    def extend_bounds(self, min_bound: datetime, max_bound: datetime) -> "TimeSeries[T]":
        old_min_bound, old_max_bound = self.bounds()
        new_min_bound = min(align_dt_to_interval(min_bound, self._step), old_min_bound)
        new_max_bound = max(align_dt_to_interval(max_bound, self._step), old_max_bound)

        min_interval_count = dt_to_interval_count(new_min_bound, self._step)
        max_interval_count = dt_to_interval_count(new_max_bound, self._step)

        values = []
        for interval_count in range(max_interval_count - min_interval_count + 1):
            current_count = min_interval_count + interval_count
            if self._first_count <= current_count < self._first_count + len(self._values):
                values.append(self._values[current_count - self._first_count])
            else:
                values.append(None)
        return TimeSeries[T](datetime(1970, 1, 1) + self._step * min_interval_count, self._step, values)

    def trim(self) -> "TimeSeries[T]":
        rightmost_left_null_index = None
        leftmost_right_null_index = None

        for index, value in enumerate(self._values):
            if value is not None:
                break
            else:
                rightmost_left_null_index = index

        for index, value in enumerate(reversed(self._values)):
            if value is not None:
                break
            else:
                leftmost_right_null_index = len(self._values) - index - 1

        if rightmost_left_null_index is None:
            if leftmost_right_null_index is None:
                first_date = self._first_date
                values = self._values[:]
            else:
                first_date = self._first_date
                values = self._values[:leftmost_right_null_index]
        else:
            if leftmost_right_null_index is None:
                first_date = self._first_date + self._step * (rightmost_left_null_index + 1)
                values = self._values[rightmost_left_null_index + 1 :]
            else:
                first_date = self._first_date + self._step * (rightmost_left_null_index + 1)
                values = self._values[rightmost_left_null_index + 1 : leftmost_right_null_index]

        return TimeSeries[T](first_date, self._step, values)

    def fill_nulls_via_interpolation(self, interpolate: Callable[[float, T, T], T]) -> "TimeSeries[T]":
        assert len(self._values) > 0
        assert self._values[0] is not None and self._values[-1] is not None

        values: List[Optional[T]] = []
        last_non_null_index = -1

        for index, value in enumerate(self._values):
            if last_non_null_index < 0:
                if value is not None:
                    values.append(value)
                else:
                    last_non_null_index = index - 1
            else:
                if value is not None:
                    gap_size = index - last_non_null_index - 1
                    for i in range(gap_size):
                        weight = (i + 1) / (gap_size + 1)
                        left_value = values[last_non_null_index]
                        assert left_value is not None
                        interpolated_value = interpolate(weight, left_value, value)
                        values.append(interpolated_value)

                    values.append(value)
                    last_non_null_index = -1
                else:
                    continue

        return TimeSeries[T](self._first_date, self._step, values)


def merge_time_series(series: List[TimeSeries[T]]) -> TimeSeries[List[Optional[T]]]:
    bounds = None
    step = None
    for element in series:
        if bounds is None:
            bounds = element.bounds()
        else:
            assert bounds == element.bounds()

        if step is None:
            step = element.step
        else:
            assert step == element.step
    assert bounds is not None and step is not None

    values: List[Optional[List[Optional[T]]]] = []
    current_time = bounds[0]

    while current_time <= bounds[1]:
        merged_value = []
        for element in series:
            merged_value.append(element.at(current_time))
        values.append(merged_value)
        current_time += step

    return TimeSeries(bounds[0], step, values)


def get_ciso8601(d: datetime) -> str:
    return datetime.strftime(d, "%Y-%m-%dT%H:%M:%S.%f%z")


def hour_floor(d: datetime) -> datetime:
    return d.replace(minute=0, second=0, microsecond=0, hour=d.hour)
