from collections import defaultdict
from typing import Callable, DefaultDict, Dict, FrozenSet, Iterable, Iterator, List, Set, TypeVar, Union

A = TypeVar("A")
T = TypeVar("T")
R = TypeVar("R")
Key_T = TypeVar("Key_T")


def batchify(items: Iterable[T], batch_size: int) -> Iterator[List[T]]:
    batch = []

    for item in items:
        batch.append(item)

        if len(batch) >= batch_size:
            yield batch
            batch = []

    if len(batch) > 0:
        yield batch


def flatten(iterables: Iterable[Iterable[T]]) -> List[T]:
    result: List[T] = []

    for iterable in iterables:
        result.extend(it for it in iterable)

    return result


def merge_sets(sets: Iterable[Union[Set[T], FrozenSet[T]]]) -> Set[T]:
    result = set()

    for s in sets:
        for item in s:
            result.add(item)

    return result


def split_into_sets_by_key(items: Iterable[T], key: Callable[[T], Key_T]) -> Dict[Key_T, Set[T]]:
    result: DefaultDict[Key_T, Set[T]] = defaultdict(set)

    for i in items:
        result[key(i)].add(i)

    return result


def compose_1(first: Callable[[A], T], second: Callable[[T], R]) -> Callable[[A], R]:
    def proc(arg: A) -> R:
        return second(first(arg))

    return proc
