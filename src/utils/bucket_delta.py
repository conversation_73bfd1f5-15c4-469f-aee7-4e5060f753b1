from dataclasses import dataclass
from typing import Dict, Generic, List, Set, TypeVar

from src.utils.ops import batchify

BucketType = TypeVar("BucketType")
ItemType = TypeVar("ItemType")


@dataclass(frozen=True)
class BucketDelta(Generic[BucketType, ItemType]):
    delete_buckets: List[BucketType]
    add_buckets: List[List[ItemType]]


def compute_bucket_delta(
    input_buckets: Dict[BucketType, Set[ItemType]], additions: Set[ItemType], deletions: Set[ItemType], bucket_size: int
) -> BucketDelta[BucketType, ItemType]:
    assert len(additions.intersection(deletions)) == 0

    mutated_buckets = set()
    buckets = {bucket: set(items) for bucket, items in input_buckets.items()}

    for deleted_item in deletions:
        for bucket, items in buckets.items():
            if deleted_item in items:
                mutated_buckets.add(bucket)
                items.remove(deleted_item)
                break

    sorted_not_full_buckets = sorted(
        [(bucket, items) for bucket, items in buckets.items() if len(items) < bucket_size],
        key=lambda pair: (0 if pair[0] in mutated_buckets else 1, len(pair[1])),
    )

    current_bucket_index = 0
    processed_additions: Set[ItemType] = set()

    for added_item in additions:
        if current_bucket_index >= len(sorted_not_full_buckets):
            break

        bucket, bucket_items = sorted_not_full_buckets[current_bucket_index]

        mutated_buckets.add(bucket)
        bucket_items.add(added_item)
        processed_additions.add(added_item)

        if len(bucket_items) >= bucket_size:
            current_bucket_index += 1

    excess_buckets = [b for b in batchify(list(additions.difference(processed_additions)), bucket_size)]
    updated_buckets = [list(buckets[bucket]) for bucket in mutated_buckets if len(buckets[bucket]) > 0]
    return BucketDelta(list(mutated_buckets), updated_buckets + excess_buckets)
