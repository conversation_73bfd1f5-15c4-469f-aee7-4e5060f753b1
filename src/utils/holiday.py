from datetime import datetime, timedelta
from enum import Enum, auto
from typing import Set

from src.resources.holidays import glib_holidays


class HolidayCalendar(Enum):
    NONE = auto()
    WEEKDAY = auto()
    NYSE = auto()  # New York Stock Exchange - https://www.nyse.com/markets/hours-calendars
    USD = auto()  # Federal Reserve - https://www.frbservices.org/about/holiday-schedules
    # More to come - use ISO-10383 market identifier codes?


class Calendar:
    """
    At present the Calendar class has one function: is_trade_date(datetime).
    The omission of is_holiday(datetime) == not is_trade_date() is intentional because it is better to
    structure logic around trade dates than around holidays.

    """

    def __init__(self, calendar: HolidayCalendar) -> None:
        self._calendar: HolidayCalendar = calendar
        self._holidays = self._load_holidays()

        if any(self._holidays):
            # Note: this assumes that holidays are fully specified for every year in [min_year-01-01 to max_year-12-31]
            self._min_holiday_dt = datetime(min(self._holidays).year, 1, 1)
            self._max_holiday_dt = datetime(max(self._holidays).year, 12, 31)

    def __repr__(self) -> str:
        return f"{self._calendar}:[{self._min_holiday_dt}..{self._max_holiday_dt}]"

    def _load_holidays(self) -> Set[datetime]:
        if self._calendar == HolidayCalendar.NYSE:
            return glib_holidays().get_holiday_dates("nyse")
        if self._calendar == HolidayCalendar.USD:
            return glib_holidays().get_holiday_dates("usd")
        return set()

    def is_trading_day(self, dt: datetime) -> bool:
        if self._calendar == HolidayCalendar.NONE:
            return True

        # Assume all calendars do not trade on weekends - true for most major western markets
        day = dt.strftime("%A")
        if day == "Saturday" or day == "Sunday":
            return False
        if not any(self._holidays):
            return True

        date = datetime(dt.year, dt.month, dt.day)
        if date < self._min_holiday_dt or date > self._max_holiday_dt:
            raise Exception(f"No holidays specified for {date} in {self}")
        return date not in self._holidays

    def add_trade_days(self, dt: datetime, n_days: int) -> datetime:
        """
        n_days can be positive, negative or zero.
        n_days == zero returns first trade date <= dt (i.e. backs up to prior trade date if today is weekend or holiday)
        n_days is bijective - i.e. add_trade_dates(add_trade_dates(dt, -10), +10) == dt.
        """
        while not self.is_trading_day(dt):  # Ensure we are starting on a trade day
            dt = dt + timedelta(days=-1)

        delta = timedelta(days=1)
        if n_days < 0:
            delta = timedelta(days=-1)
            n_days = -n_days

        for day in range(n_days):
            while True:  # Move one trade day
                dt += delta
                if self.is_trading_day(dt):
                    break

        return dt
