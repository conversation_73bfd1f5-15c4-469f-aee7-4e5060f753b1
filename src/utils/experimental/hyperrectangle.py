from typing import List, Optional, Tuple


class ZHyperRectangle:
    def __init__(self, bounds: List[Tuple[int, int]]):
        self._bounds: List[Tuple[int, int]] = []
        self._dimension = len(bounds)

        for a, b in bounds:
            if a > b:
                a, b = b, a
            self._bounds.append((a, b))

    def __repr__(self) -> str:
        return "{}".format(str(self._bounds))

    @property
    def dimension(self) -> int:
        return self._dimension

    @property
    def bounds(self) -> List[Tuple[int, int]]:
        return self._bounds


class SparseZHyperRectangle:
    def __init__(self, ranges: List[List[Tuple[int, int]]]) -> None:
        self._dimension = len(ranges)
        self._ranges = [r for r in ranges]

    def __repr__(self) -> str:
        return "{}".format(str(self._ranges))

    @property
    def dimension(self) -> int:
        return self._dimension

    @property
    def ranges(self) -> List[List[Tuple[int, int]]]:
        return self._ranges


class ZDisjointSet:
    def __init__(self, dimension: int, rectangles: List[ZHyperRectangle]):
        self._dimension = dimension
        self._rectangles = [r for r in rectangles]

    def __repr__(self) -> str:
        rectangles = "\n".join([str(r) for r in self._rectangles])
        return "{}-dimensional rects:\n{}".format(self._dimension, rectangles)

    @property
    def dimension(self) -> int:
        return self._dimension

    @property
    def rectangles(self) -> List[ZHyperRectangle]:
        return self._rectangles


def set_product(a: ZDisjointSet, b: ZDisjointSet) -> ZDisjointSet:
    dimension = a.dimension + b.dimension
    rectangles = []
    for ra in a.rectangles:
        for rb in b.rectangles:
            product_bounds = []
            product_bounds.extend(ra.bounds)
            product_bounds.extend(rb.bounds)
            rectangles.append(ZHyperRectangle(product_bounds))
    return ZDisjointSet(dimension, rectangles)


def cut_set_from_set(target: ZDisjointSet, cutter: ZDisjointSet) -> ZDisjointSet:
    assert target.dimension == cutter.dimension
    current_target_rectangles = target.rectangles
    for cutter_r in cutter.rectangles:
        new_target_rectangles = []
        for target_r in current_target_rectangles:
            pieces = cut_zhyperrectangle_by_other(target_r, cutter_r)
            new_target_rectangles.extend(pieces)
        current_target_rectangles = new_target_rectangles
    return ZDisjointSet(target.dimension, current_target_rectangles)


def intersect_sets(a: ZDisjointSet, b: ZDisjointSet) -> ZDisjointSet:
    assert a.dimension == b.dimension
    rectangles = []
    for ra in a.rectangles:
        for rb in b.rectangles:
            intersection = intersect_zhyperrectangles(ra, rb)
            if intersection is not None:
                rectangles.append(intersection)
    return ZDisjointSet(a.dimension, rectangles)


def intersect_zhyperrectangles(a: ZHyperRectangle, b: ZHyperRectangle) -> Optional[ZHyperRectangle]:
    assert a.dimension == b.dimension
    bounds = []
    for bounds_a, bounds_b in zip(a.bounds, b.bounds):
        intersection_min = max(bounds_a[0], bounds_b[0])
        intersection_max = min(bounds_a[1], bounds_b[1])
        if intersection_min > intersection_max:
            return None
        else:
            bounds.append((intersection_min, intersection_max))
    return ZHyperRectangle(bounds)


def is_zhyperrectangle_inside(needle: ZHyperRectangle, haystack: ZHyperRectangle) -> bool:
    assert needle.dimension == haystack.dimension
    for bounds_needle, bounds_haystack in zip(needle.bounds, haystack.bounds):
        if bounds_needle[0] < bounds_haystack[0] or bounds_needle[1] > bounds_haystack[1]:
            return False
    return True


def cut_zhyperrectangle_by_other(target: ZHyperRectangle, cutter: ZHyperRectangle) -> List[ZHyperRectangle]:
    intersection = intersect_zhyperrectangles(target, cutter)
    if intersection is None:
        return [target]

    result = []
    for dimension_index, cutter_bounds in enumerate(cutter.bounds):
        remains, cut = cut_zhyperrectangle_by_dimension(target, dimension_index, cutter_bounds[0] - 1, False)
        if remains is not None and cut is not None:
            result.append(cut)
            target = remains
        elif remains is None and cut is not None:
            break

        remains, cut = cut_zhyperrectangle_by_dimension(target, dimension_index, cutter_bounds[1] + 1, True)
        if remains is not None and cut is not None:
            result.append(cut)
            target = remains

    return result


def zhyperrectangle_new_bounds(target: ZHyperRectangle, dimension_index: int, new_bounds: Tuple[int, int]) -> ZHyperRectangle:
    all_new_bounds = []
    for index, bounds in enumerate(target.bounds):
        if index == dimension_index:
            all_new_bounds.append(new_bounds)
        else:
            all_new_bounds.append(bounds)
    return ZHyperRectangle(all_new_bounds)


def cut_zhyperrectangle_by_dimension(
    target: ZHyperRectangle, dimension_index: int, cutter: int, right_cut: bool
) -> Tuple[Optional[ZHyperRectangle], Optional[ZHyperRectangle]]:
    assert target.dimension > dimension_index
    target_bounds = target.bounds[dimension_index]
    new_bounds = None
    cut_bounds = None
    if not right_cut:
        if cutter < target_bounds[1]:
            new_bounds = (max(cutter + 1, target_bounds[0]), target_bounds[1])
        if cutter >= target_bounds[0]:
            cut_bounds = (target_bounds[0], cutter)
    else:
        if cutter > target_bounds[0]:
            new_bounds = (target_bounds[0], min(cutter - 1, target_bounds[1]))
        if cutter <= target_bounds[1]:
            cut_bounds = (cutter, target_bounds[1])

    if new_bounds is None:
        if cut_bounds is None:
            assert False
        else:
            return None, target
    else:
        if cut_bounds is None:
            return target, None
        else:
            remaining = zhyperrectangle_new_bounds(target, dimension_index, new_bounds)
            cut = zhyperrectangle_new_bounds(target, dimension_index, cut_bounds)
            return remaining, cut


def try_merge_non_intersecting_zhyperrectangles(a: ZHyperRectangle, b: ZHyperRectangle) -> Optional[ZHyperRectangle]:
    assert a.dimension == b.dimension
    diff_dimension_index = None
    b_bounds = b.bounds
    for index, a_bound in enumerate(a.bounds):
        b_bound = b_bounds[index]
        if a_bound != b_bound:
            if diff_dimension_index is not None:
                return None
            else:
                diff_dimension_index = index

    assert diff_dimension_index is not None
    a_bound = a.bounds[diff_dimension_index]
    b_bound = b_bounds[diff_dimension_index]

    if a_bound[1] + 1 == b_bound[0]:
        merged_bound: Optional[Tuple[int, int]] = (a_bound[0], b_bound[1])
    elif a_bound[0] == b_bound[1] + 1:
        merged_bound = (b_bound[0], a_bound[1])
    else:
        merged_bound = None

    if merged_bound is None:
        return None
    else:
        new_bounds = []
        for index, a_bound in enumerate(a.bounds):
            if index != diff_dimension_index:
                new_bounds.append(a_bound)
            else:
                new_bounds.append(merged_bound)
        return ZHyperRectangle(new_bounds)


def try_merge_set_rectangles(s: ZDisjointSet) -> ZDisjointSet:
    current_set = [r for r in s.rectangles]
    while True:
        merge_index_a, merge_index_b = None, None
        merged = None
        for i, a in enumerate(current_set):
            for j in range(i + 1, len(current_set)):
                b = current_set[j]
                merged = try_merge_non_intersecting_zhyperrectangles(a, b)
                if merged is not None:
                    merge_index_a = i
                    merge_index_b = j
                    break
            if merge_index_a is not None:
                break

        if merge_index_a is None or merge_index_b is None or merged is None:
            break
        else:
            current_set[merge_index_a] = merged
            current_set[merge_index_b] = current_set[-1]
            current_set.pop()
    return ZDisjointSet(s.dimension, current_set)


def try_merge_sparse_zhyperrectangles(a: SparseZHyperRectangle, b: SparseZHyperRectangle) -> Optional[SparseZHyperRectangle]:
    assert a.dimension == b.dimension

    diff_dimension_index = None
    for index, ranges_a in enumerate(a.ranges):
        ranges_b = b.ranges[index]
        if len(ranges_a) != len(ranges_b):
            if diff_dimension_index is not None:
                return None
            else:
                diff_dimension_index = index
            continue

        for ra, rb in zip(ranges_a, ranges_b):
            if ra != rb:
                if diff_dimension_index is not None:
                    return None
                else:
                    diff_dimension_index = index
                    break

    assert diff_dimension_index is not None
    merged_ranges = []
    ranges_a = a.ranges[diff_dimension_index]
    ranges_b = b.ranges[diff_dimension_index]
    merged_set = set()
    for min_a, max_a in ranges_a:
        for i in range(min_a, max_a + 1):
            merged_set.add(i)
    for min_b, max_b in ranges_b:
        for i in range(min_b, max_b + 1):
            merged_set.add(i)
    members = sorted(list(merged_set))

    current_begin = None
    current_end = None
    for member in members:
        if current_begin is None:
            current_begin = member
            current_end = member
        else:
            if member == current_end + 1:
                current_end = member
            else:
                merged_ranges.append((current_begin, current_end))
                current_begin = member
                current_end = member
    assert current_begin is not None and current_end is not None
    merged_ranges.append((current_begin, current_end))

    new_ranges = []
    for i in range(a.dimension):
        if i != diff_dimension_index:
            new_ranges.append(a.ranges[i])
        else:
            new_ranges.append(merged_ranges)

    return SparseZHyperRectangle(new_ranges)


def try_merge_sparse_rectangles(rectangles: List[SparseZHyperRectangle]) -> List[SparseZHyperRectangle]:
    current_set = [r for r in rectangles]
    while True:
        merge_index_a, merge_index_b = None, None
        merged = None
        for i, a in enumerate(current_set):
            for j in range(i + 1, len(current_set)):
                b = current_set[j]
                merged = try_merge_sparse_zhyperrectangles(a, b)
                if merged is not None:
                    merge_index_a = i
                    merge_index_b = j
                    break
            if merge_index_a is not None:
                break

        if merge_index_a is None or merge_index_b is None or merged is None:
            break
        else:
            current_set[merge_index_a] = merged
            current_set[merge_index_b] = current_set[-1]
            current_set.pop()
    return current_set
