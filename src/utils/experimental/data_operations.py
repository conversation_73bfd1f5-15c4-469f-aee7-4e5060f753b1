from datetime import date, datetime, timedelta

import psycopg2

start_date = date(2017, 1, 6)
end_date = datetime.utcnow().date()

for day_delta in range((end_date - start_date).days + 1):
    sd = (start_date + timedelta(days=day_delta)).strftime("%Y-%m-%d")
    ed = (start_date + timedelta(days=day_delta + 1)).strftime("%Y-%m-%d")
    print("running update for", sd, ed, "range")
    with psycopg2.connect(
        "host=localhost port=6432 dbname=postgres-books user=swt password=swt",
        connect_timeout=5,
        keepalives_idle=15,
        keepalives_interval=1,
        keepalives_count=5,
    ) as connection:
        with connection.cursor() as cursor:
            cursor.execute(
                f"UPDATE futures_trades SET trade_amount=trade_amount/10 where trade_exchange_id=37 "
                f"and trade_symbol in "
                f"(SELECT contract_symbol FROM futures_metadata where contract_exchange_id=37 "
                f"and contract_underlying_base_id=0) and trade_time between '{sd}' and '{ed}'"
            )
