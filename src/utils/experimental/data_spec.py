from __future__ import annotations

from decimal import Decimal
from enum import IntEnum
from typing import Any, Callable, Generic, List, Literal, Tuple, Type, TypeVar, Union, get_args, get_origin, get_type_hints

from src.utils.types import JsonValue

T = TypeVar("T")
V = TypeVar("V")
IntEnumType = TypeVar("IntEnumType", bound=IntEnum)


Extractor = Callable[[V], Union[T, Exception]]

JsonExtractor = Callable[[JsonValue], Union[T, Exception]]


class IExtractorFactory(Generic[V]):
    def dataclass_extractor(self, dataclass_type: Type[T]) -> Extractor[V, T]:
        raise NotImplementedError

    def constant_string_extractor(self, constant: str) -> Extractor[V, str]:
        raise NotImplementedError

    def string_extractor(self) -> Extractor[V, str]:
        raise NotImplementedError

    def int_extractor(self) -> Extractor[V, int]:
        raise NotImplementedError

    def decimal_extractor(self) -> Extractor[V, Decimal]:
        raise NotImplementedError

    def int_enum_extractor(self, enum_type: IntEnumType) -> Extractor[V, IntEnumType]:
        raise NotImplementedError

    def none_extractor(self) -> Extractor[V, None]:
        raise NotImplementedError

    def list_extractor(self, item_type: Type[T]) -> Extractor[V, List[T]]:
        raise NotImplementedError

    def tuple_extractor(self, tuple_types: Tuple[Type[Any], ...]) -> Extractor[V, Tuple[Any, ...]]:
        """Callable[[Tuple[Type[T0], Type[T1], ...]], Extractor[V, Tuple[T0, T1, ...]]]"""
        raise NotImplementedError

    def union_extractor(self, union_types: Tuple[Type[Any], ...]) -> Extractor[V, Any]:
        """Callable[[Tuple[Type[T0], Type[T1], ...]], Extractor[V, Union[T0, T1, ...]]]"""
        raise NotImplementedError


class JsonExtractorFactory(IExtractorFactory[JsonValue]):
    def dataclass_extractor(self, dataclass_type: Type[T]) -> Extractor[JsonValue, T]:
        fields = {}

        if hasattr(dataclass_type, "__aliases__"):
            aliases = getattr(dataclass_type, "__aliases__")
        else:
            aliases = {}

        for field_name, field_type in get_type_hints(dataclass_type).items():
            fields[aliases.get(field_name, field_name)] = (field_name, make_extractor(self, field_type))

        optional_fields = set()

        for field_name, field in dataclass_type.__dataclass_fields__.items():  # type: ignore
            if field.default is None:
                optional_fields.add(field_name)

        def proc(value: JsonValue) -> Union[T, Exception]:
            if not isinstance(value, dict):
                return ValueError("expected a dictionary")

            kwargs = {}

            for alias, (field_name, field_extractor) in fields.items():
                if alias not in value:
                    if field_name not in optional_fields:
                        return ValueError("expected field with name `{}`".format(alias))
                    else:
                        continue

                field_value = field_extractor(value[alias])

                if isinstance(field_value, Exception):
                    return ValueError("`{}`: {}".format(alias, field_value))

                kwargs[field_name] = field_value

            try:
                return dataclass_type(**kwargs)  # type: ignore
            except AssertionError as assertion:
                return assertion.args[0]  # type: ignore
            except Exception as e:
                return e

        return proc

    def constant_string_extractor(self, constant: str) -> Extractor[JsonValue, str]:
        def proc(value: JsonValue) -> Union[str, Exception]:
            if not isinstance(value, str) or value != constant:
                return ValueError("expected string `{}`, got `{}`".format(constant, value))
            else:
                return value

        return proc

    def string_extractor(self) -> Extractor[JsonValue, str]:
        def proc(value: JsonValue) -> Union[str, Exception]:
            if isinstance(value, str):
                return value
            else:
                return ValueError("expected string, got value `{}` of type {}".format(value, type(value)))

        return proc

    def int_extractor(self) -> Extractor[JsonValue, int]:
        def proc(value: JsonValue) -> Union[int, Exception]:
            if isinstance(value, int):
                return value
            elif isinstance(value, str):
                try:
                    return int(value)
                except Exception as e:
                    return ValueError("failed to convert `{}` to integer -- {}".format(value, e))
            else:
                return ValueError("expected integer, got value `{}` of type {}".format(value, type(value)))

        return proc

    def decimal_extractor(self) -> Extractor[JsonValue, Decimal]:
        def proc(value: JsonValue) -> Union[Decimal, Exception]:
            if isinstance(value, Decimal):
                return value
            elif isinstance(value, str) or isinstance(value, int):
                try:
                    return Decimal(value)
                except Exception as e:
                    return ValueError("failed to convert `{}` to decimal -- {}".format(value, e))
            else:
                return ValueError("expected decimal, got value `{}` of type {}".format(value, type(value)))

        return proc

    def int_enum_extractor(self, enum_type: IntEnumType) -> Extractor[JsonValue, IntEnumType]:
        members = {value.value: value for value in enum_type.__members__.values()}  # type: ignore

        def proc(value: JsonValue) -> Union[IntEnumType, Exception]:
            if isinstance(value, int):
                if value not in members:
                    return ValueError("unexpected int enum value `{}`".format(value))
                else:
                    return members[value]  # type: ignore
            else:
                return ValueError("expected integer, got value `{}` of type {}".format(value, type(value)))

        return proc

    def none_extractor(self) -> Extractor[JsonValue, None]:
        def proc(value: JsonValue) -> Union[None, Exception]:
            if value is None:
                return None
            else:
                return ValueError("expected null, got value `{}`".format(value))

        return proc

    def list_extractor(self, item_type: Type[T]) -> Extractor[JsonValue, List[T]]:
        item_extractor: Extractor[JsonValue, T] = make_extractor(self, item_type)

        def proc(value: JsonValue) -> Union[List[T], Exception]:
            if not isinstance(value, list):
                return ValueError("expected list, got value `{}` of type {}".format(value, type(value)))

            result = []

            for index, item in enumerate(value):
                extracted_item = item_extractor(item)

                if isinstance(extracted_item, Exception):
                    return ValueError("{}: {}".format(index, extracted_item))
                else:
                    result.append(extracted_item)

            return result

        return proc

    def tuple_extractor(self, tuple_types: Tuple[Type[Any], ...]) -> Extractor[JsonValue, Any]:
        item_extractors = [make_extractor(self, arg) for arg in tuple_types]

        def proc(value: JsonValue) -> Union[Tuple[Any, ...], Exception]:
            if not isinstance(value, list):
                return ValueError("expected list, got value `{}` of type {}".format(value, type(value)))

            if len(value) != len(item_extractors):
                return ValueError("expected {} items, got `{}`".format(len(item_extractors), value))

            result = []

            for index, (item_extractor, list_item) in enumerate(zip(item_extractors, value)):
                extracted_item = item_extractor(list_item)

                if isinstance(extracted_item, Exception):
                    return ValueError("{}: {}".format(index, extracted_item))
                else:
                    result.append(extracted_item)

            return tuple(result)

        return proc

    def union_extractor(self, union_types: Tuple[Type[Any], ...]) -> Extractor[JsonValue, Any]:
        options = [make_extractor(self, arg) for arg in union_types]

        def proc(value: JsonValue) -> Union[Any, Exception]:
            errors = []

            for union_type, option in zip(union_types, options):
                result = option(value)

                if isinstance(result, Exception):
                    errors.append((union_type, result))
                else:
                    return result

            return ValueError("no fitting option -- {}".format(errors))

        return proc


def make_extractor(factory: IExtractorFactory[V], message_type: Type[T]) -> Extractor[V, T]:
    origin = get_origin(message_type)

    if origin is None:
        if hasattr(message_type, "__dataclass_fields__"):
            return factory.dataclass_extractor(message_type)
        elif message_type is str:
            return factory.string_extractor()  # type: ignore
        elif message_type is int:
            return factory.int_extractor()  # type: ignore
        elif message_type is Decimal:
            return factory.decimal_extractor()  # type: ignore
        elif issubclass(message_type, IntEnum):
            return factory.int_enum_extractor(message_type)  # type: ignore
        elif message_type is type(None):
            return factory.none_extractor()  # type: ignore
        else:
            raise ValueError("unsupported message type -- {}".format(message_type))

    if origin is Literal:
        literal_args = get_args(message_type)

        if len(literal_args) != 1:
            raise ValueError("Literal should have exactly one argument")

        argument = literal_args[0]

        if not isinstance(argument, str):
            raise ValueError("only str Literals are supported")

        return factory.constant_string_extractor(argument)  # type: ignore

    elif origin is Union:
        return factory.union_extractor(get_args(message_type))
    elif origin is list:
        return factory.list_extractor(get_args(message_type)[0])  # type: ignore
    elif origin is tuple:
        return factory.tuple_extractor(get_args(message_type))  # type: ignore
    else:
        raise ValueError("unsupported message type -- {}".format(message_type))


def json_extractor(message_type: Type[T]) -> JsonExtractor[T]:
    return make_extractor(JsonExtractorFactory(), message_type)


"""
class TradeDirection(IntEnum):

    BUY = 0
    SELL = 1


@dataclass(frozen=True)
class ReconnectRequestMessage:

    event: Literal['bts:request_reconnect']


@dataclass(frozen=True)
class SubscriptionSuccessMessage:

    event: Literal['bts:subscription_succeeded']
    channel: str


@dataclass(frozen=True)
class ErrorMessage:

    event: Literal['bts:error']


@dataclass(frozen=True)
class TradeMessage:

    @dataclass(frozen=True)
    class Data:

        __aliases__ = {'trade_id': 'id', 'trade_type': 'type'}

        trade_id: int
        amount_str: Decimal
        price_str: Decimal
        trade_type: TradeDirection
        microtimestamp: int

    event: Literal['trade']
    channel: str
    data: TradeMessage.Data


@dataclass(frozen=True)
class BookMessage:

    @dataclass(frozen=True)
    class Data:

        microtimestamp: int
        bids: List[Tuple[Decimal, Decimal]]
        asks: List[Tuple[Decimal, Decimal]]

    event: Literal['data']
    channel: str
    data: BookMessage.Data


@dataclass(frozen=True)
class TradingPairInfo:

    url_symbol: str


class AssetType(Enum):

    UTXO = 'utxo'
    ERC20 = 'erc20'
    OMNI = 'omni'
    OTHER = 'other'


class AssetTimeFieldType(Enum):

    TIME = 'time'
    MEDIAN_TIME = 'median_time'


@dataclass(frozen=True)
class Asset:

    __aliases__ = {'asset_id': 'id'}

    @dataclass(frozen=True)
    class Metadata:

        __aliases__ = {
            'asset_type': 'type', 'timeField': 'time_field', 'hasReferenceRate': 'has_reference_rate',
            'hasRTRR': 'has_rtrr', 'propertyId': 'property_id'
        }

        has_reference_rate: bool
        has_rtrr: bool
        decimals: Optional[int] = None
        genesis: Optional[date] = None
        asset_type: Optional[AssetType] = None
        time_field: Optional[AssetTimeFieldType] = None
        deprecation: Optional[date] = None
        property_id: Optional[int] = None
        contracts: Optional[List[str]] = None

    asset_id: int
    name: str
    cm_ticker: str
    metadata: Asset.Metadata
    fiat: Optional[bool] = None
    dead: Optional[bool] = None


e = json_extractor(Asset)

extractor = json_extractor(
    Union[ReconnectRequestMessage, SubscriptionSuccessMessage, ErrorMessage, TradeMessage, BookMessage])

print(extractor({'event': 'bts:request_reconnect'}))

print(extractor({'event': 'bts:error'}))

print(extractor({'event': 'bts:subscription_succeeded', 'channel': 'f'}))

print(extractor(
    {
        'event': 'trade',
        'channel': 'btcusd',
        'data': {'id': 3434, 'amount_str': '5555.335', 'price_str': '33.45', 'type': 0, 'microtimestamp': 5
    }}))

print(extractor(
    {'event': 'data', 'channel': 'btcusd', 'data': {'bids': [['434.7', '55']], 'asks': [], 'microtimestamp': 5}}))

print(json_extractor(List[TradingPairInfo])([{'url_symbol': 'eurusd'}]))

"""
