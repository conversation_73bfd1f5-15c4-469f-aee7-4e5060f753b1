from collections import deque
from threading import Condition, Lock
from time import time
from typing import Deque, Generic, List, TypeVar

T = TypeVar("T")


class _TicketBox:
    def __init__(self, current_tickets: int, max_tickets: int):
        if max_tickets < 1:
            raise ValueError("max_tickets must be a positive number")

        if current_tickets < 0:
            raise ValueError("current_tickets must be a non-negative number")

        if current_tickets > max_tickets:
            raise ValueError("max_tickets must be greater or equal current_tickets")

        self._max_tickets = max_tickets
        self._ticket_count = current_tickets

        self._lock = Lock()
        self._not_empty = Condition(self._lock)

    def try_obtain(self, count: int) -> int:
        if count < 1:
            raise ValueError("count must be a positive number")

        if count > self._max_tickets:
            raise ValueError("count should be less than maximum amount of tickets")

        with self._lock:
            obtained = min(count, self._ticket_count)
            self._ticket_count -= obtained
            return obtained

    def obtain(self, count: int, timeout: float) -> int:
        if count < 1:
            raise ValueError("count must be a positive number")

        if count > self._max_tickets:
            raise ValueError("count should be less than maximum amount of tickets")

        total_obtained = 0
        end_time = time() + timeout

        with self._lock:
            while True:
                remaining = end_time - time()
                if remaining <= 0.0:
                    break

                obtained = min(count - total_obtained, self._ticket_count)
                self._ticket_count -= obtained
                total_obtained += obtained

                if self._ticket_count == 0:
                    if total_obtained < count:
                        self._not_empty.wait(timeout=remaining)
                    else:
                        break
                else:
                    self._not_empty.notify()
                    break

        return total_obtained

    def release(self, count: int) -> None:
        if count < 1:
            raise ValueError("count must be a positive number")

        with self._lock:
            new_count = self._ticket_count + count

            if new_count > self._max_tickets:
                raise ValueError("ticket overflow")

            self._ticket_count = new_count
            self._not_empty.notify()


class BatchQueue(Generic[T]):
    def __init__(self, max_size: int):
        self._max_size = max_size
        self._deque: Deque[T] = deque()
        self._put_tickets = _TicketBox(max_size, max_size)
        self._get_tickets = _TicketBox(0, max_size)

    def size(self) -> int:
        """Returns the approximate size of the queue (not reliable!)."""
        return len(self._deque)

    def try_put(self, items: List[T]) -> int:
        if len(items) == 0:
            return 0

        tickets_obtained = self._put_tickets.try_obtain(len(items))
        for item in items[:tickets_obtained]:
            self._deque.append(item)

        self._get_tickets.release(tickets_obtained)
        return tickets_obtained

    def put(self, items: List[T], timeout: float) -> int:
        if len(items) == 0:
            return 0

        tickets_obtained = self._put_tickets.obtain(len(items), timeout)
        for item in items[:tickets_obtained]:
            self._deque.append(item)

        self._get_tickets.release(tickets_obtained)
        return tickets_obtained

    def try_get(self, max_items: int) -> List[T]:
        result = []

        if max_items < 1:
            raise ValueError("max_items must be a positive number")

        tickets_obtained = self._get_tickets.try_obtain(max_items)
        for _ in range(tickets_obtained):
            result.append(self._deque.popleft())

        self._put_tickets.release(tickets_obtained)
        return result

    def get(self, max_items: int, timeout: float) -> List[T]:
        result = []

        if max_items < 1:
            raise ValueError("max_items must be a positive number")

        tickets_obtained = self._get_tickets.obtain(max_items, timeout)
        for _ in range(tickets_obtained):
            result.append(self._deque.popleft())

        self._put_tickets.release(tickets_obtained)
        return result
