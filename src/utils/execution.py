import time
from dataclasses import dataclass, field
from threading import Event, Lock, Thread
from types import TracebackType
from typing import Callable, Generic, Iterable, List, Optional, Protocol, Type, TypeVar, Union

R = TypeVar("R")
T = TypeVar("T")


StopSignal = Callable[[], bool]


@dataclass(frozen=True)
class RetryState:
    start_time: float = field(default_factory=lambda: time.time())
    failed_attempts: int = 0

    def attempt_failed(self) -> "RetryState":
        return RetryState(self.start_time, self.failed_attempts + 1)


class RetryStrategy(Protocol):
    def __call__(self, state: RetryState) -> Optional[float]: ...


def limited_retry(max_attempts: int, interval: float = 0.0) -> RetryStrategy:
    return lambda state: interval if state.failed_attempts < max_attempts else None


def endless_retry(interval: float = 0.0) -> RetryStrategy:
    return lambda state: interval


def endless_retry_backoff(interval: float = 0.0) -> RetryStrategy:
    """exponential backoff to avoid hammering exchanges that shut down for maintenance etc. 2^6 caps this around 60 sec"""
    return lambda state: 2 ** min(state.failed_attempts, 6) * interval


def execute_with_retries(proc: Callable[[], R], retry_strategy: RetryStrategy) -> R:
    state = RetryState()

    while True:
        try:
            return proc()
        except Exception:
            state = state.attempt_failed()

            if (retry_after := retry_strategy(state)) is None:
                raise
            else:
                time.sleep(retry_after)


def execute_in_parallel(procs: List[Callable[[], R]]) -> List[Union[R, Exception]]:
    def thread(index: int, proc: Callable[[], R]) -> None:
        try:
            result[index] = proc()
        except Exception as e:
            result[index] = e

    threads = []
    result: List[Union[R, Exception]] = []

    for index, proc in enumerate(procs):
        result.append(Exception(f"proc {index} failed to compute"))
        t = Thread(target=thread, args=(index, procs[index]), name="ParallelExecution")
        t.start()
        threads.append(t)

    for t in threads:
        t.join()

    return result


class Tick:
    def __init__(self, seconds: float, stop_event: Optional[Event] = None):
        self._duration = seconds
        self._stop_event = stop_event

    def __enter__(self) -> None:
        self._time_start = time.time()

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_value: Optional[Type[Exception]], tb: Optional[TracebackType]
    ) -> None:
        if exc_type is not None:
            return

        time_spent = time.time() - self._time_start
        sleep_time = max(0.0, self._duration - time_spent)

        if sleep_time > 0:
            if self._stop_event is not None:
                self._stop_event.wait(sleep_time)
            else:
                time.sleep(sleep_time)


class Singleton(Generic[T]):
    def __init__(self, factory: Callable[[], T]):
        self._factory = factory
        self._lock = Lock()
        self._instance: Optional[T] = None

    def __call__(self) -> T:
        if self._instance is None:
            with self._lock:
                if self._instance is None:
                    self._instance = self._factory()
        return self._instance


class NotAvailable:
    pass


class IBackgroundTask(Generic[T]):
    def result(self) -> Union[T, Exception, NotAvailable]:
        raise NotImplementedError

    def request_stop(self) -> None:
        raise NotImplementedError

    def wait_for_termination(self) -> None:
        raise NotImplementedError


class IBackgroundTaskExecutor:
    def submit(self, proc: Callable[[StopSignal], Union[T, Exception]]) -> IBackgroundTask[T]:
        raise NotImplementedError


class NaiveBackgroundTaskExecutor(IBackgroundTaskExecutor):
    class _Task(IBackgroundTask[T]):
        def __init__(self) -> None:
            self._result: Union[T, Exception, NotAvailable] = NotAvailable()
            self._stop_event = Event()
            self._stopped_event = Event()

        def result(self) -> Union[T, Exception, NotAvailable]:
            return self._result

        def request_stop(self) -> None:
            self._stop_event.set()

        def wait_for_termination(self) -> None:
            self._stopped_event.wait()

        def set_result(self, result: Union[T, Exception]) -> None:
            self._result = result
            self._stopped_event.set()

        def stop_signal(self) -> bool:
            return self._stop_event.is_set()

    def submit(self, proc: Callable[[StopSignal], Union[T, Exception]]) -> IBackgroundTask[T]:
        task: "NaiveBackgroundTaskExecutor._Task[T]" = NaiveBackgroundTaskExecutor._Task()

        def wrap() -> None:
            try:
                result = proc(task.stop_signal)
            except Exception as e:
                result = e

            task.set_result(result)

        t = Thread(target=wrap, daemon=True, name="BackgroundTask")
        t.start()
        return task


class ImmediateBackgroundTaskExecutor(IBackgroundTaskExecutor):
    class _Task(IBackgroundTask[T]):
        def __init__(self, result: Union[T, Exception]):
            self._result = result

        def result(self) -> Union[T, Exception, NotAvailable]:
            return self._result

        def shutdown(self) -> None:
            return

        def wait(self) -> None:
            return

    def submit(self, proc: Callable[[StopSignal], Union[T, Exception]]) -> IBackgroundTask[T]:
        try:
            result = proc(lambda: False)
        except Exception as e:
            result = e
        finally:
            return ImmediateBackgroundTaskExecutor._Task(result)


class IRunnable:
    def start(self) -> None:
        raise NotImplementedError

    def request_stop(self) -> None:
        raise NotImplementedError

    def wait_for_termination(self) -> None:
        raise NotImplementedError

    def run(self) -> None:
        self.start()
        self.wait_for_termination()


class MultiRunnable(IRunnable):
    def __init__(self, runnables: Iterable[IRunnable]):
        self._runnables = [r for r in runnables]

    def start(self) -> None:
        for r in self._runnables:
            r.start()

    def request_stop(self) -> None:
        for r in self._runnables:
            r.request_stop()

    def wait_for_termination(self) -> None:
        for r in self._runnables:
            r.wait_for_termination()
