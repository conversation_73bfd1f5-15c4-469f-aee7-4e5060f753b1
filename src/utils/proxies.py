import re
from typing import Callable, Dict, List, Optional, Set, Tuple

from src.utils.diagnostics import IGaugeMetric
from src.utils.http import ProxyParams

PROXY_GROUP_PARTS_RE = re.compile(r".*?\[(\d+)/(\d+)]")


def get_proxy_group_segment(key: str) -> Tuple[int, int]:
    proxy_group_segments = re.findall(PROXY_GROUP_PARTS_RE, key)
    if not proxy_group_segments:
        return 1, 1

    segment_number, segments_count = proxy_group_segments[0]
    return int(segment_number), int(segments_count)


def get_segment_proxies(all_proxies: List[str], segment_number: int, segments_count: int) -> List[str]:
    if len(all_proxies) < 2 * segments_count:
        # if there is not enough proxies to allocate at least 2 proxy to each, we will reuse the groups
        _segments_count = max(len(all_proxies) // 2, 1)
        segment_number = int(_segments_count / segments_count * (segment_number - 1)) + 1
        segments_count = _segments_count

    # there will be independent segments
    segment_size = len(all_proxies) // segments_count
    increased_size_segments = len(all_proxies) % segments_count
    from_ = segment_size * (segment_number - 1) + min(increased_size_segments, segment_number - 1)
    to_ = segment_size * segment_number + min(increased_size_segments, segment_number)
    return all_proxies[from_:to_]


def get_segmented_proxies(
    proxy_groups: Dict[str, List[str]], keys: List[Optional[str]], excluded: Set[str], instance_number: int
) -> List[ProxyParams]:
    proxies = []
    assert isinstance(keys, list), f"proxy group keys must be a list of Optional[str], got {type(keys)}"
    for key in keys:
        segment_number, segments_count = get_proxy_group_segment(key)
        key = key.split("[")[0]

        any_group_found = False
        used = set()
        for proxy_key in proxy_groups:
            if not re.match(key, proxy_key):
                continue

            any_group_found = True
            all_proxies = proxy_groups.get(proxy_key, [])
            all_proxies = sorted({proxy for proxy in all_proxies if proxy not in excluded and proxy not in used})
            if not all_proxies:
                continue

            if instance_number == 2:
                # take next segment for the instance 2 to ensure different proxies used for both instances
                segment_number = segment_number % segments_count + 1
            segment_proxies = get_segment_proxies(all_proxies, segment_number, segments_count)
            proxies.extend(segment_proxies)
            used.update(segment_proxies)

        if not any_group_found:
            raise ValueError(f"No group found for key: {key}, proxy_groups: {proxy_groups}")

    return [ProxyParams.from_string(proxy_str) for proxy_str in proxies]


def report_desired_proxies_func(
    desired_proxies_gauge: IGaugeMetric, requests_per_instrument: int, requests_per_second_limit: float
) -> Callable[[int], None]:
    def func(instruments_count: int) -> None:
        desired_proxies_gauge.set(instruments_count * requests_per_instrument / requests_per_second_limit)

    return func
