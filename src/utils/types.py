from decimal import Decimal
from enum import Enum
from typing import Any, Generic, List, Protocol, TypeVar, Union

# mypy doesn't support recursive types yet
# JsonValue = Union[None, Decimal, int, bool, str, List[JsonValue], Dict[str, JsonValue]]
JsonValue = Any

Number = TypeVar("Number", int, float, Decimal)

A = TypeVar("A")
B = TypeVar("B")
T = TypeVar("T")


class BijectiveMapping(Generic[A, B]):
    @classmethod
    def encode(cls, value: A) -> Union[B, Exception]:
        raise NotImplementedError

    @classmethod
    def decode(cls, value: B) -> Union[A, Exception]:
        raise NotImplementedError

    @classmethod
    def test_equivalence(cls, value: A) -> bool:
        mapped = cls.encode(value)
        if isinstance(mapped, Exception):
            return False

        reverse_mapped = cls.decode(mapped)
        if isinstance(reverse_mapped, Exception):
            return False

        return value == reverse_mapped


class BatchCallback(Protocol[T]):
    def __call__(self, batch: List[T]) -> None: ...


class Filter(Protocol[T]):
    def __call__(self, batch: List[T]) -> List[T]: ...


class ErrorList(List[str]):
    pass


class Order(Enum):
    ASC = 0
    DESC = 1


class Direction(Enum):
    FORWARD = 0
    BACKWARD = 1
