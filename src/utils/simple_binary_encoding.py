import enum
import struct
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, Iterable, List, NewType, Optional, TextIO, Tuple, Union

import bitstring
import lxml
import lxml.etree


class PrimitiveType(enum.Enum):
    CHAR = "char"
    UINT8 = "uint8"
    UINT16 = "uint16"
    UINT32 = "uint32"
    UINT64 = "uint64"
    INT8 = "int8"
    INT16 = "int16"
    INT32 = "int32"
    INT64 = "int64"
    FLOAT = "float"
    DOUBLE = "double"


class SetEncodingType(enum.Enum):
    UINT8 = "uint8"
    UINT16 = "uint16"
    UINT32 = "uint32"
    UINT64 = "uint64"


class EnumEncodingType(enum.Enum):
    UINT8 = "uint8"
    CHAR = "char"


FormatString = NewType("FormatString", str)


FORMAT = dict(zip(PrimitiveType.__members__.values(), "cBHIQbhiqfd"))
FORMAT_TO_TYPE = {v: k for k, v in FORMAT.items()}
FORMAT_SIZES = {k: struct.calcsize(v) for k, v in FORMAT.items()}
PRIMITIVE_TYPES = {x.value for x in PrimitiveType.__members__.values()}
ENUM_ENCODING_TYPES = {x.value for x in EnumEncodingType.__members__.values()}
SET_ENCODING_TYPES = {x.value for x in SetEncodingType.__members__.values()}


class Presence(enum.Enum):
    CONSTANT = "constant"
    REQUIRED = "required"
    OPTIONAL = "optional"

    @staticmethod
    def get_constant(presence_str: str) -> "Presence":
        if presence_str == "constant":
            return Presence.CONSTANT
        elif presence_str == "required":
            return Presence.REQUIRED
        elif presence_str == "optional":
            return Presence.OPTIONAL
        else:
            raise Exception(f"Unknown Presence parameter value: {presence_str}")


class CharacterEncoding(enum.Enum):
    ASCII = "ASCII"


@dataclass
class UnpackedValue:
    value: dict
    size: int

    __slots__ = ("value", "size")

    def __repr__(self):
        return self.value.__repr__()


@dataclass
class DecodedMessage:
    message_name: str
    header: dict
    value: dict
    publish_time: datetime

    __slots__ = ("message_name", "header", "value", "publish_time")


@dataclass()
class Type:
    name: str
    primitiveType: PrimitiveType
    presence: Presence = Presence.REQUIRED
    semanticType: Optional[str] = None
    description: Optional[str] = None
    length: int = 1
    characterEncoding: Optional[CharacterEncoding] = None
    constant: Optional[Any] = None
    null_value: Optional[Any] = None

    def __repr__(self):
        rv = self.name + " ("
        rv += self.primitiveType.value
        if rv == PrimitiveType.CHAR or self.length > 1:
            rv += f"[{self.length}]"
        rv += ")"
        return rv


@dataclass
class EnumValue:
    name: str
    value: str

    __slots__ = ("name", "value")

    def __repr__(self):
        return (self.name, self.value).__repr__()


@dataclass
class Pointer:
    offset: int
    value: Union[FormatString, Dict[str, "Pointer"]]
    size: int
    is_group: bool
    enum: Optional["Enum"]
    set_: Optional["Set"]

    __slots__ = ("offset", "value", "size", "is_group", "enum", "set_")

    def __init__(self, offset: int, value: Union[FormatString, Dict[str, "Pointer"]], size: int):
        self.offset = offset
        self.value = value
        self.size = size
        self.is_group = False
        self.enum = None
        self.set_ = None

    def return_value(self, buf: memoryview, offset: int):
        start = self.offset + offset
        end = start + self.size

        if self.value.endswith("s"):
            rv = buf[start:end].tobytes()
        else:
            rv = buf[start:end].cast(self.value)[0]

        if self.enum:
            return self.enum.find_name_by_value(rv.decode("ascii") if isinstance(rv, bytes) else str(rv))
        elif self.set_:
            return self.set_.find_name_by_value(rv.decode("ascii") if isinstance(rv, bytes) else str(rv))
        elif self.value.endswith("s"):
            return rv.replace(b"\x00", b"").decode("ascii", errors="ignore").strip()

        return rv

    def unpack(self, buf: memoryview):
        if self.value[-1] == "s":
            return buf[self.offset : self.offset + self.size].tobytes()

        return buf[self.offset : self.offset + self.size].cast(self.value)[0]

    def __repr__(self):
        if self.enum:
            return f"{self.enum.name}@{self.offset}"
        elif self.value in FORMAT_TO_TYPE:
            return f"{FORMAT_TO_TYPE[self.value].value}@{self.offset}"
        else:
            return f"{self.value}@{self.offset}"


@dataclass
class SetChoice:
    name: str
    value: int

    __slots__ = ("name", "value")

    def __repr__(self):
        return (self.name, self.value).__repr__()


@dataclass
class Enum:
    name: str
    encodingType: Union[EnumEncodingType, Type]
    presence = Presence.REQUIRED
    semanticType: Optional[str] = None
    description: Optional[str] = None
    valid_values: List[EnumValue] = field(default_factory=list)

    def find_value_by_name(self, name: str) -> str:
        return int(next(x for x in self.valid_values if x.name == name).value)  # selects first value from a generator

    def find_name_by_value(self, val: str) -> str:
        if isinstance(self.encodingType, Type) and str(self.encodingType.null_value) == val:
            return "Null"
        return next(x for x in self.valid_values if x.value == val).name

    def __repr__(self):
        return f"<Enum '{self.name}'>"


@dataclass
class Composite:
    name: str
    types: List[Union["Composite", Type]] = field(default_factory=list)
    description: Optional[str] = None

    def __repr__(self):
        return f"<Composite '{self.name}'>"


@dataclass
class Set:
    name: str
    encodingType: Union[SetEncodingType, Type]
    presence = Presence.REQUIRED
    semanticType: Optional[str] = None
    description: Optional[str] = None
    choices: Dict[int, str] = field(default_factory=dict)

    def encode(self, vals: Iterable[str]) -> int:
        vals = set(vals)
        return bitstring.BitArray(v in vals for i, v in self.choices.items()).int

    def decode(self, val: int) -> List[str]:
        def _le(idx: int, bits: bitstring.Bits) -> int:
            """convert bitstring big-endian index to little-endian used in Set definitions"""
            return bits.length - idx - 1

        if isinstance(self.encodingType, SetEncodingType):
            length = FORMAT_SIZES[PrimitiveType[self.encodingType.value]] * 8
        else:
            length = FORMAT_SIZES[self.encodingType.primitiveType] * 8

        bits = bitstring.Bits(uint=val, length=length)
        return [self.choices[_le(i, bits)] for i, v in enumerate(bits) if v and _le(i, bits) in self.choices]

    def __repr__(self):
        return f"<Set '{self.name}'>"


@dataclass(init=False)
class Field:
    name: str
    id: str
    type: Union[PrimitiveType, Set, Enum]
    description: Optional[str]
    sinceVersion: int

    def __init__(self, name: str, id_: str, type_: Union[PrimitiveType, str]):
        self.name = name
        self.id = id_
        self.type = type_
        self.description = None
        self.sinceVersion = 0

    __slots__ = ("name", "id", "type", "description", "sinceVersion")

    def __repr__(self):
        if isinstance(self.type, PrimitiveType):
            return f"<{self.name} ({self.type.value})>"
        else:
            return f"<{self.name} ({self.type})>"


@dataclass
class Group:
    name: str
    id: str
    dimensionType: Composite
    block_length: int
    description: Optional[str] = None
    fields: List[Union["Group", Field]] = field(default_factory=list)


@dataclass
class Message:
    name: str
    id: int
    description: Optional[str] = None
    fields: List[Union[Group, Field]] = field(default_factory=list, repr=False)


@dataclass
class Cursor:
    val: int
    __slots__ = ("val",)


@dataclass
class Schema:
    id: int
    version: int
    types: Dict[str, Union[Composite, Type]] = field(default_factory=dict)
    messages: Dict[int, Message] = field(default_factory=dict)
    header_size: int = None

    @classmethod
    def parse(cls, f: TextIO) -> "Schema":
        rv = _parse_schema(f)
        return rv

    def decode_header(self, buffer: Union[bytes, memoryview]) -> dict:
        buffer = memoryview(buffer)
        return unpack_composite(self, self.types["messageHeader"], buffer).value

    def decode(self, buffer: Union[bytes, memoryview], publish_time: datetime) -> DecodedMessage:
        buffer = memoryview(buffer)

        header = unpack_composite(self, self.types["messageHeader"], buffer)
        body_offset = header.size

        m = self.messages[header.value["templateId"]]

        cursor = Cursor(0)
        format_str_parts = []
        first_group_reached = False
        for f in m.fields:
            if isinstance(f, Group) and not first_group_reached:
                first_group_reached = True
                assert cursor.val <= header.value["blockLength"]
                if cursor.val < header.value["blockLength"]:
                    format_str_parts.append(str(header.value["blockLength"] - cursor.val) + "x")
                cursor.val = header.value["blockLength"]
            format_str_parts.append(_unpack_format(self, f, "", buffer[body_offset:], cursor))
        format_str = "<" + "".join(format_str_parts)

        body_size = struct.calcsize(format_str)

        rv = {}
        vals = struct.unpack(format_str, buffer[body_offset : body_offset + body_size])
        _walk_fields_decode(self, rv, m.fields, vals, Cursor(0))
        return DecodedMessage(m.name, header.value, rv, publish_time)


def _unpack_format(
    schema: Schema,
    type_: Union[Field, Group, PrimitiveType, Type, Set, Enum, Composite],
    prefix="<",
    buffer=None,
    buffer_cursor=None,
):
    if isinstance(type_, PrimitiveType):
        if buffer_cursor:
            buffer_cursor.val += FORMAT_SIZES[type_]
        return prefix + FORMAT[type_]

    elif isinstance(type_, Field):
        return _unpack_format(schema, type_.type, "", buffer, buffer_cursor)

    elif isinstance(type_, Group):
        dimension = unpack_composite(schema, type_.dimensionType, buffer[buffer_cursor.val :])
        buffer_cursor.val += dimension.size

        rv = _unpack_format(schema, type_.dimensionType, "")
        for _ in range(dimension.value["numInGroup"]):
            cursor0 = buffer_cursor.val
            rv += "".join(_unpack_format(schema, f, "", buffer, buffer_cursor) for f in type_.fields)
            assert buffer_cursor.val <= cursor0 + dimension.value["blockLength"]
            if buffer_cursor.val < cursor0 + dimension.value["blockLength"]:
                rv += str(cursor0 + dimension.value["blockLength"] - buffer_cursor.val) + "x"
            buffer_cursor.val = cursor0 + dimension.value["blockLength"]

        return rv

    elif isinstance(type_, Type):
        if type_.presence == Presence.CONSTANT:
            return ""
        if type_.primitiveType == PrimitiveType.CHAR:
            if buffer_cursor:
                buffer_cursor.val += type_.length
            return prefix + f"{type_.length}s"
        else:
            if buffer_cursor:
                buffer_cursor.val += FORMAT_SIZES[type_.primitiveType]
            return prefix + FORMAT[type_.primitiveType]

    elif isinstance(type_, (Set, Enum)):
        if isinstance(type_.encodingType, (PrimitiveType, EnumEncodingType)):
            if type_.encodingType.value in PRIMITIVE_TYPES:
                if buffer_cursor:
                    buffer_cursor.val += FORMAT_SIZES[PrimitiveType(type_.encodingType.value)]
                return prefix + FORMAT[PrimitiveType(type_.encodingType.value)]
        elif isinstance(type_.encodingType.primitiveType, PrimitiveType):
            if type_.encodingType.primitiveType.value in PRIMITIVE_TYPES:
                if buffer_cursor:
                    buffer_cursor.val += FORMAT_SIZES[PrimitiveType(type_.encodingType.primitiveType.value)]
                return prefix + FORMAT[PrimitiveType(type_.encodingType.primitiveType.value)]

        return _unpack_format(schema, type_.encodingType, "", buffer, buffer_cursor)

    elif isinstance(type_, Composite):
        return prefix + "".join(
            _unpack_format(schema, t, "", buffer, buffer_cursor) for t in type_.types if t.presence != Presence.CONSTANT
        )


def unpack_composite(schema: Schema, composite: Union[Composite, Type], buffer: Union[memoryview, bytes]):
    fmt = _unpack_format(schema, composite)
    size = struct.calcsize(fmt)
    vals = struct.unpack(fmt, buffer[:size])

    rv = {}
    for t, v in zip(composite.types, vals):
        rv[t.name] = _prettify_type(schema, t, v)

    return UnpackedValue(rv, size)


def _prettify_type(_schema: Schema, t: Type, v):
    if t.primitiveType == PrimitiveType.CHAR and t.characterEncoding == CharacterEncoding.ASCII:
        return v.replace(b"\x00", b"").decode("ascii", errors="ignore").strip()

    return v


def _decode_value(schema: Schema, rv: dict, name: str, t: Union[Type, Set, Enum, PrimitiveType], vals: list, cursor: Cursor):
    if isinstance(t, Type):
        if t.presence == Presence.CONSTANT:
            rv[name] = t.constant
        else:
            rv[name] = _prettify_type(schema, t, vals[cursor.val])
            cursor.val += 1

    elif isinstance(t, Set):
        v = vals[cursor.val]
        cursor.val += 1
        rv[name] = t.decode(v)

    elif isinstance(t, Enum):
        v = vals[cursor.val]
        cursor.val += 1

        if isinstance(v, bytes):
            if v == b"\x00":
                rv[name] = v
            else:
                rv[name] = t.find_name_by_value(v.decode("ascii", errors="ignore"))
        else:
            rv[name] = t.find_name_by_value(str(v))

    elif isinstance(t, PrimitiveType):
        v = vals[cursor.val]
        cursor.val += 1
        rv[name] = v

    else:
        assert 0


def _walk_fields_decode_composite(schema: Schema, rv: dict, composite: Composite, vals: list, cursor: Cursor):
    for t in composite.types:
        if isinstance(t, Composite):
            rv[t.name] = {}
            _walk_fields_decode_composite(schema, rv[t.name], t, vals, cursor)
        if t.presence == Presence.CONSTANT:
            rv[t.name] = t.constant
        else:
            _decode_value(schema, rv, t.name, t, vals, cursor)


def _walk_fields_decode(
    schema: Schema, rv: dict, fields: List[Union[Group, Field]], vals: Union[List[Any], Tuple[Any]], cursor: Cursor
):
    for f in fields:
        if isinstance(f, Group):
            num_in_group = vals[cursor.val + 1]
            cursor.val += 2

            rv[f.name] = []
            for _ in range(num_in_group):
                rv1 = {}
                _walk_fields_decode(schema, rv1, f.fields, vals, cursor)
                rv[f.name].append(rv1)

        elif isinstance(f.type, Composite):
            rv[f.name] = {}
            _walk_fields_decode_composite(schema, rv[f.name], f.type, vals, cursor)

        else:
            _decode_value(schema, rv, f.name, f.type, vals, cursor)


def _parse_schema(f: TextIO) -> Schema:
    doc = lxml.etree.parse(f)
    stack = []

    for action, elem in lxml.etree.iterwalk(doc, ("start", "end")):
        assert action in ("start", "end")

        tag = elem.tag
        if "}" in tag:
            tag = tag[tag.index("}") + 1 :]

        if tag == "messageSchema":
            if action == "start":
                attrs = dict(elem.items())
                x = Schema(attrs["id"], attrs["version"])
                stack.append(x)
            elif action == "end":
                pass

        elif tag == "types":
            pass

        elif tag == "composite":
            if action == "start":
                name = next(v for k, v in elem.items() if k == "name")
                x = Composite(name=name)
                for k, v in elem.items():
                    if k == "name":
                        pass
                    elif k == "description":
                        x.description = v

                stack.append(x)

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Schema)
                stack[-1].types[x.name] = x

        elif tag == "type":
            if action == "start":
                attrs = dict(elem.items())
                x = Type(
                    name=attrs["name"],
                    primitiveType=PrimitiveType(attrs["primitiveType"]),
                    presence=Presence.get_constant(attrs.get("presence", "required")),
                )

                if x.primitiveType == PrimitiveType.CHAR:
                    if "length" in attrs:
                        x.length = int(attrs["length"])
                    if "characterEncoding" in attrs:
                        x.characterEncoding = CharacterEncoding(attrs["characterEncoding"])
                    else:
                        x.characterEncoding = CharacterEncoding.ASCII
                if x.presence == Presence.CONSTANT:
                    if x.primitiveType == PrimitiveType.CHAR:
                        x.constant = elem.text
                    elif x.primitiveType in [PrimitiveType.DOUBLE, PrimitiveType.FLOAT]:
                        x.constant = float(elem.text)
                    elif "int" in x.primitiveType.value:
                        x.constant = int(elem.text)
                    else:
                        raise Exception(f"Unknown primitive type for constant {elem.text}")
                if x.name.endswith("NULL"):
                    if "int" in x.primitiveType.value:
                        x.null_value = int(attrs["nullValue"])
                    else:
                        x.null_value = attrs["nullValue"]
                stack.append(x)

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], (Composite, Schema))
                if isinstance(stack[-1], Composite):
                    stack[-1].types.append(x)
                elif isinstance(stack[-1], Schema):
                    stack[-1].types[x.name] = x

        elif tag == "enum":
            if action == "start":
                attrs = dict(elem.items())

                if attrs["encodingType"] in ENUM_ENCODING_TYPES:
                    encoding_type = EnumEncodingType(attrs["encodingType"])
                else:
                    encoding_type = stack[0].types[attrs["encodingType"]]

                stack.append(Enum(name=attrs["name"], encodingType=encoding_type))

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Schema)
                stack[-1].types[x.name] = x

        elif tag == "validValue":
            if action == "start":
                attrs = dict(elem.items())
                stack.append(EnumValue(name=attrs["name"], value=elem.text.strip()))

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Enum)
                stack[-1].valid_values.append(x)

        elif tag == "set":
            if action == "start":
                attrs = dict(elem.items())

                if attrs["encodingType"] in SET_ENCODING_TYPES:
                    encoding_type = SetEncodingType(attrs["encodingType"])
                else:
                    encoding_type = stack[0].types[attrs["encodingType"]]

                stack.append(Set(name=attrs["name"], encodingType=encoding_type))

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Schema)

                stack[-1].types[x.name] = x

        elif tag == "choice":
            if action == "start":
                attrs = dict(elem.items())
                stack.append(SetChoice(name=attrs["name"], value=int(elem.text.strip())))

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Set)
                stack[-1].choices[int(x.value)] = x.name

        elif tag == "field":
            if action == "start":
                assert len(elem) == 0

                attrs = dict(elem.items())
                if attrs["type"] in PRIMITIVE_TYPES:
                    field_type = PrimitiveType(attrs["type"])
                else:
                    field_type = stack[0].types[attrs["type"]]

                assert isinstance(stack[-1], (Group, Message))
                stack[-1].fields.append(Field(name=attrs["name"], id_=attrs["id"], type_=field_type))

        elif tag == "message":
            if action == "start":
                attrs = dict(elem.items())
                stack.append(Message(name=attrs["name"], id=int(attrs["id"]), description=attrs.get("description")))

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], Schema)
                stack[-1].messages[x.id] = x

        elif tag == "group":
            if action == "start":
                attrs = dict(elem.items())
                stack.append(
                    Group(
                        name=attrs["name"],
                        id=attrs["id"],
                        dimensionType=stack[0].types[attrs.get("dimensionType", "groupSizeEncoding")],
                        block_length=attrs["blockLength"],
                    )
                )

            elif action == "end":
                x = stack.pop()
                assert isinstance(stack[-1], (Group, Message))
                stack[-1].fields.append(x)

        else:
            assert 0, f"Unknown tag '{tag}'"

    return stack[0]
