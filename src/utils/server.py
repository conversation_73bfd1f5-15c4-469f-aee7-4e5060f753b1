import copy
import http.server
import socket
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from http.server import ThreadingHTTPServer
from threading import Thread
from typing import Any, Callable, Dict, Optional, Union

from SimpleWebSocketServer import SimpleWebSocketServer, WebSocket  # type: ignore

from src.utils.diagnostics import Diagnostics
from src.utils.pyroutine import IPyRoutineEnv, PyRoutine


@dataclass(frozen=True)
class BindParams:
    host: str
    port: int

    def __str__(self) -> str:
        host = f"[{self.host}]" if ":" in self.host else self.host
        return "{}:{}".format(host, self.port)


def bind_argument(value: str) -> BindParams:
    host, port = value.rsplit(":", 1)
    host = host.lstrip("[").rstrip("]")
    return BindParams(host, int(port))


ClientAddress = BindParams
ClientMessage = Union[str, bytes]


class IServer:
    def tick(self) -> None:
        raise NotImplementedError

    def shutdown(self) -> None:
        raise NotImplementedError


class WebSocketServer(IServer):
    class Client(WebSocket):
        def __init__(self, server: "WebSocketServer", impl: SimpleWebSocketServer, sock: Any, address: Any) -> None:
            super().__init__(impl, sock, address)
            self.server = server
            self.last_tick_time = float("-inf")

        def handleConnected(self) -> None:
            self.server._client_connected_callback(self)

        def handleMessage(self) -> None:
            self.server._client_message_callback(self)

        def handleClose(self) -> None:
            self.server._client_disconnected_callback(self)

    def __init__(
        self, bind_params: BindParams, diagnostics: Diagnostics, select_interval: float = 0.01, tick_interval: float = 0.01
    ):
        self._bind_params = bind_params
        self._diagnostics = diagnostics
        self._select_interval = select_interval
        self._tick_interval = tick_interval

        self._clients: Dict[ClientAddress, "WebSocketServer.Client"] = {}
        self._server = SimpleWebSocketServer(
            self._bind_params.host, self._bind_params.port, self._factory, selectInterval=self._select_interval
        )

    @property
    def diagnostics(self) -> Diagnostics:
        return self._diagnostics

    def tick(self) -> None:
        self._server.serveonce()
        self.on_after_serve()

        for client_address, client in self._clients.items():
            if time.time() - client.last_tick_time > self._tick_interval:
                try:
                    self.on_client_tick(client_address)
                except Exception as e:
                    self._diagnostics.error(e, f"on_client_tick failed for {client_address}")
                finally:
                    client.last_tick_time = time.time()

    def on_after_serve(self) -> None:
        pass

    def on_client_connected(self, client: ClientAddress) -> None:
        pass

    def on_client_tick(self, client: ClientAddress) -> None:
        pass

    def on_client_message(self, message: ClientMessage, client: ClientAddress) -> None:
        pass

    def on_client_disconnected(self, client: ClientAddress) -> None:
        pass

    def send(self, message: ClientMessage, client: ClientAddress) -> bool:
        if client in self._clients:
            self._clients[client].sendMessage(message)
            return True
        else:
            return False

    def shutdown(self) -> None:
        self._server.close()

    def _factory(self, server: SimpleWebSocketServer, sock: Any, address: Any) -> WebSocket:
        return WebSocketServer.Client(self, server, sock, address)

    def _client_connected_callback(self, client: "WebSocketServer.Client") -> None:
        host, port = client.address[:2]
        client_address = ClientAddress(host, port)

        try:
            self._clients[client_address] = client
            self.on_client_connected(client_address)
        except Exception as e:
            self._diagnostics.error(e, f"on_client_connected failed for {client_address}")
            del self._clients[client_address]
            client.close()

    def _client_disconnected_callback(self, client: "WebSocketServer.Client") -> None:
        host, port = client.address[:2]
        client_address = ClientAddress(host, port)

        del self._clients[client_address]

        try:
            self.on_client_disconnected(client_address)
        except Exception as e:
            self._diagnostics.error(e, f"on_client_disconnected failed for {client_address}")

    def _client_message_callback(self, client: "WebSocketServer.Client") -> None:
        host, port = client.address[:2]
        client_address = ClientAddress(host, port)

        data = copy.deepcopy(client.data)

        try:
            self.on_client_message(data, client_address)
        except Exception as e:
            self._diagnostics.error(e, f"on_client_message failed for {client_address}")


class ThreadingHTTPServerV6(ThreadingHTTPServer):
    address_family = socket.AF_INET6


class ReadinessHttpServer(IServer):
    LOGS_INTERVAL = timedelta(hours=1)

    class ReadinessHttpHandler(http.server.SimpleHTTPRequestHandler):
        def __init__(self, *args, diagnostics: Diagnostics, log_enabled, **kwargs) -> None:
            self._diagnostics = diagnostics
            self._log_enabled: bool = log_enabled
            super().__init__(*args, **kwargs)

        def do_GET(self):
            if self.path == "/ready":
                self.send_response(200)
                self.send_header("Content-type", "text/html")
                self.end_headers()
                self.wfile.write(bytes("OK", "utf8"))
            else:
                self.send_error(404)

        def log_message(self, format: str, *args):
            if self._log_enabled:
                self._diagnostics.info(f"Readiness check: {format % args}")

    def __init__(self, port: int, diagnostics: Diagnostics):
        self._diagnostics = diagnostics
        self._last_log_message_time: Optional[datetime] = None
        self.port = port
        self.server = None
        self.server_thread = Thread(target=self.run_server, name="ReadinessServer")
        self.server_thread.daemon = True

    def start(self) -> None:
        self.server_thread.start()

    def run_server(self) -> None:
        self.server = ThreadingHTTPServerV6(("", self.port), self._request_handler_factory)
        self._diagnostics.info(f"Readiness server started on port {self.port}")
        self.server.serve_forever(poll_interval=0.5)

    def tick(self) -> None:
        time.sleep(1)

    def shutdown(self) -> None:
        if self.server:
            self._diagnostics.info("Terminating readiness server")
            self.server.shutdown()
            self.server_thread.join()

    def _request_handler_factory(self, *args, **kwargs):
        log_enabled = self._last_log_message_time is None or self._last_log_message_time + self.LOGS_INTERVAL < datetime.now()
        if log_enabled:
            self._last_log_message_time = datetime.now()

        return ReadinessHttpServer.ReadinessHttpHandler(*args, diagnostics=self._diagnostics, log_enabled=log_enabled, **kwargs)


def server_routine(factory: Callable[[Diagnostics], IServer]) -> PyRoutine:
    def dtor(env: IPyRoutineEnv) -> None:
        if server is not None:
            server.shutdown()

    env = yield dtor
    server = factory(env.diagnostics)

    while env.stop_not_requested():
        server.tick()
