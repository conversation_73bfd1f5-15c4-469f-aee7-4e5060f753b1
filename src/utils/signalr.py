import json
from decimal import Decimal
from typing import Iterator, Protocol
from urllib.parse import quote_plus, urlparse, urlunparse

from src.utils.diagnostics import Diagnostics
from src.utils.http import IHttpClient, get_json
from src.utils.stream import ConnectionEvent, IStreamingConnection, MessageReceived, StreamingClient
from src.utils.types import JsonValue
from src.websocket.api import WebSocketMessage


class SignalRConnection(IStreamingConnection[JsonValue]):
    def __init__(self, hub: str, connection: IStreamingConnection[WebSocketMessage]):
        self._hub = hub
        self._connection = connection

    def close(self, gracefully: bool = True) -> None:
        self._connection.close(gracefully)

    def send(self, message: JsonValue) -> bool:
        data = json.dumps({"H": self._hub, "M": message[0], "A": message[1], "I": message[2]})
        return self._connection.send(data)

    def communicate(
        self,
        timeout: float,
    ) -> Iterator[ConnectionEvent[JsonValue]]:
        for event in self._connection.communicate(timeout):
            if isinstance(event, MessageReceived):
                message = json.loads(event.data, parse_float=Decimal)

                if "R" in message or "E" in message:
                    yield MessageReceived(message)

                messages = message["M"] if "M" in message and len(message["M"]) > 0 else []
                for inner_data in messages:
                    yield MessageReceived({inner_data["M"]: inner_data["A"]})
            else:
                yield event


class SignalRConnectionError(Exception):
    pass


class OpenWsConnection(Protocol):
    def __call__(self, url: str, timeout: float, diagnostics: Diagnostics) -> IStreamingConnection[WebSocketMessage]: ...


def signalr_client(
    url: str, hub: str, http_client: IHttpClient, open_ws_connection: OpenWsConnection
) -> StreamingClient[JsonValue]:
    def get_url(action: str, token: str, hub: str, **kwargs: str) -> str:
        args = kwargs.copy()
        args["transport"] = "webSockets"
        args["connectionToken"] = token
        args["connectionData"] = json.dumps([{"name": hub}])
        return get_base_url(action, **args)

    def get_base_url(action: str, **kwargs: str) -> str:
        args = kwargs.copy()
        args["clientProtocol"] = "1.5"
        query = "&".join([f"{key}={quote_plus(args[key])}" for key in args])
        return f"{url}/{action}?{query}"

    def connect(timeout: float, diagnostics: Diagnostics) -> IStreamingConnection[JsonValue]:
        negotiation_url = get_base_url("negotiate", connectionData=json.dumps([{"name": hub}]))
        negotiation_response = http_client.get(negotiation_url)
        diagnostics.info(f"hub responded to negotiation request: {negotiation_response.body}")

        token = str(get_json(negotiation_response)["ConnectionToken"])
        parsed = urlparse(get_url("connect", token, hub))
        scheme = "wss" if parsed.scheme == "https" else "ws"
        url = urlunparse((scheme, parsed.netloc, parsed.path, parsed.params, parsed.query, parsed.fragment))

        connection = open_ws_connection(url, timeout, diagnostics)

        try:
            start_response = http_client.get(get_url("start", token, hub))
        except Exception as e:
            connection.close()
            raise SignalRConnectionError(f"hub failed to respond to start request: {e}")

        diagnostics.info(f"hub responded to start request: {start_response.body}")
        return SignalRConnection(hub, connection)

    return connect
