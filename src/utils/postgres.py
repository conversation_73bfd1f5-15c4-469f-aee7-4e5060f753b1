from dataclasses import dataclass
from enum import Enum
from typing import Any, List, Optional

import psycopg2
import psycopg2.extensions
import psycopg2.extras


class PgConnectionParams:
    def __init__(self, connection_string: str):
        pieces = connection_string.split(":")
        if len(pieces) != 5:
            raise ValueError(f"Expected host:port:db:user:password string, got {connection_string}")
        self._set(pieces)

    def __repr__(self) -> str:
        return f"{self.db}@{self.host}:{self.port}"

    def get_connection_string(self) -> str:
        return "host={} port={} dbname={} user={} password={}".format(self.host, self.port, self.db, self.user, self.password)

    def _set(self, pieces: List[str]) -> None:
        self.host = pieces[0]
        self.port = int(pieces[1])
        self.db = pieces[2]
        self.user = pieces[3]
        self.password = pieces[4]


def postgres_connect(params: PgConnectionParams) -> psycopg2.extensions.connection:
    return psycopg2.connect(
        params.get_connection_string(), connect_timeout=5, keepalives_idle=15, keepalives_interval=1, keepalives_count=5
    )


class PostgresDb:
    def __init__(self, params: PgConnectionParams) -> None:
        self._params = params
        self._connection: Optional[psycopg2.extensions.connection] = None
        self._cursor: Optional[psycopg2.extensions.cursor] = None

    def __del__(self):
        if self._connection and not self._connection.closed:
            self._connection.close()

    def execute(self, query: str, query_vars: Any = None) -> psycopg2.extensions.cursor:
        try:
            self._ensure_connection()
            self._cursor.execute(query, query_vars)
        except (psycopg2.OperationalError, psycopg2.InterfaceError):
            self._connect()
            self._cursor.execute(query, query_vars)
        except psycopg2.errors.InFailedSqlTransaction:
            self._connection.rollback()
            self._connection.close()
            self._connect()
            self._cursor.execute(query, query_vars)

        try:
            self._connection.commit()
        except psycopg2.errors.InFailedSqlTransaction:
            self._connection.rollback()
            self._connection.close()
            raise

        return self._cursor

    def execute_fetch_thread_safe(self, query: str, query_vars: Any = None) -> list[any]:
        """
        It is important to execute it in separate connection because operations from other threads may alter
        the cursor state and prevent any rows to be returned on cursor.fetch calls
        """
        with postgres_connect(self._params) as connection:
            with connection.cursor() as cursor:
                cursor.execute(query, query_vars)
                return cursor.fetchall()

    def execute_values(self, query: str, *args, **kwargs) -> Any:
        try:
            self._ensure_connection()
            results = psycopg2.extras.execute_values(self._cursor, query, *args, **kwargs)
        except (psycopg2.OperationalError, psycopg2.InterfaceError):
            self._connect()
            results = psycopg2.extras.execute_values(self._cursor, query, *args, **kwargs)
        except psycopg2.errors.InFailedSqlTransaction:
            self._connection.rollback()
            self._connection.close()
            self._connect()
            results = psycopg2.extras.execute_values(self._cursor, query, *args, **kwargs)

        try:
            self._connection.commit()
        except psycopg2.errors.InFailedSqlTransaction:
            self._connection.rollback()
            self._connection.close()
            raise

        return results

    def _ensure_connection(self) -> None:
        if self._connection and not self._connection.closed and self._cursor and not self._cursor.closed:
            return
        self._connect()

    def _connect(self) -> None:
        self._connection = postgres_connect(self._params)
        assert not self._connection.closed
        self._cursor = self._connection.cursor()
        assert not self._cursor.closed

    def table_exists(self, schema_name: str, table_name: str) -> bool:
        cursor = self.execute(
            """SELECT EXISTS(SELECT 1 from pg_tables WHERE schemaname = '{}' AND tablename = '{}')""".format(
                schema_name, table_name
            )
        )
        result = cursor.fetchone()
        return result[0] is True


class PostgresColumnType(Enum):
    SMALLINT = "SMALLINT"
    TEXT = "TEXT"
    TIMESTAMPZ = "TIMESTAMPTZ"
    NUMERIC = "NUMERIC"
    INTEGER = "INTEGER"


@dataclass(frozen=True)
class PostgresColumn:
    name: str
    column_type: PostgresColumnType
    is_primary_key: bool = False
