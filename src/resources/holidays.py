from dataclasses import dataclass
from datetime import datetime
from enum import Enum, auto
from os.path import abspath, dirname, join
from typing import Dict, List, Set, Union

from src.utils.execution import Singleton
from src.utils.json_spec import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JsonResourceParser, JString
from src.utils.types import ErrorList, JsonValue


class HolidaySchedule(Enum):
    CLOSED = auto()
    PARTIAL = auto()


@dataclass(frozen=True)
class HolidayInfo:
    holiday_date: datetime
    holiday_name: str
    holiday_schedule: HolidaySchedule


class _ExchangeHoliday:
    def __init__(self, holiday_info: List[HolidayInfo]):
        self._holiday_info = holiday_info
        self.holiday_dates = {h.holiday_date for h in holiday_info if h.holiday_schedule == HolidaySchedule.CLOSED}


class HolidayLibrary:
    def __init__(self) -> None:
        self._holiday_info: Dict[str, _ExchangeHoliday] = {}

    def _add_exchange_holiday(self, exchange: str, holiday_info: _ExchangeHoliday) -> None:
        self._holiday_info[exchange] = holiday_info

    def get_holiday_dates(self, exchange: str) -> Set[datetime]:
        return self._holiday_info[exchange].holiday_dates


class HolidayLibraryParser(IParser[JsonValue, _ExchangeHoliday]):
    def parse(self, holidays: JsonValue) -> Union[_ExchangeHoliday, ErrorList]:
        schema = (
            JList(
                JRecord(("date", True, JDate()), ("name", True, JString()), ("schedule", True, JString())).map(
                    lambda e: HolidayInfo(
                        e["date"],
                        e["date"],
                        HolidaySchedule[e["schedule"].upper()],
                    )
                )
            )
            .unique_element_properties(
                "date",
            )
            .map(lambda info_list: _ExchangeHoliday(info_list))
        )
        return schema.validate_and_transform(holidays)


def make_exchange_holiday_library(path: str) -> Union[_ExchangeHoliday, ErrorList]:
    return CombineParser(JsonResourceParser(), HolidayLibraryParser()).parse(path)


def _make_global_library() -> HolidayLibrary:
    known_holidays: Dict[str, str] = {
        "nyse": "holidays_nyse.json",
        "usd": "holidays_usd.json",
    }

    hl = HolidayLibrary()
    for exchange in known_holidays.keys():
        result = make_exchange_holiday_library(
            abspath(join(dirname(__file__), "..", "..", "resources", known_holidays[exchange]))
        )
        if isinstance(result, ErrorList):
            raise ValueError(result)
        hl._add_exchange_holiday(exchange, result)

    return hl


glib_holidays = Singleton(_make_global_library)
