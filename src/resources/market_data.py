from typing import Any, Union

from src.octopus.json_spec import JInstrument
from src.resources.currency import CurrencyLibrary
from src.resources.exchange import ExchangeLibrary, JExchange
from src.utils.json_spec import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ars<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ord, JsonResourceParser
from src.utils.types import ErrorList, JsonValue


class MarketDataParser(IParser[JsonValue, Any]):
    def __init__(self, currency_lib: CurrencyLibrary, exchange_lib: ExchangeLibrary):
        self._currency_lib = currency_lib
        self._exchange_lib = exchange_lib

    def parse(self, market_data: JsonValue) -> Union[Any, ErrorList]:
        schema = JRecord((
            "exchanges",
            True,
            JDict(JExchange(self._exchange_lib), JRecord(("markets", True, JList(JInstrument(self._currency_lib))))),
        ))
        return schema.validate_and_transform(market_data)


def make_market_data_library(currency_lib: CurrencyLibrary, exchange_lib: ExchangeLibrary, path: str) -> Union[Any, ErrorList]:
    return CombineParser(JsonResourceParser(), MarketDataParser(currency_lib, exchange_lib)).parse(path)
