from typing import Any, Callable, Iterator, List, Optional, Union

from confluent_kafka import Kafka<PERSON>rror, KafkaException, Message, Producer

from src.octopus.data import KafkaMessageWithHeaders
from src.utils.diagnostics import Diagnostics
from src.utils.stream import ConnectionEvent, IStreamingConnection, MessageReceived, StreamingClient

KafkaMessage = Union[str, bytes]


def kafka_client(
    url: str, topic: str, on_message_delivery: Callable[[KafkaError, Message], None]
) -> StreamingClient[List[KafkaMessage]]:
    return lambda timeout, diagnostics: KafkaConnectionWithHeaders(
        url, topic, diagnostics, on_message_delivery=on_message_delivery
    )


class KafkaConnectionBase(IStreamingConnection[List[Any]]):
    def __init__(
        self,
        url: str,
        topic: str,
        diagnostics: Diagnostics,
        client_id: Optional[str] = None,
        linger_ms: int = 5,
        on_message_delivery: Callable[[KafkaError, Message], None] = lambda error, message: None,
    ) -> None:
        self._producer = Producer({
            "bootstrap.servers": url,
            "client.id": client_id,
            "max.in.flight.requests.per.connection": 1,
            "linger.ms": linger_ms,
            "queue.buffering.max.kbytes": 100_000,  # 100 MBi
            "message.max.bytes": 30_000_000,  # 30 MBi
        })
        self._topic = topic
        self._diagnostics = diagnostics
        self._connection_closed = False
        self._on_message_delivery = on_message_delivery

    def send(self, messages: List[Any]) -> bool: ...

    def close(self, gracefully: bool = True) -> None:
        self._connection_closed = True

    def communicate(self, timeout: float) -> Iterator[ConnectionEvent[List[Any]]]:
        # there must be infinite loop that waits for message from IStreamingConnection but since with kafka it
        # is one direction connection only we have to have just an empty while loop
        while not self._connection_closed:
            self._producer.poll(1)
            yield MessageReceived(data=None)  # heartbeat


class KafkaConnection(KafkaConnectionBase):
    def send(self, messages: List[KafkaMessage]) -> bool:
        try:
            for message in messages:
                self._producer.produce(self._topic, message, partition=0, on_delivery=self._on_message_delivery)
                self._producer.poll(0)
        except BufferError as error:
            self._diagnostics.error(error, "Internal Kafka producer message queue is full")
            return False
        except NotImplementedError as error:
            self._diagnostics.error(error, "timestamp was specified without underlying library support")
            return False
        except KafkaException as error:
            self._diagnostics.error(error, "Kafka producer error")
            return False

        return True


class KafkaConnectionWithHeaders(KafkaConnectionBase):
    def send(self, messages: List[KafkaMessageWithHeaders]) -> bool:
        try:
            for message in messages:
                self._producer.produce(
                    self._topic, message.value, headers=message.headers, partition=0, on_delivery=self._on_message_delivery
                )
        except BufferError as error:
            self._diagnostics.error(error, "Internal Kafka producer message queue is full")
            return False
        except NotImplementedError as error:
            self._diagnostics.error(error, "timestamp was specified without underlying library support")
            return False
        except KafkaException as error:
            self._diagnostics.error(error, "Kafka producer error")
            return False

        return True
