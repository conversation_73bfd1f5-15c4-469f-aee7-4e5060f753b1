- hosts: "{{ hosts }}"
  tasks:
  - name: create directory
    file:
      path: "/opt/staging_datavis_servant"
      state: directory
      mode: 0755
  - name: copy docker-compose file
    template:
      src: "staging_datavis_servant/docker-compose.yml"
      dest: "/opt/staging_datavis_servant/docker-compose.yml"
  - name: build & deploy containers
    docker_compose:
      build: yes
      recreate: always
      pull: yes
      project_src: "/opt/staging_datavis_servant"
      remove_orphans: yes
  - name: deploy nginx config
    template:
      src: "staging_datavis_servant/nginx.conf"
      dest: "/opt/nginx/conf.d/staging_datavis_servant.conf"
    notify:
    - reload nginx router

  handlers:
  - name: reload nginx router
    command: "/opt/nginx/reload.sh"
