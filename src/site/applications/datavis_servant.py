from argparse import Argument<PERSON>ars<PERSON>
from dataclasses import dataclass
from typing import List, NewType, Optional, Union, cast

import psycopg2.extensions
import requests
import uvicorn
from fastapi import Fast<PERSON><PERSON>, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from pydantic import BaseModel

from src.utils.application import Application
from src.utils.postgres import PgConnectionParams, postgres_connect
from src.utils.pyroutine import PyRoutineSystem
from src.utils.server import BindParams, bind_argument

""" Types """


UserId = NewType("UserId", str)
ChartId = NewType("ChartId", int)
QueryId = NewType("QueryId", int)
ChartKind = NewType("ChartKind", str)


@dataclass(frozen=True)
class ChartData:
    identifier: ChartId
    owner: UserId
    kind: ChartKind
    name: str
    content: str
    public: bool


@dataclass(frozen=True)
class QueryData:
    identifier: QueryId
    query: str


@dataclass(frozen=True)
class UserData:
    charts: List[ChartData]
    preferences: str


""" API keys """


class ApiKeyProvider:
    def has(self, key: UserId) -> bool:
        resp = requests.get(f"http://ams-api:7771/v1/check?api_key={key}", timeout=60)
        return resp.status_code == 200


""" Storage """


class ChartNotFound:
    pass


class QueryNotFound:
    pass


class ChartKindMismatch:
    pass


class InvalidAccess:
    pass


class IDatavisStorage:
    def create_chart(self, owner: UserId, kind: ChartKind, name: str, content: str, public: bool) -> ChartId:
        raise NotImplementedError

    def read_chart(
        self, user: Optional[UserId], chart_id: ChartId, chart_kind: ChartKind
    ) -> Union[ChartData, ChartNotFound, InvalidAccess, ChartKindMismatch]:
        raise NotImplementedError

    def create_query(self, owner: UserId, query: str) -> QueryId:
        raise NotImplementedError

    def read_query(self, query_id: QueryId) -> Union[QueryData, QueryNotFound]:
        raise NotImplementedError

    def read_user_data(self, user: UserId) -> UserData:
        raise NotImplementedError

    def update_chart(
        self,
        user: UserId,
        identifier: ChartId,
        name: Optional[str] = None,
        content: Optional[str] = None,
        public: Optional[bool] = None,
    ) -> Union[None, ChartNotFound, InvalidAccess]:
        raise NotImplementedError

    def update_user_preferences(self, user: UserId, preferences: str) -> None:
        raise NotImplementedError

    def delete_chart(self, owner: UserId, chart_id: ChartId) -> Union[None, ChartNotFound, InvalidAccess]:
        raise NotImplementedError


class PostgresDatavisStorage(IDatavisStorage):
    def __init__(self, connection: PgConnectionParams, schema: str):
        self._connection = connection
        self._schema = schema

        self._users = "{}.users".format(schema)
        self._charts = "{}.charts".format(schema)
        self._queries = "{}.queries".format(schema)

    def init_schema(self) -> None:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                cursor.execute("""CREATE SCHEMA IF NOT EXISTS {}""".format(self._schema))

                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS {} (
                        user_api_key TEXT,
                        user_preferences TEXT,
                        PRIMARY KEY (user_api_key)
                    )
                """.format(self._users)
                )

                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS {} (
                        chart_id BIGSERIAL,
                        chart_owner_api_key TEXT NOT NULL,
                        chart_kind TEXT NOT NULL,
                        chart_name TEXT NOT NULL,
                        chart_data TEXT NOT NULL,
                        chart_public BOOLEAN NOT NULL,
                        PRIMARY KEY (chart_id)
                    )
                """.format(self._charts)
                )

                cursor.execute(
                    """
                    CREATE INDEX IF NOT EXISTS charts_owner_api_key_index ON {}(chart_owner_api_key)
                """.format(self._charts)
                )

                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS {} (
                        query_id BIGSERIAL,
                        query TEXT NOT NULL,
                        query_owner_api_key TEXT NOT NULL,
                        PRIMARY KEY (query_id)
                    )
                """.format(self._queries)
                )

    def create_chart(self, owner: UserId, kind: ChartKind, name: str, content: str, public: bool) -> ChartId:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO {} (chart_kind, chart_name, chart_owner_api_key, chart_data, chart_public)
                    VALUES (%s, %s, %s, %s, %s) RETURNING chart_id
                """.format(self._charts),
                    (kind, name, owner, content, public),
                )

                return cast(ChartId, cursor.fetchone()[0])  # type: ignore

    def read_chart(
        self, user: Optional[UserId], chart_id: ChartId, kind: ChartKind
    ) -> Union[ChartData, ChartNotFound, ChartKindMismatch, InvalidAccess]:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                chart = self._get_chart(chart_id, cursor)

                if isinstance(chart, ChartNotFound):
                    return chart

                if chart.public:
                    return chart

                if user is None or chart.owner != user:
                    return InvalidAccess()

                if kind != chart.kind:
                    return ChartKindMismatch()

                return chart

    def create_query(self, owner: UserId, query: str) -> QueryId:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO {} (query, query_owner_api_key)
                    VALUES (%s, %s) RETURNING query_id
                """.format(self._queries),
                    (query, owner),
                )

                return cast(QueryId, cursor.fetchone()[0])  # type: ignore

    def read_query(self, query_id: QueryId) -> Union[QueryData, QueryNotFound]:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                query = self._get_query(query_id, cursor)

                if isinstance(query, QueryNotFound):
                    return query

                return query

    def read_user_data(self, user: UserId) -> UserData:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                cursor.execute("""SELECT user_preferences FROM {} WHERE user_api_key=%s""".format(self._users), (user,))
                rows = cursor.fetchall()

                if len(rows) == 0:
                    preferences = ""
                else:
                    preferences = rows[0][0]

                cursor.execute(
                    """
                    SELECT chart_id, chart_kind, chart_name, chart_data, chart_public
                    FROM {} WHERE chart_owner_api_key = %s
                """.format(self._charts),
                    (user,),
                )

                charts = [ChartData(row[0], user, *row[1:]) for row in cursor.fetchall()]
                return UserData(charts, preferences)

    def update_chart(
        self,
        owner: UserId,
        identifier: ChartId,
        name: Optional[str] = None,
        content: Optional[str] = None,
        public: Optional[bool] = None,
    ) -> Union[None, ChartNotFound, InvalidAccess]:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                owner_check = self._is_chart_owned_by(identifier, owner, cursor)

                if isinstance(owner_check, ChartNotFound):
                    return owner_check
                elif not owner_check:
                    return InvalidAccess()

                query_pieces = []
                parameters: List[Union[str, bool]] = []

                if name is not None:
                    query_pieces.append("chart_name = %s")
                    parameters.append(name)

                if content is not None:
                    query_pieces.append("chart_data = %s")
                    parameters.append(content)

                if public is not None:
                    query_pieces.append("chart_public = %s")
                    parameters.append(public)

                if len(query_pieces) == 0:
                    return None

                cursor.execute(
                    """
                    UPDATE {} SET {} WHERE chart_id = %s
                """.format(self._charts, ", ".join(query_pieces)),
                    (*parameters, identifier),
                )

                return None

    def update_user_preferences(self, user: UserId, preferences: str) -> None:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO {} (user_api_key, user_preferences) VALUES (%s, %s)
                    ON CONFLICT (user_api_key) DO UPDATE SET user_preferences = EXCLUDED.user_preferences
                """.format(self._users),
                    (user, preferences),
                )

    def delete_chart(self, owner: UserId, chart_id: ChartId) -> Union[None, ChartNotFound, InvalidAccess]:
        with postgres_connect(self._connection) as connection:
            with connection.cursor() as cursor:
                owner_check = self._is_chart_owned_by(chart_id, owner, cursor)

                if isinstance(owner_check, ChartNotFound):
                    return owner_check
                elif not owner_check:
                    return InvalidAccess()
                else:
                    cursor.execute("""DELETE FROM {} WHERE chart_id = %s""".format(self._charts), (chart_id,))
                    return None

    def _is_chart_owned_by(
        self, chart_id: ChartId, user: UserId, cursor: psycopg2.extensions.cursor
    ) -> Union[bool, ChartNotFound]:
        cursor.execute("""SELECT chart_owner_api_key FROM {} WHERE chart_id = %s""".format(self._charts), (chart_id,))
        rows = cursor.fetchall()

        if len(rows) == 0:
            return ChartNotFound()
        else:
            return cast(UserId, rows[0][0]) == user

    def _get_chart(self, chart_id: ChartId, cursor: psycopg2.extensions.cursor) -> Union[ChartData, ChartNotFound]:
        cursor.execute(
            """
            SELECT chart_owner_api_key, chart_kind, chart_name, chart_data, chart_public FROM {} WHERE chart_id = %s
        """.format(self._charts),
            (chart_id,),
        )

        rows = cursor.fetchall()

        if len(rows) == 0:
            return ChartNotFound()
        else:
            return ChartData(chart_id, *rows[0])

    def _get_query(self, query_id: QueryId, cursor: psycopg2.extensions.cursor) -> Union[QueryData, QueryNotFound]:
        cursor.execute(
            """
            SELECT query FROM {} WHERE query_id = %s
        """.format(self._queries),
            (query_id,),
        )

        rows = cursor.fetchall()

        if len(rows) == 0:
            return QueryNotFound()
        else:
            return QueryData(query_id, *rows[0])


""" Utilities """


def user_id_from_header(header: str, api_key_provider: ApiKeyProvider) -> Optional[UserId]:
    pieces = header.split(" ")

    if len(pieces) != 2:
        return None

    if pieces[0].lower() != "bearer":
        return None

    user_id = UserId(pieces[1])
    return user_id if api_key_provider.has(user_id) else None


""" Requests """


class CreateChartRequest(BaseModel):
    kind: ChartKind
    name: str
    content: str
    public: bool = False


class CreateQueryRequest(BaseModel):
    query: str


class ReadChartRequest(BaseModel):
    chart_id: ChartId
    kind: ChartKind


class ReadQueryRequest(BaseModel):
    query_id: QueryId


class UpdateChartRequest(BaseModel):
    chart_id: ChartId
    name: Optional[str] = None
    content: Optional[str] = None
    public: Optional[bool] = None


class UpdateUserPreferencesRequest(BaseModel):
    preferences: str


class DeleteChartRequest(BaseModel):
    chart_id: ChartId


""" Responses """


class ChartIdResponse(BaseModel):
    chart_id: ChartId


class ChartResponse(BaseModel):
    @staticmethod
    def from_chart_data(data: ChartData) -> "ChartResponse":
        return ChartResponse(chart_id=data.identifier, kind=data.kind, name=data.name, content=data.content, public=data.public)

    chart_id: ChartId
    kind: ChartKind
    name: str
    content: str
    public: bool


class QueryIdResponse(BaseModel):
    query_id: ChartId


class UserDataResponse(BaseModel):
    preferences: str
    charts: List[ChartResponse]


class OkResponse(BaseModel):
    pass


""" Business logic """


def customize_docs(api: FastAPI) -> None:
    schema = get_openapi(title="Data visualizer backend", version="3.0.0", description="", routes=api.routes)

    for route, route_schema in schema["paths"].items():
        route_schema["post"]["responses"]["401"] = {
            "description": "Authorization failed",
            "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthFail"}}},
        }

    for route in ("/read_chart", "/update_chart", "/delete_chart"):
        schema["paths"][route]["post"]["responses"]["404"] = {
            "description": "Chart not found",
            "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartNotFound"}}},
        }
        schema["paths"][route]["post"]["responses"]["403"] = {
            "description": "Access denied",
            "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessDenied"}}},
        }

    schema["paths"]["/read_chart"]["post"]["responses"]["400"] = {
        "description": "Chart kind mismatch",
        "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartKindMismatch"}}},
    }

    for obj_name in ("AuthFail", "ChartNotFound", "AccessDenied", "ChartKindMismatch"):
        schema["components"]["schemas"][obj_name] = {
            "title": obj_name,
            "type": "object",
            "properties": {"detail": {"title": "Detail", "type": "string"}},
        }

    api.openapi_schema = schema


def create_api(
    api_key_provider: ApiKeyProvider, storage: IDatavisStorage, cors_origins: List[str], disable_docs: bool
) -> FastAPI:
    if disable_docs:
        api = FastAPI(docs_url=None, redoc_url=None, openapi_url=None)
    else:
        api = FastAPI()

    auth_header_description = "Format: `bearer $API_KEY`"
    auth_header = Header("", description=auth_header_description)

    @api.post("/create_chart", response_model=ChartIdResponse)
    def create_chart(request: CreateChartRequest, authorization: str = auth_header) -> ChartIdResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        chart_id = storage.create_chart(user_id, request.kind, request.name, request.content, request.public)
        return ChartIdResponse(chart_id=chart_id)

    @api.post("/read_chart", response_model=ChartResponse)
    def read_chart(
        request: ReadChartRequest, authorization: Optional[str] = Header(None, description=auth_header_description)
    ) -> ChartResponse:
        if authorization is not None:
            user_id = user_id_from_header(authorization, api_key_provider)
            if user_id is None:
                raise HTTPException(status_code=400, detail="authorization failed")
        else:
            user_id = None

        chart = storage.read_chart(user_id if user_id is not None else None, request.chart_id, request.kind)

        if isinstance(chart, ChartNotFound):
            raise HTTPException(status_code=404, detail="chart not found")
        elif isinstance(chart, InvalidAccess):
            raise HTTPException(status_code=403, detail="access denied")
        elif isinstance(chart, ChartKindMismatch):
            raise HTTPException(status_code=400, detail=" chart kind mismatch")

        return ChartResponse.from_chart_data(chart)

    @api.post("/create_query", response_model=QueryIdResponse)
    def create_query(request: CreateQueryRequest, authorization: str = auth_header) -> QueryIdResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        query_id = storage.create_query(user_id, request.query)
        return QueryIdResponse(query_id=query_id)

    @api.post("/read_query")
    def read_query(request: ReadQueryRequest) -> Response:
        query = storage.read_query(request.query_id)

        if isinstance(query, QueryNotFound):
            raise HTTPException(status_code=404, detail="query not found")

        response = requests.get(query.query, timeout=60)
        if response.status_code != 200:
            raise HTTPException(
                status_code=500,
                detail=f"query failed with status: {response.status_code} and error {response.content.decode()}",
            )

        return Response(content=response.content, media_type="application/json")

    @api.post("/read_user_data", response_model=UserDataResponse)
    def read_user_data(authorization: str = auth_header) -> UserDataResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        data = storage.read_user_data(user_id)

        return UserDataResponse(
            preferences=data.preferences, charts=[ChartResponse.from_chart_data(chart) for chart in data.charts]
        )

    @api.post("/update_chart", response_model=OkResponse)
    def update_chart(request: UpdateChartRequest, authorization: str = auth_header) -> OkResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        result = storage.update_chart(user_id, request.chart_id, request.name, request.content, request.public)

        if isinstance(result, ChartNotFound):
            raise HTTPException(status_code=404, detail="chart not found")
        elif isinstance(result, InvalidAccess):
            raise HTTPException(status_code=403, detail="access denied")
        else:
            return OkResponse()

    @api.post("/update_user_preferences", response_model=OkResponse)
    def update_user_preferences(request: UpdateUserPreferencesRequest, authorization: str = auth_header) -> OkResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        storage.update_user_preferences(user_id, request.preferences)
        return OkResponse()

    @api.post("/delete_chart", response_model=OkResponse)
    def delete_chart(request: DeleteChartRequest, authorization: str = auth_header) -> OkResponse:
        user_id = user_id_from_header(authorization, api_key_provider)
        if user_id is None:
            raise HTTPException(status_code=400, detail="authorization failed")

        result = storage.delete_chart(user_id, request.chart_id)

        if isinstance(result, ChartNotFound):
            raise HTTPException(status_code=404, detail="chart not found")
        elif isinstance(result, InvalidAccess):
            raise HTTPException(status_code=403, detail="access denied")
        else:
            return OkResponse()

    if len(cors_origins) > 0:
        api.add_middleware(
            CORSMiddleware, allow_origins=cors_origins, allow_credentials=True, allow_methods=["*"], allow_headers=["*"]
        )

    customize_docs(api)
    return api


def run() -> None:
    arg_parser = ArgumentParser()
    arg_parser.add_argument("database", type=PgConnectionParams, help="PostgreSQL connection host:port:dbname:user:password")
    arg_parser.add_argument("db_schema", type=str, help="PostgreSQL schema name")
    arg_parser.add_argument(
        "--bind", type=bind_argument, default=BindParams("localhost", 8000), help="Server bind params host:port"
    )
    arg_parser.add_argument("--cors-origins", type=str, nargs="+", default=[])
    arg_parser.add_argument(
        "--init-db", action="store_true", help="If set, tables required by application will be created inside the DB_SCHEMA"
    )
    arg_parser.add_argument("--prometheus", type=bind_argument, default=None)
    arg_parser.add_argument("--no-docs", action="store_true", help="Disables documentation available at /redoc and /docs")
    args = arg_parser.parse_args()

    with Application(prometheus=args.prometheus) as app:
        with PyRoutineSystem(app.diagnostics) as system:
            provider = ApiKeyProvider()

            storage = PostgresDatavisStorage(args.database, args.db_schema)
            if args.init_db:
                storage.init_schema()
                app.diagnostics.info("initialized database")

            uvicorn.run(create_api(provider, storage, args.cors_origins, args.no_docs), host=args.bind.host, port=args.bind.port)

            system.request_stop()


if __name__ == "__main__":
    run()
