#!/usr/bin/env bash
#------------------------------------------------------------------------------------
# What: Disable feed handler pods in K8s environments
# Author: <PERSON>
#
# Features:
#   * Disable exchange specific pods
#   * Disable market/type pods
#
# Usage:
#   ./disable-fh -c copper -e hitbtc -m spot -t trades
#
#
#
#
SCRIPT_NAME=$(basename "${0}")
VERSION=0.1
CONTEXT=
EXCHANGE=
MARKET=
TYPE=
INSTANCE=
PREVIEW=

NAMESPACE="fh"
EXIT_CODE=0

# Print a log a message
log() {
	echo "[${SCRIPT_NAME}]: $1" >/dev/stderr
}

# Print an error message and exit()
error() {
	echo "[${SCRIPT_NAME}] ERROR: $1" >/dev/stderr
	[ "${#}" -gt 1 ] && exit "${2}"
	EXIT_CODE=1
	exit "${EXIT_CODE}"
}

# Get feed handlers deployments
get_deployments() {
    local deployments
    deployments=$(kubectl --context "${CONTEXT}" -n "${NAMESPACE}" get deployments --no-headers -o custom-columns=":metadata.name")
    # Filter by feed-handler (no shippers, tickers)
    deployments=$(echo "${deployments}" | grep "feed-handler-")
    # Filter by exchange
	if [ -n "${EXCHANGE}" ]; then
        deployments=$(echo "${deployments}" | grep -- -"${EXCHANGE}"-)
	fi

    # Filter by type, e.g book, trade
    deployments=$(echo "${deployments}" | grep -- -"${TYPE}"-)
    # # # Filter by market, e.g. spot, futures
    deployments=$(echo "${deployments}" | grep -- -"${MARKET}"-)
    # # # Filter by instance, e.g. 1, 2
    deployments=$(echo "${deployments}" | grep -e ".*-${INSTANCE}\$")


    echo "${deployments}"
}

# Scale down feed handler deployments
scale_deployments() {
    local deployments
    deployments=$(get_deployments)
    echo "${deployments}" | xargs -I {} kubectl --context "${CONTEXT}" -n "${NAMESPACE}" scale --replicas="${1}" deployment/{}
}

# Print a usage message
usage() {
	cat <<USAGE
usage: $0 [-Vhpe] -c CONTEXT -i INSTANCE -m MARKET -t TYPE

Scale down feed handler deployments in kubernetes environment.


REQUIRED OPTIONS:
    -c CONTEXT K8s context, e.g. 'cdev1', 'cp1'
    -i INSTANCE feed handler stack instance {1|2}
    -t TYPE feed handler type {book|trade|meta|quote|ticker|open}
    -m MARKET feed handler market {spot|futures|options}
OTHER OPTIONS:
    -p         Preview mode (deployments will not be scaled)
    -e         Filter for specific exchange
    -h         Show this help message
    -V         Show version
Examples:
    # Disable all stack 1 spot trade feed handlers on copper
    ./disable-fh -c copper -t trade -m spot -i 1
    # Disable stack 2 binance futures books on copper
    ./disable-fh -c copper -t book -m futures -i 2 -e binance
    # Preview disable stack 2 open interest  futures on copper
    ./disable-fh -c copper -t open -m futures -p
USAGE
}

# Get the script options
get_options() {
    if [[ ! $* =~ ^\-.+ ]]; then
	    usage && exit 0
    fi
	while getopts ":c:n:e:m:t:i:Vhp-:" OPTION; do
		if [ "${OPTION}" == "-" ]; then
			OPTION=$OPTARG
		fi
		case $OPTION in
		c) CONTEXT=${OPTARG} ;;
		i) INSTANCE=${OPTARG} ;;
        e) EXCHANGE=$(echo "${OPTARG}" | awk '{print tolower($0)}' ) ;;
		n) NAMESPACE=${OPTARG} ;;
        p) PREVIEW="1" ;;
        m) MARKET=$(echo "${OPTARG}" | awk '{print tolower($0)}' ) ;;
        t) TYPE=$(echo "${OPTARG}" | awk '{print tolower($0)}') ;;
		h) usage && exit 0 ;;
		'help') usage && exit 0 ;;
		V) echo $VERSION && exit 0 ;;
		'version') echo $VERSION && exit 0 ;;
		\?) echo "Invalid option" && usage && exit 1 ;;
		esac
	done
}

main() {
	get_options "$@"
	if [ -z "${CONTEXT}" ]; then
		error "Provide -c CONTEXT"
	fi

	if [ -z "${NAMESPACE}" ]; then
		error "Provide -n NAMESPACE"
	fi

    if [[ "${TYPE}" = @(book|trade|meta|quote|ticker|open) ]]; then
        TYPE=$(echo "${TYPE}" | cut -c1-4)
    else
        error "Provide -t TYPE [book, trade, meta, quote, ticker, open]"
    fi

    if [[ ! "${INSTANCE}" = @(1|2) ]]; then
        error "Provide -i INSTANCE [1, 2]"
    fi

    if [[ "${MARKET}" = @(spot|futures|options) ]]; then
        MARKET=$(echo "${MARKET}" | cut -c1-4)
    else
        error "Provide -m MARKET [spot, futures, options]"
    fi

    if [[ "${PREVIEW}" = 1 ]]; then
        log "Preview mode"
        get_deployments
    else
        log "Scaling down feed handlers"
        scale_deployments 0
    fi

	exit ${EXIT_CODE}
}

trap EXIT ERR INT TERM

main "$@"
