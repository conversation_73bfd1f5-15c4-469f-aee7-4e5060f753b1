#!/usr/bin/env python3

import json
import os
import pkgutil
import sys

from enum import Enum
from typing import List

import ams_package_generators

class TimeRestrictions(Enum):
    BACK_1H_FROM_LATEST = "latest-1h"
    """
    Can access data one hour back relative to the time of the latest data point.
    """
    BACK_1D_FROM_LATEST = "latest-1d"
    """
    Can access data one day back relative to the time of the latest data point.
    """
    BACK_30D_FROM_LATEST = "latest-30d"
    """
    Can access data thirty days back relative to the time of the latest data point.
    """
    BACK_30D_FROM_NOW = "now-30d"
    """
    Can access data thirty days back relative to the current time.
    """
    NONE = "null"
    """
    Can access all the data.
    """

    @classmethod
    def get_list_of_time_restrictions(cls) -> List[str]:
        return ['null'] + [item.value for item in cls if isinstance(item.value, str) if item.value != "null"]


load_testing_keys = {'xWhwon4HuJbueq1QMUbv'}
cmbi_internal_key = ["ujwrC33GjUBXif0Lbtsd"]

def check_reference_rate_assets():
    with open('currency.json') as json_file:
        data = json.load(json_file)
        for currency in data:
            asset = currency['cm_ticker']
            print('Check ref rates for ' + asset)
            # todo handle "dead"
            metadata = currency["metadata"]
            if "genesis" not in metadata:
                continue
            if metadata["hasReferenceRate"]:
                assert metadata["hasRTRR"]  # simplification
            else:
                assert not metadata["hasRTRR"]  # simplification


def join_lines(lines, prefix_for_non_first_lines ='+ '):
    if len(lines) == 0:
        return ""
    if len(lines) == 1:
        return lines[0]
    lines_with_operation = []
    for line in lines:
        if len(lines_with_operation) == 0 or line.startswith('- ') or line.startswith(' -') or line.startswith('+ '):
            prefix = ""
        else:
            prefix = prefix_for_non_first_lines
        lines_with_operation.append(f"{prefix}{line}")
    return lines_with_operation


def build_regular_packages():
    print('Building regular packages...')

    out_regular_packages = {}

    for importer, modname, ispkg in pkgutil.iter_modules(ams_package_generators.__path__):
        submodule = sys.modules["ams_package_generators." + modname]
        package_type = submodule.package_type() if hasattr(submodule, 'package_type') else "regular"
        if package_type == "regular":
            script = []
            if hasattr(submodule, "package_script"):
                script += submodule.package_script()
            package_obj = {"script": join_lines(script)}
            if hasattr(submodule, "package_discovery_script"):
                package_obj["discovery_script"] = join_lines(submodule.package_discovery_script())
            out_regular_packages[modname] = package_obj

    # write result
    if not os.path.exists("ams-data/packages"):
        os.mkdir("ams-data/packages")

    for package_name, package_data in out_regular_packages.items():
        with open(f'ams-data/packages/{package_name}.json', 'w') as out_file:
            json.dump(package_data, out_file, indent=4)
            print(file=out_file)

def build_composite_packages():
    print('Composite packages...')
    out_composite_packages = {}

    from ams_package_generators.asset_metrics import calc_composite_asset_metrics_package_script

    for importer, modname, ispkg in pkgutil.iter_modules(ams_package_generators.__path__):
        submodule = sys.modules["ams_package_generators." + modname]
        package_type = submodule.package_type() if hasattr(submodule, 'package_type') else "regular"
        if package_type == "composite":
            script = []
            if hasattr(submodule, "compute_and_append_asset_metrics"):
                script = calc_composite_asset_metrics_package_script(modname)
            if hasattr(submodule, "package_script"):
                script += submodule.package_script()
            out_composite_packages[modname] = {"script": join_lines(script)}

    prepend_dict = {
        "type": "composite"
    }
    for package_name, package_data in out_composite_packages.items():
        with open(f'ams-data/packages/{package_name}.json', 'w') as out_file:
            # prepend "type": "composite" to every composite package
            package_data = {**prepend_dict, **package_data}
            json.dump(package_data, out_file, indent=4)
            print(file=out_file)


def calc_api_key_script(key):
    # Just to speed up ams-data generation
    if not key.get('enabled', True):
        return []

    lines = []

    if key.get("script"):
        lines.extend(key.get("script"))

    # All the grayscale institution metrics should be available to everyone,
    # since that was a requirement in order for them to agree to share the data with us.
    # And grayscale is currently the only institution metrics we have. If we added more institution metrics though,
    # most likely they would only be available to paid users.
    lines.append("institution_metrics")

    # Market cap metrics
    lines.append('cg')

    if key['key'] in cmbi_internal_key:
        lines = [line for line in lines if 'asset_metrics' not in line]
        lines.append('asset_metrics')

    return lines


def build_api_keys():
    # read api keys into memory and convert
    print('Keys...')
    out_api_keys = {}

    with open('api_keys.json') as json_file:
        data = json.load(json_file)
        for k in data:
            print('Converting ' + k['key'])
            out_api_keys[k['key']] = {
                'script': join_lines(calc_api_key_script(k))
            }
            if not k.get('enabled', True):
                out_api_keys[k['key']]['enabled'] = False
            if k.get('expires'):
                out_api_keys[k['key']]['expires'] = k['expires']
            if k.get('type') == 'viz':
                out_api_keys[k['key']]['type'] = 'vis'
            if k.get('key') in load_testing_keys:
                out_api_keys[k['key']]["rate_limit_plan"] = "unlim"
            if 'trial' in k['description'].lower():
                out_api_keys[k['key']]["rate_limit_plan"] = "trial"

    # api v4 specific keys

    # modify community key
    out_api_keys["T1z1tZcIY2tnX4jcUhve"]["rate_limit_plan"] = "community"
    out_api_keys["T1z1tZcIY2tnX4jcUhve"]["rate_limit_by_ip"] = True

    # write result
    with open('ams-data/api_keys.json', 'w') as out_file:
        json.dump(out_api_keys, out_file, indent=4)
        print(file=out_file)

def build_rate_limits():
    out_rate_limits = {
        "default": {
            "unlim": "1000000/1s",
            "default": "6000/20s",
            "trial": "10/6s",
            "community": "10/6s"
        }
    }
    # write result
    with open('ams-data/rate_limits.json', 'w') as out_file:
        json.dump(out_rate_limits, out_file, indent=4)
        print(file=out_file)

if __name__ == '__main__':
    if not os.path.exists("ams-data"):
        os.mkdir("ams-data")

    check_reference_rate_assets()
    build_regular_packages()
    build_composite_packages()
    build_api_keys()
    build_rate_limits()
