[{"name": "Anchorage", "supported_assets": ["aave", "alpha", "ant", "audio", "rep", "axs", "badger", "bal", "bnt", "bat", "btc", "bch", "auction", "btrst", "celo", "cusd", "ceur", "cel", "link", "comp", "atom", "crv", "dai", "mana", "dodo", "dydx", "eth", "fil", "prints", "flow", "foam", "fort", "fwb", "ftt", "fst", "hegic", "inx", "keep", "knc", "ldo", "ltc", "lrc", "mkr", "mpl", "mir", "nii", "nftx", "note", "nmr", "rose", "omg", "oxt", "osmo", "usdp", "perp", "hash", "matic", "rad", "rly", "flx", "ren", "rbn", "sai", "srm", "xlm", "strp", "sushi", "snx", "xtz", "grt", "rune", "tribe", "tusd", "tru", "uma", "uni", "usdc", "wcelo", "wcusd", "wfil", "wflow", "wzec", "xsushi", "yfi", "zec", "zrx", "1inch"]}, {"name": "Bakkt Warehouse", "supported_assets": ["btc", "eth"]}, {"name": "BitGo", "supported_assets": ["algo", "avax", "btc", "bch", "btg", "cspr", "celo", "dash", "eos", "eth", "hbar", "ltc", "rbtc", "stx", "xlm", "xtz", "trx", "xrp", "zec", "1inch", "1up", "aave", "abt", "ace", "acxt", "ach", "ae", "aergo", "aergo1", "agwd", "aion", "ali", "alpha", "amn", "amo", "amon", "amp", "ampx", "ana", "ape", "api3", "ant", "antv2", "aoa", "appc", "aqt", "arct", "arcx", "ast", "atri", "audio", "axl", "audx", "aust", "axpr", "axs", "axsv2", "badger", "bal", "band", "basic", "bat", "bax", "bbx", "bcap", "bcc", "bcio", "bepro", "bed", "bid", "blocks", "bidl", "bird", "bit", "bnb", "bnk", "bnl", "bnt", "bnty", "boba", "bond", "box", "brd", "brz", "bsx", "btrst", "btt", "btu", "burp", "busd", "buy", "bxx", "bxxv1", "bzz", "c8p", "cacxt", "cadx", "cag", "cbat", "cbc", "cbrl", "cct", "cdag", "cdai", "cdt", "cel", "celr", "ceth", "chfx", "chsb", "chz", "cix100", "cliq", "cln", "clt", "clv", "cng", "cnyx", "comp", "cover", "cpay", "cplt", "cqt", "cqx", "cra", "crdt", "cre", "cream", "crep", "cro", "crpt", "crpt1", "crv", "cslv", "csp", "ctsi", "cusdc", "cvc", "cvx", "cwbtc", "czrx", "coti", "dacxi", "dai", "dao", "data", "datav2", "dataecon", "dec", "dent", "dep", "dexa", "dfd", "dfi", "dgcl", "dgd", "dgx", "digg", "dmt", "dodo", "dpi", "drpu", "drv", "duc", "dx1u", "dxgt", "dxpt", "dxst", "dydx", "dyn", "easy", "ebtcq", "echt", "edison", "edn", "edr", "efi", "egl", "egld", "egold", "elf", "erd", "emx", "eng", "enj", "ens", "eqo", "eta", "ethos", "etv", "eurs", "<PERSON><PERSON><PERSON>", "euroc", "eurt", "eurx", "eux", "evx", "exe", "fdt", "fei", "fet", "fet1", "ff1", "fft", "fire", "front", "fmf", "ftm", "ftt", "fun", "fwb", "fxrt", "fxs", "gal", "gala", "gamma", "gbpx", "gdt", "gec", "gen", "gohm", "ghub", "gigdrop", "gldx", "glm", "gno", "gnt", "gods", "gog", "gold", "got", "grt", "gtc", "gto", "gusd", "gxc", "gyen", "hcn", "hdo", "hedg", "hkdx", "hlc", "hmt", "hold", "hot", "hqt", "hst", "ht", "hum", "husd", "hxro", "hyb", "hydro", "i8", "iceth", "idex", "idrc", "idrt", "imxv2", "incx", "ind", "index", "inf", "inj", "injv2", "inst", "inx", "isf", "isr", "ivo", "ivy", "jbc", "jfin", "jpyx", "keep", "key", "kin", "kiro", "knc", "knc2", "koin", "koz", "kp3r", "kze", "layer", "lba", "lend", "leo", "lgo", "link", "lion", "lnc", "loom", "loom1", "looks", "lrc", "lrcv2", "lyn", "mana", "maps", "matic", "mcdai", "mco", "mco2", "mcs", "mcx", "mdfc", "mdx", "medx", "meme", "met", "meta", "mfg", "mfph", "mft", "milkv2", "mir", "mith", "mix", "mizn", "mkr", "mns", "moc", "mof", "mpay", "mpl", "mtcn", "mtl", "musd", "mvl", "mvi", "mwt", "nas", "nct", "ndx", "neu", "nexo", "nftx", "ngnt", "niax", "nmr", "npxs", "ns2drp", "nu", "nym", "nzdx", "ocean", "oceanv2", "ogn", "okb", "om", "omg", "onl", "op", "opt", "orai", "orbs", "oxt", "oxy", "ohm", "par", "pass", "pau", "pax", "paxg", "pay", "pbch", "pbtc", "pdata", "peg", "perp", "peth", "pfct", "phnx", "pie", "planet", "plc", "plnx", "plx", "pma", "poly", "powr", "ppt", "prdx", "prints", "pro", "prts", "pstake", "pundix", "pusd", "pxp", "pyr", "qash", "qcad", "qdt", "qkc", "qnt", "qrdo", "qrl", "qsp", "quick", "qvt", "rad", "rare", "rari", "ray", "rby", "rdn", "reb", "rebl", "reef", "rep", "repv2", "seth-h", "reth-h", "rfr", "rfuel", "rgt", "rif", "ringx", "rlc", "rsr", "rly", "rndr", "ron", "ronc", "roobee", "rook", "rubx", "ruedatk", "salt", "sand", "sashimi", "sd", "sga", "sgdx", "sgr", "shib", "shk", "shopx", "shr", "sih", "sipher", "sis", "silv", "skale", "slab", "slot", "slp", "slvx", "snc", "snov", "snt", "snx", "soc", "sohm", "solve", "spell", "spo", "srnt", "stbu", "stc", "stcv2", "stkaave", "store", "storj", "storm", "stmx", "stzen", "squig", "super", "sushi", "sxp", "t", "taud", "tbtc1", "tcad", "tco", "tel", "ten", "tenx", "tgbp", "thkd", "tiox", "trl", "tknt", "tkx", "tlab", "tnt", "tok", "traxx", "trac", "tribe", "trst", "tru", "tryb", "bico", "tryx", "tusd", "txl", "uair", "uco", "ukg", "uma", "umee", "unb", "uni", "up", "upbtc", "upp", "upt", "upusd", "uqc", "usdc", "usdt", "usdx", "usg", "uspx", "ust", "usx", "utk", "utk1", "valor", "vdx", "vega", "visr", "vrgx", "vsp", "vxc", "wabi", "wafl", "wax", "wbtc", "wcfg", "wec", "wet", "weth", "whale", "wht", "wnxm", "woo", "wpx", "wsteth", "wtc", "wtk", "wxrp", "wxrpv0", "wluna", "wlxt", "wxt", "wild", "xaud", "xbgold", "xcd", "xex", "xrl", "xsgd", "xsushi", "xtp", "yfdai", "yfi", "yfii", "ygg", "yld", "yng", "ysey", "zarx", "zco", "zil", "zix", "zlw", "zmt", "zoom", "zrx", "zusd", "seth2", "reth2", "ldo", "gbpt", "ever"]}, {"name": "Coinbase Custody", "supported_assets": ["btc", "eth", "usdc", "xrp", "busd", "ada", "sol", "doge", "matic", "dot", "shib", "dai", "avax", "wbtc", "ftt", "atom", "ltc", "etc", "link", "uni", "xlm", "cro", "qnt", "algo", "bch", "fil", "ape", "icp", "egld", "xtz", "eos", "ldo", "sand", "aave", "chz", "mana", "apt", "axs", "pax", "mkr", "1inch", "cusdc", "snx", "zec", "ceth", "grt", "cdai", "nexo", "ftm", "crv", "paxg", "ens", "gusd", "enj", "bat", "stx", "sushi", "cvx", "amp", "cel", "lrc", "comp", "hot", "cgld", "ksm", "rose", "bal", "srm", "iotx", "gala", "yfi", "lpt", "ankr", "omg", "poly", "dydx", "zrx", "rsr", "woo", "zen", "audio", "inj", "rndr", "uma", "skl", "pla", "ogn", "fx", "mask", "ren", "api3", "tribe", "imx", "coti", "snt", "dexe", "reef", "cvc", "exrd", "orbs", "alice", "nmr", "prom", "bnt", "spell", "powr", "ctsi", "celr", "req", "rad", "chr", "tel", "dent", "ocean", "pha", "xyo", "rlc", "sure", "trac", "ant", "keep", "dodo", "nest", "mtl", "utk", "aergo", "storj", "ach", "fet", "nkn", "badger", "ern", "oxt", "ads", "repv2", "forth", "xsgd", "rly", "super", "wnxm", "bond", "yfii", "band", "pols", "rare", "pro", "cbat", "pond", "tru", "idex", "hunt", "perp", "lcx", "orn", "trb", "mln", "unfi", "alcx", "cre", "arpa", "dpi", "ubt", "dia", "albt", "mir", "key", "blz", "looks", "rari", "farm", "rook", "adx", "plu", "fida", "noia", "ast", "om", "musd", "ramp", "jasmy", "fis", "orca", "qash", "front", "coval", "upp", "uft", "hegic", "mix", "titan", "mdt", "prq", "cvp", "tvk", "rgt", "avt", "ddx", "dad", "boa", "nct", "math", "krl", "pnt", "fox", "btu", "rai", "abt", "qsp", "ttt", "foam", "cream", "zcn", "swftc", "crpt", "atlas", "rac", "vid", "dvi", "el", "suku", "muse", "grid", "jup", "mona", "xed", "maha", "orai", "dyp", "dsla", "mta", "yop", "srk", "gswap", "czrx", "mph", "cube", "stake", "rfuel", "upi", "nord", "fkx", "edda", "minds", "brd", "onx", "mfg", "dfd", "props", "syn", "clv", "cpu", "gfdg", "antv1", "gffc", "hlno", "rsva", "rscp", "bayc", "loot", "csp", "idea", "hlab", "dora", "tvsp", "paper", "beta", "1717", "hllx", "hapi", "udo", "blocks", "oct", "swth", "rfox", "nftd", "radar", "cqt", "eden", "twr27", "hlfb", "ntvl", "edsn", "lhok", "vr", "isp", "lmr", "hotcross", "xmon", "fxc", "krom", "sbr", "dao", "nwc", "pilot", "00", "frel", "alk", "<PERSON><PERSON><PERSON>", "eth2", "gal", "ndx", "sofi", "alpha", "zcx", "pbx", "tone", "inxt", "vsp", "matter", "angle", "boson", "sweat", "youc", "psp", "cell", "dpx", "fscc", "bfc", "si", "punks", "deso", "mpl", "dola", "gods", "aioz", "urqa", "xcn", "mint", "ygg", "umee", "cbeth", "stg", "vgx", "rbn", "oja", "rpl", "dhv", "gf", "cast", "tbtc", "mim", "cft", "hopr", "nu", "ufo", "op", "btrst", "dfa", "auction", "wxt", "lqty", "emb", "wcfg", "gfi", "elon", "ctx", "metis", "bico", "c98", "dar", "mco2", "knc", "agld", "high", "gyen", "quick", "glm", "asm", "boba", "wampl", "bcap"]}, {"name": "Copper", "supported_assets": ["code", "1inch", "4art", "a5t", "aave", "abyss", "ada", "adx", "ae", "aergo", "ageur", "agix", "aid", "aion", "akro", "aleph", "algo", "alice", "alpha", "amp", "ampl", "ankr", "ant", "antv1", "ape", "apt", "ar", "arb<PERSON>", "arcc", "aria", "ata", "atlas", "atmi", "atom", "auc", "audio", "audt", "aurora", "aury", "auto", "avax", "avaxc", "avt", "axl", "axs", "b21", "bacon", "bake", "bal", "band", "bank", "bat", "bbn", "bch", "best", "beth", "bico", "bit", "bkk", "blnd", "blz", "bnb", "bnt", "boba", "bond", "boson", "box", "bpt", "brkl", "bsc", "bsv", "btc", "btcb", "btrst", "btt", "busd", "cake", "cbt", "ccd", "cdai", "cel", "celr", "cennz", "ceth", "cfx", "chr", "chz", "cirus", "clny", "clv", "cnd", "cnft", "cnn", "comp", "cope", "cos", "coti", "cream", "cro", "cru", "crv", "ctk", "ctsi", "ctxc", "cube", "cusdc", "cusdt", "cvc", "cwbtc", "dadi", "dai", "dao", "daofi", "dash", "data", "dego", "dent", "derc", "dgb", "dgx", "dia", "divi", "dmg", "dmlg", "dnt", "dnxc", "doge", "dot", "dpx", "dta", "dth", "dusk", "dydx", "eco", "edo", "efi", "egld", "elf", "enj", "eos", "eps", "ept", "esd", "ess", "etc", "eth", "eurs", "ewt", "exm", "exrd", "farm", "fdai", "fear", "fet", "fil", "flex", "flexusd", "flow", "flx", "foam", "fodl", "forth", "fox", "frax", "ftm", "ftt", "fun", "fusdc", "fusdt", "fuse", "fxs", "game", "gene", "ghst", "glm", "gmx", "gno", "gnt", "gobtc", "goeth", "grt", "gusd", "gyen", "hbb", "hdl", "he", "hnt", "holo", "hop", "hopr", "hot", "ht", "husd", "hxro", "idex", "imx", "inj", "instar", "iost", "ipad", "jbx", "jet", "jpeg", "jst", "keep", "key", "kin", "klay", "knc", "kncl", "ksm", "ldo", "lend", "light", "lina", "link", "lit", "lnd", "looks", "loom", "lpt", "lqty", "lrc", "ltc", "lto", "ltoold", "luna", "lusd", "lym", "lyra", "man", "mana", "maps", "mask", "mat", "matic", "mbs", "mc", "mcau", "mdx", "mft", "mgo", "mimo", "miota", "mir", "mith", "mkr", "mln", "mngo", "mob", "mpl", "mplx", "mtl", "mtn", "multi", "myst", "ncash", "near", "nec", "neon", "nftd", "nfti", "nftx", "nio", "nkn", "nmr", "noia", "npxs", "nu", "nym", "ocean", "ode", "ogn", "okb", "om", "omg", "omni", "ont", "opul", "orbs", "orca", "orion", "oris", "ors", "oxt", "oxy", "pan", "par", "pax", "paxg", "pbx", "pcl", "pdex", "perp", "pha", "planets", "plbt", "plr", "plug", "poly", "pond", "powr", "ppt", "prints", "prom", "props", "prq", "pundix", "qash", "qdao", "qkc", "qnt", "quad", "rare", "rari", "ray", "rbn", "rbtc", "rcn", "rdn", "reef", "regen", "ren", "rep", "repv1", "req", "reth", "ride", "rif", "rlc", "rly", "rndr", "rook", "rose", "rpl", "rsr", "rsv", "rte", "rune", "sai", "san", "sand", "sb", "sbr", "seer", "sen", "sf", "sfi", "sgb", "shib", "skl", "slm", "slnd", "slp", "smt", "sngls", "snt", "snx", "sol", "solo", "solve", "spell", "srm", "srp", "stakedeth", "stakedxem", "stakedxzc", "steth", "stg", "stmatic", "stmx", "storj", "stsol", "stx", "sub", "suku", "sunny", "super", "susd", "sushi", "swell", "swm", "sxp", "sylo", "syn", "t", "taur", "tcp", "tcr", "theta", "time", "tkn", "tko", "tlm", "tnb", "toke", "tomo", "toncoin", "trb", "tribe", "troy", "trx", "ttt", "tusd", "tvk", "uma", "uni", "uop", "usdc", "usdt", "ust", "utk", "utnp", "vai", "vee", "vega", "vent", "vet", "vgx", "vtho", "vusdc", "vxt", "wabi", "walgo", "wax", "wbnb", "wbtc", "wcfg", "weth", "win", "wnxm", "woo", "wpr", "wsteth", "wtc", "xcn", "xdb", "xdc", "xdce", "xem", "xidr", "xlm", "xmon", "xor", "xra", "xrp", "xsgd", "xsushi", "xtp", "xtz", "xvg", "xvs", "xym", "xzc", "yfi", "yfii", "ygg", "yldy", "yoyow", "zbc", "zcn", "zen", "zero", "zil", "zrx", "zusd"]}, {"name": "Gemini Custody", "supported_assets": ["zrx", "1inch", "aave", "alcx", "ach", "ali", "amp", "ankr", "ape", "api3", "audio", "axs", "bal", "bnt", "bond", "bat", "bico", "btc", "bch", "fida", "brd", "ash", "csp", "link", "chz", "cvc", "comp", "czrx", "cbat", "cdai", "ceth", "cwbtc", "ctx", "crv", "dai", "mana", "dpi", "ddx", "doge", "elon", "enj", "eth", "ens", "ern", "eul", "ftm", "fet", "fil", "frax", "fxs", "gala", "gal", "gusd", "jam", "gfi", "gnt", "ilv", "imx", "index", "inj", "iotx", "keep", "kp3r", "knc", "ldo", "lqty", "lusd", "ltc", "lpt", "loom", "lrc", "mim", "mkr", "mpl", "mask", "mc", "metis", "mir", "mco2", "nmr", "ocean", "omg", "orca", "oxt", "paxg", "pla", "matic", "qrdo", "qnt", "rad", "rly", "ray", "rfr", "ren", "rndr", "revv", "rbn", "sbr", "samo", "shib", "skl", "slp", "sol", "cube", "spell", "gmt", "storj", "rare", "sushi", "snx", "tbtc", "luna", "ust", "grt", "sand", "toke", "tcap", "tru", "uma", "uni", "usdc", "wbtc", "wcfg", "efil", "wnxm", "wton", "yfi", "zec", "zbc"]}, {"name": "Genesis", "supported_assets": ["btc", "bch", "ltc", "eth", "etc", "mana", "zec", "zen", "xrp", "xlm", "1inch", "1up", "aave", "abt", "ace", "acxt", "ach", "ae", "aergo", "aergo1", "agwd", "aion", "ali", "alpha", "amn", "amo", "amon", "amp", "ampx", "ana", "ape", "api3", "ant", "antv2", "aoa", "appc", "aqt", "arct", "arcx", "ast", "atri", "audio", "axl", "audx", "aust", "axpr", "axs", "axsv2", "badger", "bal", "band", "basic", "bat", "bax", "bbx", "bcap", "bcc", "bcio", "bepro", "bed", "bid", "blocks", "bidl", "bird", "bit", "bnb", "bnk", "bnl", "bnt", "bnty", "boba", "bond", "box", "brd", "brz", "bsx", "btrst", "btt", "btu", "burp", "busd", "buy", "bxx", "bxxv1", "bzz", "c8p", "cacxt", "cadx", "cag", "cbat", "cbc", "cbrl", "cct", "cdag", "cdai", "cdt", "cel", "celr", "ceth", "chfx", "chsb", "chz", "cix100", "cliq", "cln", "clt", "clv", "cng", "cnyx", "comp", "cover", "cpay", "cplt", "cqt", "cqx", "cra", "crdt", "cre", "cream", "crep", "cro", "crpt", "crpt1", "crv", "cslv", "csp", "ctsi", "cusdc", "cvc", "cvx", "cwbtc", "czrx", "coti", "dacxi", "dai", "dao", "data", "datav2", "dataecon", "dec", "dent", "dep", "dexa", "dfd", "dfi", "dgcl", "dgd", "dgx", "digg", "dmt", "dodo", "dpi", "drpu", "drv", "duc", "dx1u", "dxgt", "dxpt", "dxst", "dydx", "dyn", "easy", "ebtcq", "echt", "edison", "edn", "edr", "efi", "egl", "egld", "egold", "elf", "erd", "emx", "eng", "enj", "ens", "eqo", "eta", "ethos", "etv", "eurs", "<PERSON><PERSON><PERSON>", "euroc", "eurt", "eurx", "eux", "evx", "exe", "fdt", "fei", "fet", "fet1", "ff1", "fft", "fire", "front", "fmf", "ftm", "ftt", "fun", "fwb", "fxrt", "fxs", "gal", "gala", "gamma", "gbpx", "gdt", "gec", "gen", "gohm", "ghub", "gigdrop", "gldx", "glm", "gno", "gnt", "gods", "gog", "gold", "got", "grt", "gtc", "gto", "gusd", "gxc", "gyen", "hcn", "hdo", "hedg", "hkdx", "hlc", "hmt", "hold", "hot", "hqt", "hst", "ht", "hum", "husd", "hxro", "hyb", "hydro", "i8", "iceth", "idex", "idrc", "idrt", "imxv2", "incx", "ind", "index", "inf", "inj", "injv2", "inst", "inx", "isf", "isr", "ivo", "ivy", "jbc", "jfin", "jpyx", "keep", "key", "kin", "kiro", "knc", "knc2", "koin", "koz", "kp3r", "kze", "layer", "lba", "lend", "leo", "lgo", "link", "lion", "lnc", "loom", "loom1", "looks", "lrc", "lrcv2", "lyn", "maps", "matic", "mcdai", "mco", "mco2", "mcs", "mcx", "mdfc", "mdx", "medx", "meme", "met", "meta", "mfg", "mfph", "mft", "milkv2", "mir", "mith", "mix", "mizn", "mkr", "mns", "moc", "mof", "mpay", "mpl", "mtcn", "mtl", "musd", "mvl", "mvi", "mwt", "nas", "nct", "ndx", "neu", "nexo", "nftx", "ngnt", "niax", "nmr", "npxs", "ns2drp", "nu", "nym", "nzdx", "ocean", "oceanv2", "ogn", "okb", "om", "omg", "onl", "op", "opt", "orai", "orbs", "oxt", "oxy", "ohm", "par", "pass", "pau", "pax", "paxg", "pay", "pbch", "pbtc", "pdata", "peg", "perp", "peth", "pfct", "phnx", "pie", "planet", "plc", "plnx", "plx", "pma", "poly", "powr", "ppt", "prdx", "prints", "pro", "prts", "pstake", "pundix", "pusd", "pxp", "pyr", "qash", "qcad", "qdt", "qkc", "qnt", "qrdo", "qrl", "qsp", "quick", "qvt", "rad", "rare", "rari", "ray", "rby", "rdn", "reb", "rebl", "reef", "rep", "repv2", "seth-h", "reth-h", "rfr", "rfuel", "rgt", "rif", "ringx", "rlc", "rsr", "rly", "rndr", "ron", "ronc", "roobee", "rook", "rubx", "ruedatk", "salt", "sand", "sashimi", "sd", "sga", "sgdx", "sgr", "shib", "shk", "shopx", "shr", "sih", "sipher", "sis", "silv", "skale", "slab", "slot", "slp", "slvx", "snc", "snov", "snt", "snx", "soc", "sohm", "solve", "spell", "spo", "srnt", "stbu", "stc", "stcv2", "stkaave", "store", "storj", "storm", "stmx", "stzen", "squig", "super", "sushi", "sxp", "t", "taud", "tbtc1", "tcad", "tco", "tel", "ten", "tenx", "tgbp", "thkd", "tiox", "trl", "tknt", "tkx", "tlab", "tnt", "tok", "traxx", "trac", "tribe", "trst", "tru", "tryb", "bico", "tryx", "tusd", "txl", "uair", "uco", "ukg", "uma", "umee", "unb", "uni", "up", "upbtc", "upp", "upt", "upusd", "uqc", "usdc", "usdt", "usdx", "usg", "uspx", "ust", "usx", "utk", "utk1", "valor", "vdx", "vega", "visr", "vrgx", "vsp", "vxc", "wabi", "wafl", "wax", "wbtc", "wcfg", "wec", "wet", "weth", "whale", "wht", "wnxm", "woo", "wpx", "wsteth", "wtc", "wtk", "wxrp", "wxrpv0", "wluna", "wlxt", "wxt", "wild", "xaud", "xbgold", "xcd", "xex", "xrl", "xsgd", "xsushi", "xtp", "yfdai", "yfi", "yfii", "ygg", "yld", "yng", "ysey", "zarx", "zco", "zil", "zix", "zlw", "zmt", "zoom", "zrx", "zusd", "seth2", "reth2", "ldo", "gbpt", "ever"]}, {"name": "Kingdom Trust", "supported_assets": ["btc", "eth", "ada", "ltc", "link", "bch", "xlm", "eos", "xtz", "dash", "doge", "zec", "bat", "icx"]}, {"name": "New York Digital Investment Group", "supported_assets": ["btc", "eth", "ltc", "bch"]}]