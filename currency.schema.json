{"type": "object", "additionalProperties": false, "required": ["id", "name", "cm_ticker", "metadata"], "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "cm_ticker": {"type": "string", "pattern": "^[a-z0-9_.\\-]+$"}, "metadata": {"additionalProperties": false, "type": "object", "required": ["hasReferenceRate", "hasRTRR"], "properties": {"experimental": {"type": "boolean"}, "hasReferenceRate": {"type": "boolean"}, "hasRTRR": {"type": "boolean"}, "genesis": {"type": "string", "format": "date"}, "type": {"type": "string"}, "aggregate_of": {"type": "array", "items": {"type": "string"}}, "deprecation": {"type": "string", "pattern": "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}, "enabled": {"type": "boolean"}, "propertyId": {"type": "integer"}, "pricing_currency": {"type": "string"}, "firstBlockHeight": {"type": "integer"}, "contract": {"type": "string", "pattern": "^[0-9A-Za-z]+$"}, "instanceOf": {"type": "string", "pattern": "^[a-z0-9_.\\-]+$"}, "timeField": {"type": "string"}, "parent": {"type": "string"}, "siblings": {"type": "array", "items": {"type": "string"}}, "exchangeRateRegime": {"type": "string"}, "decimals": {"type": "integer"}, "encoding": {"type": "object", "required": ["blockHash", "txHash", "account"], "additionalProperties": false, "properties": {"blockHash": {"type": "string"}, "txHash": {"type": "string"}, "account": {"type": "string"}}}, "ckgo_id": {"type": "string"}, "has_principal_market_price": {"type": "boolean"}, "has_rt_pmp": {"type": "boolean"}}, "allOf": [{"if": {"properties": {"contract": {"type": "string"}}, "required": ["contract"]}, "then": {"required": ["decimals"]}}]}, "fiat": {"type": "boolean"}, "dead": {"type": "boolean"}, "prev_cm_tickers": {"type": "array", "items": {"type": "string"}}}}