#!/bin/bash

# Config
DB_NAME="pg-books-prd-cp1-p"
DB_USER="postgres"
DB_HOST="pgbouncer.pgbouncer.svc"
DB_PORT="5432"

# Time range
START="2025-02-01 00:00:00"
END="2025-05-01 00:00:00"
STEP_SECONDS=$((4 * 60 * 60))  # 4 hours in seconds

# Convert to epoch
start_epoch=$(date -d "$START" +%s)
end_epoch=$(date -d "$END" +%s)

# Loop in steps
while [ "$start_epoch" -lt "$end_epoch" ]; do
    next_epoch=$((start_epoch + STEP_SECONDS))

    # Format timestamps
    start_ts=$(date -d "@$start_epoch" +"%Y-%m-%d %H:%M:%S")
    end_ts=$(date -d "@$next_epoch" +"%Y-%m-%d %H:%M:%S")

    echo "$(date '+%Y-%m-%d %H:%M:%S') Migrating from $start_ts to $end_ts..."

    psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "
      INSERT INTO spot_books_unique_keys (
          book_exchange_id, book_symbol, book_depth_limit, book_time_old, book_time_new
      )
      SELECT
          book_exchange_id,
          book_base_id,
          book_quote_id,
          book_depth_limit,
          book_time AS book_time_old,
          COALESCE(book_exchange_time, book_time) AS book_time_new,
          (
              select market_symbol from spot_metadata
              where
                  market_exchange_id = book_exchange_id and
                  market_base_id = book_base_id and
                  market_quote_id = book_quote_id and
                  market_is_current = true
              order by market_listing_date desc limit 1
          ) as book_symbol
      FROM spot_books s
      WHERE book_time >= TIMESTAMP '$start_ts'
        AND book_time < TIMESTAMP '$end_ts'
        AND book_base_id is NOT NULL
        AND book_quote_id is NOT NULL
        AND book_symbol is not NULL
        AND not exists (select * from spot_books_cp1_keys c where c.book_exchange_id = s.book_exchange_id and c.book_symbol = book_symbol and c.book_depth_limit = s.book_depth_limit and c.book_time = book_time_new limit 1)
      ON CONFLICT DO NOTHING;
    "

    if [ $? -ne 0 ]; then
        echo "Migration failed for range $start_ts to $end_ts"
        exit 1
    fi

    start_epoch=$next_epoch
done

echo "All done!"
