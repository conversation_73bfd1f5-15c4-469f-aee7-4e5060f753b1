#!/bin/bash
# This script migrates data from old book DB structure to new book DB structure in batches.
# It uses PostgreSQL's COPY command and pipes to transfer data between two databases without intermediate files.
# Discussion: https://coinmetrics.slack.com/archives/C01V97CKSUQ/p1745501266224409?thread_ts=1744936473.430989&cid=C01V97CKSUQ
# Usage: PGPASSWORD=iamstrong ./books_spot_migrate_to_partitioned_db.sh

# ---- CONFIGURATION ----
SRC_HOST="pgbouncer.pgbouncer.svc"
SRC_PORT="5432"
SRC_DB_NAME="pg-books-prd-cp1-p"
SRC_DB_USER="postgres"

DST_HOST="pgbouncer.pgbouncer.svc"
DST_PORT="5432"
DST_DB_NAME="pg-books-partitioned-prd-cp1-a"
DST_DB_USER="postgres"

START_TIME="2025-02-01 00:00:00"
END_TIME="2025-04-18 00:00:00"
STEP=60  # in seconds

# ---- DO NOT EDIT BELOW UNLESS NEEDED ----

start_epoch=$(date -d "$START_TIME" +%s)
end_epoch=$(date -d "$END_TIME" +%s)

echo "Starting batch copy from $START_TIME to $END_TIME in $STEP second steps."

while [ "$start_epoch" -lt "$end_epoch" ]; do
    next_epoch=$((start_epoch + STEP))
    start_ts=$(date -d "@$start_epoch" '+%Y-%m-%d %H:%M:%S')
    end_ts=$(date -d "@$next_epoch" '+%Y-%m-%d %H:%M:%S')

    echo "$(date '+%Y-%m-%d %H:%M:%S') Migrating from $start_ts to $end_ts..."

    psql -h "$SRC_HOST" -d "$SRC_DB_NAME" -U "$SRC_DB_USER" -p "$SRC_PORT" -At -c "\COPY (
        SELECT
          k.book_exchange_id,
          k.book_symbol,
          k.book_time_new,
          k.book_depth_limit,
          b.book_database_time,
          b.book_exchange_sequence_id,
          b.book_bids,
          b.book_asks
        FROM spot_books_unique_keys k
        JOIN spot_books b
          ON k.book_exchange_id = b.book_exchange_id
            and k.book_base_id = b.book_base_id
            and k.book_quote_id = b.book_quote_id
            and k.book_depth_limit = b.book_depth_limit
            and k.book_time_old = b.book_time
        WHERE k.book_time_old >= TIMESTAMP '$start_ts'
          AND k.book_time_old < TIMESTAMP '$end_ts'
          AND k.book_time_new >= TIMESTAMP '$START_TIME'
          AND k.book_time_new < TIMESTAMP '$END_TIME'
    ) TO STDOUT WITH CSV" | \
    psql -h "$DST_HOST" -d "$DST_DB_NAME" -U "$DST_DB_USER" -p "$DST_PORT" -c "\COPY books_spot FROM STDIN WITH CSV"

    if [ $? -ne 0 ]; then
        echo "❌ Batch from $start_ts to $end_ts failed."
        exit 1
    fi

    start_epoch=$next_epoch
done

echo "✅ Migration complete."