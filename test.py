import argparse
import json
import os
import re
import requests
import sys

from collections import namedtuple, Counter
from datetime import datetime
from jsonschema import validate
from typing import Any, Dict, List
from requests.exceptions import RequestException

POOL_HASH_REGEX = re.compile(r"^(?:[0-9a-f]{40}|[0-9a-f]{64})$")
MetricAssetPair = namedtuple('MetricAssetPair', ['asset', 'metric', 'granularity'])

# This scripts runs several tests on the resources file to ensure that some
# business logic is preserved

def get_internal_metrics():
    """
    Returns the set of internal metrics
    """
    with open('metrics.json') as f:
        metrics_json = json.load(f)
        return {m['short_form'] for m in metrics_json if m['internal'] == True}


def validate_property_uniqueness(dtos, property):
    counter = Counter((d[property] for d in dtos))
    # We only check the most common element
    instance, count = counter.most_common(1)[0]
    if count > 1:
        print(f"{property} is not unique, {instance} found {count} times")
        sys.exit(1)


def is_valid_defi_market_format(market: str) -> bool:
    """
    Validates defi markets follow the expected format: {exchange}-{pool_hash}-{base}-{quote}-spot.
    Can be used to validate markets in the market_whitelist.json file.
    """
    parts = market.split("-")
    if len(parts) != 5 or parts[-1] != "spot":
        return False

    exchange, pool_hash, base, quote, market_type = parts

    if not POOL_HASH_REGEX.fullmatch(pool_hash):
        return False

    return True


def validate_market_whitelist():

    with open('market_whitelist.schema.json') as f:
        schema = json.load(f)

    with open('market_whitelist.json') as f:
        market_whitelist = json.load(f)

    with open("exchange.json") as f:
        exchanges = json.load(f)

    # Manually exclude defi exchanges that do not have pool ids
    excluded_defi_exchanges = {
        "uniswap_v2_eth",
        "sushiswap_v1_eth",
    }

    defi_exchanges_with_pool_hashes: set[str] = {
        exchange["name"]
        for exchange in exchanges
        if exchange.get("defi") is True
        and exchange.get("defi_pools_supported") is True
        and exchange["name"] not in excluded_defi_exchanges
    }

    errors = []

    for num, record in enumerate(market_whitelist):

        validate(instance=record, schema=schema)

        pair = record["pair"]
        dates = []

        for entry in record.get("ranges", []):
            # Validate start date
            date_str = entry.get("start")
            try:
                parsed = datetime.strptime(date_str, "%Y-%m-%d")
                dates.append(parsed.strftime("%Y-%m-%d"))
            except (ValueError, TypeError):
                errors.append(f"Invalid start date '{date_str}' in pair '{pair}'")

            # Validate markets for defi exchanges
            for market in entry.get("markets", []):
                exchange = market.split("-")[0]
                if exchange in defi_exchanges_with_pool_hashes and not is_valid_defi_market_format(market):
                    errors.append(f"Invalid defi market in market_whitelist.json pair '{pair}': {market}")

        if len(dates) != len(set(dates)):
            errors.append(f"Duplicate start dates in pair {pair}")
        if dates != sorted(dates):
            errors.append(f"Out-of-order start dates in pair {pair}")

    if errors:
        print("Errors found parsing market_whitelist.json:")
        print("\n".join(errors))
        sys.exit(1)


def validate_calendar_date(date: str, id: str = "unknown") -> bool:
    """
    Validate whether the given date string is a valid calendar date (YYYY-MM-DD).
    
    Args:
        date: The date string to validate.
        id: An optional identifier to help locate the source of the error.
    
    Returns:
        True if the date is valid, False otherwise.
    """
    try:
        datetime.strptime(date, "%Y-%m-%d")
        return True
    except ValueError:
        print(f"Invalid date for ID {id}: {date}")
        return False
    

def validate_currencies():
    with open('currency.schema.json') as f:
        currency_schema = json.load(f)

    with open('currency.json') as f:
        currencies = json.load(f)

    with open('market_whitelist.json') as f:
        market_whitelist = json.load(f)
        usd_markets = {m['pair'] for m in market_whitelist if m['pair'].endswith('-usd')}

    erc20_contracts = set()
    trc20_contracts = set()
    
    # Some assets may intentionally have identical contract address
    ignored_duplicate_contract_assets = [
        "cbbtc_base.eth",
        "cbbtc_eth",
        "weth_base.eth",
        "weth_op.eth"
    ]

    # We keep this in memory to print all the occurences at once
    detected_invalid_reference_rate_value = False

    for currency in currencies:

        # First, validate schema
        validate(instance=currency, schema=currency_schema)

        # Validate that genesis is a valid date
        metadata = currency.get("metadata", {})
        genesis = metadata.get("genesis")
        id = currency.get("id")
        if isinstance(genesis, str):
            if validate_calendar_date(genesis, id=str(id)):
                continue
            else:
                exit(1)

        # For ERC-20/TRC-20 tokens, validate that their contract is unique to them
        if ('type' in currency['metadata'] and currency['metadata']['type'] in ['erc20', 'trc20']):
            contract = currency['metadata']['contract']
            if currency["cm_ticker"] in ignored_duplicate_contract_assets:
                continue
            if (currency['metadata']['type'] == 'erc20'):
                if contract in erc20_contracts:
                    print(f'ERC-20 contract {contract} is duplicated')
                    exit(1)
                erc20_contracts.add(contract)
            if (currency['metadata']['type'] == 'trc20'):
                if contract in erc20_contracts:
                    print(f'TRC-20 contract {contract} is duplicated')
                    exit(1)
                trc20_contracts.add(contract)

        # If the currency indicates it has reference rates,
        # we check whether it has at least one USD market whitelisted.
        # If that's not the case, then it cannot have a reference rate
        usd_pair = f"{currency['cm_ticker']}-usd"
        if currency['metadata']['hasReferenceRate'] and usd_pair not in usd_markets:
            print(f"{currency['cm_ticker']} hasReferenceRate=true but no USD market detected in market_whitelist.json")
            detected_invalid_reference_rate_value = True


    if detected_invalid_reference_rate_value:
        exit(1)

    # Then validate uniqueness of certain properties
    unique_properties = {'id', 'name', 'cm_ticker'}
    for unique_property in unique_properties:
        validate_property_uniqueness(currencies, unique_property)

    return currencies


def validate_metric_cyclic(metrics_dict, ancestors, dependencies):
    if not ancestors.isdisjoint(dependencies):
        print(f"A cyclic dependency found between {ancestors} and {dependencies}")
        sys.exit(1)
    for dependency in dependencies:
        inner_dependencies = {m for m in metrics_dict[dependency]['depends_on'].split(',') if len(m) > 0}
        validate_metric_cyclic(metrics_dict, ancestors | {dependency}, inner_dependencies)


def validate_metrics():
    with open('metric.schema.json') as f:
        metric_schema = json.load(f)

    with open('metrics.json') as f:
        metrics = json.load(f)

    for metric in metrics:
        validate(instance=metric, schema=metric_schema)

    # Then validate uniqueness of certain properties
    unique_properties = {'short_form', 'name'}
    for unique_property in unique_properties:
        validate_property_uniqueness(metrics, unique_property)

    # We also validate that each metric dependency exists
    unique_metrics = {m['short_form']: m for m in metrics}
    for metric in metrics:
        dependencies = {m for m in metric['depends_on'].split(',') if len(m) > 0}
        for dependency in dependencies:
            if dependency not in unique_metrics:
                print(f"{metric['short_form']} depends on '{dependency}' but '{dependency}' is not a valid metric")
                sys.exit(1)
        validate_metric_cyclic(unique_metrics, {metric['short_form']}, dependencies)

    # Ignore further validation of institution metrics because they only apply to institutions, not assets
    metrics = [metric for metric in metrics if metric["category"] != "Institutions"]

    return metrics


def validate_holidays(calendars: List[str]) -> None:
    today = datetime.utcnow()
    one_year_out = today.replace(year=today.year + 1)
    for calendar in calendars:
        holiday_file = f'holidays_{calendar}.json'
        with open(holiday_file) as f:
            holidays = json.load(f)
            dates = [datetime.strptime(h['date'], '%Y-%m-%d') for h in holidays]
            # print(f'#{calendar} = {len(holidays)}, min = {min(dates)}, max = {max(dates)}')
            if max(dates) < one_year_out:
                print(f'Calendar {calendar} expires in less than one year.')
                exit(1)


def validate_coin_gecko_ids():
    with open('currency.json') as f:
        currencies_list = json.load(f)
        # our_coin_gecko_ids = {c["metadata"]["ckgo_id"] for c in currencies_list if "ckgo_id" in c["metadata"]}
    dict_cg_ids_to_cm_ticker = {}
    list_multiple_coingecko_ids = []
    dict_multiple_coingecko_ids = {}
    for c in currencies_list:
        if "ckgo_id" in c.get("metadata").keys():
            cm_ticker = c["cm_ticker"]
            cg_id = c.get("metadata").get("ckgo_id") 
            if cg_id in dict_cg_ids_to_cm_ticker:
                dict_cg_ids_to_cm_ticker[cg_id].append(cm_ticker)
                list_multiple_coingecko_ids.append((cm_ticker, cg_id))
            else:
                dict_cg_ids_to_cm_ticker[cg_id] = [cm_ticker]
    for cm_ticker, cg_id in list_multiple_coingecko_ids:
        dict_multiple_coingecko_ids[cg_id] = dict_cg_ids_to_cm_ticker[cg_id]
    if dict_multiple_coingecko_ids:
        print("List of coingecko ids with multiple cm tickers:")
        for cg_id, cm_ticker in dict_multiple_coingecko_ids.items():
            print(f"{cg_id}: {cm_ticker}")
        print()

        # sys.exit(1)
    # real_coin_gecko_ids = {c["id"] for c in requests.get("https://api.coingecko.com/api/v3/coins/list").json()}
    # invalid_ids = our_coin_gecko_ids.difference(real_coin_gecko_ids)
    # if len(invalid_ids) > 0:
    #     print(f"Invalid Coin Gecko IDs: {invalid_ids}")
    #     exit(1)


def validate_indexes():
    with open('indexes.schema.json') as json_schema:
        indexes_schema = json.load(json_schema)
    with open('indexes.json') as json_file:
        indexes = json.load(json_file)
    for index in indexes:
        validate(instance=index, schema=indexes_schema)


def validate_json(json_files):
    for json_file_name in json_files:
        with open(json_file_name) as json_file:
            try:
                json.load(json_file)
            except ValueError as e:
                print(f'Invalid JSON file "{json_file_name}". {e}')
                exit(1)


def detect_json_key_duplicates(pairs):
    # pairs is a list of (key, value) for one JSON object
    keys = [k for k, _ in pairs]
    duplicate_keys = {k: count for k, count in Counter(keys).items() if count > 1}
    if duplicate_keys:
        print(f"Duplicate json keys: {duplicate_keys}")
        print()
    return dict(pairs)


def validate_exchanges():
    with open('exchange.json') as json_file:
        exchanges = json.load(json_file, object_pairs_hook=detect_json_key_duplicates)

    with open('currency.json') as json_file:
        currency = json.load(json_file, object_pairs_hook=detect_json_key_duplicates)

    validate_property_uniqueness(exchanges, "name")
    currencies_cm_tickers = set(i['cm_ticker'] for i in currency)
    dict_exchanges_duplicated_cm_tickers = {}
    dict_exchange_tickers_not_in_currency = {}

    for exchange in exchanges:
        tickers = exchange.get('tickers')
        exchange_name = exchange['name']

        if tickers:

            cm_tickers = sorted(tickers.values())
            unique_cm_tickers = sorted(set(cm_tickers))
            cm_ticker_counts = Counter(cm_tickers)
            cm_duplicates = [ticker for ticker, count in cm_ticker_counts.items() if count > 1]
            
            if cm_duplicates:
                dict_exchanges_duplicated_cm_tickers[exchange_name] = cm_duplicates

            if not set(unique_cm_tickers).issubset(currencies_cm_tickers):
                dict_exchange_tickers_not_in_currency[exchange_name] = set(unique_cm_tickers) - currencies_cm_tickers

    if dict_exchanges_duplicated_cm_tickers:
        print("Exchanges with duplicated cm_tickers:")
        for exchange, duplicates in dict_exchanges_duplicated_cm_tickers.items():
            print(f"{exchange}: {duplicates}")
        print()

    if dict_exchange_tickers_not_in_currency:
        print(f"Exchanges with cm_tickers not in currency.json: {dict_exchange_tickers_not_in_currency}")
        exit(1)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="CI validation helper for resource config files"
    )
    parser.add_argument(
        "--validate-doc-urls",
        action="store_true",
        help="Fetch each GitBook docs URL and fail if it’s missing."
    )

    return parser.parse_args()


def validate_metric_doc_urls(
    metrics,
    base_url: str = "https://docs.coinmetrics.io",
    timeout: int = 10
) -> None:
    """
    For each metric that contains a 'url_slug_doc', fetch the GitBook page and
    fail if it returns status 404.

    A single failure aborts the test run (mimics the other validators).
    """
    
    broken: dict[str, str] = {}

    for metric in metrics:
        slug = metric.get("url_slug_doc")
        if not slug:
            continue

        url = f"{base_url.rstrip('/')}/{slug.lstrip('/')}"
        try:
            resp = requests.get(url, timeout=timeout)
            if resp.status_code == 404:
                broken[metric["short_form"]] = url
        except RequestException as exc:
            print(f"Error fetching {url}: {exc}")
            sys.exit(1)
    
    if broken:
        print("\n*** Broken docs URLs detected ***")
        for k, v in broken.items():
            print(f"{k:<20}: {v}")
        print(f"\nTotal broken links: {len(broken)}")
        sys.exit(1)   # fail the job
    else:
        print("All docs URLs resolved successfully.")




if __name__ == "__main__":

    args = parse_args()

    # First we validate the schemas of the JSON files
    validate_market_whitelist()
    currencies = validate_currencies()
    metrics = validate_metrics()
    validate_holidays(['nyse', 'usd'])

    # Check if critical files contain valid JSON
    validate_json(['exchange.json', 'api_keys.json', 'volume_trusted_spot_constituents.json'])

    validate_coin_gecko_ids()
    validate_indexes()
    validate_exchanges()
    
    # Validating metrics.json documentation URLs can take a long time, so it is optional
    if args.validate_doc_urls:
        validate_metric_doc_urls(metrics)
