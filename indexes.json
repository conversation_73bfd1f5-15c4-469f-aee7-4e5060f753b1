[{"name": "FIDBTCP", "base": "fid", "full_name": "Fidelity Bitcoin Price Index", "description": "Price Return is designed to reflect the performance of Bitcoin in U.S. dollars.", "id": 1001, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDBTCT", "base": "fid", "full_name": "Fidelity Bitcoin Total Return Index", "description": "Total Return is designed to reflect the performance of Bitcoin, including the liquidation value of significant forks, in U.S. dollars.", "id": 1001, "return_type": "total_return", "return_multipliers": [{"time": "2018-03-16T20:00", "value": "1.12058507"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDETHP", "base": "fid", "full_name": "Fidelity Ethereum Price Index", "description": "Price Return is designed to reflect the performance of Ethereum in U.S. dollars.", "id": 1002, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDETHT", "base": "fid", "full_name": "Fidelity Ethereum Total Return Index", "description": "Total Return is designed to reflect the performance of Ethereum, including the liquidation value of significant forks, in U.S. dollars.", "id": 1002, "return_type": "total_return", "return_multipliers": [{"time": "2016-09-16T20:00", "value": "1.10523543"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDBEIP", "base": "fid", "full_name": "Fidelity Bitcoin and Ethereum Price Index", "description": "Price Return is designed to reflect the performance of Bitcoin and Ethereum in U.S. dollars.", "id": 1003, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDBEIT", "base": "fid", "full_name": "Fidelity Bitcoin and Ethereum Total Return Index", "description": "Total Return is designed to reflect the performance of Bitcoin and Ethereum, including the liquidation value of significant forks, in U.S. dollars.", "id": 1003, "return_type": "total_return", "return_multipliers": [{"time": "2016-09-16T20:00:00", "value": "1.011394945"}, {"time": "2018-03-16T20:00:00", "value": "1.092737342"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDBCRP", "base": "fid", "full_name": "Fidelity Bitcoin Reference Rate PR", "description": "Price Return is designed to reflect the performance of Bitcoin in U.S. dollars.", "id": 1004, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDBCRT", "base": "fid", "full_name": "Fidelity Bitcoin Reference Rate TR", "description": "Total Return is designed to reflect the performance of Bitcoin, including the liquidation value of hard forks, in U.S. dollars.", "id": 1004, "return_type": "total_return", "return_multipliers": [{"time": "2018-03-16T20:00", "value": "1.12039317"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDERRP", "base": "fid", "full_name": "Fidelity Ethereum Reference Rate PR", "description": "Price Return is designed to reflect the performance of Ethereum in U.S. dollars.", "id": 1005, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDERRT", "base": "fid", "full_name": "Fidelity Ethereum Reference Rate TR", "description": "Total Return is designed to reflect the performance of Ethereum, including the liquidation value of significant forks, in U.S. dollars.", "id": 1005, "return_type": "total_return", "return_multipliers": [{"time": "2016-09-16T20:00", "value": "1.10406166"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDEBEP", "base": "fid", "full_name": "Fidelity Bitcoin and Ethereum Even Price Index", "description": "Price Return is designed to reflect the performance of Bitcoin and Ethereum in U.S. dollars.", "id": 1006, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDEBET", "base": "fid", "full_name": "Fidelity Bitcoin and Ethereum Even Total Return Index", "description": "Total Return is designed to reflect the performance of Bitcoin and Ethereum, including the liquidation value of significant forks, in U.S. dollars.", "id": 1006, "return_type": "total_return", "return_multipliers": [{"time": "2016-09-16T20:00:00", "value": "1.04618368982"}, {"time": "2018-03-16T20:00:00", "value": "1.10660184343"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDSOLP", "base": "fid", "full_name": "Fidelity Solana Reference Rate PR", "description": "Price Return is designed to reflect the performance of Solana in U.S. dollars.", "id": 1007, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "FIDSOLT", "base": "fid", "full_name": "Fidelity Solana Reference Rate TR", "description": "Total Return is designed to reflect the performance of Solana, including the liquidation value of hard forks, in U.S. dollars.", "id": 1007, "return_type": "total_return", "return_multipliers": [], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["15s", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "RTREE", "base": "rmw", "full_name": "RWM WisdomTree Crypto Index", "description": "The RWM WisdomTree Crypto Index is designed to track the performance of a diversified basket of crypto assets representing the broad crypto asset market.", "id": 2001, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "RTREET", "base": "rmw", "full_name": "RWM WisdomTree Crypto Total Return Index", "description": "The RWM WisdomTree Crypto Total Return Index is designed to track the performance of a diversified basket of crypto assets representing the broad crypto asset market, including the liquidation value of significant forks.", "id": 2001, "return_type": "total_return", "return_multipliers": [{"time": "2021-12-08T01:00", "value": "1.0"}], "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "WALTGM", "base": "rmw", "full_name": "RWM WisdomTree Altcoin Index", "description": "needs update", "id": 2002, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "WDFIGM", "base": "rmw", "full_name": "RWM WisdomTree DeFi Index", "description": "needs update", "id": 2003, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "WVRSGM", "base": "rmw", "full_name": "RWM WisdomTree Metaverse Index", "description": "needs update", "id": 2004, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTC", "base": "cmbi", "full_name": "CMBI Bitcoin Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin.", "id": 1, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTCT", "base": "cmbi", "full_name": "CMBI Bitcoin Total Return Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.", "id": 1, "return_type": "total_return", "return_multipliers": [{"time": "2017-11-01T20:00", "value": "1.07438833"}], "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETH", "base": "cmbi", "full_name": "CMBI Ethereum Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum.", "id": 2, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHT", "base": "cmbi", "full_name": "CMBI Ethereum Total Return Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum as well as liquidating legitimate forked assets.", "id": 2, "return_type": "total_return", "return_multipliers": [{"time": "2017-03-01T21:00", "value": "1.08559236"}], "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIHASH", "base": "cmbi", "full_name": "CMBI Bitcoin Hash Rate Index", "description": "Measure of the amount of hash rate being contributed to mining Bitcoin blocks.", "id": 3, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "thematic", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-midday"]}, {"name": "CMBI10", "base": "cmbi", "full_name": "CMBI 10 Index", "description": "An index consisting of the 10 largest cryptoassets, weighted by their free float market capitalization.", "id": 4, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIWORK", "base": "cmbi", "full_name": "CMBI Bitcoin Observed Work Index", "description": "Measure of the amount of mining activity being conducted on the Bitcoin Network throughout a 24 hour period.", "id": 5, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "thematic", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-midday"]}, {"name": "CMBI10E", "base": "cmbi", "full_name": "CMBI 10 Even Index", "description": "An index consisting of the 10 largest cryptoassets, weighted evenly at the start of each month.", "id": 6, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBI10EX", "base": "cmbi", "full_name": "CMBI 10 Excluding Bitcoin Index", "description": "An index consisting of the 9 largest non-Bitcoin cryptoassets, weighted by their free float market capitalization.", "id": 7, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBE", "base": "cmbi", "full_name": "CMBI Bitcoin & Ethereum Index", "description": "An index consisting of Bitcoin and Ethereum, weighted by their free float market capitalization.", "id": 8, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "type": "multi_asset", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBILTC", "base": "cmbi", "full_name": "CMBI Litecoin Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Litecoin.", "id": 9, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBILINK", "base": "cmbi", "full_name": "CMBI Chainlink Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Chainlink.", "id": 10, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIALGO", "base": "cmbi", "full_name": "CMBI Algorand Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Algorand.", "id": 11, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIDOT", "base": "cmbi", "full_name": "CMBI Polkadot Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Polkadot.", "id": 12, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBAT", "base": "cmbi", "full_name": "CMBI Basic Attention Token Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Basic Attention Token.", "id": 13, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBI10M", "base": "cmbi", "full_name": "CMBI 10 Momentum Index", "description": "A multi asset index measuring the performance of the momentum factor for CMBI10 Index.", "id": 14, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "type": "thematic", "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBISOL", "base": "cmbi", "full_name": "CMBI Solana Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Solana.", "id": 15, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTCFTX", "base": "cmbi", "full_name": "CMBI FTX Bitcoin Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin using FTX and FTX.US constituent data.", "id": 16, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHFTX", "base": "cmbi", "full_name": "CMBI FTX Ethereum Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum using FTX and FTX.US constituent data.", "id": 17, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBISOLFTX", "base": "cmbi", "full_name": "CMBI FTX Solana Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Solana using FTX and FTX.US constituent data.", "id": 18, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIDOGEFTX", "base": "cmbi", "full_name": "CMBI FTX Doge Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Doge using FTX and FTX.US constituent data.", "id": 19, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIADA", "base": "cmbi", "full_name": "CMBI Cardano Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Cardano.", "id": 20, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIEOS", "base": "cmbi", "full_name": "CMBI EOS Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding EOS.", "id": 21, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIXRP", "base": "cmbi", "full_name": "CMBI Ripple Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ripple.", "id": 22, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIXTZ", "base": "cmbi", "full_name": "CMBI Tezos Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Tezos.", "id": 23, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBISAND", "base": "cmbi", "full_name": "CMBI The Sandbox Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Sandbox.", "id": 24, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIMANA", "base": "cmbi", "full_name": "CMBI Decentraland Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Decentraland.", "id": 25, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTCR", "base": "cmbi", "full_name": "CMBI Bitcoin Index from Moorgate", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin constituent data.", "id": 26, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHR", "base": "cmbi", "full_name": "CMBI Ethereum Index from Moorgate", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum constituent data.", "id": 27, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTCRX", "base": "cmbi", "full_name": "CMBI FTX Bitcoin Index from Moorgate", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin using FTX and FTX.US constituent data.", "id": 28, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHRX", "base": "cmbi", "full_name": "CMBI FTX Ethereum Index from Moorgate", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum using FTX and FTX.US constituent data.", "id": 29, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "GX10C", "base": "cmbi", "full_name": "Global X Crypto 10 Capped Index", "description": "Needs update", "id": 3001, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIATOM", "base": "cmbi", "full_name": "CMBI Cosmos Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Cosmos.", "id": 30, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIAVAX", "base": "cmbi", "full_name": "CMBI Avalanche Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Avalanche.", "id": 31, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBILUNA", "base": "cmbi", "full_name": "CMBI Terra Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Terra.", "id": 32, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIAAVE", "base": "cmbi", "full_name": "CMBI Aave Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Aave.", "id": 33, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIUNI", "base": "cmbi", "full_name": "CMBI Uniswap Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Uniswap.", "id": 34, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIAPE", "base": "cmbi", "full_name": "CMBI ApeCoin Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding ApeCoin.", "id": 35, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBI70", "base": "cmbi", "full_name": "CMBI 70 Index", "description": "An index consisting of the 70 largest cryptoassets, weighted by their free float market capitalization.", "id": 36, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBI70E", "base": "cmbi", "full_name": "CMBI 70 Even Index", "description": "An index consisting of the 70 largest cryptoassets, weighted evenly at the start of each month.", "id": 37, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": false, "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIAUE", "base": "cmbi", "full_name": "CMBI Application Utilities Sector Even Index", "description": "Application Utilities sector from Datonomy", "id": 38, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBSE", "base": "cmbi", "full_name": "CMBI Business Services Sector Even Index", "description": "Business Services sector from Datonomy", "id": 40, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBUE", "base": "cmbi", "full_name": "CMBI Blockchain Utilities Even Sector Index", "description": "Blockchain Utilities sector from Datonomy", "id": 42, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIDFIE", "base": "cmbi", "full_name": "CMBI Decentralized Finance Sector Even Index", "description": "DEFI sector from Datonomy", "id": 44, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIDEXE", "base": "cmbi", "full_name": "CMBI Decentralized Exchanges Sub-Sector Even Index", "description": "Decentralized Exchanges subsector from Datonomy", "id": 46, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIIFE", "base": "cmbi", "full_name": "CMBI Intermediated Finance Sector Even Index", "description": "Intermediated Finance sector from Datonomy", "id": 48, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIITE", "base": "cmbi", "full_name": "CMBI Information Technology Sector Even Index", "description": "IT sector from Datonomy", "id": 50, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBITM", "base": "cmbi", "full_name": "CMBI Total Market Index", "description": "All Datonomy assets excluding stablecoins and wrapped coins", "id": 52, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBITME", "base": "cmbi", "full_name": "CMBI Total Market Even Index", "description": "All Datonomy assets excluding stablecoins and wrapped coins", "id": 53, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIMTAE", "base": "cmbi", "full_name": "CMBI Metaverse Sector Even Index", "description": "Metaverse sector from Datonomy", "id": 54, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIMSE", "base": "cmbi", "full_name": "CMBI Media Services Sector Even Index", "description": "Media Services sector from Datonomy", "id": 56, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBINFTE", "base": "cmbi", "full_name": "CMBI NFT Ecosystem Sub-Sector Even Index", "description": "NFT subsector from Datonomy", "id": 58, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBINSE", "base": "cmbi", "full_name": "CMBI Network Scaling Sub-Sector Even Index", "description": "Network Scaling subsector from Datonomy", "id": 60, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBISCE", "base": "cmbi", "full_name": "CMBI Specialized Coins Sector Even Index", "description": "meme coins and privacy tokens", "id": 62, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBISCPE", "base": "cmbi", "full_name": "CMBI Smart Contract Platforms Sector Even Index", "description": "Smart Contract Platforms sector from Datonomy", "id": 64, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIVTCE", "base": "cmbi", "full_name": "CMBI Value Transfer Coins Sector Even Index", "description": "BTC and BTC forks", "id": 66, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": true, "released": true, "level_frequencies": ["15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIBTCV", "base": "cmbi", "full_name": "CMBI Bitcoin Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin.", "id": 67, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "_GFOXBE", "base": "cmbi", "full_name": "BITA BTC Hourly Internal Index", "description": "BITA BTC Hourly Internal Index", "id": 67, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHV", "base": "cmbi", "full_name": "CMBI Bita ETH Tracking Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum.", "id": 68, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "_GFOXEE", "base": "cmbi", "full_name": "BITA ETH Hourly Internal Index", "description": "BITA ETH Hourly Internal Index", "id": 68, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "GFOXBR", "base": "cmbi", "full_name": "BITA CMBIBTC Real-time Index", "description": "BITA CMBIBTC Real-time Index", "id": 69, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1s"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHVX", "base": "cmbi", "full_name": "BITA CMBIETH Index", "description": "BITA CMBIETH Index", "id": 70, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIUSDT", "base": "cmbi", "full_name": "CMBI USDT Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding USDT.", "id": 71, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "GFOXBE", "base": "cmbi", "full_name": "BITA CMBIBTC Hourly Index", "description": "BITA CMBIBTC Hourly Index", "id": 72, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIMATIC", "base": "cmbi", "full_name": "CMBI Polygon Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding MATIC.", "id": 73, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": false, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "GFOXER", "base": "cmbi", "full_name": "BITA CMBIETH Real-time Index", "description": "BITA CMBIETH Real-time Index", "id": 74, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1s"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "GFOXEE", "base": "cmbi", "full_name": "BITA CMBIETH Hourly Index", "description": "BITA CMBIETH Hourly Index", "id": 75, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "BTCFIX", "base": "cmbi", "full_name": "CMBI BTC Derivative Fixing", "description": "CMBI BTC Derivative Fixing", "id": 76, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "ETHFIX", "base": "cmbi", "full_name": "CMBI ETH Derivative Fixing", "description": "CMBI ETH Derivative Fixing", "id": 77, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "_GFOXER", "base": "cmbi", "full_name": "BITA ETH Realtime Internal Index", "description": "BITA ETH Realtime Internal Index", "id": 79, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s"], "extra_daily_frequencies": []}, {"name": "_GFOXBR", "base": "cmbi", "full_name": "BITA BTC Realtime Internal Index", "description": "BITA BTC Realtime Internal Index", "id": 80, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s"], "extra_daily_frequencies": []}, {"name": "CMBIPOL", "base": "cmbi", "full_name": "CMBI Polygon Network Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and holding Polygon.", "id": 81, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "CMBIETHS", "base": "cmbi", "full_name": "CMBI Ethereum Staking Index", "description": "A single asset index measuring the performance an investor would expect from purchasing and staking Ethereum.", "id": 83, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1h", "1d"], "extra_daily_frequencies": ["1d-ny-close", "1d-sg-close"]}, {"name": "AARKBTC", "base": "aark", "full_name": "Aark Digital BTC USDC Index", "description": "", "id": 4001, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "AARKETH", "base": "aark", "full_name": "Aark Digital ETH USDC Index", "description": "", "id": 4002, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "AARKMATIC", "base": "aark", "full_name": "Aark Digital MATIC USDC Index", "description": "", "id": 4003, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "AARKBNB", "base": "aark", "full_name": "Aark Digital BNB USDC Index", "description": "", "id": 4004, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "AARKUSDC", "base": "aark", "full_name": "Aark Digital USDC USD Index", "description": "", "id": 4005, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "AARKUSDT", "base": "aark", "full_name": "Aark Digital USDT USD Index", "description": "", "id": 4006, "return_type": "price_return", "has_hourly_values_table": true, "has_hourly_constituents": false, "released": true, "type": "single_asset", "level_frequencies": ["1s", "15s", "1h", "1d"], "extra_daily_frequencies": ["1d-ny-close"]}, {"name": "GMCI30", "base": "gmci", "full_name": "GMCI Top 30 Index", "description": "GMCI 30 represents a selection of the top 30 cryptocurrencies, showcasing the giants of the digital asset world.", "id": 5001, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["1s"], "extra_daily_frequencies": []}, {"name": "GML2", "base": "gmci", "full_name": "GMCI L2 Index", "description": "The GMCI L2 Index embodies a meticulously curated index of leading Layer 2 blockchain technologies, shining a spotlight on innovations driving scalability and efficiency in the blockchain sphere.", "id": 5002, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["1s"], "extra_daily_frequencies": []}, {"name": "GMMEME", "base": "gmci", "full_name": "GMCI Meme Index", "description": "The GMCI Meme contains the leading meme coins by market capitalization, capturing the unique essence and humor intrinsic to crypto culture and web3 communities. It focuses on meme coins that have not only captured the imagination of the digital world but have also demonstrated significant community engagement and market presence.", "id": 5003, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": true, "type": "multi_asset", "level_frequencies": ["1s"], "extra_daily_frequencies": []}, {"name": "GMAI", "base": "gmci", "full_name": "GMCI AI Index", "description": "The GMCI AI (GMAI) is an index tracking the performance of the top AI coins based on their circulating market capitalization.", "id": 5004, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "type": "multi_asset", "level_frequencies": ["1s"], "extra_daily_frequencies": []}, {"name": "GMSOL", "base": "gmci", "full_name": "GMCI SOL Index", "description": "GMCI Solana Ecosystem index", "id": 5005, "return_type": "price_return", "has_hourly_values_table": false, "has_hourly_constituents": false, "released": false, "type": "multi_asset", "level_frequencies": ["1s"], "extra_daily_frequencies": []}]