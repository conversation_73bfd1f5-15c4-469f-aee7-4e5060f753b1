#!/usr/bin/env bash

pgm=$(basename $0)

K8S_SCRIPT_DIR="src/octopus/applications/deploy_k8s"

KUBE_NAMESPACE=${KUBE_NAMESPACE:-"fh"}
CI_ENVIRONMENT_SLUG=${CI_ENVIRONMENT_SLUG:-"staging"}
CI_PROJECT_PATH_SLUG=${CI_PROJECT_PATH_SLUG:-"coinmetrics-octopus"}
CI_REGISTRY_IMAGE=${CI_REGISTRY_IMAGE:-"registry.gitlab.com/coinmetrics/feed-handlers/octopus"}
CI_COMMIT_SHA=${CI_COMMIT_SHA:-$(git rev-parse origin/$(git rev-parse --abbrev-ref HEAD))}

CI_SECRET_NAME=${CI_SECRET_NAME:-"gitlab-registry"}

export KUBE_NAMESPACE CI_ENVIRONMENT_SLUG CI_PROJECT_PATH_SLUG CI_REGISTRY_IMAGE CI_COMMIT_SHA CI_SECRET_NAME
unset EXCHANGE DATA_TYPE MARKET_TYPE CONNECTION_MODE

usage() {
    echo "Usage: $pgm [-S] deployment_script_name [script_args]" 1>&2
    exit 1
}

# usage of existing secret to pull images
SKIP_SECRET=true
if [ "$1" == '-S' ] ; then
    shift
    SKIP_SECRET=false
fi

if [ $# -lt 1 ] || [ $# -gt 2 ]; then
    usage
fi

TARGET="$K8S_SCRIPT_DIR/$1"
SCRIPT_ARGS="$2"

# TODO: Remove SKIP_SECRET once hand-crafted testing has been completed
$SKIP_SECRET || kubectl create secret docker-registry $CI_SECRET_NAME \
    --docker-server="$CI_REGISTRY" --docker-username="gitlab-ci-token" --docker-password="$CI_JOB_TOKEN" \
    -o yaml --dry-run=client | kubectl -n $KUBE_NAMESPACE apply -f - || exit 1

if [ ! -s $TARGET ] ; then
    echo "$pgm: Deployment target not found: $TARGET" 1>&2
    exit 1
fi

bash $TARGET $SCRIPT_ARGS || exit 1

exit 0
